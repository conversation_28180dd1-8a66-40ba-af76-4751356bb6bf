{"name": "privacy-fingerprint-example", "displayName": "Privacy Fingerprint Example", "description": "Example VS Code extension demonstrating privacy-focused fingerprinting", "version": "0.1.0", "engines": {"vscode": "^1.82.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "privacy-fingerprint.generateFingerprint", "title": "Generate Privacy Fingerprint"}, {"command": "privacy-fingerprint.checkTrialStatus", "title": "Check Trial Status"}, {"command": "privacy-fingerprint.rotateFingerprint", "title": "Rotate Fingerprint"}, {"command": "privacy-fingerprint.analyzePrivacy", "title": "Analyze Privacy Protection"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.82.0", "@types/node": "18.x", "typescript": "^5.1.6"}, "dependencies": {"crypto": "^1.0.1"}}