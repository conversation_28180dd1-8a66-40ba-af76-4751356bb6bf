
/**
 * Data expiration management for privacy compliance
 */

import * as vscode from 'vscode';

export interface ExpirationPolicy {
    maxAge: number; // milliseconds
    checkInterval: number; // milliseconds
    autoCleanup: boolean;
}

export class DataExpirationManager {
    private context: vscode.ExtensionContext;
    private policy: ExpirationPolicy;
    private cleanupTimer?: NodeJS.Timeout;

    constructor(context: vscode.ExtensionContext, policy?: ExpirationPolicy) {
        this.context = context;
        this.policy = policy || {
            maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
            checkInterval: 24 * 60 * 60 * 1000, // 24 hours
            autoCleanup: true,
        };

        if (this.policy.autoCleanup) {
            this.startCleanupTimer();
        }
    }

    async markDataCreated(key: string): Promise<void> {
        const timestamp = Date.now();
        await this.context.globalState.update(`${key}_created`, timestamp);
    }

    async isDataExpired(key: string): Promise<boolean> {
        const created = this.context.globalState.get<number>(`${key}_created`);
        if (!created) return true;

        const age = Date.now() - created;
        return age > this.policy.maxAge;
    }

    async cleanupExpiredData(): Promise<string[]> {
        const cleanedKeys: string[] = [];
        const allKeys = this.context.globalState.keys();

        for (const key of allKeys) {
            if (key.endsWith('_created')) continue;

            if (await this.isDataExpired(key)) {
                await this.context.globalState.update(key, undefined);
                await this.context.globalState.update(`${key}_created`, undefined);
                cleanedKeys.push(key);
            }
        }

        return cleanedKeys;
    }

    private startCleanupTimer(): void {
        this.cleanupTimer = setInterval(async () => {
            const cleaned = await this.cleanupExpiredData();
            if (cleaned.length > 0) {
                console.log(`Cleaned up ${cleaned.length} expired data entries`);
            }
        }, this.policy.checkInterval);
    }

    dispose(): void {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }
    }
}
