
/**
 * System information collection with privacy protection
 */

import * as vscode from 'vscode';
import * as os from 'os';
import * as crypto from 'crypto';

export class SystemInfoCollector {
    /**
     * Collect privacy-protected system information
     */
    static collectSystemInfo(): any {
        return {
            platform: os.platform(),
            arch: os.arch(),
            release: this.getOSVersionClass(),
            hostname_hash: this.hashSensitiveData(os.hostname()),
            username_hash: this.hashSensitiveData(os.userInfo().username),
            memory_class: this.classifyMemory(os.totalmem()),
            cpu_signature: this.getCPUSignature(),
            timezone_offset: new Date().getTimezoneOffset(),
            locale: vscode.env.language,
        };
    }

    private static getOSVersionClass(): string {
        const release = os.release();
        const platform = os.platform();

        if (platform === 'darwin') {
            return 'macos-recent';
        } else if (platform === 'win32') {
            return 'windows-10-11';
        } else {
            return 'linux-modern';
        }
    }

    private static classifyMemory(totalMemory: number): string {
        const gb = totalMemory / (1024 * 1024 * 1024);
        if (gb <= 4) return '0-4GB';
        if (gb <= 8) return '4-8GB';
        if (gb <= 16) return '8-16GB';
        if (gb <= 32) return '16-32GB';
        return '32GB+';
    }

    private static getCPUSignature(): string {
        const cpus = os.cpus();
        if (cpus.length === 0) return 'unknown';

        const model = cpus[0].model.toLowerCase();
        let vendor = 'unknown';
        let tier = 'standard';

        if (model.includes('intel')) vendor = 'intel';
        else if (model.includes('amd')) vendor = 'amd';
        else if (model.includes('apple')) vendor = 'apple';

        if (model.includes('i3') || model.includes('ryzen 3')) tier = 'entry';
        else if (model.includes('i5') || model.includes('ryzen 5')) tier = 'mid';
        else if (model.includes('i7') || model.includes('ryzen 7')) tier = 'high';
        else if (model.includes('i9') || model.includes('ryzen 9')) tier = 'premium';
        else if (model.includes('m1') || model.includes('m2')) tier = 'apple-silicon';

        const noise = Math.floor(Math.random() * 1000);
        return `${vendor}-${tier}-${noise}`;
    }

    private static hashSensitiveData(data: string): string {
        return crypto.createHash('sha256').update(data + 'privacy_salt').digest('hex').substring(0, 16);
    }
}
