
/**
 * Workspace state management for fingerprint data
 */

import * as vscode from 'vscode';

export class WorkspaceStateManager {
    private context: vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
    }

    async storeWorkspaceFingerprint(workspaceId: string, signature: string): Promise<void> {
        await this.context.workspaceState.update(`workspace_fingerprint_${workspaceId}`, signature);
    }

    async getWorkspaceFingerprint(workspaceId: string): Promise<string | undefined> {
        return this.context.workspaceState.get<string>(`workspace_fingerprint_${workspaceId}`);
    }

    async clearWorkspaceData(): Promise<void> {
        const keys = this.context.workspaceState.keys();
        for (const key of keys) {
            if (key.startsWith('workspace_fingerprint_')) {
                await this.context.workspaceState.update(key, undefined);
            }
        }
    }
}
