
/**
 * Trial prevention system with privacy-protected fingerprinting
 */

import * as vscode from 'vscode';
import { FingerprintStorage, PrivacyFingerprint } from './fingerprint_storage';

export interface TrialState {
    isTrialActive: boolean;
    trialStartDate: string;
    trialEndDate: string;
    usageCount: number;
    fingerprintId: string;
    bypassAttempts: number;
}

export class TrialPreventionSystem {
    private storage: FingerprintStorage;
    private context: vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.storage = new FingerprintStorage(context);
    }

    async initializeTrial(): Promise<TrialState> {
        let fingerprint = await this.storage.retrieveFingerprint();

        if (!fingerprint) {
            // Generate new fingerprint for new trial
            fingerprint = await this.generateNewFingerprint();
            await this.storage.storeFingerprint(fingerprint);
        }

        const existingTrial = await this.getTrialState(fingerprint.id);
        if (existingTrial) {
            return existingTrial;
        }

        // Create new trial state
        const trialState: TrialState = {
            isTrialActive: true,
            trialStartDate: new Date().toISOString(),
            trialEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
            usageCount: 1,
            fingerprintId: fingerprint.id,
            bypassAttempts: 0,
        };

        await this.storeTrialState(trialState);
        return trialState;
    }

    async checkTrialStatus(): Promise<{ valid: boolean; daysRemaining: number }> {
        const fingerprint = await this.storage.retrieveFingerprint();
        if (!fingerprint) {
            return { valid: false, daysRemaining: 0 };
        }

        const trialState = await this.getTrialState(fingerprint.id);
        if (!trialState) {
            return { valid: false, daysRemaining: 0 };
        }

        const now = new Date();
        const endDate = new Date(trialState.trialEndDate);
        const daysRemaining = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000)));

        return {
            valid: trialState.isTrialActive && now < endDate,
            daysRemaining,
        };
    }

    async detectBypassAttempt(): Promise<boolean> {
        const fingerprint = await this.storage.retrieveFingerprint();
        if (!fingerprint) return false;

        // Check for signs of bypass attempts
        const stats = await this.storage.getStorageStats();
        const trialState = await this.getTrialState(fingerprint.id);

        if (!trialState) return false;

        // Detect if fingerprint was tampered with
        const currentFingerprint = await this.generateCurrentFingerprint();
        const similarity = this.calculateSimilarity(fingerprint, currentFingerprint);

        if (similarity < 0.7) {
            // Significant change detected - possible bypass attempt
            trialState.bypassAttempts++;
            await this.storeTrialState(trialState);
            return true;
        }

        return false;
    }

    private async generateNewFingerprint(): Promise<PrivacyFingerprint> {
        // This would call the Rust fingerprint generator
        // For now, return a mock fingerprint
        return {
            id: this.generateId(),
            version: '1.0.0',
            created_at: new Date().toISOString(),
            privacy_level: 'High',
            rotation_enabled: true,
            next_rotation: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
            device_id: this.generateId(),
            hardware_signature: {
                cpu_signature: 'mock-cpu',
                memory_class: '8-16GB',
                architecture: 'x64',
                hardware_uuid_hash: this.generateId(),
                performance_class: 'high',
            },
            system_signature: {
                os_family: process.platform,
                os_version_class: 'modern',
                locale_region: 'US',
                timezone_offset: new Date().getTimezoneOffset(),
                hostname_hash: this.generateId(),
                username_hash: this.generateId(),
            },
            vscode_signature: {
                version_class: '1.80-1.90',
                machine_id_hash: this.generateId(),
                session_signature: vscode.env.sessionId,
                workspace_signature: this.generateId(),
                extensions_signature: this.generateId(),
            },
            network_signature: {
                interface_count: 1,
                mac_signature: this.generateId(),
                network_class: 'ethernet',
                connectivity_signature: this.generateId(),
            },
            privacy_metadata: {
                anonymization_applied: ['hashing', 'classification'],
                obfuscation_methods: ['sha256'],
                data_retention_policy: '24h-rotation',
                privacy_score: 0.8,
                tracking_resistance: 0.75,
            },
            fingerprint_hash: this.generateId(),
        };
    }

    private async generateCurrentFingerprint(): Promise<PrivacyFingerprint> {
        // Generate fingerprint based on current system state
        return this.generateNewFingerprint();
    }

    private calculateSimilarity(fp1: PrivacyFingerprint, fp2: PrivacyFingerprint): number {
        // Simple similarity calculation
        let matches = 0;
        let total = 0;

        if (fp1.hardware_signature.cpu_signature === fp2.hardware_signature.cpu_signature) matches++;
        total++;

        if (fp1.hardware_signature.memory_class === fp2.hardware_signature.memory_class) matches++;
        total++;

        if (fp1.system_signature.os_family === fp2.system_signature.os_family) matches++;
        total++;

        return matches / total;
    }

    private async getTrialState(fingerprintId: string): Promise<TrialState | null> {
        const stateData = this.context.globalState.get<string>(`trial_state_${fingerprintId}`);
        return stateData ? JSON.parse(stateData) : null;
    }

    private async storeTrialState(state: TrialState): Promise<void> {
        await this.context.globalState.update(`trial_state_${state.fingerprintId}`, JSON.stringify(state));
    }

    private generateId(): string {
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }
}
