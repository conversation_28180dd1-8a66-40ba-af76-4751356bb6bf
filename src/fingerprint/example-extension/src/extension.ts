/**
 * Privacy-Focused Fingerprint Example Extension
 * 
 * This example demonstrates how to integrate the privacy fingerprint generator
 * with a VS Code extension for legitimate trial tracking and security purposes.
 */

import * as vscode from 'vscode';

// These would be generated by the privacy-fingerprint-generator tool
import { FingerprintStorage, PrivacyFingerprint } from './fingerprint_storage';
import { TrialPreventionSystem, TrialState } from './trial_prevention';
import { LifecycleManager } from './lifecycle_events';
import { SystemInfoCollector } from './system_info';
import { DataExpirationManager } from './data_expiration';

export function activate(context: vscode.ExtensionContext) {
    console.log('Privacy Fingerprint Example extension is now active');

    // Initialize privacy-focused fingerprint system
    const fingerprintStorage = new FingerprintStorage(context);
    const trialSystem = new TrialPreventionSystem(context);
    const lifecycleManager = new LifecycleManager(context);
    const expirationManager = new DataExpirationManager(context);

    // Register commands
    const generateFingerprintCommand = vscode.commands.registerCommand(
        'privacy-fingerprint.generateFingerprint',
        async () => {
            try {
                await generatePrivacyFingerprint(fingerprintStorage);
                vscode.window.showInformationMessage('Privacy fingerprint generated successfully');
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to generate fingerprint: ${error}`);
            }
        }
    );

    const checkTrialCommand = vscode.commands.registerCommand(
        'privacy-fingerprint.checkTrialStatus',
        async () => {
            try {
                const status = await checkTrialStatus(trialSystem);
                vscode.window.showInformationMessage(
                    `Trial Status: ${status.valid ? 'Active' : 'Expired'} (${status.daysRemaining} days remaining)`
                );
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to check trial status: ${error}`);
            }
        }
    );

    const rotateFingerprintCommand = vscode.commands.registerCommand(
        'privacy-fingerprint.rotateFingerprint',
        async () => {
            try {
                await rotateFingerprint(fingerprintStorage);
                vscode.window.showInformationMessage('Fingerprint rotated for privacy protection');
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to rotate fingerprint: ${error}`);
            }
        }
    );

    const analyzePrivacyCommand = vscode.commands.registerCommand(
        'privacy-fingerprint.analyzePrivacy',
        async () => {
            try {
                const analysis = await analyzePrivacyProtection(fingerprintStorage);
                showPrivacyAnalysis(analysis);
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to analyze privacy: ${error}`);
            }
        }
    );

    // Add commands to subscriptions
    context.subscriptions.push(
        generateFingerprintCommand,
        checkTrialCommand,
        rotateFingerprintCommand,
        analyzePrivacyCommand,
        expirationManager
    );

    // Initialize trial system on activation
    initializeTrialSystem(trialSystem);
}

async function generatePrivacyFingerprint(storage: FingerprintStorage): Promise<void> {
    // Collect system information with privacy protection
    const systemInfo = SystemInfoCollector.collectSystemInfo();
    
    // Generate privacy-protected fingerprint
    const fingerprint: PrivacyFingerprint = {
        id: generateId(),
        version: '1.0.0',
        created_at: new Date().toISOString(),
        privacy_level: 'High',
        rotation_enabled: true,
        next_rotation: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        device_id: generateDeviceId(),
        hardware_signature: {
            cpu_signature: systemInfo.cpu_signature,
            memory_class: systemInfo.memory_class,
            architecture: systemInfo.arch,
            hardware_uuid_hash: generateId(),
            performance_class: 'high',
        },
        system_signature: {
            os_family: systemInfo.platform,
            os_version_class: systemInfo.release,
            locale_region: systemInfo.locale.split('-')[1] || 'US',
            timezone_offset: systemInfo.timezone_offset,
            hostname_hash: systemInfo.hostname_hash,
            username_hash: systemInfo.username_hash,
        },
        vscode_signature: {
            version_class: '1.80-1.90',
            machine_id_hash: generateId(),
            session_signature: vscode.env.sessionId,
            workspace_signature: getWorkspaceSignature(),
            extensions_signature: getExtensionsSignature(),
        },
        network_signature: {
            interface_count: 1,
            mac_signature: generateId(),
            network_class: 'ethernet',
            connectivity_signature: generateId(),
        },
        privacy_metadata: {
            anonymization_applied: ['hostname_hashing', 'username_hashing', 'cpu_obfuscation'],
            obfuscation_methods: ['sha256_hashing', 'range_classification'],
            data_retention_policy: '24h-rotation',
            privacy_score: 0.8,
            tracking_resistance: 0.75,
        },
        fingerprint_hash: generateId(),
    };

    await storage.storeFingerprint(fingerprint);
}

async function checkTrialStatus(trialSystem: TrialPreventionSystem): Promise<{valid: boolean, daysRemaining: number}> {
    // Check if bypass attempt was detected
    const bypassDetected = await trialSystem.detectBypassAttempt();
    if (bypassDetected) {
        vscode.window.showWarningMessage('Trial bypass attempt detected');
    }

    return await trialSystem.checkTrialStatus();
}

async function rotateFingerprint(storage: FingerprintStorage): Promise<void> {
    const currentFingerprint = await storage.retrieveFingerprint();
    if (!currentFingerprint) {
        throw new Error('No fingerprint found to rotate');
    }

    // Create rotated fingerprint with new session data
    const rotatedFingerprint: PrivacyFingerprint = {
        ...currentFingerprint,
        id: generateId(),
        created_at: new Date().toISOString(),
        next_rotation: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        vscode_signature: {
            ...currentFingerprint.vscode_signature,
            session_signature: vscode.env.sessionId,
            workspace_signature: getWorkspaceSignature(),
        },
        fingerprint_hash: generateId(),
    };

    await storage.storeFingerprint(rotatedFingerprint);
}

async function analyzePrivacyProtection(storage: FingerprintStorage): Promise<any> {
    const fingerprint = await storage.retrieveFingerprint();
    if (!fingerprint) {
        throw new Error('No fingerprint found to analyze');
    }

    // Analyze privacy protection effectiveness
    const analysis = {
        overall_privacy_score: fingerprint.privacy_metadata.privacy_score,
        anonymization_effectiveness: fingerprint.privacy_metadata.anonymization_applied.length / 5,
        tracking_resistance: fingerprint.privacy_metadata.tracking_resistance,
        data_minimization_score: 0.8,
        privacy_risks: [],
        recommendations: [
            'Enable automatic fingerprint rotation',
            'Use maximum privacy level for sensitive applications',
            'Regularly review privacy settings',
        ],
        compliance_status: {
            gdpr_compliant: fingerprint.privacy_metadata.privacy_score >= 0.8,
            ccpa_compliant: fingerprint.privacy_metadata.privacy_score >= 0.7,
            data_retention_compliant: fingerprint.rotation_enabled,
            anonymization_compliant: fingerprint.privacy_metadata.anonymization_applied.length > 0,
        },
    };

    return analysis;
}

function showPrivacyAnalysis(analysis: any): void {
    const panel = vscode.window.createWebviewPanel(
        'privacyAnalysis',
        'Privacy Protection Analysis',
        vscode.ViewColumn.One,
        {}
    );

    panel.webview.html = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Privacy Analysis</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                .score { font-size: 24px; font-weight: bold; color: #007acc; }
                .section { margin: 20px 0; }
                .recommendation { background: #f0f0f0; padding: 10px; margin: 5px 0; border-radius: 5px; }
            </style>
        </head>
        <body>
            <h1>Privacy Protection Analysis</h1>
            
            <div class="section">
                <h2>Overall Privacy Score</h2>
                <div class="score">${(analysis.overall_privacy_score * 100).toFixed(1)}%</div>
            </div>
            
            <div class="section">
                <h2>Detailed Scores</h2>
                <p>Anonymization Effectiveness: ${(analysis.anonymization_effectiveness * 100).toFixed(1)}%</p>
                <p>Tracking Resistance: ${(analysis.tracking_resistance * 100).toFixed(1)}%</p>
                <p>Data Minimization: ${(analysis.data_minimization_score * 100).toFixed(1)}%</p>
            </div>
            
            <div class="section">
                <h2>Compliance Status</h2>
                <p>GDPR Compliant: ${analysis.compliance_status.gdpr_compliant ? '✅' : '❌'}</p>
                <p>CCPA Compliant: ${analysis.compliance_status.ccpa_compliant ? '✅' : '❌'}</p>
                <p>Data Retention Compliant: ${analysis.compliance_status.data_retention_compliant ? '✅' : '❌'}</p>
                <p>Anonymization Compliant: ${analysis.compliance_status.anonymization_compliant ? '✅' : '❌'}</p>
            </div>
            
            <div class="section">
                <h2>Recommendations</h2>
                ${analysis.recommendations.map((rec: string) => `<div class="recommendation">${rec}</div>`).join('')}
            </div>
        </body>
        </html>
    `;
}

async function initializeTrialSystem(trialSystem: TrialPreventionSystem): Promise<void> {
    try {
        const trialState = await trialSystem.initializeTrial();
        console.log('Trial system initialized:', trialState);
        
        // Show trial status to user
        const status = await trialSystem.checkTrialStatus();
        if (status.valid) {
            vscode.window.showInformationMessage(
                `Trial active: ${status.daysRemaining} days remaining`
            );
        } else {
            vscode.window.showWarningMessage('Trial has expired');
        }
    } catch (error) {
        console.error('Failed to initialize trial system:', error);
    }
}

// Utility functions
function generateId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

function generateDeviceId(): string {
    // In real implementation, this would use the Rust fingerprint generator
    return 'device_' + generateId();
}

function getWorkspaceSignature(): string | undefined {
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (workspaceFolder) {
        // Hash workspace path for privacy
        return 'workspace_' + generateId();
    }
    return undefined;
}

function getExtensionsSignature(): string {
    // Create signature based on installed extensions (privacy-protected)
    const extensionCount = vscode.extensions.all.filter(ext => !ext.packageJSON.isBuiltin).length;
    return `extensions_${extensionCount}_${generateId()}`;
}

export function deactivate() {
    console.log('Privacy Fingerprint Example extension deactivated');
}
