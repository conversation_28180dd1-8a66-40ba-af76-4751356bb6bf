
/**
 * Privacy-focused fingerprint storage using VS Code APIs
 *
 * This module provides secure storage and retrieval of device fingerprints
 * using VS Code's built-in storage APIs with privacy protection.
 */

import * as vscode from 'vscode';
import * as crypto from 'crypto';

export interface PrivacyFingerprint {
    id: string;
    version: string;
    created_at: string;
    privacy_level: 'Low' | 'Medium' | 'High' | 'Maximum';
    rotation_enabled: boolean;
    next_rotation?: string;
    device_id: string;
    hardware_signature: HardwareSignature;
    system_signature: SystemSignature;
    vscode_signature: VSCodeSignature;
    network_signature: NetworkSignature;
    privacy_metadata: PrivacyMetadata;
    fingerprint_hash: string;
}

export interface HardwareSignature {
    cpu_signature: string;
    memory_class: string;
    architecture: string;
    hardware_uuid_hash: string;
    performance_class: string;
}

export interface SystemSignature {
    os_family: string;
    os_version_class: string;
    locale_region: string;
    timezone_offset: number;
    hostname_hash: string;
    username_hash: string;
}

export interface VSCodeSignature {
    version_class: string;
    machine_id_hash: string;
    session_signature: string;
    workspace_signature?: string;
    extensions_signature: string;
}

export interface NetworkSignature {
    interface_count: number;
    mac_signature: string;
    network_class: string;
    connectivity_signature: string;
}

export interface PrivacyMetadata {
    anonymization_applied: string[];
    obfuscation_methods: string[];
    data_retention_policy: string;
    privacy_score: number;
    tracking_resistance: number;
}

export class FingerprintStorage {
    private context: vscode.ExtensionContext;
    private encryptionKey: string;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.encryptionKey = this.generateEncryptionKey();
    }

    /**
     * Store fingerprint using VS Code's secure storage
     */
    async storeFingerprint(fingerprint: PrivacyFingerprint): Promise<void> {
        try {
            const encryptedData = this.encryptData(JSON.stringify(fingerprint));

            // Store in global storage for persistence across workspaces
            await this.context.globalState.update('privacy_fingerprint', encryptedData);

            // Also store in secure storage if available
            if (this.context.secrets) {
                await this.context.secrets.store('fingerprint_data', encryptedData);
            }

            // Update last access timestamp
            await this.context.globalState.update('fingerprint_last_access', Date.now());

        } catch (error) {
            throw new Error(`Failed to store fingerprint: ${error}`);
        }
    }

    /**
     * Retrieve fingerprint from storage
     */
    async retrieveFingerprint(): Promise<PrivacyFingerprint | null> {
        try {
            // Try secure storage first
            let encryptedData: string | undefined;

            if (this.context.secrets) {
                encryptedData = await this.context.secrets.get('fingerprint_data');
            }

            // Fallback to global storage
            if (!encryptedData) {
                encryptedData = this.context.globalState.get<string>('privacy_fingerprint');
            }

            if (!encryptedData) {
                return null;
            }

            const decryptedData = this.decryptData(encryptedData);
            const fingerprint: PrivacyFingerprint = JSON.parse(decryptedData);

            // Update last access
            await this.context.globalState.update('fingerprint_last_access', Date.now());

            return fingerprint;

        } catch (error) {
            console.error('Failed to retrieve fingerprint:', error);
            return null;
        }
    }

    /**
     * Check if fingerprint rotation is due
     */
    async isRotationDue(): Promise<boolean> {
        const fingerprint = await this.retrieveFingerprint();
        if (!fingerprint || !fingerprint.rotation_enabled) {
            return false;
        }

        if (fingerprint.next_rotation) {
            const nextRotation = new Date(fingerprint.next_rotation);
            return Date.now() >= nextRotation.getTime();
        }

        return false;
    }

    /**
     * Clear stored fingerprint data
     */
    async clearFingerprint(): Promise<void> {
        await this.context.globalState.update('privacy_fingerprint', undefined);
        if (this.context.secrets) {
            await this.context.secrets.delete('fingerprint_data');
        }
        await this.context.globalState.update('fingerprint_last_access', undefined);
    }

    /**
     * Get storage statistics
     */
    async getStorageStats(): Promise<{lastAccess?: number, hasFingerprint: boolean}> {
        const lastAccess = this.context.globalState.get<number>('fingerprint_last_access');
        const hasFingerprint = (await this.retrieveFingerprint()) !== null;

        return { lastAccess, hasFingerprint };
    }

    private generateEncryptionKey(): string {
        // Generate a deterministic key based on VS Code machine ID
        const machineId = vscode.env.machineId;
        return crypto.createHash('sha256').update(machineId + 'fingerprint_key').digest('hex');
    }

    private encryptData(data: string): string {
        const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return encrypted;
    }

    private decryptData(encryptedData: string): string {
        const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
        let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }
}

/**
 * Utility functions for VS Code integration
 */
export class VSCodeUtils {
    /**
     * Get VS Code machine ID (hashed for privacy)
     */
    static getMachineIdHash(): string {
        return crypto.createHash('sha256').update(vscode.env.machineId).digest('hex').substring(0, 16);
    }

    /**
     * Get VS Code session ID
     */
    static getSessionId(): string {
        return vscode.env.sessionId;
    }

    /**
     * Get workspace signature
     */
    static getWorkspaceSignature(): string | undefined {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (workspaceFolder) {
            return crypto.createHash('sha256').update(workspaceFolder.uri.fsPath).digest('hex').substring(0, 16);
        }
        return undefined;
    }

    /**
     * Get installed extensions signature
     */
    static getExtensionsSignature(): string {
        const extensions = vscode.extensions.all
            .filter(ext => !ext.packageJSON.isBuiltin)
            .map(ext => ext.id)
            .sort()
            .join(',');

        return crypto.createHash('sha256').update(extensions).digest('hex').substring(0, 16);
    }

    /**
     * Get VS Code version class
     */
    static getVersionClass(): string {
        const version = vscode.version;
        const majorMinor = version.split('.').slice(0, 2).join('.');
        return `${majorMinor}.x`;
    }
}
