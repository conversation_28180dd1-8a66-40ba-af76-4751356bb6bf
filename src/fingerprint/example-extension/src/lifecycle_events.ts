
/**
 * Extension lifecycle event handlers for fingerprint management
 */

import * as vscode from 'vscode';
import { FingerprintStorage } from './fingerprint_storage';

export class LifecycleManager {
    private storage: FingerprintStorage;
    private rotationTimer?: NodeJS.Timeout;

    constructor(context: vscode.ExtensionContext) {
        this.storage = new FingerprintStorage(context);
        this.setupLifecycleHandlers(context);
    }

    private setupLifecycleHandlers(context: vscode.ExtensionContext): void {
        // Handle extension activation
        this.onActivation();

        // Handle workspace changes
        vscode.workspace.onDidChangeWorkspaceFolders(() => {
            this.onWorkspaceChange();
        });

        // Handle extension deactivation
        context.subscriptions.push({
            dispose: () => this.onDeactivation()
        });

        // Setup automatic rotation check
        this.setupRotationTimer();
    }

    private async onActivation(): Promise<void> {
        console.log('Privacy fingerprint system activated');

        // Check if rotation is due
        if (await this.storage.isRotationDue()) {
            console.log('Fingerprint rotation is due');
            // Trigger rotation process
        }
    }

    private async onWorkspaceChange(): Promise<void> {
        console.log('Workspace changed - updating fingerprint');
        // Update workspace-specific components of fingerprint
    }

    private onDeactivation(): void {
        console.log('Privacy fingerprint system deactivated');
        if (this.rotationTimer) {
            clearInterval(this.rotationTimer);
        }
    }

    private setupRotationTimer(): void {
        // Check for rotation every hour
        this.rotationTimer = setInterval(async () => {
            if (await this.storage.isRotationDue()) {
                console.log('Automatic fingerprint rotation triggered');
                // Trigger rotation
            }
        }, 60 * 60 * 1000); // 1 hour
    }
}
