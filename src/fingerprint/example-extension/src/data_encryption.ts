
/**
 * Data encryption utilities for fingerprint storage
 */

import * as crypto from 'crypto';

export class DataEncryption {
    private static readonly ALGORITHM = 'aes-256-gcm';
    private static readonly KEY_LENGTH = 32;
    private static readonly IV_LENGTH = 16;
    private static readonly TAG_LENGTH = 16;

    static encrypt(data: string, key: string): string {
        const keyBuffer = crypto.scryptSync(key, 'salt', DataEncryption.KEY_LENGTH);
        const iv = crypto.randomBytes(DataEncryption.IV_LENGTH);
        const cipher = crypto.createCipherGCM(DataEncryption.ALGORITHM, keyBuffer, iv);

        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');

        const tag = cipher.getAuthTag();

        return iv.toString('hex') + ':' + tag.toString('hex') + ':' + encrypted;
    }

    static decrypt(encryptedData: string, key: string): string {
        const parts = encryptedData.split(':');
        if (parts.length !== 3) {
            throw new Error('Invalid encrypted data format');
        }

        const iv = Buffer.from(parts[0], 'hex');
        const tag = Buffer.from(parts[1], 'hex');
        const encrypted = parts[2];

        const keyBuffer = crypto.scryptSync(key, 'salt', DataEncryption.KEY_LENGTH);
        const decipher = crypto.createDecipherGCM(DataEncryption.ALGORITHM, keyBuffer, iv);
        decipher.setAuthTag(tag);

        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');

        return decrypted;
    }

    static generateKey(): string {
        return crypto.randomBytes(DataEncryption.KEY_LENGTH).toString('hex');
    }
}
