# Examples and Usage Guide

This directory contains example configurations, test data, and usage scenarios for the VS Code Extension Security Research Tools.

## 📁 Directory Structure

```
examples/
├── README.md                    # This file
├── configurations/             # Example configuration files
├── test-data/                  # Mock data for testing
├── scenarios/                  # Common usage scenarios
└── scripts/                    # Helper scripts
```

## 🚀 Quick Start Examples

### 1. Basic Fingerprint Analysis

```bash
# Collect and analyze system fingerprint
cargo run --bin fingerprint-analyzer -- collect --detailed

# Compare two fingerprints
cargo run --bin fingerprint-analyzer -- compare

# Explain fingerprinting concepts
cargo run --bin fingerprint-analyzer -- explain basics
```

### 2. Trial Reset Simulation

```bash
# Simulate all bypass attempts
cargo run --bin trial-reset-simulator -- simulate-all

# Simulate storage clearing only
cargo run --bin trial-reset-simulator -- clear-storage --all

# Explain protection mechanisms
cargo run --bin trial-reset-simulator -- explain fingerprinting
```

### 3. Storage Inspection

```bash
# Inspect VS Code storage patterns
cargo run --bin storage-inspector -- inspect

# Analyze trial tracking mechanisms
cargo run --bin storage-inspector -- analyze-trial-tracking --detailed

# Simulate storage clearing
cargo run --bin storage-inspector -- simulate-clear --show-persistent
```

### 4. Network Traffic Simulation

```bash
# Simulate authentication flow
cargo run --bin network-traffic-simulator -- simulate-auth

# Simulate subscription validation
cargo run --bin network-traffic-simulator -- simulate-validation --cycles 5

# Start mock server for testing
cargo run --bin network-traffic-simulator -- mock-server --port 8080
```

## 📊 Output Formats

All tools support multiple output formats:

```bash
# Console output (default)
cargo run --bin fingerprint-analyzer -- collect

# JSON output
cargo run --bin fingerprint-analyzer -- collect --format json

# Save to file
cargo run --bin fingerprint-analyzer -- collect --output results.json --format json

# Markdown report
cargo run --bin storage-inspector -- inspect --format markdown --output report.md
```

## 🎯 Common Scenarios

### Educational Workshop

For teaching about extension security:

```bash
# 1. Show how fingerprinting works
cargo run --bin fingerprint-analyzer -- collect --detailed

# 2. Demonstrate why trial resets fail
cargo run --bin trial-reset-simulator -- simulate-all

# 3. Explain storage persistence
cargo run --bin storage-inspector -- simulate-clear --show-persistent

# 4. Show network validation
cargo run --bin network-traffic-simulator -- simulate-validation
```

### Security Research

For analyzing extension protection mechanisms:

```bash
# Comprehensive analysis with detailed output
cargo run --bin fingerprint-analyzer -- collect --detailed --format json --output fingerprint.json
cargo run --bin storage-inspector -- analyze-trial-tracking --detailed --format json --output storage.json
cargo run --bin network-traffic-simulator -- analyze-endpoints --detailed --format json --output network.json
```

### Development Testing

For extension developers testing their protection mechanisms:

```bash
# Test fingerprint uniqueness
cargo run --bin fingerprint-analyzer -- compare

# Test storage persistence
cargo run --bin storage-inspector -- simulate-clear

# Test network validation
cargo run --bin network-traffic-simulator -- simulate-validation --frequency 60 --cycles 10
```

## 🔧 Configuration

### Environment Variables

Set these environment variables for different behaviors:

```bash
# Accept ethics agreement automatically (for CI/testing)
export SECURITY_TOOLS_ETHICS_ACCEPTED=true

# Enable educational mode
export EDUCATIONAL_MODE=true

# Set logging level
export RUST_LOG=info
```

### Mock Mode

All tools default to mock mode for safety:

```bash
# Explicitly enable mock mode (default)
cargo run --bin fingerprint-analyzer -- collect --mock-mode true

# Disable mock mode (not recommended for production)
cargo run --bin fingerprint-analyzer -- collect --mock-mode false
```

## 📚 Educational Use Cases

### Understanding Fingerprinting

```bash
# Learn about different fingerprint components
cargo run --bin fingerprint-analyzer -- explain basics
cargo run --bin fingerprint-analyzer -- explain privacy
cargo run --bin fingerprint-analyzer -- explain countermeasures

# See fingerprint in action
cargo run --bin fingerprint-analyzer -- collect --detailed
```

### Learning About Storage

```bash
# Understand storage types
cargo run --bin storage-inspector -- explain types
cargo run --bin storage-inspector -- explain persistence
cargo run --bin storage-inspector -- explain security

# See storage patterns
cargo run --bin storage-inspector -- map-locations --system-wide
```

### Network Security Education

```bash
# Learn about validation mechanisms
cargo run --bin network-traffic-simulator -- explain validation
cargo run --bin network-traffic-simulator -- explain security
cargo run --bin network-traffic-simulator -- explain authentication

# See network flows
cargo run --bin network-traffic-simulator -- simulate-auth --method oauth
```

## 🧪 Testing and Validation

### Running Tests

```bash
# Run all tests
cargo test

# Run tests for specific tool
cargo test --bin fingerprint-analyzer

# Run with verbose output
cargo test -- --nocapture
```

### Validation Scripts

```bash
# Validate all tools work correctly
./examples/scripts/validate-tools.sh

# Run comprehensive test suite
./examples/scripts/run-test-suite.sh
```

## ⚠️ Safety and Ethics

### Always Remember

- These tools are for **educational purposes only**
- Use **mock mode** for safety (default behavior)
- **Never** attempt to bypass real commercial software
- Follow the **ethical guidelines** in ETHICS.md
- Respect **terms of service** of all software

### Safe Usage Checklist

- ✅ Read and understand ETHICS.md
- ✅ Use mock mode for all testing
- ✅ Only analyze your own systems
- ✅ Follow responsible disclosure for vulnerabilities
- ✅ Respect intellectual property rights

## 🤝 Contributing Examples

To contribute new examples:

1. Create a new scenario in `scenarios/`
2. Add any required test data to `test-data/`
3. Document the example in this README
4. Ensure it follows ethical guidelines
5. Test thoroughly with mock data

## 📞 Support

For questions about examples or usage:

- Check the tool-specific README files
- Review the main project documentation
- Open an issue on GitHub
- Contact the maintainers

---

**Remember: The goal is education and security improvement, not exploitation!**
