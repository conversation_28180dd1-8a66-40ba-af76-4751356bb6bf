# Profile Validator

An educational tool that validates VS Code profile configurations and analyzes how different profile setups affect trial tracking persistence. This tool helps security researchers understand the impact of profile isolation on trial mechanisms.

## Features

- **Profile Detection**: Automatically detect current VS Code profile configuration
- **Persistence Analysis**: Analyze how trial data persists across different profile types
- **Cross-Profile Testing**: Test if trial data persists when switching profiles
- **Server-Side Validation**: Test server-side tracking mechanisms
- **Security Assessment**: Evaluate bypass resistance and isolation effectiveness
- **Recommendations**: Generate profile setup recommendations for different testing scenarios

## Usage

### Basic Profile Detection

```bash
# Detect current profile with detailed information
cargo run --bin profile-validator -- detect-profile --detailed

# Basic profile detection
cargo run --bin profile-validator -- detect-profile
```

### Persistence Validation

```bash
# Validate trial persistence with cross-profile testing
cargo run --bin profile-validator -- validate-persistence --cross-profile --server-side

# Basic persistence validation
cargo run --bin profile-validator -- validate-persistence
```

### Profile Impact Analysis

```bash
# Analyze impact with profile comparison
cargo run --bin profile-validator -- analyze-impact --compare

# Basic impact analysis
cargo run --bin profile-validator -- analyze-impact
```

### Profile Recommendations

```bash
# Get recommendations for trial testing
cargo run --bin profile-validator -- recommend --scenario trial

# Get recommendations for bypass research
cargo run --bin profile-validator -- recommend --scenario bypass

# Get recommendations for complete isolation
cargo run --bin profile-validator -- recommend --scenario isolation
```

### Profile Switch Simulation

```bash
# Simulate switching from default to temporary profile
cargo run --bin profile-validator -- simulate-switch --from Default --to Temporary

# Basic switch simulation
cargo run --bin profile-validator -- simulate-switch
```

### Educational Information

```bash
# Learn about VS Code profiles
cargo run --bin profile-validator -- explain profiles

# Learn about data persistence
cargo run --bin profile-validator -- explain persistence

# Learn about profile isolation
cargo run --bin profile-validator -- explain isolation
```

## Output Formats

The tool supports multiple output formats:

```bash
# JSON output
cargo run --bin profile-validator -- detect-profile --format json

# Markdown output
cargo run --bin profile-validator -- detect-profile --format markdown

# HTML output
cargo run --bin profile-validator -- detect-profile --format html

# Console output (default)
cargo run --bin profile-validator -- detect-profile --format console
```

## Profile Types Analyzed

### Default Profile
- **Location**: `~/Library/Application Support/Code` (macOS)
- **Persistence**: High - survives restarts and updates
- **Isolation**: Low - affects production environment
- **Use Case**: Realistic trial experience testing

### Temporary Profile
- **Location**: `/tmp/vscode-temp-*`
- **Persistence**: Low - may not survive restarts
- **Isolation**: High - completely separate from production
- **Use Case**: Isolated testing without contamination

### Custom Profile
- **Location**: User-specified directory
- **Persistence**: Configurable based on location
- **Isolation**: Medium to High
- **Use Case**: Controlled testing environments

## Security Assessment Metrics

The tool evaluates profiles using several metrics:

- **Bypass Resistance**: How difficult it is to bypass trial restrictions
- **Isolation Effectiveness**: How well the profile isolates test data
- **Persistence Strength**: How well trial data survives across sessions
- **Overall Security Score**: Combined assessment of all factors

## Educational Use Cases

### 1. Trial Mechanism Research
```bash
# Understand how trials work in production
cargo run --bin profile-validator -- detect-profile --detailed
```

### 2. Isolation Testing
```bash
# Test profile isolation effectiveness
cargo run --bin profile-validator -- recommend --scenario isolation
```

### 3. Persistence Analysis
```bash
# Analyze data persistence patterns
cargo run --bin profile-validator -- validate-persistence --cross-profile
```

### 4. Security Assessment
```bash
# Evaluate overall security posture
cargo run --bin profile-validator -- analyze-impact --compare
```

## Example Output

### Profile Detection
```json
{
  "current_profile": {
    "profile_name": "Default",
    "is_default": true,
    "is_temporary": false,
    "user_data_dir": "/Users/<USER>/Library/Application Support/Code",
    "persistence_level": "System"
  },
  "security_assessment": {
    "bypass_resistance": 0.875,
    "isolation_effectiveness": 0.3,
    "overall_security_score": 0.658
  }
}
```

### Recommendations
```json
{
  "scenario": "Complete Isolation",
  "recommended_setup": "Use temporary profile with network isolation",
  "command_line": "code --user-data-dir=/tmp/vscode-isolated --disable-extensions",
  "benefits": [
    "Maximum isolation from system",
    "No network-based tracking",
    "Clean testing environment"
  ]
}
```

## Ethical Guidelines

This tool is designed for educational and legitimate security research purposes only:

- ✅ Understanding profile mechanisms
- ✅ Educational security research
- ✅ Privacy impact assessment
- ✅ Architecture analysis

- ❌ Actual bypass attempts
- ❌ Commercial software manipulation
- ❌ Trial extension or reset
- ❌ Terms of service violations

## Safety Features

- **Mock Mode**: Uses simulated data for safety
- **Read-Only**: Never modifies actual VS Code data
- **Ethics Enforcement**: Requires explicit consent for educational use
- **Logging**: All usage is logged for accountability

## Integration

The profile validator integrates with other security research tools:

- **Storage Inspector**: Analyzes storage mechanisms
- **Fingerprint Analyzer**: Examines device fingerprinting
- **Trial Reset Simulator**: Tests bypass resistance
- **Network Traffic Simulator**: Validates server-side tracking

## Contributing

When contributing to this educational tool:

1. Maintain focus on educational value
2. Ensure all features respect ethical guidelines
3. Add comprehensive documentation
4. Include safety checks and mock modes
5. Test with various profile configurations

## License

MIT License - See LICENSE file for details.

This tool is provided for educational purposes only. Users are responsible for complying with all applicable laws and terms of service.
