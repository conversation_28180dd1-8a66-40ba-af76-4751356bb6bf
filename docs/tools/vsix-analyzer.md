# VSIX Analyzer

A comprehensive Python command-line tool for analyzing VS Code extension VSIX files to detect trial/subscription mechanisms, multi-trial prevention systems, and security features.

## Features

- **Comprehensive VSIX Analysis**: Extract and analyze VSIX package structure, manifests, and code
- **Trial Detection**: Identify trial period tracking and multi-trial prevention mechanisms
- **System Fingerprinting**: Detect hardware and system fingerprinting techniques
- **Authentication Analysis**: Analyze OAuth flows, token management, and session handling
- **Storage Mechanisms**: Find data persistence patterns (globalState, secrets, localStorage)
- **Network Analysis**: Extract API endpoints and subscription validation calls
- **Multiple Output Formats**: Console, Markdown, JSON, and HTML reports
- **Batch Processing**: Analyze multiple VSIX files in a directory
- **Custom Pattern Search**: Search for specific regex patterns in extension code

## Installation

### From Source

```bash
git clone https://github.com/example/vsix-analyzer.git
cd vsix-analyzer
pip install -e .
```

### Using pip

```bash
pip install vsix-analyzer
```

## Quick Start

### Basic Analysis

```bash
# Analyze a single VSIX file
vsix-analyzer extension.vsix

# Analyze with verbose output
vsix-analyzer extension.vsix --verbose
```

### Specific Analysis Modules

```bash
# Analyze only fingerprinting and authentication
vsix-analyzer extension.vsix --fingerprinting --auth

# Analyze trial detection mechanisms
vsix-analyzer extension.vsix --trial-detection --format markdown --output report.md
```

### Batch Analysis

```bash
# Analyze all VSIX files in a directory
vsix-analyzer --batch ./extensions/ --format json --output results.json
```

### Custom Pattern Search

```bash
# Search for specific patterns
vsix-analyzer extension.vsix --patterns "trial.*expired" --verbose

# Search with custom output
vsix-analyzer extension.vsix --patterns "machineId|deviceId" --format markdown
```

## Command-Line Options

### Required Arguments
- `VSIX_FILE` - Path to the VSIX file to analyze

### Analysis Options
- `--all` - Run all analysis modules (default)
- `--fingerprinting` - Analyze system fingerprinting mechanisms
- `--auth` - Analyze authentication and OAuth flows
- `--storage` - Analyze data storage and persistence
- `--network` - Analyze network calls and API endpoints
- `--trial-detection` - Analyze trial and subscription logic
- `--patterns PATTERN` - Search for custom regex patterns

### Output Options
- `--format {console,markdown,json,html}` - Output format (default: console)
- `--output FILE` - Save results to file instead of stdout
- `--verbose` - Enable detailed logging and debug output
- `--quiet` - Suppress non-essential output

### Advanced Options
- `--extract-dir DIR` - Directory to extract VSIX contents (temp by default)
- `--keep-extracted` - Don't delete extracted files after analysis
- `--config FILE` - Load configuration from file
- `--batch DIR` - Analyze all VSIX files in directory

## Analysis Modules

### System Fingerprinting Detection
Identifies techniques used to create unique system signatures:
- VS Code machine ID usage
- Hardware information collection (CPU, memory, OS)
- System identifiers and device IDs
- Fingerprint creation and hashing

### Authentication Analysis
Analyzes authentication and authorization mechanisms:
- OAuth 2.0 flows and state management
- Token handling (access, refresh, bearer tokens)
- Session management and persistence
- Authentication headers and API calls

### Storage Mechanisms
Detects data persistence and storage patterns:
- VS Code storage APIs (globalState, workspaceState, secrets)
- Web storage (localStorage, sessionStorage)
- Storage key patterns and data structures

### Network Analysis
Examines network communication and API usage:
- API endpoints and base URLs
- HTTP methods and request patterns
- Subscription and billing API calls
- Authentication and authorization endpoints

### Trial Detection
Identifies trial period and subscription management:
- Trial expiration and validation logic
- Subscription status tracking
- License validation mechanisms
- Premium feature restrictions

## Output Formats

### Console Output
Formatted text output with emojis and colors for terminal viewing.

### Markdown Report
Structured markdown document suitable for documentation and sharing.

### JSON Output
Machine-readable format for integration with other tools and scripts.

### HTML Report
Web-viewable report with styling and tables for presentation.

## Configuration

Create a JSON configuration file to customize analysis behavior:

```json
{
  "analysis": {
    "max_matches_per_pattern": 50,
    "context_chars": 50
  },
  "output": {
    "truncate_length": 100
  }
}
```

Use with `--config config.json`.

## Examples

### Security Research
```bash
# Comprehensive security analysis
vsix-analyzer suspicious-extension.vsix --all --format html --output security-report.html

# Focus on trial bypass detection
vsix-analyzer extension.vsix --trial-detection --fingerprinting --verbose
```

### Compliance Auditing
```bash
# Generate compliance report
vsix-analyzer enterprise-extension.vsix --format markdown --output compliance-report.md

# Batch audit of all extensions
vsix-analyzer --batch ./company-extensions/ --format json --output audit-results.json
```

### Development Analysis
```bash
# Analyze your own extension
vsix-analyzer my-extension.vsix --storage --network --quiet

# Search for specific API usage
vsix-analyzer extension.vsix --patterns "globalState\.(get|update)" --format console
```

## Security Considerations

This tool is designed for legitimate security research, compliance auditing, and development purposes. Users should:

- Only analyze extensions they own or have permission to analyze
- Respect intellectual property and licensing terms
- Use findings responsibly and ethically
- Report security vulnerabilities through appropriate channels

## Contributing

Contributions are welcome! Please read our contributing guidelines and submit pull requests for any improvements.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This tool is provided for educational and research purposes only. Users are responsible for ensuring their use complies with applicable laws and regulations.
