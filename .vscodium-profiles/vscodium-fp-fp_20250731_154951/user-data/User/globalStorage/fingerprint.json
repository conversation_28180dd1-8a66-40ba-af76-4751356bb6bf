{"id": "4157b85a-03a2-4b51-8601-fc93939ef7e8", "version": "1.0.0", "created_at": "2025-07-31T08:49:51.828767Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-01T08:49:51.828767Z", "device_id": "oJarGSpnHeT1GjpxfbO2og", "hardware_signature": {"cpu_signature": "apple-apple-silicon-244", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "hvZrESeCFxPPv1HXeOmbdw", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "Lq4TpWWu_0NwqY8s0yt8Jw", "username_hash": "PPYQQ-VAx2UwTufAix0aKg"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "0MnO4Cv3MVSGtCQG6y32nA", "session_signature": "-EW8oFR0uzM", "workspace_signature": "DhKtIA7aEKY", "extensions_signature": "eXDE813KTTk"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-g1gBZ6Qt", "network_class": "ethernet", "connectivity_signature": "zMy6DOL9xPY"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "ba2d47450e3721a56f52107cbf75f2f39c4bd871e1ff785c41628590c43e8130"}