{"id": "4fa93520-0e03-43b2-b6c9-3256964ad66a", "version": "1.0.0", "created_at": "2025-07-31T08:45:50.531592Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-01T08:45:50.531592Z", "device_id": "f1gc6BkTqvq0LoF0tyuxIg", "hardware_signature": {"cpu_signature": "apple-apple-silicon-518", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "WavLNjRC-QkkbOMZ0sGqQQ", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "R4d1QXk3j8wW3LJOBx6J6A", "username_hash": "nI6TYQKDHgPA5yJyzma7zg"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "vhWqlnGfl8YoWEKMdC6iiw", "session_signature": "1NbH-n2d3uU", "workspace_signature": "7p3di4EvNSE", "extensions_signature": "S1yhJVlxur4"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-d9msc35c", "network_class": "ethernet", "connectivity_signature": "exXKSzNsrIg"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "481c8950ee26c86f2e7f5bb29eee97b6fc78182a5b050a9415b2e347a3ed4463"}