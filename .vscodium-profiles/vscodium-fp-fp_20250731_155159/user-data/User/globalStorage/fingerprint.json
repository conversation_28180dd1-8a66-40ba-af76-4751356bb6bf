{"id": "14c2260e-4a25-4bdb-bd52-19b61d9322c6", "version": "1.0.0", "created_at": "2025-07-31T08:52:00.469353Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-01T08:52:00.469353Z", "device_id": "a7KoVhqAW5rgMASEgl6Z3g", "hardware_signature": {"cpu_signature": "apple-apple-silicon-91", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "_lGEnC2b00nAD4S-Nu2T2g", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "S1YgB2o2_2skwRPc6XZohA", "username_hash": "LAMw4NGWw2_xRx7G7BFuPA"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "fTMXoqjAGhZGz9taWao_gA", "session_signature": "PjXv2h4ytLs", "workspace_signature": "_9IpGEo4bVM", "extensions_signature": "KdakjOdAcqA"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-x8Y1fuZJ", "network_class": "ethernet", "connectivity_signature": "FyEeEHS0He4"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "519c1a5b5385dd8936cfe6133e9168b0ad62d34f66ce0f877cb4ca7363a67968"}