{"id": "99e8e71c-e8a9-431d-bf19-4d0420c0fc0c", "version": "1.0.0", "created_at": "2025-07-31T08:53:03.468763Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-01T08:53:03.468763Z", "device_id": "PLML2q_QNZM0CRQwNiCpSw", "hardware_signature": {"cpu_signature": "apple-apple-silicon-549", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "V2DrUcGYuSmqwONNVEnsUQ", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "wODTSjZQRpaf_hNProDLig", "username_hash": "RoyMHtLb1HMBiow1hspqcA"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "7uuvmN4EB4_cta7apUdCzA", "session_signature": "Fkyz0k825Ko", "workspace_signature": "TMycifMx1WE", "extensions_signature": "YWxYpZNlE_U"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-Cr6JpoEe", "network_class": "ethernet", "connectivity_signature": "I6PbxYO4xrE"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "039e7774ce429d4bfcac90d512e74753b5b8d6577ead969d09a4ab9af546b5c2"}