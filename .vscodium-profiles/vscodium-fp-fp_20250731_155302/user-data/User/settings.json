{"security.workspace.trust.enabled": false, "telemetry.telemetryLevel": "off", "update.mode": "none", "extensions.autoUpdate": false, "workbench.enableExperiments": false, "workbench.startupEditor": "none", "window.restoreWindows": "none", "window.newWindowDimensions": "inherit", "window.openFilesInNewWindow": "off", "window.openFoldersInNewWindow": "off", "window.openWithoutArgumentsInNewWindow": "off", "window.restoreFullscreen": false, "window.titleBarStyle": "custom", "window.menuBarVisibility": "toggle", "window.title": "🔒 Isolated VSCodium - ${activeEditorShort}${separator}${rootName}", "window.titleSeparator": " - ", "workbench.editor.enablePreview": false, "workbench.editor.enablePreviewFromQuickOpen": false, "workbench.editor.showTabs": true, "workbench.editor.tabCloseButton": "right", "workbench.editor.tabSizing": "fit", "workbench.editor.wrapTabs": false, "workbench.editor.scrollToSwitchTabs": false, "workbench.editor.focusRecentEditorAfterClose": true, "workbench.editor.limit.enabled": false, "workbench.editor.restoreViewState": false, "workbench.colorTheme": "Default Dark+", "workbench.iconTheme": "vs-seti", "workbench.productIconTheme": "<PERSON><PERSON><PERSON>", "privacy.fingerprintIsolation": true, "privacy.profileName": "vscodium-fp-fp_20250731_155302", "privacy.fingerprintId": "fp_20250731_155302", "privacy.fingerprintPath": "/Users/<USER>/Documents/augment-projects/exploit-extension/.vscodium-profiles/vscodium-fp-fp_20250731_155302/user-data/User/globalStorage/fingerprint.json", "privacy.privacyLevel": "high", "augment.fingerprintOverride": "/Users/<USER>/Documents/augment-projects/exploit-extension/.vscodium-profiles/vscodium-fp-fp_20250731_155302/user-data/User/globalStorage/fingerprint.json", "extensions.gallery.serviceUrl": "https://open-vsx.org/vscode/gallery", "extensions.gallery.itemUrl": "https://open-vsx.org/vscode/item", "extensions.gallery.searchUrl": "https://open-vsx.org/vscode/search", "extensions.gallery.resourceUrlTemplate": "https://open-vsx.org/vscode/unpkg/{publisher}/{name}/{version}/{path}", "extensions.gallery.cacheUrl": "https://open-vsx.org/vscode/cache", "extensions.gallery.controlUrl": "https://open-vsx.org/vscode/control", "extensions.gallery.nlsBaseUrl": "https://www.bing.com/api/v7.0/suggestions", "editor.suggestSelection": "first", "vsintellicode.modify.editor.suggestSelection": "automaticallyOverrodeDefaultValue"}