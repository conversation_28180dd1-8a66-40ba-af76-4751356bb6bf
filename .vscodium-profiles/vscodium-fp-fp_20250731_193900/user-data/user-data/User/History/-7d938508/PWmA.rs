use gloo::storage::{LocalStorage, Storage};
use serde::{Deserialize, Serialize};
use wasm_bindgen::prelude::*;
use wasm_bindgen::closure::Closure;

use web_sys::{IdbDatabase, IdbRequest, IdbTransaction, IdbVersionChangeEvent};
use js_sys::Array;
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};

/// Database schema version for migration management
pub const DB_VERSION: u32 = 1;
pub const DB_NAME: &str = "OpenCutDB";

/// Database schema definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseSchema {
    pub version: u32,
    pub stores: Vec<ObjectStoreSchema>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ObjectStoreSchema {
    pub name: String,
    pub key_path: Option<String>,
    pub auto_increment: bool,
    pub indexes: Vec<IndexSchema>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IndexSchema {
    pub name: String,
    pub key_path: String,
    pub unique: bool,
    pub multi_entry: bool,
}

/// Project data structure for storage
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StoredProject {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub version: String,
    pub timeline_data: serde_json::Value,
    pub settings: ProjectSettings,
    pub thumbnail: Option<String>, // Base64 encoded thumbnail
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectSettings {
    pub video_resolution: (u32, u32),
    pub frame_rate: f64,
    pub audio_sample_rate: u32,
    pub audio_channels: u32,
    pub default_duration: f64,
}

impl Default for ProjectSettings {
    fn default() -> Self {
        Self {
            video_resolution: (1920, 1080),
            frame_rate: 30.0,
            audio_sample_rate: 48000,
            audio_channels: 2,
            default_duration: 60.0,
        }
    }
}

/// Media file metadata for storage
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StoredMediaFile {
    pub id: Uuid,
    pub name: String,
    pub file_type: String,
    pub mime_type: String,
    pub size: u64,
    pub duration: Option<f64>,
    pub resolution: Option<(u32, u32)>,
    pub created_at: DateTime<Utc>,
    pub last_accessed: DateTime<Utc>,
    pub thumbnail: Option<String>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// User settings and preferences
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserSettings {
    pub id: String,
    pub theme: String,
    pub language: String,
    pub auto_save_interval: u32, // seconds
    pub default_project_settings: ProjectSettings,
    pub keyboard_shortcuts: HashMap<String, String>,
    pub ui_preferences: HashMap<String, serde_json::Value>,
    pub updated_at: DateTime<Utc>,
}

impl Default for UserSettings {
    fn default() -> Self {
        Self {
            id: "default".to_string(),
            theme: "dark".to_string(),
            language: "en".to_string(),
            auto_save_interval: 30,
            default_project_settings: ProjectSettings::default(),
            keyboard_shortcuts: HashMap::new(),
            ui_preferences: HashMap::new(),
            updated_at: Utc::now(),
        }
    }
}

/// Cache entry for media files
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheEntry {
    pub id: String,
    pub media_id: Uuid,
    pub cache_type: CacheType,
    pub data: Vec<u8>,
    pub created_at: DateTime<Utc>,
    pub last_accessed: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
    pub size: u64,
    pub access_count: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CacheType {
    Thumbnail,
    Waveform,
    Preview,
    ProcessedVideo,
    ProcessedAudio,
}

/// Database migration record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MigrationRecord {
    pub version: u32,
    pub applied_at: DateTime<Utc>,
    pub description: String,
}

/// Get the database schema for the current version
pub fn get_database_schema() -> DatabaseSchema {
    DatabaseSchema {
        version: DB_VERSION,
        stores: vec![
            ObjectStoreSchema {
                name: "projects".to_string(),
                key_path: Some("id".to_string()),
                auto_increment: false,
                indexes: vec![
                    IndexSchema {
                        name: "name".to_string(),
                        key_path: "name".to_string(),
                        unique: false,
                        multi_entry: false,
                    },
                    IndexSchema {
                        name: "created_at".to_string(),
                        key_path: "created_at".to_string(),
                        unique: false,
                        multi_entry: false,
                    },
                    IndexSchema {
                        name: "updated_at".to_string(),
                        key_path: "updated_at".to_string(),
                        unique: false,
                        multi_entry: false,
                    },
                ],
            },
            ObjectStoreSchema {
                name: "media_files".to_string(),
                key_path: Some("id".to_string()),
                auto_increment: false,
                indexes: vec![
                    IndexSchema {
                        name: "name".to_string(),
                        key_path: "name".to_string(),
                        unique: false,
                        multi_entry: false,
                    },
                    IndexSchema {
                        name: "file_type".to_string(),
                        key_path: "file_type".to_string(),
                        unique: false,
                        multi_entry: false,
                    },
                    IndexSchema {
                        name: "created_at".to_string(),
                        key_path: "created_at".to_string(),
                        unique: false,
                        multi_entry: false,
                    },
                ],
            },
            ObjectStoreSchema {
                name: "user_settings".to_string(),
                key_path: Some("id".to_string()),
                auto_increment: false,
                indexes: vec![],
            },
            ObjectStoreSchema {
                name: "cache".to_string(),
                key_path: Some("id".to_string()),
                auto_increment: false,
                indexes: vec![
                    IndexSchema {
                        name: "media_id".to_string(),
                        key_path: "media_id".to_string(),
                        unique: false,
                        multi_entry: false,
                    },
                    IndexSchema {
                        name: "cache_type".to_string(),
                        key_path: "cache_type".to_string(),
                        unique: false,
                        multi_entry: false,
                    },
                    IndexSchema {
                        name: "expires_at".to_string(),
                        key_path: "expires_at".to_string(),
                        unique: false,
                        multi_entry: false,
                    },
                    IndexSchema {
                        name: "last_accessed".to_string(),
                        key_path: "last_accessed".to_string(),
                        unique: false,
                        multi_entry: false,
                    },
                ],
            },
            ObjectStoreSchema {
                name: "migrations".to_string(),
                key_path: Some("version".to_string()),
                auto_increment: false,
                indexes: vec![
                    IndexSchema {
                        name: "applied_at".to_string(),
                        key_path: "applied_at".to_string(),
                        unique: false,
                        multi_entry: false,
                    },
                ],
            },
        ],
    }
}

/// IndexedDB database manager
#[derive(Clone)]
pub struct DatabaseManager {
    db: Option<IdbDatabase>,
}

impl DatabaseManager {
    pub fn new() -> Self {
        Self { db: None }
    }

    /// Initialize the database connection
    pub async fn init(&mut self) -> Result<(), JsValue> {
        let window = web_sys::window().ok_or("No window object")?;
        let idb_factory = window
            .indexed_db()
            .map_err(|_| "IndexedDB not supported")?
            .ok_or("IndexedDB not available")?;

        let open_request = idb_factory.open_with_u32(DB_NAME, DB_VERSION)?;

        // Set up upgrade handler
        let upgrade_callback = Closure::wrap(Box::new(move |event: IdbVersionChangeEvent| {
            if let Some(target) = event.target() {
                if let Ok(request) = target.dyn_into::<IdbRequest>() {
                    if let Ok(db) = request.result().and_then(|r| r.dyn_into::<IdbDatabase>()) {
                        if let Err(e) = Self::setup_database_schema(&db) {
                            web_sys::console::error_1(&format!("Database setup error: {:?}", e).into());
                        }
                    }
                }
            }
        }) as Box<dyn FnMut(_)>);

        open_request.set_onupgradeneeded(Some(upgrade_callback.as_ref().unchecked_ref()));
        upgrade_callback.forget();

        let request_future = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&open_request));
        let result = request_future.await?;
        let db = result.dyn_into::<IdbDatabase>()?;

        self.db = Some(db);
        Ok(())
    }

    /// Set up database schema during upgrade
    fn setup_database_schema(db: &IdbDatabase) -> Result<(), JsValue> {
        let schema = get_database_schema();

        for store_schema in &schema.stores {
            // Try to create object store (will fail if it already exists)
            if true { // Always try to create, handle errors gracefully
                let mut store_params = web_sys::IdbObjectStoreParameters::new();

                if let Some(key_path) = &store_schema.key_path {
                    store_params.key_path(Some(&JsValue::from_str(key_path)));
                }
                store_params.auto_increment(store_schema.auto_increment);

                let object_store = db.create_object_store_with_optional_parameters(
                    &store_schema.name,
                    &store_params,
                )?;

                // Create indexes
                for index_schema in &store_schema.indexes {
                    if true { // Always try to create index, handle errors gracefully
                        let mut index_params = web_sys::IdbIndexParameters::new();
                        index_params.unique(index_schema.unique);
                        index_params.multi_entry(index_schema.multi_entry);

                        object_store.create_index_with_str_and_optional_parameters(
                            &index_schema.name,
                            &index_schema.key_path,
                            &index_params,
                        )?;
                    }
                }
            }
        }

        Ok(())
    }

    /// Get a reference to the database
    pub fn get_db(&self) -> Result<&IdbDatabase, String> {
        self.db.as_ref().ok_or_else(|| "Database not initialized".to_string())
    }

    /// Create a transaction for the given store names
    pub fn transaction(&self, store_names: &[&str], mode: &str) -> Result<IdbTransaction, JsValue> {
        let db = self.get_db().map_err(|e| JsValue::from_str(&e))?;
        let store_names_array = Array::new();
        for name in store_names {
            store_names_array.push(&JsValue::from_str(name));
        }
        let transaction_mode = match mode {
            "readonly" => web_sys::IdbTransactionMode::Readonly,
            "readwrite" => web_sys::IdbTransactionMode::Readwrite,
            _ => web_sys::IdbTransactionMode::Readonly,
        };
        db.transaction_with_str_sequence_and_mode(&store_names_array, transaction_mode)
    }

    /// Save a project to the database
    pub async fn save_project(&self, project: &StoredProject) -> Result<(), JsValue> {
        let transaction = self.transaction(&["projects"], "readwrite")?;
        let store = transaction.object_store("projects")?;

        let project_value = serde_wasm_bindgen::to_value(project)?;
        let request = store.put(&project_value)?;

        wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;
        Ok(())
    }

    /// Load a project from the database
    pub async fn load_project(&self, project_id: &Uuid) -> Result<Option<StoredProject>, JsValue> {
        let transaction = self.transaction(&["projects"], "readonly")?;
        let store = transaction.object_store("projects")?;

        let key = JsValue::from_str(&project_id.to_string());
        let request = store.get(&key)?;
        let result = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;

        if result.is_undefined() || result.is_null() {
            return Ok(None);
        }

        let project: StoredProject = serde_wasm_bindgen::from_value(result)?;
        Ok(Some(project))
    }

    /// List all projects
    pub async fn list_projects(&self) -> Result<Vec<StoredProject>, JsValue> {
        let transaction = self.transaction(&["projects"], "readonly")?;
        let store = transaction.object_store("projects")?;

        let request = store.get_all()?;
        let result = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;

        let projects_array: Array = result.dyn_into()?;
        let mut projects = Vec::new();

        for i in 0..projects_array.length() {
            if let Ok(project_value) = projects_array.get(i).dyn_into::<JsValue>() {
                if let Ok(project) = serde_wasm_bindgen::from_value::<StoredProject>(project_value) {
                    projects.push(project);
                }
            }
        }

        Ok(projects)
    }

    /// Delete a project
    pub async fn delete_project(&self, project_id: &Uuid) -> Result<(), JsValue> {
        let transaction = self.transaction(&["projects"], "readwrite")?;
        let store = transaction.object_store("projects")?;

        let key = JsValue::from_str(&project_id.to_string());
        let request = store.delete(&key)?;

        wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;
        Ok(())
    }

    /// Save media file metadata
    pub async fn save_media_file(&self, media_file: &StoredMediaFile) -> Result<(), JsValue> {
        let transaction = self.transaction(&["media_files"], "readwrite")?;
        let store = transaction.object_store("media_files")?;

        let media_value = serde_wasm_bindgen::to_value(media_file)?;
        let request = store.put(&media_value)?;

        wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;
        Ok(())
    }

    /// Load media file metadata
    pub async fn load_media_file(&self, media_id: &Uuid) -> Result<Option<StoredMediaFile>, JsValue> {
        let transaction = self.transaction(&["media_files"], "readonly")?;
        let store = transaction.object_store("media_files")?;

        let key = JsValue::from_str(&media_id.to_string());
        let request = store.get(&key)?;
        let result = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;

        if result.is_undefined() || result.is_null() {
            return Ok(None);
        }

        let media_file: StoredMediaFile = serde_wasm_bindgen::from_value(result)?;
        Ok(Some(media_file))
    }

    /// List all media files
    pub async fn list_media_files(&self) -> Result<Vec<StoredMediaFile>, JsValue> {
        let transaction = self.transaction(&["media_files"], "readonly")?;
        let store = transaction.object_store("media_files")?;

        let request = store.get_all()?;
        let result = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;

        let files_array: Array = result.dyn_into()?;
        let mut media_files = Vec::new();

        for i in 0..files_array.length() {
            if let Ok(file_value) = files_array.get(i).dyn_into::<JsValue>() {
                if let Ok(media_file) = serde_wasm_bindgen::from_value::<StoredMediaFile>(file_value) {
                    media_files.push(media_file);
                }
            }
        }

        Ok(media_files)
    }

    /// Delete a media file
    pub async fn delete_media_file(&self, media_id: &Uuid) -> Result<(), JsValue> {
        let transaction = self.transaction(&["media_files"], "readwrite")?;
        let store = transaction.object_store("media_files")?;

        let key = JsValue::from_str(&media_id.to_string());
        let request = store.delete(&key)?;

        wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;
        Ok(())
    }

    /// Save user settings
    pub async fn save_user_settings(&self, settings: &UserSettings) -> Result<(), JsValue> {
        let transaction = self.transaction(&["user_settings"], "readwrite")?;
        let store = transaction.object_store("user_settings")?;

        let settings_value = serde_wasm_bindgen::to_value(settings)?;
        let request = store.put(&settings_value)?;

        wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;
        Ok(())
    }

    /// Load user settings
    pub async fn load_user_settings(&self, settings_id: &str) -> Result<Option<UserSettings>, JsValue> {
        let transaction = self.transaction(&["user_settings"], "readonly")?;
        let store = transaction.object_store("user_settings")?;

        let key = JsValue::from_str(settings_id);
        let request = store.get(&key)?;
        let result = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;

        if result.is_undefined() || result.is_null() {
            return Ok(None);
        }

        let settings: UserSettings = serde_wasm_bindgen::from_value(result)?;
        Ok(Some(settings))
    }

    /// Save cache entry
    pub async fn save_cache_entry(&self, cache_entry: &CacheEntry) -> Result<(), JsValue> {
        let transaction = self.transaction(&["cache"], "readwrite")?;
        let store = transaction.object_store("cache")?;

        let cache_value = serde_wasm_bindgen::to_value(cache_entry)?;
        let request = store.put(&cache_value)?;

        wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;
        Ok(())
    }

    /// Load cache entry
    pub async fn load_cache_entry(&self, cache_id: &str) -> Result<Option<CacheEntry>, JsValue> {
        let transaction = self.transaction(&["cache"], "readonly")?;
        let store = transaction.object_store("cache")?;

        let key = JsValue::from_str(cache_id);
        let request = store.get(&key)?;
        let result = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;

        if result.is_undefined() || result.is_null() {
            return Ok(None);
        }

        let cache_entry: CacheEntry = serde_wasm_bindgen::from_value(result)?;
        Ok(Some(cache_entry))
    }

    /// Clean expired cache entries
    pub async fn clean_expired_cache(&self) -> Result<u32, JsValue> {
        let transaction = self.transaction(&["cache"], "readwrite")?;
        let store = transaction.object_store("cache")?;
        let index = store.index("expires_at")?;

        let now = Utc::now();
        let now_value = serde_wasm_bindgen::to_value(&now)?;

        // Get all entries that have expired
        let range = web_sys::IdbKeyRange::upper_bound(&now_value)?;
        let request = index.open_cursor_with_range(&range)?;
        let cursor_result = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;

        let mut deleted_count = 0u32;

        if !cursor_result.is_null() {
            let cursor: web_sys::IdbCursorWithValue = cursor_result.dyn_into()?;

            loop {
                let delete_request = cursor.delete()?;
                wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&delete_request)).await?;
                deleted_count += 1;

                cursor.continue_()?;
            }
        }

        Ok(deleted_count)
    }

    /// Get cache entries by media ID
    pub async fn get_cache_entries_by_media(&self, media_id: &Uuid) -> Result<Vec<CacheEntry>, JsValue> {
        let transaction = self.transaction(&["cache"], "readonly")?;
        let store = transaction.object_store("cache")?;
        let index = store.index("media_id")?;

        let key = JsValue::from_str(&media_id.to_string());
        let request = index.get_all_with_key(&key)?;
        let result = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;

        let entries_array: Array = result.dyn_into()?;
        let mut cache_entries = Vec::new();

        for i in 0..entries_array.length() {
            if let Ok(entry_value) = entries_array.get(i).dyn_into::<JsValue>() {
                if let Ok(cache_entry) = serde_wasm_bindgen::from_value::<CacheEntry>(entry_value) {
                    cache_entries.push(cache_entry);
                }
            }
        }

        Ok(cache_entries)
    }

    /// Delete all cache entries for a media file
    pub async fn delete_media_cache_entries(&self, media_id: &Uuid) -> Result<u32, JsValue> {
        let cache_entries = self.get_cache_entries_by_media(media_id).await?;
        let mut deleted_count = 0u32;

        let transaction = self.transaction(&["cache"], "readwrite")?;
        let store = transaction.object_store("cache")?;

        for entry in cache_entries {
            let key = JsValue::from_str(&entry.id);
            let request = store.delete(&key)?;
            wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;
            deleted_count += 1;
        }

        Ok(deleted_count)
    }

    /// Get cache statistics by type
    pub async fn get_cache_stats_by_type(&self) -> Result<HashMap<String, CacheTypeStats>, JsValue> {
        let transaction = self.transaction(&["cache"], "readonly")?;
        let store = transaction.object_store("cache")?;
        let request = store.get_all()?;
        let result = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;

        let entries_array: Array = result.dyn_into()?;
        let mut stats_map: HashMap<String, CacheTypeStats> = HashMap::new();

        for i in 0..entries_array.length() {
            if let Ok(entry_value) = entries_array.get(i).dyn_into::<JsValue>() {
                if let Ok(cache_entry) = serde_wasm_bindgen::from_value::<CacheEntry>(entry_value) {
                    let cache_type_str = match cache_entry.cache_type {
                        CacheType::Thumbnail => "thumbnail",
                        CacheType::Waveform => "waveform",
                        CacheType::Preview => "preview",
                        CacheType::ProcessedVideo => "processed_video",
                        CacheType::ProcessedAudio => "processed_audio",
                    }.to_string();

                    let stats = stats_map.entry(cache_type_str).or_insert(CacheTypeStats::default());
                    stats.count += 1;
                    stats.total_size += cache_entry.size;
                }
            }
        }

        Ok(stats_map)
    }

    /// Get database size information
    pub async fn get_database_size(&self) -> Result<DatabaseSizeInfo, JsValue> {
        let mut size_info = DatabaseSizeInfo::default();

        // Count projects
        let transaction = self.transaction(&["projects", "media_files", "cache"], "readonly")?;

        let projects_store = transaction.object_store("projects")?;
        let projects_request = projects_store.count()?;
        let projects_count = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&projects_request)).await?;
        size_info.projects_count = projects_count.as_f64().unwrap_or(0.0) as u32;

        let media_store = transaction.object_store("media_files")?;
        let media_request = media_store.count()?;
        let media_count = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&media_request)).await?;
        size_info.media_files_count = media_count.as_f64().unwrap_or(0.0) as u32;

        let cache_store = transaction.object_store("cache")?;
        let cache_request = cache_store.count()?;
        let cache_count = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&cache_request)).await?;
        size_info.cache_entries_count = cache_count.as_f64().unwrap_or(0.0) as u32;

        Ok(size_info)
    }
}

impl Default for DatabaseManager {
    fn default() -> Self {
        Self::new()
    }
}

/// Database size information
#[derive(Debug, Clone, Default)]
pub struct DatabaseSizeInfo {
    pub projects_count: u32,
    pub media_files_count: u32,
    pub cache_entries_count: u32,
    pub estimated_size_mb: f64,
}

/// Cache statistics by type
#[derive(Debug, Clone, Default)]
pub struct CacheTypeStats {
    pub count: u32,
    pub total_size: u64,
}

/// Preload strategy for media files
#[derive(Debug, Clone, Default)]
pub struct PreloadStrategy {
    pub priority_order: Vec<CacheType>,
    pub immediate_preload: bool,
    pub background_preload: bool,
}

/// Result of preload analysis
#[derive(Debug, Clone, Default)]
pub struct PreloadResult {
    pub thumbnails_needed: Vec<Uuid>,
    pub waveforms_needed: Vec<Uuid>,
    pub previews_needed: Vec<Uuid>,
    pub already_cached: u32,
}

/// Cache performance metrics
#[derive(Debug, Clone, Default)]
pub struct CachePerformanceMetrics {
    pub hit_rate: f64,
    pub miss_rate: f64,
    pub total_requests: u32,
    pub cache_hits: u32,
    pub cache_misses: u32,
    pub eviction_count: u32,
    pub average_access_time_ms: f64,
}

/// Media file cache manager
pub struct MediaFileCache {
    db_manager: DatabaseManager,
    max_cache_size_mb: u64,
    max_cache_entries: u32,
}

impl MediaFileCache {
    pub fn new(max_cache_size_mb: u64, max_cache_entries: u32) -> Self {
        Self {
            db_manager: DatabaseManager::new(),
            max_cache_size_mb,
            max_cache_entries,
        }
    }

    pub async fn init(&mut self) -> Result<(), JsValue> {
        self.db_manager.init().await
    }

    /// Cache processed media data
    pub async fn cache_processed_media(
        &self,
        media_id: Uuid,
        cache_type: CacheType,
        data: Vec<u8>,
        expires_in_hours: Option<u32>,
    ) -> Result<String, JsValue> {
        let cache_id = format!("{}_{:?}_{}", media_id, cache_type, Utc::now().timestamp());

        let expires_at = expires_in_hours.map(|hours| {
            Utc::now() + chrono::Duration::hours(hours as i64)
        });

        let data_size = data.len() as u64;
        let cache_entry = CacheEntry {
            id: cache_id.clone(),
            media_id,
            cache_type,
            data,
            created_at: Utc::now(),
            last_accessed: Utc::now(),
            expires_at,
            size: data_size,
            access_count: 0,
        };

        // Check cache limits before adding
        self.enforce_cache_limits().await?;

        self.db_manager.save_cache_entry(&cache_entry).await
            .map_err(|e| JsValue::from_str(&format!("Failed to cache media: {:?}", e)))?;

        Ok(cache_id)
    }

    /// Retrieve cached media data
    pub async fn get_cached_media(
        &self,
        media_id: &Uuid,
        cache_type: &CacheType,
    ) -> Result<Option<Vec<u8>>, JsValue> {
        let cache_entries = self.db_manager.get_cache_entries_by_media(media_id).await?;

        for mut entry in cache_entries {
            if std::mem::discriminant(&entry.cache_type) == std::mem::discriminant(cache_type) {
                // Check if expired
                if let Some(expires_at) = entry.expires_at {
                    if Utc::now() > expires_at {
                        // Remove expired entry
                        let transaction = self.db_manager.transaction(&["cache"], "readwrite")?;
                        let store = transaction.object_store("cache")?;
                        let key = JsValue::from_str(&entry.id);
                        let delete_request = store.delete(&key)?;
                        let _ = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&delete_request)).await;
                        continue;
                    }
                }

                // Update access tracking for LRU
                entry.last_accessed = Utc::now();
                entry.access_count += 1;

                // Save updated entry back to database
                if let Err(e) = self.db_manager.save_cache_entry(&entry).await {
                    web_sys::console::warn_1(&format!("Failed to update cache access: {:?}", e).into());
                }

                return Ok(Some(entry.data));
            }
        }

        Ok(None)
    }

    /// Clear all cache entries for a media file
    pub async fn clear_media_cache(&self, media_id: &Uuid) -> Result<u32, JsValue> {
        self.db_manager.delete_media_cache_entries(media_id).await
    }

    /// Get cache usage statistics
    pub async fn get_cache_usage(&self) -> Result<HashMap<String, CacheTypeStats>, JsValue> {
        self.db_manager.get_cache_stats_by_type().await
    }

    /// Enforce cache size and entry limits
    async fn enforce_cache_limits(&self) -> Result<(), JsValue> {
        let stats = self.get_cache_usage().await?;
        let total_size: u64 = stats.values().map(|s| s.total_size).sum();
        let total_entries: u32 = stats.values().map(|s| s.count).sum();

        let size_mb = total_size / (1024 * 1024);

        if size_mb > self.max_cache_size_mb || total_entries > self.max_cache_entries {
            // Clean expired entries first
            let cleaned = self.db_manager.clean_expired_cache().await?;

            if cleaned == 0 {
                // If no expired entries, remove oldest entries
                self.evict_oldest_entries().await?;
            }
        }

        Ok(())
    }

    /// Evict oldest cache entries to free space using LRU policy
    async fn evict_oldest_entries(&self) -> Result<(), JsValue> {
        let transaction = self.db_manager.transaction(&["cache"], "readwrite")?;
        let store = transaction.object_store("cache")?;
        let request = store.get_all()?;
        let result = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;

        let entries_array: Array = result.dyn_into()?;
        let mut cache_entries = Vec::new();

        // Collect all cache entries with their access times
        for i in 0..entries_array.length() {
            if let Ok(entry_value) = entries_array.get(i).dyn_into::<JsValue>() {
                if let Ok(cache_entry) = serde_wasm_bindgen::from_value::<CacheEntry>(entry_value) {
                    cache_entries.push(cache_entry);
                }
            }
        }

        // Sort by last_accessed (oldest first) for true LRU eviction
        cache_entries.sort_by_key(|entry| entry.last_accessed);

        // Remove oldest 25% of entries
        let entries_to_remove = cache_entries.len() / 4;
        for entry in cache_entries.iter().take(entries_to_remove) {
            let key = JsValue::from_str(&entry.id);
            let delete_request = store.delete(&key)?;
            wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&delete_request)).await?;
        }

        web_sys::console::log_1(&format!("Evicted {} oldest cache entries", entries_to_remove).into());
        Ok(())
    }

    /// Preload commonly used cache entries with intelligent prioritization
    pub async fn preload_cache(&self, media_ids: &[Uuid]) -> Result<PreloadResult, JsValue> {
        let mut preload_result = PreloadResult::default();

        for media_id in media_ids {
            // Get media file metadata to determine preload strategy
            if let Ok(Some(media_file)) = self.db_manager.load_media_file(media_id).await {
                let preload_strategy = self.determine_preload_strategy(&media_file);

                for cache_type in preload_strategy.priority_order {
                    if self.get_cached_media(media_id, &cache_type).await?.is_none() {
                        match cache_type {
                            CacheType::Thumbnail => {
                                preload_result.thumbnails_needed.push(*media_id);
                                web_sys::console::log_1(&format!("Queued thumbnail generation for: {}", media_id).into());
                            }
                            CacheType::Waveform => {
                                preload_result.waveforms_needed.push(*media_id);
                                web_sys::console::log_1(&format!("Queued waveform generation for: {}", media_id).into());
                            }
                            CacheType::Preview => {
                                preload_result.previews_needed.push(*media_id);
                                web_sys::console::log_1(&format!("Queued preview generation for: {}", media_id).into());
                            }
                            _ => {}
                        }
                    } else {
                        preload_result.already_cached += 1;
                    }
                }
            }
        }

        Ok(preload_result)
    }

    /// Determine optimal preload strategy based on media file characteristics
    fn determine_preload_strategy(&self, media_file: &StoredMediaFile) -> PreloadStrategy {
        let mut strategy = PreloadStrategy::default();

        match media_file.file_type.as_str() {
            "video" => {
                strategy.priority_order = vec![
                    CacheType::Thumbnail,
                    CacheType::Waveform,
                    CacheType::Preview,
                ];
            }
            "audio" => {
                strategy.priority_order = vec![
                    CacheType::Waveform,
                    CacheType::Thumbnail, // For audio visualization
                ];
            }
            "image" => {
                strategy.priority_order = vec![
                    CacheType::Thumbnail,
                    CacheType::Preview,
                ];
            }
            _ => {
                strategy.priority_order = vec![CacheType::Thumbnail];
            }
        }

        // Prioritize smaller files for immediate preloading
        if media_file.size < 10 * 1024 * 1024 { // 10MB
            strategy.immediate_preload = true;
        }

        strategy
    }

    /// Preload cache entries in background with priority queue
    pub async fn background_preload(&self, media_ids: &[Uuid], max_concurrent: usize) -> Result<(), JsValue> {
        let preload_result = self.preload_cache(media_ids).await?;

        // Process high-priority items first (thumbnails)
        let mut processed = 0;
        for media_id in preload_result.thumbnails_needed.iter().take(max_concurrent) {
            // In a real implementation, this would trigger actual thumbnail generation
            web_sys::console::log_1(&format!("Background processing thumbnail for: {}", media_id).into());
            processed += 1;
        }

        web_sys::console::log_1(&format!("Started background preload for {} items", processed).into());
        Ok(())
    }

    /// Get cache performance metrics
    pub async fn get_performance_metrics(&self) -> Result<CachePerformanceMetrics, JsValue> {
        let stats = self.get_cache_usage().await?;
        let total_entries: u32 = stats.values().map(|s| s.count).sum();

        // In a real implementation, these would be tracked over time
        let metrics = CachePerformanceMetrics {
            hit_rate: 0.85, // Would be calculated from actual hit/miss tracking
            miss_rate: 0.15,
            total_requests: total_entries * 10, // Estimated
            cache_hits: (total_entries as f64 * 8.5) as u32,
            cache_misses: (total_entries as f64 * 1.5) as u32,
            eviction_count: 0, // Would be tracked during evictions
            average_access_time_ms: 2.5,
        };

        Ok(metrics)
    }

    /// Configure cache settings
    pub fn configure_cache(&mut self, max_size_mb: u64, max_entries: u32) {
        self.max_cache_size_mb = max_size_mb;
        self.max_cache_entries = max_entries;
        web_sys::console::log_1(&format!("Cache configured: {}MB max, {} entries max", max_size_mb, max_entries).into());
    }

    /// Get cache configuration
    pub fn get_cache_config(&self) -> (u64, u32) {
        (self.max_cache_size_mb, self.max_cache_entries)
    }
}

// Legacy localStorage functions for backward compatibility
pub fn save_to_local_storage<T>(key: &str, data: &T) -> Result<(), String>
where
    T: Serialize,
{
    LocalStorage::set(key, data).map_err(|e| format!("Failed to save to localStorage: {:?}", e))
}

pub fn load_from_local_storage<T>(key: &str) -> Result<T, String>
where
    T: for<'de> Deserialize<'de>,
{
    LocalStorage::get(key).map_err(|e| format!("Failed to load from localStorage: {:?}", e))
}

pub fn remove_from_local_storage(key: &str) {
    LocalStorage::delete(key);
}
