use yew::prelude::*;
use web_sys::MouseEvent;
use crate::stores::audio_store::{AudioStoreAction, AUDIO_STORE};
use crate::utils::audio_timeline_sync::{AudioSyncStats, RealtimeStats, PerformanceReport};

#[derive(Properties, PartialEq)]
pub struct SyncQualityIndicatorProps {
    #[prop_or_default]
    pub show_detailed: bool,
    #[prop_or_default]
    pub compact_mode: bool,
}

#[function_component(SyncQualityIndicator)]
pub fn sync_quality_indicator(props: &SyncQualityIndicatorProps) -> Html {
    let audio_state = AUDIO_STORE.with(|store| store.get_state());
    
    let sync_quality = audio_state.sync_quality_score;
    let sync_stats = audio_state.sync_stats.as_ref();
    let realtime_stats = audio_state.realtime_stats.as_ref();
    let performance_report = audio_state.performance_report.as_ref();
    
    // Determine quality status and color
    let (status_text, status_color, status_icon) = match sync_quality {
        q if q >= 0.9 => ("Excellent", "#4CAF50", "✓"),
        q if q >= 0.8 => ("Good", "#8BC34A", "✓"),
        q if q >= 0.7 => ("Fair", "#FFC107", "⚠"),
        q if q >= 0.5 => ("Poor", "#FF9800", "⚠"),
        _ => ("Critical", "#F44336", "✗"),
    };

    let on_toggle_monitoring = {
        Callback::from(move |_: MouseEvent| {
            AUDIO_STORE.with(|store| {
                let current_state = store.get_state();
                if current_state.performance_monitoring_enabled {
                    store.dispatch(AudioStoreAction::DisablePerformanceMonitoring);
                } else {
                    store.dispatch(AudioStoreAction::EnablePerformanceMonitoring);
                }
            });
        })
    };

    let on_refresh_stats = {
        Callback::from(move |_: MouseEvent| {
            AUDIO_STORE.with(|store| {
                store.dispatch(AudioStoreAction::RequestSyncQualityUpdate);
                store.dispatch(AudioStoreAction::RequestPerformanceReport);
            });
        })
    };

    if props.compact_mode {
        html! {
            <div class="sync-quality-compact">
                <div class="quality-indicator" style={format!("color: {}", status_color)}>
                    <span class="status-icon">{status_icon}</span>
                    <span class="quality-score">{format!("{:.1}%", sync_quality * 100.0)}</span>
                </div>
                if audio_state.realtime_position_updates_enabled {
                    <div class="realtime-indicator active" title="Real-time sync enabled">
                        {"🔄"}
                    </div>
                } else {
                    <div class="realtime-indicator inactive" title="Real-time sync disabled">
                        {"⏸"}
                    </div>
                }
            </div>
        }
    } else {
        html! {
            <div class="sync-quality-panel">
                <div class="panel-header">
                    <h3>{"Sync Quality"}</h3>
                    <div class="header-controls">
                        <button 
                            class={if audio_state.performance_monitoring_enabled { "btn-active" } else { "btn-inactive" }}
                            onclick={on_toggle_monitoring}
                            title="Toggle performance monitoring"
                        >
                            {"📊"}
                        </button>
                        <button 
                            class="btn-refresh"
                            onclick={on_refresh_stats}
                            title="Refresh statistics"
                        >
                            {"🔄"}
                        </button>
                    </div>
                </div>

                <div class="quality-overview">
                    <div class="quality-score-large" style={format!("color: {}", status_color)}>
                        <div class="score-value">{format!("{:.1}%", sync_quality * 100.0)}</div>
                        <div class="score-status">{status_text}</div>
                    </div>
                    
                    if let Some(stats) = sync_stats {
                        <div class="sync-metrics">
                            <div class="metric">
                                <span class="metric-label">{"Active Elements:"}</span>
                                <span class="metric-value">{stats.active_elements}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">{"Sync Tolerance:"}</span>
                                <span class="metric-value">{format!("{:.3}s", stats.sync_tolerance)}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">{"Drift:"}</span>
                                <span class="metric-value">{format!("{:.3}s", stats.sync_drift)}</span>
                            </div>
                        </div>
                    }
                </div>

                if props.show_detailed {
                    <div class="detailed-stats">
                        if let Some(realtime) = realtime_stats {
                            <div class="stats-section">
                                <h4>{"Real-time Performance"}</h4>
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <span class="stat-label">{"Target FPS:"}</span>
                                        <span class="stat-value">{format!("{:.1}", realtime.frame_rate)}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">{"Actual FPS:"}</span>
                                        <span class="stat-value">{format!("{:.1}", realtime.actual_frame_rate)}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">{"Dropped Frames:"}</span>
                                        <span class="stat-value">{realtime.dropped_frames}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">{"Total Frames:"}</span>
                                        <span class="stat-value">{realtime.total_frames}</span>
                                    </div>
                                </div>
                            </div>
                        }

                        if let Some(report) = performance_report {
                            <div class="stats-section">
                                <h4>{"Performance Report"}</h4>
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <span class="stat-label">{"Processing Time:"}</span>
                                        <span class="stat-value">{format!("{:.2}s", report.total_processing_time)}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">{"Effects %:"}</span>
                                        <span class="stat-value">{format!("{:.1}%", report.effects_processing_percentage)}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">{"Avg Seek Time:"}</span>
                                        <span class="stat-value">{format!("{:.1}ms", report.average_seek_time_ms)}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">{"Memory Usage:"}</span>
                                        <span class="stat-value">{format!("{:.1}MB", report.memory_usage_estimate)}</span>
                                    </div>
                                </div>
                                
                                if !report.optimization_suggestions.is_empty() {
                                    <div class="optimization-suggestions">
                                        <h5>{"Optimization Suggestions"}</h5>
                                        <ul>
                                            {for report.optimization_suggestions.iter().map(|suggestion| {
                                                html! { <li>{suggestion}</li> }
                                            })}
                                        </ul>
                                    </div>
                                }
                            </div>
                        }
                    </div>
                }

                <div class="sync-controls">
                    <label class="control-item">
                        <input 
                            type="checkbox" 
                            checked={audio_state.realtime_position_updates_enabled}
                            onchange={Callback::from(move |_| {
                                AUDIO_STORE.with(|store| {
                                    let current_state = store.get_state();
                                    if current_state.realtime_position_updates_enabled {
                                        store.dispatch(AudioStoreAction::DisableRealtimePositionUpdates);
                                    } else {
                                        store.dispatch(AudioStoreAction::EnableRealtimePositionUpdates);
                                    }
                                });
                            })}
                        />
                        {"Real-time Position Updates"}
                    </label>
                </div>
            </div>
        }
    }
}

// CSS styles for the component (would typically be in a separate CSS file)
pub fn get_sync_quality_styles() -> &'static str {
    r#"
    .sync-quality-compact {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 4px 8px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        font-size: 12px;
    }

    .quality-indicator {
        display: flex;
        align-items: center;
        gap: 4px;
        font-weight: bold;
    }

    .realtime-indicator {
        font-size: 14px;
    }

    .realtime-indicator.active {
        color: #4CAF50;
    }

    .realtime-indicator.inactive {
        color: #757575;
    }

    .sync-quality-panel {
        background: #2d2d2d;
        border: 1px solid #444;
        border-radius: 8px;
        padding: 16px;
        color: #fff;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        border-bottom: 1px solid #444;
        padding-bottom: 8px;
    }

    .panel-header h3 {
        margin: 0;
        color: #fff;
    }

    .header-controls {
        display: flex;
        gap: 8px;
    }

    .header-controls button {
        background: #444;
        border: none;
        border-radius: 4px;
        padding: 4px 8px;
        color: #fff;
        cursor: pointer;
        font-size: 14px;
    }

    .header-controls button:hover {
        background: #555;
    }

    .btn-active {
        background: #4CAF50 !important;
    }

    .quality-overview {
        display: flex;
        align-items: center;
        gap: 24px;
        margin-bottom: 16px;
    }

    .quality-score-large {
        text-align: center;
    }

    .score-value {
        font-size: 32px;
        font-weight: bold;
        line-height: 1;
    }

    .score-status {
        font-size: 14px;
        opacity: 0.8;
    }

    .sync-metrics {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .metric {
        display: flex;
        justify-content: space-between;
        padding: 4px 0;
    }

    .metric-label {
        opacity: 0.8;
    }

    .metric-value {
        font-weight: bold;
    }

    .detailed-stats {
        margin-top: 16px;
        border-top: 1px solid #444;
        padding-top: 16px;
    }

    .stats-section {
        margin-bottom: 16px;
    }

    .stats-section h4 {
        margin: 0 0 12px 0;
        color: #4CAF50;
        font-size: 14px;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    .stat-item {
        display: flex;
        justify-content: space-between;
        padding: 4px 8px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 4px;
    }

    .stat-label {
        opacity: 0.8;
        font-size: 12px;
    }

    .stat-value {
        font-weight: bold;
        font-size: 12px;
    }

    .optimization-suggestions {
        margin-top: 12px;
    }

    .optimization-suggestions h5 {
        margin: 0 0 8px 0;
        color: #FFC107;
        font-size: 12px;
    }

    .optimization-suggestions ul {
        margin: 0;
        padding-left: 16px;
        font-size: 12px;
    }

    .optimization-suggestions li {
        margin-bottom: 4px;
        opacity: 0.9;
    }

    .sync-controls {
        margin-top: 16px;
        border-top: 1px solid #444;
        padding-top: 16px;
    }

    .control-item {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        font-size: 14px;
    }

    .control-item input[type="checkbox"] {
        margin: 0;
    }
    "#
}
