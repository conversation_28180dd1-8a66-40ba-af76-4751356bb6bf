use yew::prelude::*;
use std::collections::HashMap;
use uuid::Uuid;
use crate::utils::text_animation::{KeyframeAnimationSystem, KeyframeProperties};
use crate::types::timeline::{Timeline as TimelineData, TimelineElement, ElementType};
use crate::utils::text_system::{TextAnimation, TextAnimationType};

/// State for managing text animations within the timeline
#[derive(Debu<PERSON>, <PERSON>lone, PartialEq)]
pub struct AnimationTimelineState {
    pub animation_system: KeyframeAnimationSystem,
    pub element_animations: HashMap<Uuid, String>, // element_id -> animation_sequence_id
    pub timeline_time: f64,
    pub is_playing: bool,
    pub animation_enabled: bool,
}

impl Default for AnimationTimelineState {
    fn default() -> Self {
        Self {
            animation_system: KeyframeAnimationSystem::new(),
            element_animations: HashMap::new(),
            timeline_time: 0.0,
            is_playing: false,
            animation_enabled: true,
        }
    }
}

/// Actions for managing timeline animations
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub enum AnimationTimelineAction {
    SetTimelineTime(f64),
    StartPlayback,
    StopPlayback,
    PausePlayback,
    ResumePlayback,
    AddElementAnimation { element_id: Uuid, animation_id: String },
    RemoveElementAnimation(Uuid),
    UpdateElementAnimation { element_id: Uuid, animation_id: String },
    ToggleAnimationEnabled,
    SyncWithTimeline(TimelineData),
}

/// Hook for managing animation timeline integration
#[hook]
pub fn use_animation_timeline_integration() -> (UseStateHandle<AnimationTimelineState>, Callback<AnimationTimelineAction>) {
    let state = use_state(AnimationTimelineState::default);
    
    let dispatch = {
        let state = state.clone();
        Callback::from(move |action: AnimationTimelineAction| {
            let mut new_state = (*state).clone();
            
            match action {
                AnimationTimelineAction::SetTimelineTime(time) => {
                    new_state.timeline_time = time;
                    new_state.animation_system.global_time = time;
                    
                    // Update all active animations based on timeline time
                    if new_state.animation_enabled {
                        new_state.update_animations_for_time(time);
                    }
                }
                
                AnimationTimelineAction::StartPlayback => {
                    new_state.is_playing = true;
                    if new_state.animation_enabled {
                        new_state.start_all_active_animations();
                    }
                }
                
                AnimationTimelineAction::StopPlayback => {
                    new_state.is_playing = false;
                    new_state.stop_all_animations();
                }
                
                AnimationTimelineAction::PausePlayback => {
                    new_state.is_playing = false;
                    new_state.pause_all_animations();
                }
                
                AnimationTimelineAction::ResumePlayback => {
                    new_state.is_playing = true;
                    new_state.resume_all_animations();
                }
                
                AnimationTimelineAction::AddElementAnimation { element_id, animation_id } => {
                    new_state.element_animations.insert(element_id, animation_id.clone());
                    if new_state.is_playing && new_state.animation_enabled {
                        let _ = new_state.animation_system.play_animation(&animation_id, &element_id.to_string());
                    }
                }
                
                AnimationTimelineAction::RemoveElementAnimation(element_id) => {
                    new_state.element_animations.remove(&element_id);
                    new_state.animation_system.stop_animation(&element_id.to_string());
                }
                
                AnimationTimelineAction::UpdateElementAnimation { element_id, animation_id } => {
                    // Stop current animation and start new one
                    new_state.animation_system.stop_animation(&element_id.to_string());
                    new_state.element_animations.insert(element_id, animation_id.clone());
                    if new_state.is_playing && new_state.animation_enabled {
                        let _ = new_state.animation_system.play_animation(&animation_id, &element_id.to_string());
                    }
                }
                
                AnimationTimelineAction::ToggleAnimationEnabled => {
                    new_state.animation_enabled = !new_state.animation_enabled;
                    if !new_state.animation_enabled {
                        new_state.stop_all_animations();
                    } else if new_state.is_playing {
                        new_state.start_all_active_animations();
                    }
                }
                
                AnimationTimelineAction::SyncWithTimeline(timeline_data) => {
                    new_state.sync_with_timeline_data(timeline_data);
                }
            }
            
            state.set(new_state);
        })
    };
    
    (state, dispatch)
}

impl AnimationTimelineState {
    /// Update animations based on timeline time
    fn update_animations_for_time(&mut self, time: f64) {
        let delta_time = time - self.timeline_time;
        if delta_time > 0.0 {
            self.animation_system.update(delta_time);
        }
    }
    
    /// Start all animations for elements currently active on the timeline
    fn start_all_active_animations(&mut self) {
        for (element_id, animation_id) in &self.element_animations {
            let _ = self.animation_system.play_animation(animation_id, &element_id.to_string());
        }
    }
    
    /// Stop all active animations
    fn stop_all_animations(&mut self) {
        for element_id in self.element_animations.keys() {
            self.animation_system.stop_animation(&element_id.to_string());
        }
    }
    
    /// Pause all active animations
    fn pause_all_animations(&mut self) {
        for element_id in self.element_animations.keys() {
            self.animation_system.pause_animation(&element_id.to_string());
        }
    }
    
    /// Resume all active animations
    fn resume_all_animations(&mut self) {
        for element_id in self.element_animations.keys() {
            self.animation_system.resume_animation(&element_id.to_string());
        }
    }
    
    /// Sync animation state with timeline data
    fn sync_with_timeline_data(&mut self, timeline_data: TimelineData) {
        // Get all text elements from timeline
        let text_elements: Vec<&TimelineElement> = timeline_data.elements
            .iter()
            .filter(|e| e.element_type == ElementType::Text)
            .collect();
        
        // Update element animations based on timeline elements
        let mut new_element_animations = HashMap::new();
        
        for element in text_elements {
            if let Some(text_data) = &element.properties.text_data {
                if let Some(animation) = &text_data.animation {
                    // Convert TextAnimation to animation sequence ID
                    let animation_id = self.convert_text_animation_to_sequence_id(animation);
                    new_element_animations.insert(element.id, animation_id);
                }
            }
        }
        
        // Update element animations map
        self.element_animations = new_element_animations;
        
        // Restart animations if playing
        if self.is_playing && self.animation_enabled {
            self.start_all_active_animations();
        }
    }
    
    /// Convert TextAnimation to animation sequence ID
    fn convert_text_animation_to_sequence_id(&self, animation: &TextAnimation) -> String {
        match &animation.animation_type {
            TextAnimationType::FadeIn => "fade_in".to_string(),
            TextAnimationType::FadeOut => "fade_out".to_string(),
            TextAnimationType::SlideIn { direction: _ } => "slide_in_left".to_string(),
            TextAnimationType::SlideOut { direction: _ } => "slide_out_right".to_string(),
            TextAnimationType::Scale { from: _, to: _ } => "scale".to_string(),
            TextAnimationType::Rotate { from: _, to: _ } => "rotate".to_string(),
            TextAnimationType::Bounce => "bounce_in".to_string(),
            TextAnimationType::Pulse => "pulse".to_string(),
            TextAnimationType::Shake => "shake".to_string(),
            TextAnimationType::Typewriter { speed: _ } => "typewriter".to_string(),
            TextAnimationType::Custom { name } => name.clone(),
        }
    }
    
    /// Get animated properties for a timeline element at current time
    pub fn get_element_animated_properties(&self, element_id: Uuid) -> Option<KeyframeProperties> {
        self.animation_system.get_animated_properties(&element_id.to_string())
    }
    
    /// Check if an element has active animation
    pub fn has_active_animation(&self, element_id: Uuid) -> bool {
        self.element_animations.contains_key(&element_id) && 
        self.animation_system.active_animations.contains_key(&element_id.to_string())
    }
}

/// Component for integrating animations with timeline
#[derive(Properties, PartialEq)]
pub struct AnimationTimelineIntegrationProps {
    pub timeline_data: TimelineData,
    pub current_time: f64,
    pub is_playing: bool,
    pub on_animation_update: Callback<(Uuid, KeyframeProperties)>,
}

#[function_component(AnimationTimelineIntegration)]
pub fn animation_timeline_integration(props: &AnimationTimelineIntegrationProps) -> Html {
    let (animation_state, animation_dispatch) = use_animation_timeline_integration();
    
    // Sync with timeline state
    {
        let animation_dispatch = animation_dispatch.clone();
        let timeline_data = props.timeline_data.clone();
        let current_time = props.current_time;
        let is_playing = props.is_playing;
        
        use_effect_with(
            (props.timeline_data.clone(), props.current_time, props.is_playing),
            move |(timeline_data, current_time, is_playing): &(TimelineData, f64, bool)| {
                animation_dispatch.emit(AnimationTimelineAction::SyncWithTimeline(timeline_data.clone()));
                animation_dispatch.emit(AnimationTimelineAction::SetTimelineTime(*current_time));

                if *is_playing {
                    animation_dispatch.emit(AnimationTimelineAction::StartPlayback);
                } else {
                    animation_dispatch.emit(AnimationTimelineAction::StopPlayback);
                }

                || {}
            },
        );
    }
    
    // Update animated properties for all elements
    {
        let animation_state = animation_state.clone();
        let on_animation_update = props.on_animation_update.clone();
        
        use_effect_with(
            animation_state.timeline_time,
            move |timeline_time: &f64| {
                for element_id in animation_state.element_animations.keys() {
                    if let Some(properties) = animation_state.get_element_animated_properties(*element_id) {
                        on_animation_update.emit((*element_id, properties));
                    }
                }
                || {}
            },
        );
    }
    
    html! {
        <div class="animation-timeline-integration">
            // This component manages animation state but doesn't render visible UI
            // The actual animation effects are applied through the callback
        </div>
    }
}
