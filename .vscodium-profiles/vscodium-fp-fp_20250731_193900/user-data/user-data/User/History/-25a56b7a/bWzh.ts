import { generateThumbnail } from "./ffmpeg-utils";
import { thumbnailPerformanceMonitor } from "./thumbnail-settings";

export interface ThumbnailData {
  timePosition: number;
  url: string;
  width: number;
  height: number;
  createdAt: number; // Timestamp for cache management
  lastAccessed: number; // For LRU cache management
}

export interface CachedMediaThumbnails {
  [timePosition: string]: ThumbnailData;
}

export interface ThumbnailCache {
  [mediaId: string]: CachedMediaThumbnails;
}

export interface DynamicThumbnailOptions {
  intervalSeconds?: number;
  maxThumbnails?: number;
  thumbnailWidth?: number;
  thumbnailHeight?: number;
}

export interface CacheConfig {
  maxCacheSize: number; // Maximum number of thumbnails to keep in memory
  maxAge: number; // Maximum age in milliseconds before thumbnail expires
  cleanupInterval: number; // How often to run cleanup in milliseconds
}

// Global thumbnail cache with enhanced management
const thumbnailCache: ThumbnailCache = {};
let cacheSize = 0; // Track total number of cached thumbnails

// Default options for thumbnail generation (optimized for performance)
const DEFAULT_OPTIONS: Required<DynamicThumbnailOptions> = {
  intervalSeconds: 2, // Generate thumbnail every 2 seconds (reduced from 1)
  maxThumbnails: 20, // Maximum thumbnails per video (reduced from 50)
  thumbnailWidth: 160, // Reduced from 320 for faster generation
  thumbnailHeight: 120, // Reduced from 240 for faster generation
};

// Default cache configuration
const DEFAULT_CACHE_CONFIG: CacheConfig = {
  maxCacheSize: 500, // Maximum 500 thumbnails in memory
  maxAge: 30 * 60 * 1000, // 30 minutes
  cleanupInterval: 5 * 60 * 1000, // Cleanup every 5 minutes
};

let cacheConfig = { ...DEFAULT_CACHE_CONFIG };
let cleanupIntervalId: NodeJS.Timeout | null = null;

/**
 * Find a nearby cached thumbnail within the specified tolerance
 */
const findNearbyThumbnail = (mediaId: string, targetTime: number, tolerance: number): ThumbnailData | null => {
  if (!thumbnailCache[mediaId]) return null;

  let closestThumbnail: ThumbnailData | null = null;
  let closestDistance = Infinity;

  // Search through all cached thumbnails for this media
  for (const cacheKey in thumbnailCache[mediaId]) {
    const thumbnail = thumbnailCache[mediaId][cacheKey];
    const distance = Math.abs(thumbnail.timePosition - targetTime);

    if (distance <= tolerance && distance < closestDistance) {
      closestDistance = distance;
      closestThumbnail = thumbnail;
    }
  }

  return closestThumbnail;
};

/**
 * Generate multiple thumbnails for a video at different time positions
 */
export const generateDynamicThumbnails = async (
  mediaId: string,
  videoFile: File,
  videoDuration: number,
  options: DynamicThumbnailOptions = {}
): Promise<ThumbnailData[]> => {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  // Calculate time positions for thumbnail generation
  const timePositions = calculateThumbnailPositions(videoDuration, opts);
  
  // Initialize cache for this media if it doesn't exist
  if (!thumbnailCache[mediaId]) {
    thumbnailCache[mediaId] = {};
  }
  
  const thumbnails: ThumbnailData[] = [];

  console.log(`🚀 Starting optimized thumbnail generation for ${mediaId} at ${timePositions.length} positions`);

  // Smart cache reuse: check for exact matches first, then find nearby thumbnails
  const cachedThumbnails: ThumbnailData[] = [];
  const uncachedPositions: { position: number; index: number }[] = [];

  // First pass: collect exact cached thumbnails
  for (let i = 0; i < timePositions.length; i++) {
    const timePosition = timePositions[i];
    const cacheKey = timePosition.toString();

    if (thumbnailCache[mediaId][cacheKey]) {
      thumbnailPerformanceMonitor.recordCacheHit();
      cachedThumbnails.push(thumbnailCache[mediaId][cacheKey]);
      console.log(`💾 Using exact cached thumbnail ${i + 1}/${timePositions.length} for ${mediaId} at ${timePosition}s`);
    } else {
      // Look for nearby cached thumbnails (within 0.5 seconds)
      const nearbyThumbnail = findNearbyThumbnail(mediaId, timePosition, 0.5);
      if (nearbyThumbnail) {
        thumbnailPerformanceMonitor.recordCacheHit();
        // Create a new thumbnail data with the requested time position but reused image
        const reusedThumbnail: ThumbnailData = {
          ...nearbyThumbnail,
          timePosition, // Use the requested time position
          lastAccessed: Date.now()
        };
        cachedThumbnails.push(reusedThumbnail);
        console.log(`🔄 Reusing nearby cached thumbnail for ${mediaId} at ${timePosition}s (from ${nearbyThumbnail.timePosition}s)`);
      } else {
        thumbnailPerformanceMonitor.recordCacheMiss();
        uncachedPositions.push({ position: timePosition, index: i });
      }
    }
  }

  // Add cached thumbnails to results
  thumbnails.push(...cachedThumbnails);

  // Generate uncached thumbnails sequentially for stability
  if (uncachedPositions.length > 0) {
    console.log(`🎬 Generating ${uncachedPositions.length} new thumbnails sequentially...`);

    for (const { position, index } of uncachedPositions) {
      const cacheKey = position.toString();

      try {
        // Record generation start time for performance monitoring
        const generationStart = performance.now();

        // Generate thumbnail using existing FFmpeg utility
        console.log(`🎬 Generating thumbnail ${index + 1}/${timePositions.length} for ${mediaId} at ${position}s...`);
        const url = await generateThumbnail(videoFile, position, opts.thumbnailWidth, opts.thumbnailHeight);

        // Record generation time
        const generationTime = performance.now() - generationStart;
        thumbnailPerformanceMonitor.recordGenerationTime(generationTime);

        const now = Date.now();
        const thumbnailData: ThumbnailData = {
          timePosition: position,
          url,
          width: opts.thumbnailWidth,
          height: opts.thumbnailHeight,
          createdAt: now,
          lastAccessed: now,
        };

        // Cache the thumbnail with size management
        addThumbnailToCache(mediaId, cacheKey, thumbnailData);
        thumbnails.push(thumbnailData);

        console.log(`✅ Generated thumbnail ${index + 1}/${timePositions.length} for ${mediaId} at ${position}s (${generationTime.toFixed(1)}ms) - URL: ${url.substring(0, 50)}...`);

        // Small delay between generations to prevent overwhelming FFmpeg
        if (index < uncachedPositions.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      } catch (error) {
        console.error(`❌ Failed to generate thumbnail ${index + 1}/${timePositions.length} at ${position}s:`, error);

        // If this is an FFmpeg loading error, provide helpful information
        if (error instanceof Error && error.message.includes('ffmpeg is not loaded')) {
          console.warn(`💡 FFmpeg initialization failed. This is likely due to:
          1. Missing FFmpeg files in /public/ffmpeg/
          2. Network issues loading FFmpeg WASM files
          3. CORS restrictions
          4. Browser compatibility issues

          Dynamic thumbnails will not work until FFmpeg is properly initialized.
          Consider checking the browser network tab for failed requests to ffmpeg-core.js or ffmpeg-core.wasm`);
        }
      }
    }
  }

  console.log(`🎉 Completed thumbnail generation for ${mediaId}: ${thumbnails.length}/${timePositions.length} successful`);
  return thumbnails;
};

/**
 * Calculate optimal time positions for thumbnail generation
 */
const calculateThumbnailPositions = (
  duration: number,
  options: Required<DynamicThumbnailOptions>
): number[] => {
  const positions: number[] = [];
  const { intervalSeconds, maxThumbnails } = options;

  // Calculate thumbnail count based on interval and duration (respects zoom level)
  const calculatedFromInterval = Math.ceil(duration / intervalSeconds);
  const actualThumbnailCount = Math.min(calculatedFromInterval, maxThumbnails);

  // Ensure minimum thumbnails for very short videos
  const finalThumbnailCount = Math.max(2, actualThumbnailCount);

  console.log(`🎯 Zoom-responsive thumbnail calculation for ${duration}s video: ${finalThumbnailCount} thumbnails`, {
    duration,
    intervalSeconds,
    calculatedFromInterval,
    maxThumbnails,
    finalThumbnailCount,
    zoomAware: true
  });

  // Always spread thumbnails evenly across the duration for better coverage
  if (finalThumbnailCount <= 1) {
    positions.push(Math.min(duration * 0.1, duration - 0.1)); // 10% into the video
  } else {
    const step = duration / (finalThumbnailCount - 1);
    for (let i = 0; i < finalThumbnailCount; i++) {
      const timePosition = i * step;
      positions.push(Math.min(timePosition, duration - 0.1));
    }
  }

  console.log(`📊 Calculated ${positions.length} thumbnail positions for ${duration}s video:`, positions);
  return positions;
};

/**
 * Add thumbnail to cache with size management
 */
const addThumbnailToCache = (
  mediaId: string,
  cacheKey: string,
  thumbnailData: ThumbnailData
): void => {
  // Ensure cache size doesn't exceed limit
  if (cacheSize >= cacheConfig.maxCacheSize) {
    evictOldestThumbnails();
  }

  thumbnailCache[mediaId][cacheKey] = thumbnailData;
  cacheSize++;
};

/**
 * Evict oldest thumbnails when cache is full
 */
const evictOldestThumbnails = (): void => {
  const allThumbnails: Array<{ mediaId: string; cacheKey: string; thumbnail: ThumbnailData }> = [];

  // Collect all thumbnails with their metadata
  for (const [mediaId, mediaThumbnails] of Object.entries(thumbnailCache)) {
    for (const [cacheKey, thumbnail] of Object.entries(mediaThumbnails)) {
      allThumbnails.push({ mediaId, cacheKey, thumbnail });
    }
  }

  // Sort by last accessed time (oldest first)
  allThumbnails.sort((a, b) => a.thumbnail.lastAccessed - b.thumbnail.lastAccessed);

  // Remove oldest 25% of thumbnails
  const toRemove = Math.ceil(allThumbnails.length * 0.25);
  for (let i = 0; i < toRemove; i++) {
    const { mediaId, cacheKey, thumbnail } = allThumbnails[i];
    URL.revokeObjectURL(thumbnail.url);
    delete thumbnailCache[mediaId][cacheKey];
    cacheSize--;

    // Clean up empty media entries
    if (Object.keys(thumbnailCache[mediaId]).length === 0) {
      delete thumbnailCache[mediaId];
    }
  }

  console.log(`Evicted ${toRemove} old thumbnails from cache`);
};

/**
 * Get the most appropriate thumbnail for a given time position
 */
export const getThumbnailForTime = (
  mediaId: string,
  timePosition: number
): ThumbnailData | null => {
  const mediaThumbnails = thumbnailCache[mediaId];
  if (!mediaThumbnails) return null;

  // Find the closest thumbnail to the requested time
  let closestThumbnail: ThumbnailData | null = null;
  let closestDistance = Infinity;

  for (const thumbnail of Object.values(mediaThumbnails)) {
    const distance = Math.abs(thumbnail.timePosition - timePosition);
    if (distance < closestDistance) {
      closestDistance = distance;
      closestThumbnail = thumbnail;
    }
  }

  // Update last accessed time
  if (closestThumbnail) {
    closestThumbnail.lastAccessed = Date.now();
  }

  return closestThumbnail;
};

/**
 * Get all thumbnails for a media item
 */
export const getThumbnailsForMedia = (mediaId: string): ThumbnailData[] => {
  const mediaThumbnails = thumbnailCache[mediaId];
  if (!mediaThumbnails) return [];
  
  return Object.values(mediaThumbnails).sort((a, b) => a.timePosition - b.timePosition);
};

/**
 * Clear thumbnails from cache for a specific media item
 */
export const clearThumbnailsForMedia = (mediaId: string): void => {
  const mediaThumbnails = thumbnailCache[mediaId];
  if (!mediaThumbnails) return;

  // Revoke all object URLs to prevent memory leaks
  const thumbnailCount = Object.keys(mediaThumbnails).length;
  for (const thumbnail of Object.values(mediaThumbnails)) {
    URL.revokeObjectURL(thumbnail.url);
  }

  delete thumbnailCache[mediaId];
  cacheSize -= thumbnailCount;
  console.log(`Cleared ${thumbnailCount} thumbnails for media ${mediaId}`);
};

/**
 * Clear all thumbnails from cache
 */
export const clearAllThumbnails = (): void => {
  for (const mediaId of Object.keys(thumbnailCache)) {
    clearThumbnailsForMedia(mediaId);
  }
  cacheSize = 0;
  console.log("Cleared all thumbnails from cache");
};

/**
 * Clean up expired thumbnails
 */
const cleanupExpiredThumbnails = (): void => {
  const now = Date.now();
  let removedCount = 0;

  for (const [mediaId, mediaThumbnails] of Object.entries(thumbnailCache)) {
    const keysToRemove: string[] = [];

    for (const [cacheKey, thumbnail] of Object.entries(mediaThumbnails)) {
      if (now - thumbnail.createdAt > cacheConfig.maxAge) {
        URL.revokeObjectURL(thumbnail.url);
        keysToRemove.push(cacheKey);
        removedCount++;
      }
    }

    // Remove expired thumbnails
    for (const key of keysToRemove) {
      delete mediaThumbnails[key];
      cacheSize--;
    }

    // Clean up empty media entries
    if (Object.keys(mediaThumbnails).length === 0) {
      delete thumbnailCache[mediaId];
    }
  }

  if (removedCount > 0) {
    console.log(`Cleaned up ${removedCount} expired thumbnails`);
  }
};

/**
 * Start automatic cache cleanup
 */
export const startCacheCleanup = (): void => {
  if (cleanupIntervalId) return; // Already running

  cleanupIntervalId = setInterval(() => {
    cleanupExpiredThumbnails();
  }, cacheConfig.cleanupInterval);

  console.log("Started automatic thumbnail cache cleanup");
};

/**
 * Stop automatic cache cleanup
 */
export const stopCacheCleanup = (): void => {
  if (cleanupIntervalId) {
    clearInterval(cleanupIntervalId);
    cleanupIntervalId = null;
    console.log("Stopped automatic thumbnail cache cleanup");
  }
};

/**
 * Configure cache settings
 */
export const configureThumbnailCache = (config: Partial<CacheConfig>): void => {
  cacheConfig = { ...cacheConfig, ...config };

  // Restart cleanup with new interval if it's running
  if (cleanupIntervalId) {
    stopCacheCleanup();
    startCacheCleanup();
  }

  console.log("Updated thumbnail cache configuration:", cacheConfig);
};

/**
 * Get cache statistics
 */
export const getThumbnailCacheStats = () => {
  const mediaCount = Object.keys(thumbnailCache).length;

  return {
    mediaCount,
    totalThumbnails: cacheSize,
    averageThumbnailsPerMedia: mediaCount > 0 ? cacheSize / mediaCount : 0,
    maxCacheSize: cacheConfig.maxCacheSize,
    cacheUtilization: (cacheSize / cacheConfig.maxCacheSize) * 100,
    config: { ...cacheConfig },
  };
};

/**
 * Preload thumbnails for a media item (useful for performance)
 */
export const preloadThumbnails = async (
  mediaId: string,
  videoFile: File,
  videoDuration: number,
  options: DynamicThumbnailOptions = {}
): Promise<void> => {
  console.log(`Preloading thumbnails for ${mediaId}...`);
  await generateDynamicThumbnails(mediaId, videoFile, videoDuration, options);
  console.log(`Finished preloading thumbnails for ${mediaId}`);
};

// Initialize cache cleanup on module load
if (typeof window !== "undefined") {
  startCacheCleanup();

  // Cleanup on page unload
  window.addEventListener("beforeunload", () => {
    stopCacheCleanup();
    clearAllThumbnails();
  });
}
