import { generateThumbnail } from "./ffmpeg-utils";

export interface ThumbnailData {
  timePosition: number;
  url: string;
  width: number;
  height: number;
  createdAt: number; // Timestamp for cache management
  lastAccessed: number; // For LRU cache management
}

export interface CachedMediaThumbnails {
  [timePosition: string]: ThumbnailData;
}

export interface ThumbnailCache {
  [mediaId: string]: CachedMediaThumbnails;
}

export interface DynamicThumbnailOptions {
  intervalSeconds?: number;
  maxThumbnails?: number;
  thumbnailWidth?: number;
  thumbnailHeight?: number;
}

export interface CacheConfig {
  maxCacheSize: number; // Maximum number of thumbnails to keep in memory
  maxAge: number; // Maximum age in milliseconds before thumbnail expires
  cleanupInterval: number; // How often to run cleanup in milliseconds
}

// Global thumbnail cache with enhanced management
const thumbnailCache: ThumbnailCache = {};
let cacheSize = 0; // Track total number of cached thumbnails

// Default options for thumbnail generation
const DEFAULT_OPTIONS: Required<DynamicThumbnailOptions> = {
  intervalSeconds: 1, // Generate thumbnail every 1 second
  maxThumbnails: 50, // Maximum thumbnails per video
  thumbnailWidth: 320,
  thumbnailHeight: 240,
};

// Default cache configuration
const DEFAULT_CACHE_CONFIG: CacheConfig = {
  maxCacheSize: 500, // Maximum 500 thumbnails in memory
  maxAge: 30 * 60 * 1000, // 30 minutes
  cleanupInterval: 5 * 60 * 1000, // Cleanup every 5 minutes
};

let cacheConfig = { ...DEFAULT_CACHE_CONFIG };
let cleanupIntervalId: NodeJS.Timeout | null = null;

/**
 * Generate multiple thumbnails for a video at different time positions
 */
export const generateDynamicThumbnails = async (
  mediaId: string,
  videoFile: File,
  videoDuration: number,
  options: DynamicThumbnailOptions = {}
): Promise<ThumbnailData[]> => {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  // Calculate time positions for thumbnail generation
  const timePositions = calculateThumbnailPositions(videoDuration, opts);
  
  // Initialize cache for this media if it doesn't exist
  if (!thumbnailCache[mediaId]) {
    thumbnailCache[mediaId] = {};
  }
  
  const thumbnails: ThumbnailData[] = [];
  
  // Generate thumbnails for each time position
  for (const timePosition of timePositions) {
    const cacheKey = timePosition.toString();
    
    // Check if thumbnail already exists in cache
    if (thumbnailCache[mediaId][cacheKey]) {
      thumbnails.push(thumbnailCache[mediaId][cacheKey]);
      continue;
    }
    
    try {
      // Generate thumbnail using existing FFmpeg utility
      const url = await generateThumbnail(videoFile, timePosition);

      const now = Date.now();
      const thumbnailData: ThumbnailData = {
        timePosition,
        url,
        width: opts.thumbnailWidth,
        height: opts.thumbnailHeight,
        createdAt: now,
        lastAccessed: now,
      };

      // Cache the thumbnail with size management
      addThumbnailToCache(mediaId, cacheKey, thumbnailData);
      thumbnails.push(thumbnailData);

      console.log(`Generated thumbnail for ${mediaId} at ${timePosition}s`);
    } catch (error) {
      console.error(`Failed to generate thumbnail at ${timePosition}s:`, error);
    }
  }
  
  return thumbnails;
};

/**
 * Calculate optimal time positions for thumbnail generation
 */
const calculateThumbnailPositions = (
  duration: number,
  options: Required<DynamicThumbnailOptions>
): number[] => {
  const positions: number[] = [];
  const { intervalSeconds, maxThumbnails } = options;
  
  // Calculate how many thumbnails we can generate within limits
  const maxPossibleThumbnails = Math.floor(duration / intervalSeconds);
  const actualThumbnailCount = Math.min(maxPossibleThumbnails, maxThumbnails);
  
  // If we need to limit thumbnails, spread them evenly across the duration
  if (actualThumbnailCount < maxPossibleThumbnails) {
    const step = duration / actualThumbnailCount;
    for (let i = 0; i < actualThumbnailCount; i++) {
      positions.push(Math.min(i * step, duration - 0.1)); // Ensure we don't exceed duration
    }
  } else {
    // Generate thumbnails at regular intervals
    for (let time = 0; time < duration; time += intervalSeconds) {
      positions.push(Math.min(time, duration - 0.1));
    }
  }
  
  return positions;
};

/**
 * Add thumbnail to cache with size management
 */
const addThumbnailToCache = (
  mediaId: string,
  cacheKey: string,
  thumbnailData: ThumbnailData
): void => {
  // Ensure cache size doesn't exceed limit
  if (cacheSize >= cacheConfig.maxCacheSize) {
    evictOldestThumbnails();
  }

  thumbnailCache[mediaId][cacheKey] = thumbnailData;
  cacheSize++;
};

/**
 * Evict oldest thumbnails when cache is full
 */
const evictOldestThumbnails = (): void => {
  const allThumbnails: Array<{ mediaId: string; cacheKey: string; thumbnail: ThumbnailData }> = [];

  // Collect all thumbnails with their metadata
  for (const [mediaId, mediaThumbnails] of Object.entries(thumbnailCache)) {
    for (const [cacheKey, thumbnail] of Object.entries(mediaThumbnails)) {
      allThumbnails.push({ mediaId, cacheKey, thumbnail });
    }
  }

  // Sort by last accessed time (oldest first)
  allThumbnails.sort((a, b) => a.thumbnail.lastAccessed - b.thumbnail.lastAccessed);

  // Remove oldest 25% of thumbnails
  const toRemove = Math.ceil(allThumbnails.length * 0.25);
  for (let i = 0; i < toRemove; i++) {
    const { mediaId, cacheKey, thumbnail } = allThumbnails[i];
    URL.revokeObjectURL(thumbnail.url);
    delete thumbnailCache[mediaId][cacheKey];
    cacheSize--;

    // Clean up empty media entries
    if (Object.keys(thumbnailCache[mediaId]).length === 0) {
      delete thumbnailCache[mediaId];
    }
  }

  console.log(`Evicted ${toRemove} old thumbnails from cache`);
};

/**
 * Get the most appropriate thumbnail for a given time position
 */
export const getThumbnailForTime = (
  mediaId: string,
  timePosition: number
): ThumbnailData | null => {
  const mediaThumbnails = thumbnailCache[mediaId];
  if (!mediaThumbnails) return null;

  // Find the closest thumbnail to the requested time
  let closestThumbnail: ThumbnailData | null = null;
  let closestDistance = Infinity;

  for (const thumbnail of Object.values(mediaThumbnails)) {
    const distance = Math.abs(thumbnail.timePosition - timePosition);
    if (distance < closestDistance) {
      closestDistance = distance;
      closestThumbnail = thumbnail;
    }
  }

  // Update last accessed time
  if (closestThumbnail) {
    closestThumbnail.lastAccessed = Date.now();
  }

  return closestThumbnail;
};

/**
 * Get all thumbnails for a media item
 */
export const getThumbnailsForMedia = (mediaId: string): ThumbnailData[] => {
  const mediaThumbnails = thumbnailCache[mediaId];
  if (!mediaThumbnails) return [];
  
  return Object.values(mediaThumbnails).sort((a, b) => a.timePosition - b.timePosition);
};

/**
 * Clear thumbnails from cache for a specific media item
 */
export const clearThumbnailsForMedia = (mediaId: string): void => {
  const mediaThumbnails = thumbnailCache[mediaId];
  if (!mediaThumbnails) return;

  // Revoke all object URLs to prevent memory leaks
  const thumbnailCount = Object.keys(mediaThumbnails).length;
  for (const thumbnail of Object.values(mediaThumbnails)) {
    URL.revokeObjectURL(thumbnail.url);
  }

  delete thumbnailCache[mediaId];
  cacheSize -= thumbnailCount;
  console.log(`Cleared ${thumbnailCount} thumbnails for media ${mediaId}`);
};

/**
 * Clear all thumbnails from cache
 */
export const clearAllThumbnails = (): void => {
  for (const mediaId of Object.keys(thumbnailCache)) {
    clearThumbnailsForMedia(mediaId);
  }
  cacheSize = 0;
  console.log("Cleared all thumbnails from cache");
};

/**
 * Clean up expired thumbnails
 */
const cleanupExpiredThumbnails = (): void => {
  const now = Date.now();
  let removedCount = 0;

  for (const [mediaId, mediaThumbnails] of Object.entries(thumbnailCache)) {
    const keysToRemove: string[] = [];

    for (const [cacheKey, thumbnail] of Object.entries(mediaThumbnails)) {
      if (now - thumbnail.createdAt > cacheConfig.maxAge) {
        URL.revokeObjectURL(thumbnail.url);
        keysToRemove.push(cacheKey);
        removedCount++;
      }
    }

    // Remove expired thumbnails
    for (const key of keysToRemove) {
      delete mediaThumbnails[key];
      cacheSize--;
    }

    // Clean up empty media entries
    if (Object.keys(mediaThumbnails).length === 0) {
      delete thumbnailCache[mediaId];
    }
  }

  if (removedCount > 0) {
    console.log(`Cleaned up ${removedCount} expired thumbnails`);
  }
};

/**
 * Start automatic cache cleanup
 */
export const startCacheCleanup = (): void => {
  if (cleanupIntervalId) return; // Already running

  cleanupIntervalId = setInterval(() => {
    cleanupExpiredThumbnails();
  }, cacheConfig.cleanupInterval);

  console.log("Started automatic thumbnail cache cleanup");
};

/**
 * Stop automatic cache cleanup
 */
export const stopCacheCleanup = (): void => {
  if (cleanupIntervalId) {
    clearInterval(cleanupIntervalId);
    cleanupIntervalId = null;
    console.log("Stopped automatic thumbnail cache cleanup");
  }
};

/**
 * Configure cache settings
 */
export const configureThumbnailCache = (config: Partial<CacheConfig>): void => {
  cacheConfig = { ...cacheConfig, ...config };

  // Restart cleanup with new interval if it's running
  if (cleanupIntervalId) {
    stopCacheCleanup();
    startCacheCleanup();
  }

  console.log("Updated thumbnail cache configuration:", cacheConfig);
};

/**
 * Get cache statistics
 */
export const getThumbnailCacheStats = () => {
  const mediaCount = Object.keys(thumbnailCache).length;

  return {
    mediaCount,
    totalThumbnails: cacheSize,
    averageThumbnailsPerMedia: mediaCount > 0 ? cacheSize / mediaCount : 0,
    maxCacheSize: cacheConfig.maxCacheSize,
    cacheUtilization: (cacheSize / cacheConfig.maxCacheSize) * 100,
    config: { ...cacheConfig },
  };
};

/**
 * Preload thumbnails for a media item (useful for performance)
 */
export const preloadThumbnails = async (
  mediaId: string,
  videoFile: File,
  videoDuration: number,
  options: DynamicThumbnailOptions = {}
): Promise<void> => {
  console.log(`Preloading thumbnails for ${mediaId}...`);
  await generateDynamicThumbnails(mediaId, videoFile, videoDuration, options);
  console.log(`Finished preloading thumbnails for ${mediaId}`);
};

// Initialize cache cleanup on module load
if (typeof window !== "undefined") {
  startCacheCleanup();

  // Cleanup on page unload
  window.addEventListener("beforeunload", () => {
    stopCacheCleanup();
    clearAllThumbnails();
  });
}
