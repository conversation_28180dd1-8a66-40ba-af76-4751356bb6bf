use yew::prelude::*;
use web_sys::{HtmlCanvasElement, HtmlVideoElement};
use wasm_bindgen::{JsCast, JsValue};
use gloo::timers::callback::Interval;
use crate::types::playback::{PlaybackState, PlaybackAction};
use crate::stores::video_store::{use_video_store, VideoStoreAction};
use crate::utils::{
    time::format_time,
    canvas::{get_canvas_context, clear_canvas, draw_placeholder_text},
    audio::AudioEngine
};

pub mod animation_preview;
pub use animation_preview::{AnimationPreview, AnimationPreviewState, PreviewTextElement, use_animation_preview};

#[function_component(Preview)]
pub fn preview() -> Html {
    let canvas_ref = use_node_ref();
    let video_ref = use_node_ref();
    let playback_state = use_state(|| PlaybackState::default());
    let render_interval = use_state(|| None::<Interval>);
    let current_video_url = use_state(|| None::<String>);
    let audio_engine = use_state(|| None::<AudioEngine>);
    let audio_visualization_data = use_state(|| Vec::<u8>::new());

    // Video store integration
    let (video_state, video_dispatch) = use_video_store();

    // Initialize canvas and video on mount
    {
        let canvas_ref = canvas_ref.clone();
        let video_ref = video_ref.clone();
        let audio_engine_init = audio_engine.clone();
        use_effect_with((), move |_| {
            if let Some(canvas) = canvas_ref.cast::<HtmlCanvasElement>() {
                canvas.set_width(1920);
                canvas.set_height(1080);

                if let Ok(context) = get_canvas_context(&canvas) {
                    clear_canvas(&context, 1920, 1080);
                    draw_placeholder_text(&context, "No media loaded", 1920, 1080);
                }
            }

            // Set up video element and audio engine
            if let Some(video) = video_ref.cast::<HtmlVideoElement>() {
                video.set_muted(false); // Enable audio for Web Audio API
                video.set_preload("metadata");

                // Initialize audio engine
                if let Ok(mut engine) = AudioEngine::new() {
                    if engine.connect_video_element(&video).is_ok() {
                        audio_engine_init.set(Some(engine));
                    }
                }
            }
            || ()
        });
    }

    // Render video frames to canvas and update audio visualization
    let render_frame = {
        let canvas_ref = canvas_ref.clone();
        let video_ref = video_ref.clone();
        let audio_engine = audio_engine.clone();
        let audio_visualization_data = audio_visualization_data.clone();

        Callback::from(move |_| {
            if let (Some(canvas), Some(video)) = (
                canvas_ref.cast::<HtmlCanvasElement>(),
                video_ref.cast::<HtmlVideoElement>()
            ) {
                if let Ok(context) = get_canvas_context(&canvas) {
                    if video.ready_state() >= 2 { // HAVE_CURRENT_DATA
                        // Clear canvas
                        clear_canvas(&context, 1920, 1080);

                        // Draw video frame
                        let _ = context.draw_image_with_html_video_element_and_dw_and_dh(
                            &video, 0.0, 0.0, 1920.0, 1080.0
                        );

                        // Draw current video frame from video store if available
                        if let Some(ref current_frame) = video_state.current_frame {
                            let _ = context.draw_image_with_html_canvas_element_and_dw_and_dh(
                                &current_frame.canvas, 0.0, 0.0, 1920.0, 1080.0
                            );
                        }

                        // Update audio visualization data
                        if let Some(ref engine) = *audio_engine {
                            let freq_data = engine.get_frequency_data();
                            audio_visualization_data.set(freq_data);

                            // Draw simple audio visualization overlay
                            draw_audio_visualization(&context, &audio_visualization_data);
                        }
                    }
                }
            }
        })
    };

    let handle_playback_action = {
        let playback_state = playback_state.clone();
        let video_ref = video_ref.clone();
        let render_interval = render_interval.clone();
        let render_frame = render_frame.clone();
        let audio_engine = audio_engine.clone();

        Callback::from(move |action: PlaybackAction| {
            let mut new_state = (*playback_state).clone();

            if let Some(video) = video_ref.cast::<HtmlVideoElement>() {
                match action {
                    PlaybackAction::Play => {
                        new_state.is_playing = true;
                        let _ = video.play();

                        // Resume audio context
                        if let Some(ref engine) = *audio_engine {
                            let _ = engine.resume();
                        }

                        // Sync with video store
                        video_dispatch.emit(VideoStoreAction::Play);

                        // Start render loop
                        let render_frame_clone = render_frame.clone();
                        let interval = Interval::new(16, move || { // ~60fps
                            render_frame_clone.emit(());
                        });
                        render_interval.set(Some(interval));
                    }
                    PlaybackAction::Pause => {
                        new_state.is_playing = false;
                        video.pause().unwrap();

                        // Sync with video store
                        video_dispatch.emit(VideoStoreAction::Pause);

                        // Stop render loop
                        render_interval.set(None);
                    }
                    PlaybackAction::Stop => {
                        new_state.is_playing = false;
                        new_state.current_time = 0.0;
                        video.pause().unwrap();
                        video.set_current_time(0.0);

                        // Stop render loop
                        render_interval.set(None);
                        render_frame.emit(()); // Render one final frame
                    }
                    PlaybackAction::Seek(time) => {
                        new_state.current_time = time;
                        video.set_current_time(time);

                        // Sync with video store
                        video_dispatch.emit(VideoStoreAction::Seek(time));

                        render_frame.emit(()); // Render frame at new position
                    }
                    PlaybackAction::SetVolume(volume) => {
                        new_state.volume = volume.clamp(0.0, 1.0);

                        // Set volume through audio engine for better control
                        if let Some(ref engine) = *audio_engine {
                            let _ = engine.set_volume(new_state.volume as f32);
                        } else {
                            video.set_volume(new_state.volume);
                        }
                    }
                    PlaybackAction::ToggleMute => {
                        new_state.muted = !new_state.muted;

                        // Handle muting through audio engine
                        if let Some(ref engine) = *audio_engine {
                            let volume = if new_state.muted { 0.0 } else { new_state.volume as f32 };
                            let _ = engine.set_volume(volume);
                        } else {
                            video.set_muted(new_state.muted);
                        }
                    }
                    PlaybackAction::SetPlaybackRate(rate) => {
                        new_state.playback_rate = rate;
                        video.set_playback_rate(rate);
                    }
                    _ => {}
                }
            }

            playback_state.set(new_state);
        })
    };

    let on_play_pause = {
        let handle_playback_action = handle_playback_action.clone();
        let is_playing = playback_state.is_playing;
        Callback::from(move |_| {
            if is_playing {
                handle_playback_action.emit(PlaybackAction::Pause);
            } else {
                handle_playback_action.emit(PlaybackAction::Play);
            }
        })
    };

    let on_stop = {
        let handle_playback_action = handle_playback_action.clone();
        Callback::from(move |_| {
            handle_playback_action.emit(PlaybackAction::Stop);
        })
    };

    let on_volume_change = {
        let handle_playback_action = handle_playback_action.clone();
        Callback::from(move |e: Event| {
            if let Some(input) = e.target().and_then(|t| t.dyn_into::<web_sys::HtmlInputElement>().ok()) {
                if let Ok(volume) = input.value().parse::<f64>() {
                    handle_playback_action.emit(PlaybackAction::SetVolume(volume / 100.0));
                }
            }
        })
    };

    let on_mute_toggle = {
        let handle_playback_action = handle_playback_action.clone();
        Callback::from(move |_| {
            handle_playback_action.emit(PlaybackAction::ToggleMute);
        })
    };

    // Load video from URL (for testing - in real app this would come from timeline)
    let load_test_video = {
        let current_video_url = current_video_url.clone();
        Callback::from(move |_| {
            // For demo purposes, we'll use a placeholder video URL
            // In the real implementation, this would come from the timeline elements
            current_video_url.set(Some("https://www.w3schools.com/html/mov_bbb.mp4".to_string()));
        })
    };

    html! {
        <div class="preview">
            <div class="preview-header">
                <h3>{"Preview"}</h3>
                <div class="preview-settings">
                    <button class="btn" onclick={load_test_video}>{"Load Test Video"}</button>
                    <select class="quality-select">
                        <option value="low">{"Low Quality"}</option>
                        <option value="medium" selected=true>{"Medium Quality"}</option>
                        <option value="high">{"High Quality"}</option>
                        <option value="full">{"Full Quality"}</option>
                    </select>
                </div>
            </div>

            <div class="preview-content">
                <canvas
                    ref={canvas_ref}
                    class="preview-canvas"
                    width="1920"
                    height="1080"
                >
                </canvas>

                // Hidden video element for rendering
                <video
                    ref={video_ref}
                    style="display: none;"
                    crossorigin="anonymous"
                    src={(*current_video_url).clone().unwrap_or_default()}
                >
                </video>

                <div class="preview-controls">
                    <div class="playback-controls">
                        <button
                            class="btn play-btn"
                            onclick={on_play_pause}
                            title={if playback_state.is_playing || video_state.playback_state.is_playing { "Pause" } else { "Play" }}
                        >
                            {if playback_state.is_playing || video_state.playback_state.is_playing { "⏸" } else { "▶" }}
                        </button>
                        <button class="btn" onclick={on_stop} title="Stop">{"⏹"}</button>

                        <div class="time-display">
                            <span>{format_time(playback_state.current_time)}</span>
                            {" / "}
                            <span>{format_time(60.0)}</span> // Default duration for now
                        </div>
                    </div>

                    <div class="audio-controls">
                        <button
                            class="btn volume-btn"
                            onclick={on_mute_toggle}
                            title={if playback_state.muted { "Unmute" } else { "Mute" }}
                        >
                            {if playback_state.muted { "🔇" } else { "🔊" }}
                        </button>
                        <input
                            type="range"
                            class="volume-slider"
                            min="0"
                            max="100"
                            value={(playback_state.volume * 100.0).to_string()}
                            onchange={on_volume_change}
                        />
                        <span class="volume-display">{format!("{}%", (playback_state.volume * 100.0) as i32)}</span>
                    </div>
                </div>
            </div>
        </div>
    }
}

fn draw_audio_visualization(context: &web_sys::CanvasRenderingContext2d, freq_data: &[u8]) {
    if freq_data.is_empty() {
        return;
    }

    let canvas_width = 1920.0;
    let canvas_height = 1080.0;
    let bar_width = canvas_width / freq_data.len() as f64;
    let visualization_height = 100.0; // Height of visualization area
    let y_offset = canvas_height - visualization_height - 20.0; // Position near bottom

    // Set visualization style
    context.set_fill_style(&JsValue::from_str("rgba(0, 122, 204, 0.7)"));
    context.set_stroke_style(&JsValue::from_str("rgba(0, 122, 204, 1.0)"));
    context.set_line_width(1.0);

    // Draw frequency bars
    for (i, &value) in freq_data.iter().enumerate() {
        let x = i as f64 * bar_width;
        let bar_height = (value as f64 / 255.0) * visualization_height;
        let y = y_offset + visualization_height - bar_height;

        // Draw bar
        context.fill_rect(x, y, bar_width - 1.0, bar_height);
    }

    // Draw visualization border
    context.set_stroke_style(&JsValue::from_str("rgba(255, 255, 255, 0.3)"));
    context.stroke_rect(0.0, y_offset, canvas_width, visualization_height);
}
