/**
 * Settings and preferences for dynamic thumbnail system
 */

export interface ThumbnailSettings {
  enabled: boolean;
  quality: 'low' | 'medium' | 'high';
  maxCacheSize: number;
  maxAge: number; // in milliseconds
  preloadOnMount: boolean;
  updateThreshold: number; // minimum time change to trigger update (in seconds)
  maxThumbnailsPerVideo: number;
  thumbnailInterval: number; // seconds between thumbnails
  updateDuringPlayback: boolean; // whether to update thumbnails during playback (performance impact)
}

export interface QualityPreset {
  thumbnailWidth: number;
  thumbnailHeight: number;
  intervalSeconds: number;
  maxThumbnails: number;
}

// Quality presets for different performance needs
export const QUALITY_PRESETS: Record<ThumbnailSettings['quality'], QualityPreset> = {
  low: {
    thumbnailWidth: 160,
    thumbnailHeight: 120,
    intervalSeconds: 2,
    maxThumbnails: 25,
  },
  medium: {
    thumbnailWidth: 320,
    thumbnailHeight: 240,
    intervalSeconds: 1,
    maxThumbnails: 50,
  },
  high: {
    thumbnailWidth: 480,
    thumbnailHeight: 360,
    intervalSeconds: 0.5,
    maxThumbnails: 100,
  },
};

// Default settings
export const DEFAULT_THUMBNAIL_SETTINGS: ThumbnailSettings = {
  enabled: true,
  quality: 'medium',
  maxCacheSize: 500,
  maxAge: 30 * 60 * 1000, // 30 minutes
  preloadOnMount: true, // Generate initial set of thumbnails to show different frames
  updateThreshold: 0.1, // 100ms
  maxThumbnailsPerVideo: 50,
  thumbnailInterval: 1,
  updateDuringPlayback: false, // Professional editor behavior: only update during scrubbing
};

// Local storage key
const SETTINGS_KEY = 'opencut-thumbnail-settings';

/**
 * Load thumbnail settings from localStorage
 */
export function loadThumbnailSettings(): ThumbnailSettings {
  if (typeof window === 'undefined') {
    return DEFAULT_THUMBNAIL_SETTINGS;
  }

  try {
    const stored = localStorage.getItem(SETTINGS_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      return { ...DEFAULT_THUMBNAIL_SETTINGS, ...parsed };
    }
  } catch (error) {
    console.warn('Failed to load thumbnail settings:', error);
  }

  return DEFAULT_THUMBNAIL_SETTINGS;
}

/**
 * Save thumbnail settings to localStorage
 */
export function saveThumbnailSettings(settings: Partial<ThumbnailSettings>): void {
  if (typeof window === 'undefined') return;

  try {
    const current = loadThumbnailSettings();
    const updated = { ...current, ...settings };
    localStorage.setItem(SETTINGS_KEY, JSON.stringify(updated));
  } catch (error) {
    console.warn('Failed to save thumbnail settings:', error);
  }
}

/**
 * Get quality preset for current settings
 */
export function getQualityPreset(quality: ThumbnailSettings['quality']): QualityPreset {
  return QUALITY_PRESETS[quality];
}

/**
 * Calculate optimal thumbnail settings based on zoom level and element duration
 */
export function calculateOptimalSettings(
  zoomLevel: number,
  elementDuration: number,
  baseSettings: ThumbnailSettings
): QualityPreset {
  const preset = getQualityPreset(baseSettings.quality);
  
  // Adjust interval based on zoom level - more thumbnails at higher zoom
  const adjustedInterval = Math.max(0.25, preset.intervalSeconds / Math.sqrt(zoomLevel));
  
  // Limit total thumbnails based on duration and performance
  const maxThumbnailsForDuration = Math.ceil(elementDuration / adjustedInterval);
  const adjustedMaxThumbnails = Math.min(
    maxThumbnailsForDuration,
    preset.maxThumbnails,
    baseSettings.maxThumbnailsPerVideo
  );

  return {
    ...preset,
    intervalSeconds: adjustedInterval,
    maxThumbnails: adjustedMaxThumbnails,
  };
}

/**
 * Check if thumbnail update should be triggered based on time change
 */
export function shouldUpdateThumbnail(
  currentTime: number,
  lastUpdateTime: number,
  threshold: number
): boolean {
  return Math.abs(currentTime - lastUpdateTime) >= threshold;
}

/**
 * Performance monitoring utilities
 */
export class ThumbnailPerformanceMonitor {
  private metrics: {
    generationTimes: number[];
    cacheHits: number;
    cacheMisses: number;
    memoryUsage: number[];
  } = {
    generationTimes: [],
    cacheHits: 0,
    cacheMisses: 0,
    memoryUsage: [],
  };

  recordGenerationTime(timeMs: number): void {
    this.metrics.generationTimes.push(timeMs);
    // Keep only last 100 measurements
    if (this.metrics.generationTimes.length > 100) {
      this.metrics.generationTimes.shift();
    }
  }

  recordCacheHit(): void {
    this.metrics.cacheHits++;
  }

  recordCacheMiss(): void {
    this.metrics.cacheMisses++;
  }

  recordMemoryUsage(bytes: number): void {
    this.metrics.memoryUsage.push(bytes);
    if (this.metrics.memoryUsage.length > 50) {
      this.metrics.memoryUsage.shift();
    }
  }

  getStats() {
    const generationTimes = this.metrics.generationTimes;
    const avgGenerationTime = generationTimes.length > 0 
      ? generationTimes.reduce((a, b) => a + b, 0) / generationTimes.length 
      : 0;

    const cacheHitRate = this.metrics.cacheHits + this.metrics.cacheMisses > 0
      ? (this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses)) * 100
      : 0;

    const avgMemoryUsage = this.metrics.memoryUsage.length > 0
      ? this.metrics.memoryUsage.reduce((a, b) => a + b, 0) / this.metrics.memoryUsage.length
      : 0;

    return {
      averageGenerationTime: avgGenerationTime,
      cacheHitRate,
      averageMemoryUsage: avgMemoryUsage,
      totalGenerations: generationTimes.length,
      totalCacheHits: this.metrics.cacheHits,
      totalCacheMisses: this.metrics.cacheMisses,
    };
  }

  reset(): void {
    this.metrics = {
      generationTimes: [],
      cacheHits: 0,
      cacheMisses: 0,
      memoryUsage: [],
    };
  }
}

// Global performance monitor instance
export const thumbnailPerformanceMonitor = new ThumbnailPerformanceMonitor();

/**
 * Adaptive quality adjustment based on performance
 */
export function getAdaptiveQuality(
  currentQuality: ThumbnailSettings['quality'],
  performanceStats: ReturnType<ThumbnailPerformanceMonitor['getStats']>
): ThumbnailSettings['quality'] {
  const { averageGenerationTime, cacheHitRate } = performanceStats;

  // If generation is slow and cache hit rate is low, reduce quality
  if (averageGenerationTime > 2000 && cacheHitRate < 50) {
    return currentQuality === 'high' ? 'medium' : 'low';
  }

  // If performance is good, we can increase quality
  if (averageGenerationTime < 500 && cacheHitRate > 80) {
    return currentQuality === 'low' ? 'medium' : 'high';
  }

  return currentQuality;
}
