use wasm_bindgen::prelude::*;
use web_sys::{AudioContext, AudioBufferSourceNode, GainNode};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::rc::Rc;
use std::cell::RefCell;
use uuid::Uuid;
use std::time::{Duration, Instant};

use crate::types::timeline::{TimelineElement, ElementType};
use crate::utils::audio_manager::{AudioManager, AudioPlaybackState};
use crate::utils::audio_effects_system::{AudioEffectsProcessor, AudioElementEffects};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AudioSyncSettings {
    pub sync_tolerance: f64,      // Maximum time difference before correction (seconds)
    pub correction_rate: f64,     // How aggressively to correct drift (0.0-1.0)
    pub buffer_size: u32,         // Audio buffer size for low latency
    pub lookahead_time: f64,      // How far ahead to prepare audio (seconds)
}

impl Default for AudioSyncSettings {
    fn default() -> Self {
        Self {
            sync_tolerance: 0.02,    // 20ms tolerance
            correction_rate: 0.1,    // Gentle correction
            buffer_size: 256,        // Small buffer for low latency
            lookahead_time: 0.1,     // 100ms lookahead
        }
    }
}

#[derive(Debug, Clone)]
pub struct AudioElementState {
    pub element_id: Uuid,
    pub source_node: Option<AudioBufferSourceNode>,
    pub gain_node: Option<GainNode>,
    pub start_time: f64,
    pub offset: f64,
    pub is_playing: bool,
    pub scheduled_start: Option<f64>,
}

#[derive(Debug, Clone)]
pub struct AudioSyncStats {
    pub active_elements: usize,
    pub total_elements: usize,
    pub timeline_position: f64,
    pub last_sync_time: f64,
    pub is_syncing: bool,
    pub sync_tolerance: f64,
    pub effects_processing_time: f64,
    pub last_optimization_time: f64,
}

#[derive(Debug, Clone)]
pub struct PerformanceMetrics {
    pub effects_processing_time: Duration,
    pub sync_processing_time: Duration,
    pub parameter_update_time: Duration,
    pub total_processing_time: Duration,
    pub frame_count: u64,
    pub dropped_frames: u64,
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self {
            effects_processing_time: Duration::new(0, 0),
            sync_processing_time: Duration::new(0, 0),
            parameter_update_time: Duration::new(0, 0),
            total_processing_time: Duration::new(0, 0),
            frame_count: 0,
            dropped_frames: 0,
        }
    }
}

#[derive(Debug, Clone)]
pub struct EffectsCache {
    pub cached_effects: HashMap<String, AudioElementEffects>,
    pub cache_timestamps: HashMap<String, Instant>,
    pub cache_ttl: Duration,
}

impl Default for EffectsCache {
    fn default() -> Self {
        Self {
            cached_effects: HashMap::new(),
            cache_timestamps: HashMap::new(),
            cache_ttl: Duration::from_millis(100), // 100ms cache TTL
        }
    }
}

pub struct AudioTimelineSync {
    context: Option<AudioContext>,
    audio_elements: HashMap<Uuid, AudioElementState>,
    timeline_position: f64,
    last_sync_time: f64,
    settings: AudioSyncSettings,
    master_gain: Option<GainNode>,
    effects_processor: Option<Rc<RefCell<AudioEffectsProcessor>>>,
    is_syncing: bool,
    performance_metrics: PerformanceMetrics,
    effects_cache: EffectsCache,
    last_parameter_update: HashMap<String, Instant>,
    parameter_debounce_time: Duration,
}

impl AudioTimelineSync {
    pub fn new(context: Option<AudioContext>, master_gain: Option<GainNode>) -> Self {
        Self {
            context,
            audio_elements: HashMap::new(),
            timeline_position: 0.0,
            last_sync_time: 0.0,
            settings: AudioSyncSettings::default(),
            master_gain,
            effects_processor: None,
            is_syncing: false,
            performance_metrics: PerformanceMetrics::default(),
            effects_cache: EffectsCache::default(),
            last_parameter_update: HashMap::new(),
            parameter_debounce_time: Duration::from_millis(16), // ~60 FPS debouncing
        }
    }

    pub fn update_timeline_position(&mut self, position: f64) {
        self.timeline_position = position;

        if self.is_syncing {
            self.sync_audio_elements();
        }

        // Always apply effects during scrubbing, even when not playing
        self.apply_scrubbing_effects();
    }

    fn apply_scrubbing_effects(&mut self) {
        // Apply effects for all elements at the current timeline position
        let element_ids: Vec<Uuid> = self.audio_elements.keys().cloned().collect();

        for element_id in element_ids {
            if let Some(ref effects_processor) = self.effects_processor {
                if let Ok(mut processor) = effects_processor.try_borrow_mut() {
                    let element_id_str = element_id.to_string();

                    // Apply position-based effects during scrubbing
                    if let Some(element_state) = self.audio_elements.get(&element_id) {
                        let element_start_time = element_state.start_time;
                        let relative_time = self.timeline_position - element_start_time;

                        // Only apply effects if we're within the element's time range
                        if relative_time >= 0.0 {
                            let element_duration = 10.0; // This should come from actual element data

                            // Apply fade automation for scrubbing
                            if let Err(e) = processor.apply_fade_automation(&element_id_str, relative_time, element_duration) {
                                web_sys::console::warn_1(&format!("Failed to apply scrubbing fade automation: {:?}", e).into());
                            }

                            // Apply volume based on timeline position
                            let volume_factor = self.calculate_scrubbing_volume(relative_time, element_duration as f64);
                            if let Err(e) = processor.set_volume(&element_id_str, volume_factor) {
                                web_sys::console::warn_1(&format!("Failed to set scrubbing volume: {:?}", e).into());
                            }
                        }
                    }
                }
            }
        }
    }

    fn calculate_scrubbing_volume(&self, relative_time: f64, duration: f64) -> f32 {
        // Calculate volume based on position within element for scrubbing feedback
        if relative_time < 0.0 || relative_time > duration {
            return 0.0;
        }

        // Provide audio feedback during scrubbing with reduced volume
        let base_volume = 0.3; // Reduced volume for scrubbing
        let fade_in_time = 0.1; // 100ms fade in
        let fade_out_time = 0.1; // 100ms fade out

        if relative_time < fade_in_time {
            base_volume * (relative_time / fade_in_time) as f32
        } else if relative_time > duration - fade_out_time {
            base_volume * ((duration - relative_time) / fade_out_time) as f32
        } else {
            base_volume
        }
    }

    pub fn start_sync(&mut self) {
        self.is_syncing = true;
        if let Some(ref context) = self.context {
            self.last_sync_time = context.current_time();
        }
        // Start real-time effects processing
        self.start_realtime_effects_processing();
    }

    fn start_realtime_effects_processing(&mut self) {
        if let Some(ref context) = self.context {
            // Set up a script processor for real-time effects processing
            // This would be called continuously during playback
            self.schedule_effects_update();
        }
    }

    fn schedule_effects_update(&mut self) {
        // Schedule the next effects update
        // In a real implementation, this would use requestAnimationFrame or a timer
        if self.is_syncing {
            self.process_realtime_effects();
        }
    }

    fn process_realtime_effects(&mut self) {
        if let Some(ref context) = self.context {
            let current_time = context.current_time();

            // Update timeline position based on audio context time
            let time_delta = current_time - self.last_sync_time;
            self.timeline_position += time_delta;
            self.last_sync_time = current_time;

            // Apply effects for all active elements
            self.synchronize_effects_with_timeline();

            // Process any pending fade automations
            self.process_fade_automations();
        }
    }

    fn process_fade_automations(&mut self) {
        let element_ids: Vec<Uuid> = self.audio_elements.keys().cloned().collect();

        for element_id in element_ids {
            if let Some(ref effects_processor) = self.effects_processor {
                if let Ok(mut processor) = effects_processor.try_borrow_mut() {
                    let element_id_str = element_id.to_string();

                    // Apply real-time fade automation
                    if let Some(element_state) = self.audio_elements.get(&element_id) {
                        let element_start_time = element_state.start_time;
                        let relative_time = self.timeline_position - element_start_time;
                        let element_duration = 10.0; // This should come from actual element data

                        if let Err(e) = processor.apply_fade_automation(&element_id_str, relative_time, element_duration) {
                            web_sys::console::warn_1(&format!("Failed to apply real-time fade automation: {:?}", e).into());
                        }
                    }
                }
            }
        }
    }

    pub fn stop_sync(&mut self) {
        self.is_syncing = false;
        self.stop_all_audio_elements();
    }

    pub fn set_effects_processor(&mut self, effects_processor: Rc<RefCell<AudioEffectsProcessor>>) {
        self.effects_processor = Some(effects_processor);
    }

    pub fn add_timeline_elements(&mut self, elements: &[TimelineElement]) {
        for element in elements {
            if element.element_type == ElementType::Audio {
                let audio_state = AudioElementState {
                    element_id: element.id,
                    source_node: None,
                    gain_node: None,
                    start_time: element.start_time,
                    offset: 0.0,
                    is_playing: false,
                    scheduled_start: None,
                };
                self.audio_elements.insert(element.id, audio_state);
            }
        }
    }

    pub fn sync_audio_elements(&mut self) {
        if let Some(ref context) = self.context {
            let current_audio_time = context.current_time();
            let time_diff = current_audio_time - self.last_sync_time;
            
            // Check if we need to correct for drift
            if time_diff.abs() > self.settings.sync_tolerance {
                self.correct_audio_drift(time_diff);
            }
            
            // Update playing elements
            self.update_playing_elements();
            
            // Start new elements that should be playing
            self.start_scheduled_elements();
            
            // Stop elements that should no longer be playing
            self.stop_finished_elements();
            
            self.last_sync_time = current_audio_time;
        }
    }

    fn correct_audio_drift(&mut self, drift: f64) {
        let correction = drift * self.settings.correction_rate;

        // Apply different correction strategies based on drift magnitude
        if drift.abs() > 0.1 {
            // Large drift: restart audio elements with corrected timing
            self.restart_audio_elements_with_correction(drift);
        } else {
            // Small drift: adjust playback rate gradually
            for (_, state) in self.audio_elements.iter_mut() {
                if state.is_playing {
                    if let Some(ref source) = state.source_node {
                        // Adjust playback rate slightly to correct drift
                        let current_rate = source.playback_rate().value();
                        let new_rate = current_rate * (1.0 + correction as f32 * 0.01);
                        source.playback_rate().set_value(new_rate.clamp(0.95, 1.05));
                    }
                }
            }
        }
    }

    fn restart_audio_elements_with_correction(&mut self, drift: f64) {
        // For large drifts, restart all playing elements with corrected timing
        let elements_to_restart: Vec<Uuid> = self.audio_elements
            .iter()
            .filter(|(_, state)| state.is_playing)
            .map(|(id, _)| *id)
            .collect();

        for element_id in elements_to_restart {
            if let Some(state) = self.audio_elements.get_mut(&element_id) {
                // Stop current playback
                if let Some(ref source) = state.source_node {
                    let _ = source.stop();
                }

                // Calculate corrected start time
                let corrected_offset = state.offset + drift;
                state.offset = corrected_offset;

                // Reschedule with correction
                if let Some(ref context) = self.context {
                    let new_start_time = context.current_time() + self.settings.lookahead_time;
                    state.scheduled_start = Some(new_start_time);
                }
            }
        }
    }

    fn update_playing_elements(&mut self) {
        let current_time = self.timeline_position;

        // Collect elements that need state changes to avoid borrowing conflicts
        let mut elements_to_start = Vec::new();
        let mut elements_to_stop = Vec::new();

        for (id, state) in self.audio_elements.iter() {
            let should_be_playing = current_time >= state.start_time &&
                                  current_time < state.start_time + 10.0; // Assume 10s duration for now

            if should_be_playing && !state.is_playing {
                elements_to_start.push(*id);
            } else if !should_be_playing && state.is_playing {
                elements_to_stop.push(*id);
            }
        }

        // Apply state changes
        for id in elements_to_start {
            // Apply effects first (before borrowing state mutably)
            self.apply_effects_for_element(&id);

            if let Some(state) = self.audio_elements.get_mut(&id) {
                // Inline schedule_audio_element logic
                if let Some(ref context) = self.context {
                    let start_time = context.current_time() + self.settings.lookahead_time;
                    state.scheduled_start = Some(start_time);

                    // This would be where we create and schedule the actual audio source
                    // For now, we'll mark it as playing
                    state.is_playing = true;
                }
            }
        }

        for id in elements_to_stop {
            if let Some(state) = self.audio_elements.get_mut(&id) {
                // Inline stop_audio_element logic
                if let Some(ref source) = state.source_node {
                    source.stop().ok();
                }
                state.source_node = None;
                state.is_playing = false;
                state.scheduled_start = None;
            }
        }
    }

    fn schedule_audio_element(&mut self, state: &mut AudioElementState) {
        if let Some(ref context) = self.context {
            let start_time = context.current_time() + self.settings.lookahead_time;
            state.scheduled_start = Some(start_time);

            // This would be where we create and schedule the actual audio source
            // For now, we'll mark it as playing
            state.is_playing = true;
        }
    }

    fn stop_audio_element(&mut self, state: &mut AudioElementState) {
        if let Some(ref source) = state.source_node {
            let _ = source.stop();
        }

        if let Some(ref gain) = state.gain_node {
            let _ = gain.disconnect();
        }

        state.source_node = None;
        state.gain_node = None;
        state.is_playing = false;
        state.scheduled_start = None;
    }

    fn start_scheduled_elements(&mut self) {
        if let Some(ref context) = self.context {
            let current_time = context.current_time();

            // Collect elements that need to be started
            let mut elements_to_start = Vec::new();

            for (id, state) in self.audio_elements.iter() {
                if let Some(scheduled_time) = state.scheduled_start {
                    if current_time >= scheduled_time && !state.is_playing {
                        elements_to_start.push(*id);
                    }
                }
            }

            // Start the collected elements
            for id in elements_to_start {
                if let Some(state) = self.audio_elements.get_mut(&id) {
                    // Inline the start_audio_element logic to avoid borrowing conflicts
                    if let Some(ref context) = self.context {
                        // Create gain node for this element
                        if let Ok(gain_node) = context.create_gain() {
                            if let Some(ref master_gain) = self.master_gain {
                                let _ = gain_node.connect_with_audio_node(master_gain);
                            }
                            state.gain_node = Some(gain_node);
                            state.is_playing = true;
                        }
                    }
                    state.scheduled_start = None;
                }
            }
        }
    }

    fn start_audio_element(&mut self, state: &mut AudioElementState) {
        if let Some(ref context) = self.context {
            // Create gain node for this element
            if let Ok(gain_node) = context.create_gain() {
                if let Some(ref master_gain) = self.master_gain {
                    let _ = gain_node.connect_with_audio_node(master_gain);
                }
                state.gain_node = Some(gain_node);
                state.is_playing = true;
            }
        }
    }



    fn stop_finished_elements(&mut self) {
        let current_time = self.timeline_position;
        let mut to_stop = Vec::new();
        
        for (id, state) in self.audio_elements.iter() {
            if state.is_playing {
                let end_time = state.start_time + 10.0; // Assume 10s duration
                if current_time >= end_time {
                    to_stop.push(*id);
                }
            }
        }
        
        for id in to_stop {
            if let Some(state) = self.audio_elements.get_mut(&id) {
                // Inline stop_audio_element logic
                if let Some(ref source) = state.source_node {
                    source.stop().ok();
                }
                state.source_node = None;
                state.is_playing = false;
                state.scheduled_start = None;
            }
        }
    }

    fn stop_all_audio_elements(&mut self) {
        let ids: Vec<Uuid> = self.audio_elements.keys().cloned().collect();
        for id in ids {
            if let Some(state) = self.audio_elements.get_mut(&id) {
                // Inline stop_audio_element logic
                if let Some(ref source) = state.source_node {
                    source.stop().ok();
                }
                state.source_node = None;
                state.is_playing = false;
                state.scheduled_start = None;
            }
        }
    }

    pub fn set_sync_settings(&mut self, settings: AudioSyncSettings) {
        self.settings = settings;
    }

    pub fn get_sync_settings(&self) -> &AudioSyncSettings {
        &self.settings
    }

    pub fn get_audio_element_states(&self) -> &HashMap<Uuid, AudioElementState> {
        &self.audio_elements
    }

    pub fn is_element_playing(&self, element_id: &Uuid) -> bool {
        self.audio_elements.get(element_id)
            .map(|state| state.is_playing)
            .unwrap_or(false)
    }

    pub fn seek_to_position(&mut self, position: f64) {
        self.timeline_position = position;

        // Stop all currently playing elements
        self.stop_all_audio_elements();

        // Update element states based on new position
        for (_, state) in self.audio_elements.iter_mut() {
            state.is_playing = false;
            state.scheduled_start = None;
            state.source_node = None;
        }

        // Trigger sync to start appropriate elements at new position
        if self.is_syncing {
            self.sync_audio_elements();
        }
    }

    pub fn set_playback_rate(&mut self, rate: f64) {
        // Update playback rate for all active audio sources
        for (_, state) in self.audio_elements.iter() {
            if let Some(ref source) = state.source_node {
                source.playback_rate().set_value(rate as f32);
            }
        }
    }

    pub fn get_timeline_position(&self) -> f64 {
        self.timeline_position
    }

    pub fn get_sync_drift(&self) -> Option<f64> {
        if let Some(ref context) = self.context {
            let current_audio_time = context.current_time();
            let expected_audio_time = self.last_sync_time + (self.timeline_position - self.last_sync_time);
            Some(current_audio_time - expected_audio_time)
        } else {
            None
        }
    }

    pub fn correct_sync_drift(&mut self) -> Result<(), JsValue> {
        if let Some(drift) = self.get_sync_drift() {
            if drift.abs() > self.settings.sync_tolerance {
                // Apply correction by adjusting timeline position
                let correction = drift * self.settings.correction_rate;
                self.timeline_position += correction;

                // Re-sync audio elements with corrected position
                self.sync_audio_elements();
            }
        }
        Ok(())
    }

    pub fn update_element_offset(&mut self, element_id: &Uuid, offset: f64) {
        if let Some(state) = self.audio_elements.get_mut(element_id) {
            state.offset = offset;
        }
    }

    pub fn remove_timeline_element(&mut self, element_id: &Uuid) {
        if let Some(state) = self.audio_elements.remove(element_id) {
            // Stop the element if it's playing
            if let Some(ref source) = state.source_node {
                let _ = source.stop();
            }
        }
    }

    pub fn clear_all_elements(&mut self) {
        self.stop_all_audio_elements();
        self.audio_elements.clear();
    }

    pub fn get_active_elements_count(&self) -> usize {
        self.audio_elements.values()
            .filter(|state| state.is_playing)
            .count()
    }

    pub fn set_precise_timeline_position(&mut self, position: f64, frame_rate: f64) {
        // Calculate sub-frame precision timing
        let frame_duration = 1.0 / frame_rate;
        let frame_number = (position / frame_duration).floor();
        let sub_frame_offset = position - (frame_number * frame_duration);

        self.timeline_position = position;

        // Apply sub-frame correction to audio timing
        if self.is_syncing {
            self.apply_sub_frame_correction(sub_frame_offset, frame_duration);
        }
    }

    fn apply_sub_frame_correction(&mut self, sub_frame_offset: f64, frame_duration: f64) {
        // Adjust audio timing to maintain sub-frame accuracy
        for (_, state) in self.audio_elements.iter_mut() {
            if state.is_playing {
                if let Some(ref source) = state.source_node {
                    // Calculate timing correction for sub-frame accuracy
                    let timing_correction = sub_frame_offset / frame_duration;

                    // Apply micro-adjustment to playback rate
                    let current_rate = source.playback_rate().value();
                    let adjustment = timing_correction as f32 * 0.001; // Very small adjustment
                    let new_rate = (current_rate + adjustment).clamp(0.999, 1.001);
                    source.playback_rate().set_value(new_rate);
                }
            }
        }
    }

    pub fn schedule_element_at_precise_time(&mut self, element_id: Uuid, start_time: f64, duration: f64) {
        if let Some(state) = self.audio_elements.get_mut(&element_id) {
            state.start_time = start_time;

            // Calculate precise scheduling with lookahead
            if let Some(ref context) = self.context {
                let audio_context_time = context.current_time();
                let timeline_to_audio_offset = audio_context_time - self.timeline_position;
                let precise_start_time = start_time + timeline_to_audio_offset + self.settings.lookahead_time;

                state.scheduled_start = Some(precise_start_time);
            }
        }
    }

    pub fn get_audio_latency_compensation(&self) -> f64 {
        // Calculate system audio latency for compensation
        if let Some(ref context) = self.context {
            // Estimate latency based on buffer size and sample rate
            let buffer_latency = self.settings.buffer_size as f64 / context.sample_rate() as f64;
            buffer_latency + self.settings.lookahead_time
        } else {
            self.settings.lookahead_time
        }
    }

    pub fn get_sync_stats(&self) -> AudioSyncStats {
        AudioSyncStats {
            active_elements: self.audio_elements.values().filter(|s| s.is_playing).count(),
            total_elements: self.audio_elements.len(),
            timeline_position: self.timeline_position,
            last_sync_time: self.last_sync_time,
            is_syncing: self.is_syncing,
            sync_tolerance: self.settings.sync_tolerance,
            effects_processing_time: self.performance_metrics.effects_processing_time.as_secs_f64(),
            last_optimization_time: self.last_sync_time, // Use last_sync_time as a reasonable default
        }
    }

    fn apply_effects_for_element(&self, element_id: &Uuid) {
        if let Some(ref effects_processor) = self.effects_processor {
            if let Ok(mut processor) = effects_processor.try_borrow_mut() {
                let element_id_str = element_id.to_string();

                // Apply fade automation based on timeline position
                let element_duration = 10.0; // This should come from the actual element
                if let Err(e) = processor.apply_fade_automation(&element_id_str, self.timeline_position, element_duration) {
                    web_sys::console::warn_1(&format!("Failed to apply fade automation: {:?}", e).into());
                }
            }
        }
    }

    pub fn update_element_effects(&mut self, element_id: &Uuid, effects: AudioElementEffects) {
        if let Some(ref effects_processor) = self.effects_processor {
            if let Ok(mut processor) = effects_processor.try_borrow_mut() {
                let element_id_str = element_id.to_string();
                if let Err(e) = processor.update_element_effects(&element_id_str, effects) {
                    web_sys::console::warn_1(&format!("Failed to update element effects: {:?}", e).into());
                }
            }
        }
    }

    pub fn synchronize_effects_with_timeline(&mut self) {
        // Synchronize all audio elements' effects with current timeline position
        let element_ids: Vec<Uuid> = self.audio_elements.keys().cloned().collect();

        for element_id in element_ids {
            self.apply_effects_for_element(&element_id);

            // Update fade automation based on timeline position
            if let Some(ref effects_processor) = self.effects_processor {
                if let Ok(mut processor) = effects_processor.try_borrow_mut() {
                    let element_id_str = element_id.to_string();

                    // Apply time-based effects automation
                    if let Some(element_state) = self.audio_elements.get(&element_id) {
                        let element_start_time = element_state.start_time;
                        let relative_time = self.timeline_position - element_start_time;

                        // Apply fade automation
                        let element_duration = 10.0; // This should come from actual element data
                        if let Err(e) = processor.apply_fade_automation(&element_id_str, relative_time, element_duration) {
                            web_sys::console::warn_1(&format!("Failed to apply fade automation: {:?}", e).into());
                        }
                    }
                }
            }
        }
    }

    pub fn update_realtime_parameter(&mut self, element_id: &Uuid, parameter: &str, value: f32) {
        let start_time = Instant::now();

        // Debounce parameter updates to avoid excessive processing
        let param_key = format!("{}:{}", element_id, parameter);
        if let Some(last_update) = self.last_parameter_update.get(&param_key) {
            if start_time.duration_since(*last_update) < self.parameter_debounce_time {
                return; // Skip update if too recent
            }
        }

        if let Some(ref effects_processor) = self.effects_processor {
            if let Ok(mut processor) = effects_processor.try_borrow_mut() {
                let element_id_str = element_id.to_string();

                match parameter {
                    "volume" => {
                        if let Err(e) = processor.set_volume(&element_id_str, value) {
                            web_sys::console::warn_1(&format!("Failed to update volume: {:?}", e).into());
                        }
                    },
                    "pan" => {
                        if let Err(e) = processor.set_pan(&element_id_str, value) {
                            web_sys::console::warn_1(&format!("Failed to update pan: {:?}", e).into());
                        }
                    },
                    "filter_frequency" => {
                        // Update filter frequency in real-time
                        if let Some(effects) = processor.get_element_effects(&element_id_str) {
                            let mut updated_effects = effects.clone();
                            updated_effects.filter_settings.frequency = value;
                            if let Err(e) = processor.update_element_effects(&element_id_str, updated_effects) {
                                web_sys::console::warn_1(&format!("Failed to update filter frequency: {:?}", e).into());
                            }
                        }
                    },
                    "filter_q" => {
                        // Update filter Q factor in real-time
                        if let Some(effects) = processor.get_element_effects(&element_id_str) {
                            let mut updated_effects = effects.clone();
                            updated_effects.filter_settings.q_factor = value;
                            if let Err(e) = processor.update_element_effects(&element_id_str, updated_effects) {
                                web_sys::console::warn_1(&format!("Failed to update filter Q: {:?}", e).into());
                            }
                        }
                    },
                    _ => {
                        web_sys::console::warn_1(&format!("Unknown parameter: {}", parameter).into());
                    }
                }
            }
        }

        // Update debounce tracking
        self.last_parameter_update.insert(param_key, start_time);

        // Update performance metrics
        let processing_time = start_time.elapsed();
        self.performance_metrics.parameter_update_time += processing_time;
    }

    pub fn schedule_parameter_automation(&mut self, element_id: &Uuid, parameter: &str, keyframes: Vec<(f64, f32)>) {
        // Schedule parameter automation over time
        let mut target_value: Option<f32> = None;

        // Find the appropriate value for current timeline position
        for (time, value) in keyframes {
            if (self.timeline_position - time).abs() < 0.1 { // Within 100ms tolerance
                target_value = Some(value);
                break;
            }
        }

        // Apply the value if found
        if let Some(value) = target_value {
            self.update_realtime_parameter(element_id, parameter, value);
        }
    }

    pub fn apply_parameter_smoothing(&mut self, element_id: &Uuid, parameter: &str, target_value: f32, _duration: f64) {
        // Apply smooth parameter transitions to avoid audio artifacts
        // For now, we'll just apply the target value directly
        // In a real implementation, this would use animation frames or timers for smooth transitions
        self.update_realtime_parameter(element_id, parameter, target_value);
    }

    pub fn set_scrubbing_mode(&mut self, is_scrubbing: bool) {
        if is_scrubbing {
            // Enable scrubbing mode - apply effects immediately on position changes
            self.apply_scrubbing_effects();
        } else {
            // Disable scrubbing mode - return to normal playback behavior
            if self.is_syncing {
                self.synchronize_effects_with_timeline();
            }
        }
    }

    pub fn handle_scrub_position(&mut self, position: f64) {
        // Specialized method for handling scrubbing with optimized effects processing
        self.timeline_position = position;

        // Apply effects with scrubbing-specific optimizations
        self.apply_scrubbing_effects();

        // Optionally trigger brief audio preview during scrubbing
        self.trigger_scrub_audio_preview();
    }

    fn trigger_scrub_audio_preview(&mut self) {
        // Trigger a brief audio preview at the current scrub position
        // This provides audio feedback during scrubbing
        if let Some(ref context) = self.context {
            let current_time = context.current_time();

            // Schedule a brief audio preview
            for (element_id, element_state) in &self.audio_elements {
                if element_state.is_playing {
                    let element_start_time = element_state.start_time;
                    let relative_time = self.timeline_position - element_start_time;

                    if relative_time >= 0.0 {
                        // Apply scrubbing effects for audio preview
                        self.apply_effects_for_element(element_id);
                    }
                }
            }
        }
    }

    fn get_cached_effects(&mut self, element_id: &str) -> Option<AudioElementEffects> {
        // Check if cached effects are still valid
        if let Some(timestamp) = self.effects_cache.cache_timestamps.get(element_id) {
            if timestamp.elapsed() < self.effects_cache.cache_ttl {
                return self.effects_cache.cached_effects.get(element_id).cloned();
            } else {
                // Cache expired, remove it
                self.effects_cache.cached_effects.remove(element_id);
                self.effects_cache.cache_timestamps.remove(element_id);
            }
        }

        // Fetch fresh effects and cache them
        // First, try to get effects from the processor
        let effects_clone = if let Some(ref effects_processor) = self.effects_processor {
            if let Ok(processor) = effects_processor.try_borrow() {
                processor.get_element_effects(element_id).cloned()
            } else {
                None
            }
        } else {
            None
        };

        // Now cache and return the effects if we found them
        if let Some(effects) = effects_clone {
            self.cache_effects(element_id, effects.clone());
            return Some(effects);
        }

        None
    }

    fn cache_effects(&mut self, element_id: &str, effects: AudioElementEffects) {
        self.effects_cache.cached_effects.insert(element_id.to_string(), effects);
        self.effects_cache.cache_timestamps.insert(element_id.to_string(), Instant::now());
    }

    pub fn clear_effects_cache(&mut self) {
        self.effects_cache.cached_effects.clear();
        self.effects_cache.cache_timestamps.clear();
    }

    pub fn get_performance_metrics(&self) -> &PerformanceMetrics {
        &self.performance_metrics
    }

    pub fn reset_performance_metrics(&mut self) {
        self.performance_metrics = PerformanceMetrics::default();
    }

    pub fn optimize_processing(&mut self) {
        // Perform periodic optimizations
        let now = Instant::now();

        // Clean up expired cache entries
        let expired_keys: Vec<String> = self.effects_cache.cache_timestamps
            .iter()
            .filter(|(_, timestamp)| timestamp.elapsed() > self.effects_cache.cache_ttl)
            .map(|(key, _)| key.clone())
            .collect();

        for key in expired_keys {
            self.effects_cache.cached_effects.remove(&key);
            self.effects_cache.cache_timestamps.remove(&key);
        }

        // Clean up old parameter update tracking
        let debounce_cleanup_threshold = self.parameter_debounce_time * 10;
        self.last_parameter_update.retain(|_, timestamp| {
            now.duration_since(*timestamp) < debounce_cleanup_threshold
        });

        // Update frame count
        self.performance_metrics.frame_count += 1;
    }
}

impl Default for AudioTimelineSync {
    fn default() -> Self {
        Self::new(None, None)
    }
}
