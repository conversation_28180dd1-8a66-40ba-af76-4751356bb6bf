use serde::{Serialize, Deserialize};

use crate::utils::text_system::{TextStyle, FontWeight, FontStyle};

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct RichTextDocument {
    pub content: Vec<RichTextSpan>,
    pub default_style: TextStyle,
    pub selection: Option<TextSelection>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct RichTextSpan {
    pub text: String,
    pub style: Option<TextStyleOverride>,
    pub start_index: usize,
    pub end_index: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TextStyleOverride {
    pub font_family: Option<String>,
    pub font_size: Option<f32>,
    pub font_weight: Option<FontWeight>,
    pub font_style: Option<FontStyle>,
    pub color: Option<String>,
    pub background_color: Option<String>,
    pub underline: Option<bool>,
    pub strikethrough: Option<bool>,
    pub italic: Option<bool>,
    pub bold: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TextSelection {
    pub start: usize,
    pub end: usize,
    pub direction: SelectionDirection,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum SelectionDirection {
    Forward,
    Backward,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RichTextCommand {
    InsertText { text: String, position: usize },
    DeleteText { start: usize, end: usize },
    ApplyStyle { style: TextStyleOverride, start: usize, end: usize },
    RemoveStyle { properties: Vec<String>, start: usize, end: usize },
    SetSelection { selection: TextSelection },
    ClearSelection,
}

impl RichTextDocument {
    pub fn new(text: String, default_style: TextStyle) -> Self {
        let content = if text.is_empty() {
            Vec::new()
        } else {
            vec![RichTextSpan {
                text: text.clone(),
                style: None,
                start_index: 0,
                end_index: text.len(),
            }]
        };

        Self {
            content,
            default_style,
            selection: None,
        }
    }

    /// Get the plain text content
    pub fn get_plain_text(&self) -> String {
        self.content.iter().map(|span| span.text.as_str()).collect::<Vec<_>>().join("")
    }

    /// Get the length of the document in characters
    pub fn len(&self) -> usize {
        self.content.iter().map(|span| span.text.len()).sum()
    }

    /// Check if the document is empty
    pub fn is_empty(&self) -> bool {
        self.content.is_empty() || self.content.iter().all(|span| span.text.is_empty())
    }

    /// Insert text at a specific position
    pub fn insert_text(&mut self, text: &str, position: usize) -> Result<(), String> {
        if position > self.len() {
            return Err("Position out of bounds".to_string());
        }

        // Find the span that contains this position
        let mut current_pos = 0;
        let mut target_span_index = None;
        let mut relative_pos = 0;

        // First pass: find the target span without mutable borrowing
        for (i, span) in self.content.iter().enumerate() {
            let span_end = current_pos + span.text.len();

            if position >= current_pos && position <= span_end {
                target_span_index = Some(i);
                relative_pos = position - current_pos;
                break;
            }
            current_pos = span_end;
        }

        let span_index = target_span_index.ok_or("Position not found")?;

        // Handle special case: insert at beginning and merge with previous
        if relative_pos == 0 && span_index > 0 {
            let prev_style = self.content[span_index - 1].style.clone();
            let current_style = self.content[span_index].style.clone();
            if prev_style == current_style {
                self.content[span_index - 1].text.push_str(text);
                self.update_indices();
                return Ok(());
            }
        }

        // Now we can safely get a mutable reference to the target span
        let span = &mut self.content[span_index];

        if relative_pos == span.text.len() {
            // Insert at the end of this span
            span.text.push_str(text);
        } else {
            // Split the span and insert in the middle
            let after_text = span.text.split_off(relative_pos);
            span.text.push_str(text);

            let new_span = RichTextSpan {
                text: after_text,
                style: span.style.clone(),
                start_index: 0, // Will be updated
                end_index: 0,   // Will be updated
            };

            self.content.insert(span_index + 1, new_span);
        }

        self.update_indices();
        Ok(())
    }

    /// Delete text in a range
    pub fn delete_text(&mut self, start: usize, end: usize) -> Result<(), String> {
        if start > end || end > self.len() {
            return Err("Invalid range".to_string());
        }

        if start == end {
            return Ok(()); // Nothing to delete
        }

        let mut new_content = Vec::new();
        let mut current_pos = 0;

        for span in &self.content {
            let span_start = current_pos;
            let span_end = current_pos + span.text.len();

            if span_end <= start || span_start >= end {
                // Span is completely outside the deletion range
                new_content.push(span.clone());
            } else if span_start >= start && span_end <= end {
                // Span is completely inside the deletion range - skip it
                continue;
            } else {
                // Span is partially affected
                let mut new_text = String::new();
                
                if span_start < start {
                    // Keep the part before the deletion
                    let keep_end = (start - span_start).min(span.text.len());
                    new_text.push_str(&span.text[..keep_end]);
                }
                
                if span_end > end {
                    // Keep the part after the deletion
                    let keep_start = (end - span_start).max(0);
                    if keep_start < span.text.len() {
                        new_text.push_str(&span.text[keep_start..]);
                    }
                }
                
                if !new_text.is_empty() {
                    new_content.push(RichTextSpan {
                        text: new_text,
                        style: span.style.clone(),
                        start_index: 0, // Will be updated
                        end_index: 0,   // Will be updated
                    });
                }
            }

            current_pos = span_end;
        }

        self.content = new_content;
        self.update_indices();
        Ok(())
    }

    /// Apply style to a range of text
    pub fn apply_style(&mut self, style: TextStyleOverride, start: usize, end: usize) -> Result<(), String> {
        if start > end || end > self.len() {
            return Err("Invalid range".to_string());
        }

        let mut new_content = Vec::new();
        let mut current_pos = 0;

        for span in &self.content {
            let span_start = current_pos;
            let span_end = current_pos + span.text.len();

            if span_end <= start || span_start >= end {
                // Span is completely outside the style range
                new_content.push(span.clone());
            } else if span_start >= start && span_end <= end {
                // Span is completely inside the style range
                let mut new_span = span.clone();
                new_span.style = Some(self.merge_styles(&span.style, &style));
                new_content.push(new_span);
            } else {
                // Span is partially affected - need to split
                if span_start < start {
                    // Part before the style range
                    let before_text = &span.text[..(start - span_start)];
                    new_content.push(RichTextSpan {
                        text: before_text.to_string(),
                        style: span.style.clone(),
                        start_index: 0,
                        end_index: 0,
                    });
                }

                // Part within the style range
                let style_start = start.max(span_start) - span_start;
                let style_end = end.min(span_end) - span_start;
                let styled_text = &span.text[style_start..style_end];
                
                new_content.push(RichTextSpan {
                    text: styled_text.to_string(),
                    style: Some(self.merge_styles(&span.style, &style)),
                    start_index: 0,
                    end_index: 0,
                });

                if span_end > end {
                    // Part after the style range
                    let after_text = &span.text[(end - span_start)..];
                    new_content.push(RichTextSpan {
                        text: after_text.to_string(),
                        style: span.style.clone(),
                        start_index: 0,
                        end_index: 0,
                    });
                }
            }

            current_pos = span_end;
        }

        self.content = new_content;
        self.update_indices();
        self.merge_adjacent_spans();
        Ok(())
    }

    /// Get the style at a specific position
    pub fn get_style_at_position(&self, position: usize) -> TextStyle {
        let mut current_pos = 0;
        
        for span in &self.content {
            let span_end = current_pos + span.text.len();
            
            if position >= current_pos && position < span_end {
                return self.apply_style_override(&self.default_style, &span.style);
            }
            
            current_pos = span_end;
        }

        self.default_style.clone()
    }

    /// Update the start and end indices of all spans
    fn update_indices(&mut self) {
        let mut current_pos = 0;
        
        for span in &mut self.content {
            span.start_index = current_pos;
            span.end_index = current_pos + span.text.len();
            current_pos = span.end_index;
        }
    }

    /// Merge adjacent spans with the same style
    fn merge_adjacent_spans(&mut self) {
        let mut i = 0;
        while i < self.content.len().saturating_sub(1) {
            if self.content[i].style == self.content[i + 1].style {
                let next_text = self.content[i + 1].text.clone();
                self.content[i].text.push_str(&next_text);
                self.content.remove(i + 1);
            } else {
                i += 1;
            }
        }
        self.update_indices();
    }

    /// Merge two style overrides
    fn merge_styles(&self, base: &Option<TextStyleOverride>, overlay: &TextStyleOverride) -> TextStyleOverride {
        let mut result = base.clone().unwrap_or_default();
        
        if overlay.font_family.is_some() { result.font_family = overlay.font_family.clone(); }
        if overlay.font_size.is_some() { result.font_size = overlay.font_size; }
        if overlay.font_weight.is_some() { result.font_weight = overlay.font_weight.clone(); }
        if overlay.font_style.is_some() { result.font_style = overlay.font_style.clone(); }
        if overlay.color.is_some() { result.color = overlay.color.clone(); }
        if overlay.background_color.is_some() { result.background_color = overlay.background_color.clone(); }
        if overlay.underline.is_some() { result.underline = overlay.underline; }
        if overlay.strikethrough.is_some() { result.strikethrough = overlay.strikethrough; }
        if overlay.italic.is_some() { result.italic = overlay.italic; }
        if overlay.bold.is_some() { result.bold = overlay.bold; }
        
        result
    }

    /// Apply style override to base style
    fn apply_style_override(&self, base: &TextStyle, override_style: &Option<TextStyleOverride>) -> TextStyle {
        let mut result = base.clone();
        
        if let Some(style_override) = override_style {
            if let Some(ref family) = style_override.font_family {
                result.font_family = family.clone();
            }
            if let Some(size) = style_override.font_size {
                result.font_size = size;
            }
            if let Some(ref weight) = style_override.font_weight {
                result.font_weight = weight.clone();
            }
            if let Some(ref style) = style_override.font_style {
                result.font_style = style.clone();
            }
            if let Some(ref color) = style_override.color {
                result.color = color.clone();
            }
            // Handle other style properties as needed
        }
        
        result
    }
}

impl Default for TextStyleOverride {
    fn default() -> Self {
        Self {
            font_family: None,
            font_size: None,
            font_weight: None,
            font_style: None,
            color: None,
            background_color: None,
            underline: None,
            strikethrough: None,
            italic: None,
            bold: None,
        }
    }
}
