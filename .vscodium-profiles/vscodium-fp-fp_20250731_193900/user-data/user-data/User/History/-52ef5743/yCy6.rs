use web_sys::{
    AudioContext, HtmlVideoElement, MediaElementAudioSourceNode, GainNode, AnalyserNode,
    BiquadFilterNode, AudioNode, HtmlCanvasElement
};
use wasm_bindgen::prelude::*;

use std::rc::Rc;
use std::cell::RefCell;
use serde::{Serialize, Deserialize};
use crate::utils::waveform_renderer::{WaveformRenderer, WaveformConfig, WaveformController};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RealTimeAudioConfig {
    pub buffer_size: u32,
    pub sample_rate: f32,
    pub latency_hint: String, // "interactive", "balanced", "playback"
    pub max_channels: u32,
    pub processing_quality: ProcessingQuality,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProcessingQuality {
    Low,
    Medium,
    High,
    Ultra,
}

impl Default for RealTimeAudioConfig {
    fn default() -> Self {
        Self {
            buffer_size: 256,
            sample_rate: 44100.0,
            latency_hint: "interactive".to_string(),
            max_channels: 2,
            processing_quality: ProcessingQuality::High,
        }
    }
}

#[derive(Debug, Clone)]
pub struct AudioProcessingStats {
    pub current_time: f64,
    pub sample_rate: f32,
    pub buffer_size: u32,
    pub cpu_usage: f32,
    pub memory_usage: f32,
    pub latency_ms: f32,
    pub active_nodes: u32,
    pub dropped_frames: u32,
}

pub struct RealTimeAudioProcessor {
    context: AudioContext,
    config: RealTimeAudioConfig,
    input_gain: GainNode,
    output_gain: GainNode,
    analyser: AnalyserNode,
    processing_chain: Vec<Box<dyn AudioProcessingNode>>,
    stats: Rc<RefCell<AudioProcessingStats>>,
    is_processing: bool,
}

pub trait AudioProcessingNode {
    fn get_node(&self) -> &AudioNode;
    fn process(&mut self, input_buffer: &[f32], output_buffer: &mut [f32]) -> Result<(), JsValue>;
    fn get_latency(&self) -> f32;
    fn get_cpu_usage(&self) -> f32;
    fn set_parameter(&mut self, name: &str, value: f32) -> Result<(), JsValue>;
    fn get_parameter(&self, name: &str) -> Option<f32>;
    fn bypass(&mut self, bypassed: bool);
    fn is_bypassed(&self) -> bool;
}

pub struct AudioEngine {
    context: AudioContext,
    gain_node: GainNode,
    analyser_node: AnalyserNode,
    source_node: Option<MediaElementAudioSourceNode>,
    real_time_processor: Option<RealTimeAudioProcessor>,
    waveform_controller: Option<WaveformController>,
}

impl RealTimeAudioProcessor {
    pub fn new(config: RealTimeAudioConfig) -> Result<Self, JsValue> {
        let context = AudioContext::new()?;

        // Configure context for real-time processing
        let input_gain = context.create_gain()?;
        let output_gain = context.create_gain()?;
        let analyser = context.create_analyser()?;

        // Configure analyser for real-time monitoring
        analyser.set_fft_size(2048);
        analyser.set_smoothing_time_constant(0.8);
        analyser.set_min_decibels(-90.0);
        analyser.set_max_decibels(-10.0);

        // Connect basic processing chain
        input_gain.connect_with_audio_node(&analyser)?;
        analyser.connect_with_audio_node(&output_gain)?;
        output_gain.connect_with_audio_node(&context.destination())?;

        let stats = Rc::new(RefCell::new(AudioProcessingStats {
            current_time: 0.0,
            sample_rate: context.sample_rate(),
            buffer_size: config.buffer_size,
            cpu_usage: 0.0,
            memory_usage: 0.0,
            latency_ms: 0.0,
            active_nodes: 3, // input_gain, analyser, output_gain
            dropped_frames: 0,
        }));

        Ok(Self {
            context,
            config,
            input_gain,
            output_gain,
            analyser,
            processing_chain: Vec::new(),
            stats,
            is_processing: false,
        })
    }

    pub fn initialize(&mut self) -> Result<(), JsValue> {
        // Initialize the real-time processor
        // Set up any additional configuration needed
        Ok(())
    }

    pub fn start_processing(&mut self) -> Result<(), JsValue> {
        if self.is_processing {
            return Ok(());
        }

        // Resume context if suspended
        if self.context.state() == web_sys::AudioContextState::Suspended {
            let _ = self.context.resume();
        }

        self.is_processing = true;
        self.update_stats();

        Ok(())
    }

    pub fn stop_processing(&mut self) -> Result<(), JsValue> {
        if !self.is_processing {
            return Ok(());
        }

        self.is_processing = false;

        // Optionally suspend context to save resources
        let _ = self.context.suspend();

        Ok(())
    }

    fn update_stats(&self) {
        if let Ok(mut stats) = self.stats.try_borrow_mut() {
            stats.current_time = self.context.current_time();
            stats.active_nodes = 3 + self.processing_chain.len() as u32;

            // Calculate estimated latency
            let base_latency = (self.config.buffer_size as f32 / self.config.sample_rate) * 1000.0;
            let processing_latency: f32 = self.processing_chain.iter()
                .map(|node| node.get_latency())
                .sum();
            stats.latency_ms = base_latency + processing_latency;

            // Calculate CPU usage estimate
            stats.cpu_usage = self.processing_chain.iter()
                .map(|node| node.get_cpu_usage())
                .sum::<f32>()
                .min(100.0);
        }
    }
}

impl AudioEngine {
    pub fn new() -> Result<Self, String> {
        let context = AudioContext::new().map_err(|_| "Failed to create AudioContext")?;

        let gain_node = context
            .create_gain()
            .map_err(|_| "Failed to create GainNode")?;

        let analyser_node = context
            .create_analyser()
            .map_err(|_| "Failed to create AnalyserNode")?;

        // Set up analyser
        analyser_node.set_fft_size(2048);

        // Connect nodes: source -> gain -> analyser -> destination
        gain_node
            .connect_with_audio_node(&analyser_node)
            .map_err(|_| "Failed to connect gain to analyser")?;

        analyser_node
            .connect_with_audio_node(&context.destination())
            .map_err(|_| "Failed to connect analyser to destination")?;

        Ok(Self {
            context,
            gain_node,
            analyser_node,
            source_node: None,
            real_time_processor: None,
            waveform_controller: None,
        })
    }

    pub fn enable_real_time_processing(&mut self, config: RealTimeAudioConfig) -> Result<(), String> {
        let mut processor = RealTimeAudioProcessor::new(config)
            .map_err(|_| "Failed to create real-time processor")?;

        processor.initialize()
            .map_err(|_| "Failed to initialize real-time processor")?;

        self.real_time_processor = Some(processor);
        Ok(())
    }

    pub fn start_real_time_processing(&mut self) -> Result<(), String> {
        if let Some(ref mut processor) = self.real_time_processor {
            processor.start_processing()
                .map_err(|_| "Failed to start real-time processing")?;
        }
        Ok(())
    }

    pub fn stop_real_time_processing(&mut self) -> Result<(), String> {
        if let Some(ref mut processor) = self.real_time_processor {
            processor.stop_processing()
                .map_err(|_| "Failed to stop real-time processing")?;
        }
        Ok(())
    }

    pub fn get_processing_stats(&self) -> Option<AudioProcessingStats> {
        self.real_time_processor.as_ref()
            .and_then(|processor| processor.stats.try_borrow().ok())
            .map(|stats| stats.clone())
    }
    
    pub fn connect_video_element(&mut self, video: &HtmlVideoElement) -> Result<(), String> {
        // Disconnect existing source if any
        if let Some(ref source) = self.source_node {
            source.disconnect().map_err(|_| "Failed to disconnect existing source")?;
        }

        // Create new source from video element
        let source = self.context
            .create_media_element_source(video)
            .map_err(|_| "Failed to create media element source")?;

        // Connect source to gain node or real-time processor
        if let Some(ref processor) = self.real_time_processor {
            source
                .connect_with_audio_node(&processor.input_gain)
                .map_err(|_| "Failed to connect source to real-time processor")?;
        } else {
            source
                .connect_with_audio_node(&self.gain_node)
                .map_err(|_| "Failed to connect source to gain")?;
        }

        self.source_node = Some(source);
        Ok(())
    }

    pub fn set_volume(&self, volume: f32) -> Result<(), String> {
        let clamped_volume = volume.max(0.0).min(2.0);

        if let Some(ref processor) = self.real_time_processor {
            processor.output_gain.gain().set_value(clamped_volume);
        } else {
            self.gain_node.gain().set_value(clamped_volume);
        }

        Ok(())
    }

    pub fn get_frequency_data(&self) -> Vec<u8> {
        let analyser = if let Some(ref processor) = self.real_time_processor {
            &processor.analyser
        } else {
            &self.analyser_node
        };

        let buffer_length = analyser.frequency_bin_count();
        let mut data = vec![0u8; buffer_length as usize];
        analyser.get_byte_frequency_data(&mut data);
        data
    }

    pub fn get_time_domain_data(&self) -> Vec<u8> {
        let analyser = if let Some(ref processor) = self.real_time_processor {
            &processor.analyser
        } else {
            &self.analyser_node
        };

        let buffer_length = analyser.fft_size();
        let mut data = vec![0u8; buffer_length as usize];
        analyser.get_byte_time_domain_data(&mut data);
        data
    }

    pub fn get_frequency_data_float(&self) -> Vec<f32> {
        let analyser = if let Some(ref processor) = self.real_time_processor {
            &processor.analyser
        } else {
            &self.analyser_node
        };

        let buffer_length = analyser.frequency_bin_count();
        let mut data = vec![0.0f32; buffer_length as usize];
        analyser.get_float_frequency_data(&mut data);
        data
    }

    pub fn get_time_domain_data_float(&self) -> Vec<f32> {
        let analyser = if let Some(ref processor) = self.real_time_processor {
            &processor.analyser
        } else {
            &self.analyser_node
        };

        let buffer_length = analyser.fft_size();
        let mut data = vec![0.0f32; buffer_length as usize];
        analyser.get_float_time_domain_data(&mut data);
        data
    }

    pub fn get_rms_level(&self) -> f32 {
        let time_data = self.get_time_domain_data_float();
        let sum_squares: f32 = time_data.iter().map(|&x| x * x).sum();
        (sum_squares / time_data.len() as f32).sqrt()
    }

    pub fn get_peak_level(&self) -> f32 {
        let time_data = self.get_time_domain_data_float();
        time_data.iter().map(|&x| x.abs()).fold(0.0, f32::max)
    }

    pub fn resume(&self) -> Result<(), String> {
        let _ = self.context.resume();
        if let Some(ref processor) = self.real_time_processor {
            if processor.is_processing {
                // Resume real-time processing
            }
        }
        Ok(())
    }

    pub fn suspend(&self) -> Result<(), String> {
        let _ = self.context.suspend();
        Ok(())
    }

    pub fn is_real_time_enabled(&self) -> bool {
        self.real_time_processor.is_some()
    }

    pub fn get_context(&self) -> &AudioContext {
        &self.context
    }

    pub fn enable_waveform_visualization(&mut self, canvas: HtmlCanvasElement, config: WaveformConfig) -> Result<(), String> {
        let controller = WaveformController::new(canvas, config)
            .map_err(|_| "Failed to create waveform controller")?;

        self.waveform_controller = Some(controller);
        Ok(())
    }

    pub async fn update_waveform_from_buffer(&mut self, buffer: &web_sys::AudioBuffer) -> Result<(), String> {
        if let Some(ref mut controller) = self.waveform_controller {
            // Update the waveform with audio buffer
            let renderer = controller.get_renderer_mut();
            renderer.load_audio_buffer(buffer).await
                .map_err(|_| "Failed to update waveform data")?;

            // Trigger a redraw
            controller.render()
                .map_err(|_| "Failed to render waveform")?;
        }
        Ok(())
    }

    pub fn set_waveform_playback_position(&mut self, position: f32) -> Result<(), String> {
        if let Some(ref mut controller) = self.waveform_controller {
            let renderer = controller.get_renderer_mut();
            renderer.set_current_time(position);

            controller.render()
                .map_err(|_| "Failed to render waveform")?;
        }
        Ok(())
    }

    pub fn set_waveform_selection(&mut self, start_time: f32, end_time: f32) -> Result<(), String> {
        if let Some(ref mut controller) = self.waveform_controller {
            let renderer = controller.get_renderer_mut();
            renderer.set_selection(start_time, end_time);

            controller.render()
                .map_err(|_| "Failed to render waveform")?;
        }
        Ok(())
    }

    pub fn clear_waveform_selection(&mut self) -> Result<(), String> {
        if let Some(ref mut controller) = self.waveform_controller {
            let renderer = controller.get_renderer_mut();
            renderer.clear_selection();

            controller.render()
                .map_err(|_| "Failed to render waveform")?;
        }
        Ok(())
    }

    pub fn zoom_waveform(&mut self, zoom_level: f32) -> Result<(), String> {
        if let Some(ref mut controller) = self.waveform_controller {
            controller.zoom_in(None)
                .map_err(|_| "Failed to zoom waveform")?;
        }
        Ok(())
    }

    pub fn pan_waveform_to_time(&mut self, time: f32) -> Result<(), String> {
        if let Some(ref mut controller) = self.waveform_controller {
            controller.pan_to_time(time)
                .map_err(|_| "Failed to pan waveform")?;
        }
        Ok(())
    }

    pub fn get_waveform_time_at_pixel(&self, pixel: f32) -> Option<f32> {
        self.waveform_controller.as_ref()
            .and_then(|controller| controller.get_renderer().pixel_to_time(pixel))
    }

    pub fn get_waveform_pixel_at_time(&self, time: f32) -> Option<f32> {
        self.waveform_controller.as_ref()
            .and_then(|controller| controller.get_renderer().time_to_pixel(time))
    }

    pub fn is_waveform_enabled(&self) -> bool {
        self.waveform_controller.is_some()
    }

    pub fn render_waveform(&mut self) -> Result<(), String> {
        if let Some(ref mut controller) = self.waveform_controller {
            controller.render()
                .map_err(|_| "Failed to render waveform")?;
        }
        Ok(())
    }

    pub fn create_waveform_from_analyser_data(&mut self) -> Result<(), String> {
        if self.waveform_controller.is_none() {
            return Ok(());
        }

        // Note: For real-time waveform updates, we would need to create an AudioBuffer
        // from the analyser data, which is complex in the web environment.
        // For now, this is a placeholder for future implementation.

        Ok(())
    }
}

// Concrete implementation of AudioProcessingNode for GainNode
pub struct GainProcessingNode {
    node: GainNode,
    bypassed: bool,
    cpu_usage: f32,
}

impl GainProcessingNode {
    pub fn new(context: &AudioContext) -> Result<Self, JsValue> {
        let node = context.create_gain()?;
        Ok(Self {
            node,
            bypassed: false,
            cpu_usage: 0.1, // Minimal CPU usage for gain
        })
    }
}

impl AudioProcessingNode for GainProcessingNode {
    fn get_node(&self) -> &AudioNode {
        self.node.as_ref()
    }

    fn process(&mut self, input_buffer: &[f32], output_buffer: &mut [f32]) -> Result<(), JsValue> {
        if self.bypassed {
            output_buffer.copy_from_slice(input_buffer);
        } else {
            let gain = self.node.gain().value();
            for (i, &sample) in input_buffer.iter().enumerate() {
                if i < output_buffer.len() {
                    output_buffer[i] = sample * gain;
                }
            }
        }
        Ok(())
    }

    fn get_latency(&self) -> f32 {
        0.0 // Gain node has no latency
    }

    fn get_cpu_usage(&self) -> f32 {
        self.cpu_usage
    }

    fn set_parameter(&mut self, name: &str, value: f32) -> Result<(), JsValue> {
        match name {
            "gain" => {
                self.node.gain().set_value(value);
                Ok(())
            },
            _ => Err(JsValue::from_str(&format!("Unknown parameter: {}", name)))
        }
    }

    fn get_parameter(&self, name: &str) -> Option<f32> {
        match name {
            "gain" => Some(self.node.gain().value()),
            _ => None
        }
    }

    fn bypass(&mut self, bypassed: bool) {
        self.bypassed = bypassed;
    }

    fn is_bypassed(&self) -> bool {
        self.bypassed
    }
}

// Concrete implementation for BiquadFilter
pub struct FilterProcessingNode {
    node: BiquadFilterNode,
    bypassed: bool,
    cpu_usage: f32,
}

impl FilterProcessingNode {
    pub fn new(context: &AudioContext) -> Result<Self, JsValue> {
        let node = context.create_biquad_filter()?;
        Ok(Self {
            node,
            bypassed: false,
            cpu_usage: 0.5, // Moderate CPU usage for filtering
        })
    }
}

impl AudioProcessingNode for FilterProcessingNode {
    fn get_node(&self) -> &AudioNode {
        self.node.as_ref()
    }

    fn process(&mut self, input_buffer: &[f32], output_buffer: &mut [f32]) -> Result<(), JsValue> {
        if self.bypassed {
            output_buffer.copy_from_slice(input_buffer);
        }
        // For BiquadFilter, the actual processing is handled by the Web Audio API
        Ok(())
    }

    fn get_latency(&self) -> f32 {
        0.1 // Small latency for filtering
    }

    fn get_cpu_usage(&self) -> f32 {
        self.cpu_usage
    }

    fn set_parameter(&mut self, name: &str, value: f32) -> Result<(), JsValue> {
        match name {
            "frequency" => {
                self.node.frequency().set_value(value);
                Ok(())
            },
            "q" => {
                self.node.q().set_value(value);
                Ok(())
            },
            "gain" => {
                self.node.gain().set_value(value);
                Ok(())
            },
            _ => Err(JsValue::from_str(&format!("Unknown parameter: {}", name)))
        }
    }

    fn get_parameter(&self, name: &str) -> Option<f32> {
        match name {
            "frequency" => Some(self.node.frequency().value()),
            "q" => Some(self.node.q().value()),
            "gain" => Some(self.node.gain().value()),
            _ => None
        }
    }

    fn bypass(&mut self, bypassed: bool) {
        self.bypassed = bypassed;
    }

    fn is_bypassed(&self) -> bool {
        self.bypassed
    }
}

impl RealTimeAudioProcessor {
    pub fn add_gain_node(&mut self) -> Result<usize, JsValue> {
        let gain_node = GainProcessingNode::new(&self.context)?;
        self.processing_chain.push(Box::new(gain_node));
        self.rebuild_processing_chain()?;
        Ok(self.processing_chain.len() - 1)
    }

    pub fn add_filter_node(&mut self) -> Result<usize, JsValue> {
        let filter_node = FilterProcessingNode::new(&self.context)?;
        self.processing_chain.push(Box::new(filter_node));
        self.rebuild_processing_chain()?;
        Ok(self.processing_chain.len() - 1)
    }

    pub fn remove_processing_node(&mut self, index: usize) -> Result<(), JsValue> {
        if index < self.processing_chain.len() {
            self.processing_chain.remove(index);
            self.rebuild_processing_chain()?;
        }
        Ok(())
    }

    fn rebuild_processing_chain(&self) -> Result<(), JsValue> {
        // Disconnect all nodes
        self.input_gain.disconnect()?;
        for node in &self.processing_chain {
            node.get_node().disconnect()?;
        }
        self.analyser.disconnect()?;

        // Rebuild chain
        if self.processing_chain.is_empty() {
            self.input_gain.connect_with_audio_node(&self.analyser)?;
        } else {
            // Connect input to first processing node
            self.input_gain.connect_with_audio_node(self.processing_chain[0].get_node())?;

            // Connect processing nodes in series
            for i in 0..self.processing_chain.len() - 1 {
                self.processing_chain[i].get_node()
                    .connect_with_audio_node(self.processing_chain[i + 1].get_node())?;
            }

            // Connect last processing node to analyser
            if let Some(last_node) = self.processing_chain.last() {
                last_node.get_node().connect_with_audio_node(&self.analyser)?;
            }
        }

        // Connect analyser to output
        self.analyser.connect_with_audio_node(&self.output_gain)?;
        self.output_gain.connect_with_audio_node(&self.context.destination())?;

        Ok(())
    }

    pub fn get_stats(&self) -> AudioProcessingStats {
        self.stats.borrow().clone()
    }

    pub fn set_processing_parameter(&mut self, node_index: usize, param_name: &str, value: f32) -> Result<(), JsValue> {
        if let Some(node) = self.processing_chain.get_mut(node_index) {
            node.set_parameter(param_name, value)?;
        }
        Ok(())
    }

    pub fn get_processing_parameter(&self, node_index: usize, param_name: &str) -> Option<f32> {
        self.processing_chain.get(node_index)
            .and_then(|node| node.get_parameter(param_name))
    }

    pub fn bypass_processing_node(&mut self, node_index: usize, bypassed: bool) {
        if let Some(node) = self.processing_chain.get_mut(node_index) {
            node.bypass(bypassed);
        }
    }
}

impl Default for AudioEngine {
    fn default() -> Self {
        Self::new().unwrap_or_else(|_| {
            // Fallback implementation if AudioContext creation fails
            panic!("Failed to create AudioEngine")
        })
    }
}
