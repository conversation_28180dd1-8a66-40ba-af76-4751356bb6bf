use yew::prelude::*;
use web_sys::{Drag<PERSON><PERSON>, MouseEvent, InputEvent};
use wasm_bindgen::JsCast;
use uuid::Uuid;
use crate::types::timeline::{Timeline as TimelineData, Track, TrackType, TimelineElement, ElementType, ElementProperties};
use crate::stores::audio_store::AudioStoreAction;

mod selection_rectangle;
mod multi_element_operations;
mod selection_visual_feedback;
mod snapping_engine;
mod snap_feedback_system;
mod ripple_editing;
mod ripple_mode_toggle;
mod context_menu;
mod timeline_element_menu;
mod track_context_menu;
mod timeline_background_menu;
mod text_renderer;
mod animation_timeline_integration;

pub use selection_rectangle::{SelectionRectangle, SelectionArea, use_selection_rectangle, create_selection_handlers};
pub use multi_element_operations::{MultiElementOperations, MultiElementOperation, OperationResult};
pub use selection_visual_feedback::{SelectionFeedback, SelectionStyle, ResizeHandle, use_selection_feedback, create_selection_feedback_handlers};
pub use snapping_engine::{Snapping<PERSON><PERSON><PERSON>, SnapConfig, SnapResult, SnapPoint, SnapPointType};
pub use snap_feedback_system::{SnapFeedbackSystem, SnapFeedbackConfig, use_snap_feedback};
pub use ripple_editing::{RippleEditingEngine, RippleConfig, RippleOperation, RippleResult};
pub use ripple_mode_toggle::{RippleModeToggle, RippleShortcut, RippleModeIndicator};
pub use context_menu::{ContextMenu, ContextMenuManager, ContextMenuType, use_context_menu, create_context_menu_handlers};
pub use timeline_element_menu::{TimelineElementMenu, ElementMenuAction, TimelineElementWithMenu, use_timeline_element_menu};
pub use track_context_menu::{TrackContextMenu, TrackMenuAction, TrackWithMenu, use_track_menu};
pub use timeline_background_menu::{TimelineBackgroundMenu, TimelineBackgroundAction, TimelineWithBackgroundMenu, use_timeline_background_menu};
pub use text_renderer::{TimelineTextRenderer, TextElementPreview, use_timeline_text_renderer, create_text_element_handlers};
pub use animation_timeline_integration::{AnimationTimelineIntegration, AnimationTimelineState, use_animation_timeline_integration};

#[function_component(Timeline)]
pub fn timeline() -> Html {
    let timeline_data = use_state(|| TimelineData::default());
    let zoom_level = use_state(|| 1.0f64);
    let current_time = use_state(|| 0.0f64);
    let drag_over_track = use_state(|| None::<Uuid>);
    let selected_elements = use_state(|| Vec::<Uuid>::new());
    let dragging_element = use_state(|| None::<(Uuid, f64)>); // (element_id, start_offset)

    // Selection rectangle state
    let (selection_state, selection_handle) = use_selection_rectangle();
    let timeline_container_ref = use_node_ref();

    // Multi-element operations manager
    let multi_ops = use_state(|| MultiElementOperations::new());

    // Selection visual feedback
    let (selection_feedback_state, _selection_feedback_handle) = use_selection_feedback();
    let (on_element_click, on_resize_start) = create_selection_feedback_handlers(selected_elements.clone());

    // Snapping engine
    let snapping_engine = use_state(|| SnappingEngine::new());

    // Snap feedback system
    let (snap_feedback_state, snap_feedback_handle) = use_snap_feedback();
    let snap_feedback_config = use_state(|| SnapFeedbackConfig::default());

    // Ripple editing engine
    let ripple_engine = use_state(|| RippleEditingEngine::new());

    // Context menu system
    let (context_menu_state, context_menu_handle) = use_context_menu();
    let (on_context_menu, on_menu_item_click, on_menu_close) = create_context_menu_handlers(context_menu_handle.clone());

    // Animation timeline integration
    let (animation_state, animation_dispatch) = use_animation_timeline_integration();

    // Audio store integration
    let audio_state = crate::stores::audio_store::AUDIO_STORE.with(|store| store.get_state());
    let on_animation_update = {
        let timeline_data = timeline_data.clone();
        Callback::from(move |(element_id, properties): (Uuid, crate::utils::text_animation::KeyframeProperties)| {
            // Apply animated properties to timeline elements
            // This would typically update the rendering of text elements
            web_sys::console::log_1(&format!("Animation update for element {}: {:?}", element_id, properties).into());
        })
    };

    // Audio playback controls
    let on_play_pause = {
        Callback::from(move |_: MouseEvent| {
            crate::stores::audio_store::AUDIO_STORE.with(|store| {
                store.dispatch(AudioStoreAction::PlayPause);
            });
        })
    };

    let on_seek = {
        Callback::from(move |time: f64| {
            crate::stores::audio_store::AUDIO_STORE.with(|store| {
                store.dispatch(AudioStoreAction::Seek(time));
            });
        })
    };

    let on_zoom_in = {
        let zoom_level = zoom_level.clone();
        Callback::from(move |_| {
            let new_zoom = (*zoom_level * 1.2).min(10.0);
            zoom_level.set(new_zoom);
        })
    };

    let on_zoom_out = {
        let zoom_level = zoom_level.clone();
        Callback::from(move |_| {
            let new_zoom = (*zoom_level / 1.2).max(0.1);
            zoom_level.set(new_zoom);
        })
    };

    // Handle selection area changes for multi-select
    let on_selection_area_change = {
        let timeline_data = timeline_data.clone();
        let selected_elements = selected_elements.clone();
        let zoom_level = zoom_level.clone();
        Callback::from(move |selection_area: SelectionArea| {
            let mut new_selected = Vec::new();

            // Check which elements intersect with the selection area
            for element in &timeline_data.elements {
                // Calculate element position and size in pixels
                let element_left = (element.start_time / 60.0) * 100.0 * (*zoom_level);
                let element_width = (element.duration / 60.0) * 100.0 * (*zoom_level);
                let element_top = 0.0; // This would need to be calculated based on track position
                let element_height = 60.0; // Standard track height

                if selection_area.intersects_rect(element_left, element_top, element_width, element_height) {
                    new_selected.push(element.id);
                }
            }

            selected_elements.set(new_selected);
        })
    };

    // Handle adding media to timeline
    let on_add_element = {
        let timeline_data = timeline_data.clone();
        Callback::from(move |(track_id, media_data, drop_time): (Uuid, serde_json::Value, f64)| {
            let mut new_timeline = (*timeline_data).clone();

            // Parse media data from drag event
            if let (Some(_media_id), Some(_media_name), Some(media_type), Some(_media_url)) = (
                media_data["id"].as_str().map(|s| s.to_string()),
                media_data["name"].as_str().map(|s| s.to_string()),
                media_data["type"].as_str(),
                media_data["url"].as_str().map(|s| s.to_string())
            ) {
                let element_type = match media_type {
                    "video" => ElementType::Video,
                    "audio" => ElementType::Audio,
                    "image" => ElementType::Image,
                    _ => ElementType::Video,
                };

                let duration = media_data["duration"].as_f64().unwrap_or(5.0);

                let new_element = TimelineElement {
                    id: Uuid::new_v4(),
                    element_type,
                    start_time: drop_time,
                    duration,
                    track_id,
                    layer: 0,
                    locked: false,
                    properties: ElementProperties::default(),
                };

                new_timeline.elements.push(new_element);
                timeline_data.set(new_timeline);
            }
        })
    };

    // Handle element selection
    let on_element_select = {
        let selected_elements = selected_elements.clone();
        Callback::from(move |element_id: Uuid| {
            let mut new_selection = (*selected_elements).clone();
            if new_selection.contains(&element_id) {
                new_selection.retain(|&id| id != element_id);
            } else {
                new_selection.clear();
                new_selection.push(element_id);
            }
            selected_elements.set(new_selection);
        })
    };

    // Handle multi-element operations
    let execute_multi_operation = {
        let timeline_data = timeline_data.clone();
        let selected_elements = selected_elements.clone();
        let multi_ops = multi_ops.clone();
        Callback::from(move |operation: MultiElementOperation| {
            let mut new_timeline = (*timeline_data).clone();
            let selected = (*selected_elements).clone();
            let mut ops_manager = (*multi_ops).clone();

            let result = ops_manager.execute_operation(operation, &mut new_timeline, &selected);

            if result.success {
                timeline_data.set(new_timeline);
                multi_ops.set(ops_manager);

                // Clear selection for delete operations
                if matches!(result.message.as_str(), msg if msg.contains("Deleted")) {
                    selected_elements.set(Vec::new());
                }
            }

            // TODO: Show result message to user (could use a toast notification)
            web_sys::console::log_1(&format!("Operation result: {}", result.message).into());
        })
    };

    // Handle element position update with snapping
    let on_element_move = {
        let timeline_data = timeline_data.clone();
        let snapping_engine = snapping_engine.clone();
        let current_time = current_time.clone();
        Callback::from(move |(element_id, new_start_time): (Uuid, f64)| {
            let mut new_timeline = (*timeline_data).clone();
            let mut snap_engine = (*snapping_engine).clone();

            // Apply snapping to the new position
            let snap_result = snap_engine.find_snap_point(
                &new_timeline,
                new_start_time,
                *current_time,
                &[element_id], // Exclude the element being moved
            );

            let final_time = if snap_result.snapped {
                snap_result.snapped_time
            } else {
                new_start_time
            }.max(0.0);

            if let Some(element) = new_timeline.elements.iter_mut().find(|e| e.id == element_id) {
                element.start_time = final_time;
            }

            timeline_data.set(new_timeline);
            snapping_engine.set(snap_engine);
        })
    };

    // Generate ruler marks based on zoom level
    let ruler_marks = generate_ruler_marks(*zoom_level, timeline_data.duration);

    // Create selection rectangle mouse handlers
    let (on_mouse_down, on_mouse_move, on_mouse_up) = create_selection_handlers(
        &timeline_container_ref,
        selection_handle.clone(),
    );

    // Handle keyboard shortcuts for multi-element operations
    let on_key_down = {
        let execute_multi_operation = execute_multi_operation.clone();
        let selected_elements = selected_elements.clone();
        Callback::from(move |event: web_sys::KeyboardEvent| {
            let selected = (*selected_elements).clone();
            if selected.is_empty() {
                return;
            }

            match event.key().as_str() {
                "Delete" | "Backspace" => {
                    event.prevent_default();
                    execute_multi_operation.emit(MultiElementOperation::Delete);
                }
                "c" if event.ctrl_key() => {
                    event.prevent_default();
                    execute_multi_operation.emit(MultiElementOperation::Copy);
                }
                "x" if event.ctrl_key() => {
                    event.prevent_default();
                    execute_multi_operation.emit(MultiElementOperation::Cut);
                }
                "v" if event.ctrl_key() => {
                    event.prevent_default();
                    // TODO: Get current playhead time for paste position
                    execute_multi_operation.emit(MultiElementOperation::Paste {
                        start_time: 0.0,
                        track_id: None
                    });
                }
                "d" if event.ctrl_key() => {
                    event.prevent_default();
                    execute_multi_operation.emit(MultiElementOperation::Duplicate { offset: 1.0 });
                }
                _ => {}
            }
        })
    };

    html! {
        <div
            class="timeline"
            ref={timeline_container_ref.clone()}
            onmousedown={on_mouse_down}
            onmousemove={on_mouse_move}
            onmouseup={on_mouse_up}
            onkeydown={on_key_down}
            tabindex="0"
        >
            <div class="timeline-header">
                <div class="timeline-controls">
                    <div class="playback-controls">
                        <button
                            class="btn playback-btn"
                            onclick={on_play_pause.clone()}
                            title={if audio_state.playback_state.is_playing { "Pause" } else { "Play" }}
                        >
                            {if audio_state.playback_state.is_playing { "⏸" } else { "▶" }}
                        </button>
                        <button
                            class="btn playback-btn"
                            onclick={Callback::from(move |_| {
                                crate::stores::audio_store::AUDIO_STORE.with(|store| {
                                    store.dispatch(AudioStoreAction::Stop);
                                });
                            })}
                            title="Stop"
                        >
                            {"⏹"}
                        </button>
                    </div>
                    <div class="zoom-controls">
                        <button class="btn" onclick={on_zoom_in} title="Zoom In">{"+"}</button>
                        <button class="btn" onclick={on_zoom_out} title="Zoom Out">{"-"}</button>
                    </div>
                    <div class="time-display">
                        <span>{format!("{:.2}s", *current_time)}</span>
                    </div>
                    <div class="volume-controls">
                        <button
                            class="btn volume-btn"
                            onclick={Callback::from({
                                let is_muted = audio_state.is_muted;
                                move |_| {
                                    crate::stores::audio_store::AUDIO_STORE.with(|store| {
                                        store.dispatch(AudioStoreAction::SetMuted(!is_muted));
                                    });
                                }
                            })}
                            title={if audio_state.is_muted { "Unmute" } else { "Mute" }}
                        >
                            {if audio_state.is_muted { "🔇" } else { "🔊" }}
                        </button>
                        <input
                            type="range"
                            class="volume-slider"
                            min="0"
                            max="1"
                            step="0.01"
                            value={audio_state.master_volume.to_string()}
                            oninput={Callback::from(move |e: InputEvent| {
                                if let Some(input) = e.target_dyn_into::<web_sys::HtmlInputElement>() {
                                    if let Ok(volume) = input.value().parse::<f64>() {
                                        crate::stores::audio_store::AUDIO_STORE.with(|store| {
                                            store.dispatch(AudioStoreAction::SetVolume(volume));
                                        });
                                    }
                                }
                            })}
                            title="Volume"
                        />
                    </div>
                </div>
                <div class="timeline-ruler" onclick={Callback::from({
                    let timeline_duration = timeline_data.duration;
                    move |e: MouseEvent| {
                        if let Some(target) = e.target() {
                            if let Ok(element) = target.dyn_into::<web_sys::Element>() {
                                let rect = element.get_bounding_client_rect();
                                let mouse_x = e.client_x() as f64;
                                let relative_x = mouse_x - rect.left();
                                let timeline_width = rect.width();
                                let seek_time = (relative_x / timeline_width) * timeline_duration;
                                crate::stores::audio_store::AUDIO_STORE.with(|store| {
                                    store.dispatch(AudioStoreAction::Seek(seek_time.max(0.0).min(timeline_duration)));
                                });
                            }
                        }
                    }
                })}>
                    <div class="ruler-marks">
                        {for ruler_marks.iter().map(|mark| {
                            let position = (mark.time / timeline_data.duration) * 100.0;
                            html! {
                                <div
                                    class="ruler-mark"
                                    style={format!("left: {}%", position)}
                                    key={mark.time.to_string()}
                                >
                                    <span class="ruler-label">{&mark.label}</span>
                                </div>
                            }
                        })}
                        <div
                            class="playhead"
                            style={format!("left: {}%", (*current_time / timeline_data.duration) * 100.0)}
                        ></div>
                    </div>
                </div>
            </div>

            <div class="timeline-tracks">
                {for timeline_data.tracks.iter().map(|track| {
                    html! {
                        <TimelineTrack
                            track={track.clone()}
                            zoom_level={*zoom_level}
                            elements={timeline_data.elements.iter().filter(|e| e.track_id == track.id).cloned().collect::<Vec<_>>()}
                            on_add_element={on_add_element.clone()}
                            drag_over_track={drag_over_track.clone()}
                            selected_elements={selected_elements.clone()}
                            on_element_select={on_element_select.clone()}
                            on_element_move={on_element_move.clone()}
                            dragging_element={dragging_element.clone()}
                            key={track.id.to_string()}
                        />
                    }
                })}
            </div>

            // Selection rectangle overlay
            <SelectionRectangle
                is_active={selection_state.is_selecting}
                start_position={selection_state.start_position}
                current_position={selection_state.current_position}
                on_selection_change={on_selection_area_change}
            />

            // Animation timeline integration
            <AnimationTimelineIntegration
                timeline_data={(*timeline_data).clone()}
                current_time={*current_time}
                is_playing={audio_state.playback_state.is_playing}
                on_animation_update={on_animation_update}
            />

            // Selection visual feedback
            <SelectionFeedback
                selected_elements={(*selected_elements).clone()}
                timeline_elements={timeline_data.elements.clone()}
                zoom_level={*zoom_level}
                style={selection_feedback_state.style.clone()}
                on_element_click={on_element_click}
                on_resize_start={on_resize_start}
            />

            // Snap feedback system
            <SnapFeedbackSystem
                snap_results={snap_feedback_state.active_snaps.clone()}
                zoom_level={*zoom_level}
                container_width={800.0} // TODO: Get actual container width
                container_height={400.0} // TODO: Get actual container height
                config={(*snap_feedback_config).clone()}
                dragging_element={dragging_element.as_ref().map(|(id, _)| *id)}
            />

            // Context menu
            <ContextMenu
                visible={context_menu_state.visible}
                position={context_menu_state.position.clone()}
                items={context_menu_state.items.clone()}
                on_item_click={on_menu_item_click}
                on_close={on_menu_close}
            />
        </div>
    }
}

#[derive(Properties, PartialEq)]
struct TimelineTrackProps {
    track: Track,
    zoom_level: f64,
    elements: Vec<TimelineElement>,
    on_add_element: Callback<(Uuid, serde_json::Value, f64)>,
    drag_over_track: UseStateHandle<Option<Uuid>>,
    selected_elements: UseStateHandle<Vec<Uuid>>,
    on_element_select: Callback<Uuid>,
    on_element_move: Callback<(Uuid, f64)>,
    dragging_element: UseStateHandle<Option<(Uuid, f64)>>,
}

#[function_component(TimelineTrack)]
fn timeline_track(props: &TimelineTrackProps) -> Html {
    let track_icon = match props.track.track_type {
        TrackType::Video => "🎥",
        TrackType::Audio => "🎵",
        TrackType::Text => "📝",
    };

    let is_drag_over = *props.drag_over_track == Some(props.track.id);

    let on_drag_over = {
        let drag_over_track = props.drag_over_track.clone();
        let track_id = props.track.id;
        Callback::from(move |e: DragEvent| {
            e.prevent_default();
            drag_over_track.set(Some(track_id));
        })
    };

    let on_drag_leave = {
        let drag_over_track = props.drag_over_track.clone();
        Callback::from(move |_: DragEvent| {
            drag_over_track.set(None);
        })
    };

    let on_drop = {
        let on_add_element = props.on_add_element.clone();
        let drag_over_track = props.drag_over_track.clone();
        let track_id = props.track.id;
        let zoom_level = props.zoom_level;
        Callback::from(move |e: DragEvent| {
            e.prevent_default();
            drag_over_track.set(None);

            if let Some(data_transfer) = e.data_transfer() {
                if let Ok(data) = data_transfer.get_data("application/json") {
                    if let Ok(media_data) = serde_json::from_str::<serde_json::Value>(&data) {
                        // Calculate drop time based on mouse position
                        let rect = e.target().and_then(|t| t.dyn_into::<web_sys::Element>().ok())
                            .and_then(|el| Some(el.get_bounding_client_rect()));
                        let drop_time = if let Some(rect) = rect {
                            let mouse_x = e.client_x() as f64;
                            let relative_x = mouse_x - rect.left();
                            let timeline_width = rect.width();
                            // Convert pixel position to time (assuming 60s timeline)
                            (relative_x / timeline_width) * 60.0 / zoom_level
                        } else {
                            0.0
                        };
                        on_add_element.emit((track_id, media_data, drop_time));
                    }
                }
            }
        })
    };

    html! {
        <div class="track">
            <div class="track-header">
                <div class="track-controls">
                    <span class="track-icon">{track_icon}</span>
                    <span class="track-name">{&props.track.name}</span>
                    <button class="track-btn" title="Mute">
                        {if props.track.muted { "🔇" } else { "🔊" }}
                    </button>
                    <button class="track-btn" title="Lock">
                        {if props.track.locked { "🔒" } else { "🔓" }}
                    </button>
                </div>
            </div>
            <div
                class={format!("track-content {}", if is_drag_over { "drag-over" } else { "" })}
                style={format!("height: {}px", props.track.height)}
                ondragover={on_drag_over}
                ondragleave={on_drag_leave}
                ondrop={on_drop}
            >
                // Render timeline elements
                {for props.elements.iter().map(|element| {
                    let element_width = (element.duration / 60.0) * 100.0 * props.zoom_level; // Assuming 60s timeline
                    let element_left = (element.start_time / 60.0) * 100.0 * props.zoom_level;
                    let is_selected = props.selected_elements.contains(&element.id);
                    let is_dragging = props.dragging_element.as_ref().map(|(id, _)| *id == element.id).unwrap_or(false);

                    let element_id = element.id;
                    let on_element_click = {
                        let on_element_select = props.on_element_select.clone();
                        Callback::from(move |e: MouseEvent| {
                            e.stop_propagation();
                            on_element_select.emit(element_id);
                        })
                    };

                    let on_element_mouse_down = {
                        let dragging_element = props.dragging_element.clone();
                        let element_duration = element.duration;
                        Callback::from(move |e: MouseEvent| {
                            e.prevent_default();
                            // Calculate offset from element start based on mouse position
                            let rect = e.target().and_then(|t| t.dyn_into::<web_sys::Element>().ok())
                                .and_then(|el| Some(el.get_bounding_client_rect()));
                            let offset = if let Some(rect) = rect {
                                let mouse_x = e.client_x() as f64;
                                let relative_x = mouse_x - rect.left();
                                let element_width = rect.width();
                                // Convert pixel offset to time offset
                                (relative_x / element_width) * element_duration
                            } else {
                                0.0
                            };
                            dragging_element.set(Some((element_id, offset)));
                        })
                    };

                    html! {
                        <TimelineElementComponent
                            element={element.clone()}
                            element_width={element_width}
                            element_left={element_left}
                            is_selected={is_selected}
                            is_dragging={is_dragging}
                            on_click={on_element_click}
                            on_mouse_down={on_element_mouse_down}
                            key={element.id.to_string()}
                        />
                    }
                })}

                {if props.elements.is_empty() {
                    html! {
                        <div class="track-drop-zone">
                            <span class="drop-hint">{"Drop media here"}</span>
                        </div>
                    }
                } else {
                    html! {}
                }}
            </div>
        </div>
    }
}

struct RulerMark {
    time: f64,
    label: String,
}

fn generate_ruler_marks(zoom_level: f64, duration: f64) -> Vec<RulerMark> {
    let mut marks = Vec::new();
    let interval = if zoom_level > 5.0 {
        0.1 // 100ms intervals at high zoom
    } else if zoom_level > 2.0 {
        0.5 // 500ms intervals at medium zoom
    } else {
        1.0 // 1s intervals at low zoom
    };

    let mut time = 0.0;
    while time <= duration {
        marks.push(RulerMark {
            time,
            label: format!("{:.1}s", time),
        });
        time += interval;
    }

    marks
}

#[derive(Properties, PartialEq)]
struct TimelineElementProps {
    element: TimelineElement,
    element_width: f64,
    element_left: f64,
    is_selected: bool,
    is_dragging: bool,
    on_click: Callback<MouseEvent>,
    on_mouse_down: Callback<MouseEvent>,
}

#[function_component(TimelineElementComponent)]
fn timeline_element_component(props: &TimelineElementProps) -> Html {
    let element_class = format!(
        "timeline-element {}{}",
        if props.is_selected { "selected" } else { "" },
        if props.is_dragging { " dragging" } else { "" }
    );

    html! {
        <div
            class={element_class}
            style={format!("left: {}%; width: {}%; background-color: {};",
                props.element_left,
                props.element_width,
                match props.element.element_type {
                    ElementType::Video => "#4CAF50",
                    ElementType::Audio => "#2196F3",
                    ElementType::Image => "#FF9800",
                    _ => "#9E9E9E"
                }
            )}
            onclick={props.on_click.clone()}
            onmousedown={props.on_mouse_down.clone()}
        >
            <span class="element-label">
                {format!("{:.1}s", props.element.duration)}
            </span>

            // Resize handles
            {if props.is_selected {
                html! {
                    <>
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                    </>
                }
            } else {
                html! {}
            }}
        </div>
    }
}
