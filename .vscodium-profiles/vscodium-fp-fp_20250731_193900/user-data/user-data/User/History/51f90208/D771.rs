use yew::prelude::*;
use web_sys::{Drag<PERSON>vent, MouseEvent, InputEvent, HtmlCanvasElement, CanvasRenderingContext2d};
use wasm_bindgen::{JsCast, JsValue};
use uuid::Uuid;
use crate::types::timeline::{Timeline as TimelineData, Track, TrackType, TimelineElement, ElementType, ElementProperties};
use crate::stores::audio_store::AudioStoreAction;
use crate::stores::video_store::{VideoStoreAction, use_video_store};
use crate::utils::waveform_renderer::{WaveformRenderer, WaveformConfig};
use crate::components::sync_quality_indicator::SyncQualityIndicator;

mod selection_rectangle;
mod multi_element_operations;
mod selection_visual_feedback;
mod snapping_engine;
mod snap_feedback_system;
mod ripple_editing;
mod ripple_mode_toggle;
mod context_menu;
mod timeline_element_menu;
mod track_context_menu;
mod timeline_background_menu;
mod text_renderer;
mod animation_timeline_integration;

pub use selection_rectangle::{Selection<PERSON><PERSON>tangle, Selection<PERSON><PERSON>, use_selection_rectangle, create_selection_handlers};
pub use multi_element_operations::{MultiElementOperations, MultiElementOperation, OperationResult};
pub use selection_visual_feedback::{SelectionFeedback, SelectionStyle, ResizeHandle, use_selection_feedback, create_selection_feedback_handlers};
pub use snapping_engine::{SnappingEngine, SnapConfig, SnapResult, SnapPoint, SnapPointType};
pub use snap_feedback_system::{SnapFeedbackSystem, SnapFeedbackConfig, use_snap_feedback};
pub use ripple_editing::{RippleEditingEngine, RippleConfig, RippleOperation, RippleResult};
pub use ripple_mode_toggle::{RippleModeToggle, RippleShortcut, RippleModeIndicator};
pub use context_menu::{ContextMenu, ContextMenuManager, ContextMenuType, use_context_menu, create_context_menu_handlers};
pub use timeline_element_menu::{TimelineElementMenu, ElementMenuAction, TimelineElementWithMenu, use_timeline_element_menu};
pub use track_context_menu::{TrackContextMenu, TrackMenuAction, TrackWithMenu, use_track_menu};
pub use timeline_background_menu::{TimelineBackgroundMenu, TimelineBackgroundAction, TimelineWithBackgroundMenu, use_timeline_background_menu};
pub use text_renderer::{TimelineTextRenderer, TextElementPreview, use_timeline_text_renderer, create_text_element_handlers};
pub use animation_timeline_integration::{AnimationTimelineIntegration, AnimationTimelineState, use_animation_timeline_integration};

#[function_component(Timeline)]
pub fn timeline() -> Html {
    let timeline_data = use_state(|| TimelineData::default());
    let zoom_level = use_state(|| 1.0f64);
    let current_time = use_state(|| 0.0f64);
    let drag_over_track = use_state(|| None::<Uuid>);
    let selected_elements = use_state(|| Vec::<Uuid>::new());
    let dragging_element = use_state(|| None::<(Uuid, f64)>); // (element_id, start_offset)

    // Selection rectangle state
    let (selection_state, selection_handle) = use_selection_rectangle();
    let timeline_container_ref = use_node_ref();

    // Multi-element operations manager
    let multi_ops = use_state(|| MultiElementOperations::new());

    // Selection visual feedback
    let (selection_feedback_state, _selection_feedback_handle) = use_selection_feedback();
    let (on_element_click, on_resize_start) = create_selection_feedback_handlers(selected_elements.clone());

    // Snapping engine
    let snapping_engine = use_state(|| SnappingEngine::new());

    // Snap feedback system
    let (snap_feedback_state, snap_feedback_handle) = use_snap_feedback();
    let snap_feedback_config = use_state(|| SnapFeedbackConfig::default());

    // Ripple editing engine
    let ripple_engine = use_state(|| RippleEditingEngine::new());

    // Context menu system
    let (context_menu_state, context_menu_handle) = use_context_menu();
    let (on_context_menu, on_menu_item_click, on_menu_close) = create_context_menu_handlers(context_menu_handle.clone());

    // Animation timeline integration
    let (animation_state, animation_dispatch) = use_animation_timeline_integration();

    // Audio store integration
    let audio_state = crate::stores::audio_store::AUDIO_STORE.with(|store| store.get_state());

    // Video store integration
    let (video_state, video_dispatch) = use_video_store();

    // Sync communication integration
    let sync_enabled = audio_state.realtime_position_updates_enabled;
    let sync_quality = audio_state.sync_quality_score;
    let show_sync_panel = use_state(|| false);

    // Update current time from audio store if sync is enabled, or from video store
    if sync_enabled && audio_state.playback_state.current_time != *current_time {
        current_time.set(audio_state.playback_state.current_time);
    } else if video_state.playback_state.current_time != *current_time {
        current_time.set(video_state.playback_state.current_time);
    }

    let on_animation_update = {
        let timeline_data = timeline_data.clone();
        Callback::from(move |(element_id, properties): (Uuid, crate::utils::text_animation::KeyframeProperties)| {
            // Apply animated properties to timeline elements
            // This would typically update the rendering of text elements
            web_sys::console::log_1(&format!("Animation update for element {}: {:?}", element_id, properties).into());
        })
    };

    // Audio playback controls
    let on_play_pause = {
        let video_dispatch = video_dispatch.clone();
        Callback::from(move |_: MouseEvent| {
            crate::stores::audio_store::AUDIO_STORE.with(|store| {
                store.dispatch(AudioStoreAction::PlayPause);
            });
            // Also control video playback
            if video_state.playback_state.is_playing {
                video_dispatch.emit(VideoStoreAction::Pause);
            } else {
                video_dispatch.emit(VideoStoreAction::Play);
            }
        })
    };

    let on_seek = {
        let audio_state = audio_state.clone();
        let video_dispatch = video_dispatch.clone();
        Callback::from(move |time: f64| {
            let is_playing = audio_state.playback_state.is_playing;
            crate::stores::audio_store::AUDIO_STORE.with(|store| {
                store.dispatch(AudioStoreAction::SeekToPosition(time, is_playing));
            });
            // Also seek video
            video_dispatch.emit(VideoStoreAction::Seek(time));
        })
    };

    // Sync control callbacks
    let on_toggle_realtime_sync = {
        Callback::from(move |_: MouseEvent| {
            crate::stores::audio_store::AUDIO_STORE.with(|store| {
                let current_state = store.get_state();
                if current_state.realtime_position_updates_enabled {
                    store.dispatch(AudioStoreAction::DisableRealtimePositionUpdates);
                } else {
                    store.dispatch(AudioStoreAction::EnableRealtimePositionUpdates);
                }
            });
        })
    };

    let on_request_sync_update = {
        Callback::from(move |_: MouseEvent| {
            crate::stores::audio_store::AUDIO_STORE.with(|store| {
                store.dispatch(AudioStoreAction::RequestSyncQualityUpdate);
            });
        })
    };

    // Performance monitoring callbacks
    let on_toggle_performance_monitoring = {
        Callback::from(move |_: MouseEvent| {
            crate::stores::audio_store::AUDIO_STORE.with(|store| {
                let current_state = store.get_state();
                if current_state.performance_monitoring_enabled {
                    store.dispatch(AudioStoreAction::DisablePerformanceMonitoring);
                } else {
                    store.dispatch(AudioStoreAction::EnablePerformanceMonitoring);
                }
            });
        })
    };

    let on_request_performance_report = {
        Callback::from(move |_: MouseEvent| {
            crate::stores::audio_store::AUDIO_STORE.with(|store| {
                store.dispatch(AudioStoreAction::RequestPerformanceReport);
            });
        })
    };

    let on_zoom_in = {
        let zoom_level = zoom_level.clone();
        Callback::from(move |_| {
            let new_zoom = (*zoom_level * 1.2).min(10.0);
            zoom_level.set(new_zoom);
        })
    };

    let on_zoom_out = {
        let zoom_level = zoom_level.clone();
        Callback::from(move |_| {
            let new_zoom = (*zoom_level / 1.2).max(0.1);
            zoom_level.set(new_zoom);
        })
    };

    // Handle selection area changes for multi-select
    let on_selection_area_change = {
        let timeline_data = timeline_data.clone();
        let selected_elements = selected_elements.clone();
        let zoom_level = zoom_level.clone();
        Callback::from(move |selection_area: SelectionArea| {
            let mut new_selected = Vec::new();

            // Check which elements intersect with the selection area
            for element in &timeline_data.elements {
                // Calculate element position and size in pixels
                let element_left = (element.start_time / 60.0) * 100.0 * (*zoom_level);
                let element_width = (element.duration / 60.0) * 100.0 * (*zoom_level);
                let element_top = 0.0; // This would need to be calculated based on track position
                let element_height = 60.0; // Standard track height

                if selection_area.intersects_rect(element_left, element_top, element_width, element_height) {
                    new_selected.push(element.id);
                }
            }

            selected_elements.set(new_selected);
        })
    };

    // Handle adding media to timeline
    let on_add_element = {
        let timeline_data = timeline_data.clone();
        Callback::from(move |(track_id, media_data, drop_time): (Uuid, serde_json::Value, f64)| {
            let mut new_timeline = (*timeline_data).clone();

            // Parse media data from drag event
            if let (Some(_media_id), Some(_media_name), Some(media_type), Some(_media_url)) = (
                media_data["id"].as_str().map(|s| s.to_string()),
                media_data["name"].as_str().map(|s| s.to_string()),
                media_data["type"].as_str(),
                media_data["url"].as_str().map(|s| s.to_string())
            ) {
                let element_type = match media_type {
                    "video" => ElementType::Video,
                    "audio" => ElementType::Audio,
                    "image" => ElementType::Image,
                    _ => ElementType::Video,
                };

                let duration = media_data["duration"].as_f64().unwrap_or(5.0);

                let new_element = TimelineElement {
                    id: Uuid::new_v4(),
                    element_type,
                    start_time: drop_time,
                    duration,
                    track_id,
                    layer: 0,
                    locked: false,
                    properties: ElementProperties::default(),
                };

                new_timeline.elements.push(new_element);
                timeline_data.set(new_timeline);
            }
        })
    };

    // Handle element selection
    let on_element_select = {
        let selected_elements = selected_elements.clone();
        Callback::from(move |element_id: Uuid| {
            let mut new_selection = (*selected_elements).clone();
            if new_selection.contains(&element_id) {
                new_selection.retain(|&id| id != element_id);
            } else {
                new_selection.clear();
                new_selection.push(element_id);
            }
            selected_elements.set(new_selection);
        })
    };

    // Handle multi-element operations
    let execute_multi_operation = {
        let timeline_data = timeline_data.clone();
        let selected_elements = selected_elements.clone();
        let multi_ops = multi_ops.clone();
        Callback::from(move |operation: MultiElementOperation| {
            let mut new_timeline = (*timeline_data).clone();
            let selected = (*selected_elements).clone();
            let mut ops_manager = (*multi_ops).clone();

            let result = ops_manager.execute_operation(operation, &mut new_timeline, &selected);

            if result.success {
                timeline_data.set(new_timeline);
                multi_ops.set(ops_manager);

                // Clear selection for delete operations
                if matches!(result.message.as_str(), msg if msg.contains("Deleted")) {
                    selected_elements.set(Vec::new());
                }
            }

            // TODO: Show result message to user (could use a toast notification)
            web_sys::console::log_1(&format!("Operation result: {}", result.message).into());
        })
    };

    // Handle element position update with snapping
    let on_element_move = {
        let timeline_data = timeline_data.clone();
        let snapping_engine = snapping_engine.clone();
        let current_time = current_time.clone();
        Callback::from(move |(element_id, new_start_time): (Uuid, f64)| {
            let mut new_timeline = (*timeline_data).clone();
            let mut snap_engine = (*snapping_engine).clone();

            // Apply snapping to the new position
            let snap_result = snap_engine.find_snap_point(
                &new_timeline,
                new_start_time,
                *current_time,
                &[element_id], // Exclude the element being moved
            );

            let final_time = if snap_result.snapped {
                snap_result.snapped_time
            } else {
                new_start_time
            }.max(0.0);

            if let Some(element) = new_timeline.elements.iter_mut().find(|e| e.id == element_id) {
                element.start_time = final_time;
            }

            timeline_data.set(new_timeline);
            snapping_engine.set(snap_engine);
        })
    };

    // Generate ruler marks based on zoom level
    let ruler_marks = generate_ruler_marks(*zoom_level, timeline_data.duration);

    // Create selection rectangle mouse handlers
    let (on_mouse_down, on_mouse_move, on_mouse_up) = create_selection_handlers(
        &timeline_container_ref,
        selection_handle.clone(),
    );

    // Handle keyboard shortcuts for multi-element operations
    let on_key_down = {
        let execute_multi_operation = execute_multi_operation.clone();
        let selected_elements = selected_elements.clone();
        Callback::from(move |event: web_sys::KeyboardEvent| {
            let selected = (*selected_elements).clone();
            if selected.is_empty() {
                return;
            }

            match event.key().as_str() {
                "Delete" | "Backspace" => {
                    event.prevent_default();
                    execute_multi_operation.emit(MultiElementOperation::Delete);
                }
                "c" if event.ctrl_key() => {
                    event.prevent_default();
                    execute_multi_operation.emit(MultiElementOperation::Copy);
                }
                "x" if event.ctrl_key() => {
                    event.prevent_default();
                    execute_multi_operation.emit(MultiElementOperation::Cut);
                }
                "v" if event.ctrl_key() => {
                    event.prevent_default();
                    // TODO: Get current playhead time for paste position
                    execute_multi_operation.emit(MultiElementOperation::Paste {
                        start_time: 0.0,
                        track_id: None
                    });
                }
                "d" if event.ctrl_key() => {
                    event.prevent_default();
                    execute_multi_operation.emit(MultiElementOperation::Duplicate { offset: 1.0 });
                }
                _ => {}
            }
        })
    };

    html! {
        <div
            class="timeline"
            ref={timeline_container_ref.clone()}
            onmousedown={on_mouse_down}
            onmousemove={on_mouse_move}
            onmouseup={on_mouse_up}
            onkeydown={on_key_down}
            tabindex="0"
        >
            <div class="timeline-header">
                <div class="timeline-controls">
                    <div class="playback-controls">
                        <button
                            class="btn playback-btn"
                            onclick={on_play_pause.clone()}
                            title={if audio_state.playback_state.is_playing || video_state.playback_state.is_playing { "Pause" } else { "Play" }}
                        >
                            {if audio_state.playback_state.is_playing || video_state.playback_state.is_playing { "⏸" } else { "▶" }}
                        </button>
                        <button
                            class="btn playback-btn"
                            onclick={Callback::from({
                                let video_dispatch = video_dispatch.clone();
                                move |_| {
                                    crate::stores::audio_store::AUDIO_STORE.with(|store| {
                                        store.dispatch(AudioStoreAction::Stop);
                                    });
                                    video_dispatch.emit(VideoStoreAction::Stop);
                                }
                            })}
                            title="Stop"
                        >
                            {"⏹"}
                        </button>
                    </div>
                    <div class="zoom-controls">
                        <button class="btn" onclick={on_zoom_in} title="Zoom In">{"+"}</button>
                        <button class="btn" onclick={on_zoom_out} title="Zoom Out">{"-"}</button>
                    </div>
                    <div class="time-display">
                        <span>{format!("{:.2}s", *current_time)}</span>
                    </div>
                    <div class="sync-quality-container">
                        <SyncQualityIndicator compact_mode={true} />
                        <button
                            class={if *show_sync_panel { "btn sync-panel-toggle active" } else { "btn sync-panel-toggle" }}
                            onclick={Callback::from({
                                let show_sync_panel = show_sync_panel.clone();
                                move |_| {
                                    show_sync_panel.set(!*show_sync_panel);
                                }
                            })}
                            title="Toggle sync quality panel"
                        >
                            {"📊"}
                        </button>
                    </div>
                    <div class="volume-controls">
                        <span class="volume-label">{"Audio:"}</span>
                        <button
                            class="btn volume-btn"
                            onclick={Callback::from({
                                let is_muted = audio_state.is_muted;
                                move |_| {
                                    crate::stores::audio_store::AUDIO_STORE.with(|store| {
                                        store.dispatch(AudioStoreAction::SetMuted(!is_muted));
                                    });
                                }
                            })}
                            title={if audio_state.is_muted { "Unmute Audio" } else { "Mute Audio" }}
                        >
                            {if audio_state.is_muted { "🔇" } else { "🔊" }}
                        </button>
                        <input
                            type="range"
                            class="volume-slider"
                            min="0"
                            max="1"
                            step="0.01"
                            value={audio_state.master_volume.to_string()}
                            oninput={Callback::from(move |e: InputEvent| {
                                if let Some(input) = e.target_dyn_into::<web_sys::HtmlInputElement>() {
                                    if let Ok(volume) = input.value().parse::<f64>() {
                                        crate::stores::audio_store::AUDIO_STORE.with(|store| {
                                            store.dispatch(AudioStoreAction::SetVolume(volume));
                                        });
                                    }
                                }
                            })}
                            title="Audio Volume"
                        />

                        <span class="volume-label">{"Video:"}</span>
                        <button
                            class="btn volume-btn"
                            onclick={Callback::from({
                                let video_dispatch = video_dispatch.clone();
                                let is_muted = video_state.playback_state.is_muted;
                                move |_| {
                                    video_dispatch.emit(VideoStoreAction::SetMuted(!is_muted));
                                }
                            })}
                            title={if video_state.playback_state.is_muted { "Unmute Video" } else { "Mute Video" }}
                        >
                            {if video_state.playback_state.is_muted { "🔇" } else { "🔊" }}
                        </button>
                        <input
                            type="range"
                            class="volume-slider"
                            min="0"
                            max="1"
                            step="0.01"
                            value={video_state.playback_state.volume.to_string()}
                            oninput={Callback::from({
                                let video_dispatch = video_dispatch.clone();
                                move |e: InputEvent| {
                                    if let Some(input) = e.target_dyn_into::<web_sys::HtmlInputElement>() {
                                        if let Ok(volume) = input.value().parse::<f64>() {
                                            video_dispatch.emit(VideoStoreAction::SetVolume(volume));
                                        }
                                    }
                                }
                            })}
                            title="Video Volume"
                        />
                    </div>
                </div>
                <div class="timeline-ruler" onclick={Callback::from({
                    let timeline_duration = timeline_data.duration;
                    move |e: MouseEvent| {
                        if let Some(target) = e.target() {
                            if let Ok(element) = target.dyn_into::<web_sys::Element>() {
                                let rect = element.get_bounding_client_rect();
                                let mouse_x = e.client_x() as f64;
                                let relative_x = mouse_x - rect.left();
                                let timeline_width = rect.width();
                                let seek_time = (relative_x / timeline_width) * timeline_duration;
                                crate::stores::audio_store::AUDIO_STORE.with(|store| {
                                    store.dispatch(AudioStoreAction::Seek(seek_time.max(0.0).min(timeline_duration)));
                                });
                            }
                        }
                    }
                })}>
                    <div class="ruler-marks">
                        {for ruler_marks.iter().map(|mark| {
                            let position = (mark.time / timeline_data.duration) * 100.0;
                            html! {
                                <div
                                    class="ruler-mark"
                                    style={format!("left: {}%", position)}
                                    key={mark.time.to_string()}
                                >
                                    <span class="ruler-label">{&mark.label}</span>
                                </div>
                            }
                        })}
                        <div
                            class="playhead"
                            style={format!("left: {}%", (*current_time / timeline_data.duration) * 100.0)}
                        ></div>
                    </div>
                </div>
            </div>

            {if *show_sync_panel {
                html! {
                    <div class="sync-quality-panel-container">
                        <SyncQualityIndicator show_detailed={true} compact_mode={false} />
                    </div>
                }
            } else {
                html! {}
            }}

            <div class="timeline-tracks">
                {for timeline_data.tracks.iter().map(|track| {
                    html! {
                        <TimelineTrack
                            track={track.clone()}
                            zoom_level={*zoom_level}
                            elements={timeline_data.elements.iter().filter(|e| e.track_id == track.id).cloned().collect::<Vec<_>>()}
                            on_add_element={on_add_element.clone()}
                            drag_over_track={drag_over_track.clone()}
                            selected_elements={selected_elements.clone()}
                            on_element_select={on_element_select.clone()}
                            on_element_move={on_element_move.clone()}
                            dragging_element={dragging_element.clone()}
                            key={track.id.to_string()}
                        />
                    }
                })}
            </div>

            // Selection rectangle overlay
            <SelectionRectangle
                is_active={selection_state.is_selecting}
                start_position={selection_state.start_position}
                current_position={selection_state.current_position}
                on_selection_change={on_selection_area_change}
            />

            // Animation timeline integration
            <AnimationTimelineIntegration
                timeline_data={(*timeline_data).clone()}
                current_time={*current_time}
                is_playing={audio_state.playback_state.is_playing}
                on_animation_update={on_animation_update}
            />

            // Selection visual feedback
            <SelectionFeedback
                selected_elements={(*selected_elements).clone()}
                timeline_elements={timeline_data.elements.clone()}
                zoom_level={*zoom_level}
                style={selection_feedback_state.style.clone()}
                on_element_click={on_element_click}
                on_resize_start={on_resize_start}
            />

            // Snap feedback system
            <SnapFeedbackSystem
                snap_results={snap_feedback_state.active_snaps.clone()}
                zoom_level={*zoom_level}
                container_width={800.0} // TODO: Get actual container width
                container_height={400.0} // TODO: Get actual container height
                config={(*snap_feedback_config).clone()}
                dragging_element={dragging_element.as_ref().map(|(id, _)| *id)}
            />

            // Context menu
            <ContextMenu
                visible={context_menu_state.visible}
                position={context_menu_state.position.clone()}
                items={context_menu_state.items.clone()}
                on_item_click={on_menu_item_click}
                on_close={on_menu_close}
            />
        </div>
    }
}

#[derive(Properties, PartialEq)]
struct TimelineTrackProps {
    track: Track,
    zoom_level: f64,
    elements: Vec<TimelineElement>,
    on_add_element: Callback<(Uuid, serde_json::Value, f64)>,
    drag_over_track: UseStateHandle<Option<Uuid>>,
    selected_elements: UseStateHandle<Vec<Uuid>>,
    on_element_select: Callback<Uuid>,
    on_element_move: Callback<(Uuid, f64)>,
    dragging_element: UseStateHandle<Option<(Uuid, f64)>>,
}

#[function_component(TimelineTrack)]
fn timeline_track(props: &TimelineTrackProps) -> Html {
    let track_icon = match props.track.track_type {
        TrackType::Video => "🎥",
        TrackType::Audio => "🎵",
        TrackType::Text => "📝",
    };

    let is_drag_over = *props.drag_over_track == Some(props.track.id);

    let on_drag_over = {
        let drag_over_track = props.drag_over_track.clone();
        let track_id = props.track.id;
        Callback::from(move |e: DragEvent| {
            e.prevent_default();
            drag_over_track.set(Some(track_id));
        })
    };

    let on_drag_leave = {
        let drag_over_track = props.drag_over_track.clone();
        Callback::from(move |_: DragEvent| {
            drag_over_track.set(None);
        })
    };

    let on_drop = {
        let on_add_element = props.on_add_element.clone();
        let drag_over_track = props.drag_over_track.clone();
        let track_id = props.track.id;
        let zoom_level = props.zoom_level;
        Callback::from(move |e: DragEvent| {
            e.prevent_default();
            drag_over_track.set(None);

            if let Some(data_transfer) = e.data_transfer() {
                if let Ok(data) = data_transfer.get_data("application/json") {
                    if let Ok(media_data) = serde_json::from_str::<serde_json::Value>(&data) {
                        // Calculate drop time based on mouse position
                        let rect = e.target().and_then(|t| t.dyn_into::<web_sys::Element>().ok())
                            .and_then(|el| Some(el.get_bounding_client_rect()));
                        let drop_time = if let Some(rect) = rect {
                            let mouse_x = e.client_x() as f64;
                            let relative_x = mouse_x - rect.left();
                            let timeline_width = rect.width();
                            // Convert pixel position to time (assuming 60s timeline)
                            (relative_x / timeline_width) * 60.0 / zoom_level
                        } else {
                            0.0
                        };
                        on_add_element.emit((track_id, media_data, drop_time));
                    }
                }
            }
        })
    };

    html! {
        <div class="track">
            <div class="track-header">
                <div class="track-controls">
                    <span class="track-icon">{track_icon}</span>
                    <span class="track-name">{&props.track.name}</span>
                    <button class="track-btn" title="Mute">
                        {if props.track.muted { "🔇" } else { "🔊" }}
                    </button>
                    <button class="track-btn" title="Lock">
                        {if props.track.locked { "🔒" } else { "🔓" }}
                    </button>
                </div>
            </div>
            <div
                class={format!("track-content {}", if is_drag_over { "drag-over" } else { "" })}
                style={format!("height: {}px", props.track.height)}
                ondragover={on_drag_over}
                ondragleave={on_drag_leave}
                ondrop={on_drop}
            >
                // Render timeline elements
                {for props.elements.iter().map(|element| {
                    let element_width = (element.duration / 60.0) * 100.0 * props.zoom_level; // Assuming 60s timeline
                    let element_left = (element.start_time / 60.0) * 100.0 * props.zoom_level;
                    let is_selected = props.selected_elements.contains(&element.id);
                    let is_dragging = props.dragging_element.as_ref().map(|(id, _)| *id == element.id).unwrap_or(false);

                    let element_id = element.id;
                    let on_element_click = {
                        let on_element_select = props.on_element_select.clone();
                        Callback::from(move |e: MouseEvent| {
                            e.stop_propagation();
                            on_element_select.emit(element_id);
                        })
                    };

                    let on_element_mouse_down = {
                        let dragging_element = props.dragging_element.clone();
                        let element_duration = element.duration;
                        Callback::from(move |e: MouseEvent| {
                            e.prevent_default();
                            // Calculate offset from element start based on mouse position
                            let rect = e.target().and_then(|t| t.dyn_into::<web_sys::Element>().ok())
                                .and_then(|el| Some(el.get_bounding_client_rect()));
                            let offset = if let Some(rect) = rect {
                                let mouse_x = e.client_x() as f64;
                                let relative_x = mouse_x - rect.left();
                                let element_width = rect.width();
                                // Convert pixel offset to time offset
                                (relative_x / element_width) * element_duration
                            } else {
                                0.0
                            };
                            dragging_element.set(Some((element_id, offset)));
                        })
                    };

                    html! {
                        <TimelineElementComponent
                            element={element.clone()}
                            element_width={element_width}
                            element_left={element_left}
                            is_selected={is_selected}
                            is_dragging={is_dragging}
                            on_click={on_element_click}
                            on_mouse_down={on_element_mouse_down}
                            key={element.id.to_string()}
                        />
                    }
                })}

                {if props.elements.is_empty() {
                    html! {
                        <div class="track-drop-zone">
                            <span class="drop-hint">{"Drop media here"}</span>
                        </div>
                    }
                } else {
                    html! {}
                }}
            </div>
        </div>
    }
}

struct RulerMark {
    time: f64,
    label: String,
}

fn generate_ruler_marks(zoom_level: f64, duration: f64) -> Vec<RulerMark> {
    let mut marks = Vec::new();
    let interval = if zoom_level > 5.0 {
        0.1 // 100ms intervals at high zoom
    } else if zoom_level > 2.0 {
        0.5 // 500ms intervals at medium zoom
    } else {
        1.0 // 1s intervals at low zoom
    };

    let mut time = 0.0;
    while time <= duration {
        marks.push(RulerMark {
            time,
            label: format!("{:.1}s", time),
        });
        time += interval;
    }

    marks
}

#[derive(Properties, PartialEq)]
struct TimelineElementProps {
    element: TimelineElement,
    element_width: f64,
    element_left: f64,
    is_selected: bool,
    is_dragging: bool,
    on_click: Callback<MouseEvent>,
    on_mouse_down: Callback<MouseEvent>,
}

#[function_component(TimelineElementComponent)]
fn timeline_element_component(props: &TimelineElementProps) -> Html {
    let element_class = format!(
        "timeline-element {}{}",
        if props.is_selected { "selected" } else { "" },
        if props.is_dragging { " dragging" } else { "" }
    );

    html! {
        <div
            class={element_class}
            style={format!("left: {}%; width: {}%; background-color: {};",
                props.element_left,
                props.element_width,
                match props.element.element_type {
                    ElementType::Video => "#4CAF50",
                    ElementType::Audio => "#2196F3",
                    ElementType::Image => "#FF9800",
                    _ => "#9E9E9E"
                }
            )}
            onclick={props.on_click.clone()}
            onmousedown={props.on_mouse_down.clone()}
        >
            <span class="element-label">
                {format!("{:.1}s", props.element.duration)}
            </span>

            // Add waveform visualization for audio elements
            {if props.element.element_type == ElementType::Audio {
                html! {
                    <AudioElementWaveform
                        element_id={props.element.id}
                        width={(props.element_width * 8.0) as u32} // Scale for detail
                        height={40} // Fixed height for waveform
                        start_time={props.element.start_time}
                        duration={props.element.duration}
                    />
                }
            } else {
                html! {}
            }}

            // Resize handles
            {if props.is_selected {
                html! {
                    <>
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                    </>
                }
            } else {
                html! {}
            }}
        </div>
    }
}

#[derive(Properties, PartialEq)]
struct AudioElementWaveformProps {
    element_id: Uuid,
    width: u32,
    height: u32,
    start_time: f64,
    duration: f64,
}

#[function_component(AudioElementWaveform)]
fn audio_element_waveform(props: &AudioElementWaveformProps) -> Html {
    let canvas_ref = use_node_ref();
    let waveform_data = use_state(|| Vec::<f32>::new());

    // Effect to render waveform when component mounts or props change
    {
        let canvas_ref = canvas_ref.clone();
        let waveform_data = waveform_data.clone();
        let element_id = props.element_id;
        let width = props.width;
        let height = props.height;
        let duration = props.duration;

        use_effect_with(
            (element_id, width, height, duration),
            move |_| {
                if let Some(canvas) = canvas_ref.cast::<HtmlCanvasElement>() {
                    canvas.set_width(width);
                    canvas.set_height(height);

                    if let Ok(context) = canvas
                        .get_context("2d")
                        .unwrap()
                        .unwrap()
                        .dyn_into::<CanvasRenderingContext2d>()
                    {
                        // Generate sample waveform data (in a real implementation, this would come from audio analysis)
                        let samples = generate_sample_waveform_data(duration, width as usize);
                        waveform_data.set(samples.clone());

                        // Render the waveform
                        render_waveform(&context, &samples, width, height);
                    }
                }
                || ()
            },
        );
    }

    html! {
        <canvas
            ref={canvas_ref}
            class="audio-waveform"
            style="position: absolute; top: 20px; left: 2px; pointer-events: none; opacity: 0.7;"
        />
    }
}

fn generate_sample_waveform_data(duration: f64, width: usize) -> Vec<f32> {
    // Generate sample waveform data - in a real implementation, this would be extracted from audio
    let mut samples = Vec::with_capacity(width);
    for i in 0..width {
        let t = (i as f64) / (width as f64) * duration;
        // Create a sample waveform with some variation
        let amplitude = (t * 2.0).sin() * 0.5 + (t * 5.0).sin() * 0.3 + (t * 10.0).sin() * 0.2;
        samples.push(amplitude.abs() as f32);
    }
    samples
}

fn render_waveform(context: &CanvasRenderingContext2d, samples: &[f32], width: u32, height: u32) {
    // Clear canvas
    context.clear_rect(0.0, 0.0, width as f64, height as f64);

    // Set waveform style
    context.set_stroke_style(&JsValue::from_str("#4CAF50"));
    context.set_line_width(1.0);

    // Draw waveform
    context.begin_path();
    let center_y = height as f64 / 2.0;

    for (i, &sample) in samples.iter().enumerate() {
        let x = i as f64;
        let y = center_y - (sample as f64 * center_y * 0.8);

        if i == 0 {
            context.move_to(x, y);
        } else {
            context.line_to(x, y);
        }
    }

    context.stroke();

    // Draw mirrored waveform below center
    context.begin_path();
    for (i, &sample) in samples.iter().enumerate() {
        let x = i as f64;
        let y = center_y + (sample as f64 * center_y * 0.8);

        if i == 0 {
            context.move_to(x, y);
        } else {
            context.line_to(x, y);
        }
    }

    context.stroke();
}
