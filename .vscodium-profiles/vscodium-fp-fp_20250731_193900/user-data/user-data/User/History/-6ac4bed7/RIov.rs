use yew::prelude::*;
use std::rc::Rc;
use std::cell::RefCell;
use serde::{Serialize, Deserialize};
use uuid::Uuid;
use web_sys::File;

use crate::utils::video_decoder::{VideoDecoder, VideoMetadata, VideoDecodingOptions, DecodedFrame};
use crate::utils::video_processing::{VideoProcessor, VideoProcessingOptions, ThumbnailOptions};
use crate::types::timeline::{Timeline, TimelineElement};

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct VideoPlaybackState {
    pub is_playing: bool,
    pub current_time: f64,
    pub duration: f64,
    pub playback_rate: f64,
    pub volume: f64,
    pub is_muted: bool,
    pub is_seeking: bool,
    pub buffered_ranges: Vec<(f64, f64)>,
}

impl Default for VideoPlaybackState {
    fn default() -> Self {
        Self {
            is_playing: false,
            current_time: 0.0,
            duration: 0.0,
            playback_rate: 1.0,
            volume: 1.0,
            is_muted: false,
            is_seeking: false,
            buffered_ranges: Vec::new(),
        }
    }
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct VideoElement {
    pub id: Uuid,
    pub file_name: String,
    pub metadata: Option<VideoMetadata>,
    pub is_loaded: bool,
    pub is_processing: bool,
    pub thumbnail_url: Option<String>,
    pub error_message: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct VideoStoreState {
    pub playback_state: VideoPlaybackState,
    pub current_timeline: Option<Timeline>,
    pub loaded_video_elements: std::collections::HashMap<Uuid, VideoElement>,
    pub active_video_element: Option<Uuid>,
    pub decoder_initialized: bool,
    pub processor_initialized: bool,
    pub is_initializing: bool,
    pub error_message: Option<String>,
    pub decoding_options: VideoDecodingOptions,
    pub processing_options: VideoProcessingOptions,
    #[serde(skip)]
    pub current_frame: Option<DecodedFrame>,
    #[serde(skip)]
    pub frame_cache: std::collections::HashMap<Uuid, Vec<DecodedFrame>>,
    pub thumbnail_cache: std::collections::HashMap<Uuid, String>,
}

impl Default for VideoStoreState {
    fn default() -> Self {
        Self {
            playback_state: VideoPlaybackState::default(),
            current_timeline: None,
            loaded_video_elements: std::collections::HashMap::new(),
            active_video_element: None,
            decoder_initialized: false,
            processor_initialized: false,
            is_initializing: false,
            error_message: None,
            decoding_options: VideoDecodingOptions::default(),
            processing_options: VideoProcessingOptions::default(),
            current_frame: None,
            frame_cache: std::collections::HashMap::new(),
            thumbnail_cache: std::collections::HashMap::new(),
        }
    }
}

#[derive(Debug, Clone)]
pub enum VideoStoreAction {
    InitializeVideoSystem,
    VideoSystemInitialized,
    VideoSystemError(String),
    LoadVideoFile(Uuid, File),
    VideoFileLoaded(Uuid, VideoMetadata),
    VideoFileError(Uuid, String),
    UnloadVideoElement(Uuid),
    SetActiveVideoElement(Option<Uuid>),
    Play,
    Pause,
    Stop,
    Seek(f64),
    SetVolume(f64),
    SetMuted(bool),
    SetPlaybackRate(f64),
    UpdatePlaybackState(VideoPlaybackState),
    LoadTimeline(Timeline),
    DecodeFrameAtTime(Uuid, f64),
    FrameDecoded(Uuid, DecodedFrame),
    DecodeFrameSequence(Uuid, f64, f64, f64), // element_id, start_time, end_time, frame_rate
    FrameSequenceDecoded(Uuid, Vec<DecodedFrame>),
    GenerateThumbnail(Uuid, ThumbnailOptions),
    ThumbnailGenerated(Uuid, String), // element_id, thumbnail_url
    SetDecodingOptions(VideoDecodingOptions),
    SetProcessingOptions(VideoProcessingOptions),
    ClearFrameCache(Option<Uuid>), // None clears all, Some(id) clears specific element
    ClearThumbnailCache(Option<Uuid>),
}



pub struct VideoStore {
    state: Rc<RefCell<VideoStoreState>>,
    video_decoder: Rc<RefCell<Option<VideoDecoder>>>,
    video_processor: Rc<RefCell<Option<VideoProcessor>>>,
    listeners: Rc<RefCell<Vec<Callback<VideoStoreState>>>>,
}

impl VideoStore {
    pub fn new() -> Self {
        Self {
            state: Rc::new(RefCell::new(VideoStoreState::default())),
            video_decoder: Rc::new(RefCell::new(None)),
            video_processor: Rc::new(RefCell::new(None)),
            listeners: Rc::new(RefCell::new(Vec::new())),
        }
    }

    pub fn get_state(&self) -> VideoStoreState {
        self.state.borrow().clone()
    }

    pub fn subscribe(&self, callback: Callback<VideoStoreState>) {
        self.listeners.borrow_mut().push(callback);
    }

    fn notify_listeners(&self) {
        let state = self.get_state();
        for listener in self.listeners.borrow().iter() {
            listener.emit(state.clone());
        }
    }

    pub fn dispatch(&self, action: VideoStoreAction) {
        match action {
            VideoStoreAction::InitializeVideoSystem => {
                self.initialize_video_system();
            }
            VideoStoreAction::VideoSystemInitialized => {
                let mut state = self.state.borrow_mut();
                state.decoder_initialized = true;
                state.processor_initialized = true;
                state.is_initializing = false;
                state.error_message = None;
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::VideoSystemError(error) => {
                let mut state = self.state.borrow_mut();
                state.is_initializing = false;
                state.error_message = Some(error);
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::LoadVideoFile(element_id, file) => {
                self.load_video_file(element_id, file);
            }
            VideoStoreAction::VideoFileLoaded(element_id, metadata) => {
                let mut state = self.state.borrow_mut();
                if let Some(element) = state.loaded_video_elements.get_mut(&element_id) {
                    element.metadata = Some(metadata.clone());
                    element.is_loaded = true;
                    element.is_processing = false;
                    element.error_message = None;
                    
                    // Update playback duration if this is the active element
                    if state.active_video_element == Some(element_id) {
                        state.playback_state.duration = metadata.duration;
                    }
                }
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::VideoFileError(element_id, error) => {
                let mut state = self.state.borrow_mut();
                if let Some(element) = state.loaded_video_elements.get_mut(&element_id) {
                    element.is_processing = false;
                    element.error_message = Some(error);
                }
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::UnloadVideoElement(element_id) => {
                self.unload_video_element(element_id);
            }
            VideoStoreAction::SetActiveVideoElement(element_id) => {
                let mut state = self.state.borrow_mut();
                state.active_video_element = element_id;
                
                // Update playback duration based on active element
                if let Some(id) = element_id {
                    if let Some(element) = state.loaded_video_elements.get(&id) {
                        if let Some(metadata) = &element.metadata {
                            state.playback_state.duration = metadata.duration;
                        }
                    }
                } else {
                    state.playback_state.duration = 0.0;
                }
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::Play => {
                let mut state = self.state.borrow_mut();
                state.playback_state.is_playing = true;
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::Pause => {
                let mut state = self.state.borrow_mut();
                state.playback_state.is_playing = false;
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::Stop => {
                let mut state = self.state.borrow_mut();
                state.playback_state.is_playing = false;
                state.playback_state.current_time = 0.0;
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::Seek(time) => {
                let mut state = self.state.borrow_mut();
                state.playback_state.current_time = time.max(0.0).min(state.playback_state.duration);
                state.playback_state.is_seeking = true;
                drop(state);
                self.notify_listeners();
                
                // Decode frame at new position if we have an active element
                if let Some(element_id) = self.get_state().active_video_element {
                    self.dispatch(VideoStoreAction::DecodeFrameAtTime(element_id, time));
                }
            }
            VideoStoreAction::SetVolume(volume) => {
                let mut state = self.state.borrow_mut();
                state.playback_state.volume = volume.clamp(0.0, 1.0);
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::SetMuted(muted) => {
                let mut state = self.state.borrow_mut();
                state.playback_state.is_muted = muted;
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::SetPlaybackRate(rate) => {
                let mut state = self.state.borrow_mut();
                state.playback_state.playback_rate = rate.max(0.1).min(4.0);
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::UpdatePlaybackState(playback_state) => {
                let mut state = self.state.borrow_mut();
                state.playback_state = playback_state;
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::LoadTimeline(timeline) => {
                let mut state = self.state.borrow_mut();
                state.current_timeline = Some(timeline);
                drop(state);
                self.notify_listeners();
            }
            _ => {
                // Handle other actions in the next part
                self.handle_advanced_actions(action);
            }
        }
    }

    fn handle_advanced_actions(&self, action: VideoStoreAction) {
        match action {
            VideoStoreAction::DecodeFrameAtTime(element_id, timestamp) => {
                self.decode_frame_at_time(element_id, timestamp);
            }
            VideoStoreAction::FrameDecoded(element_id, frame) => {
                let mut state = self.state.borrow_mut();
                state.current_frame = Some(frame);
                state.playback_state.is_seeking = false;
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::DecodeFrameSequence(element_id, start_time, end_time, frame_rate) => {
                self.decode_frame_sequence(element_id, start_time, end_time, frame_rate);
            }
            VideoStoreAction::FrameSequenceDecoded(element_id, frames) => {
                let mut state = self.state.borrow_mut();
                state.frame_cache.insert(element_id, frames);
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::GenerateThumbnail(element_id, options) => {
                self.generate_thumbnail(element_id, options);
            }
            VideoStoreAction::ThumbnailGenerated(element_id, thumbnail_url) => {
                let mut state = self.state.borrow_mut();
                state.thumbnail_cache.insert(element_id, thumbnail_url.clone());
                if let Some(element) = state.loaded_video_elements.get_mut(&element_id) {
                    element.thumbnail_url = Some(thumbnail_url);
                }
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::SetDecodingOptions(options) => {
                let mut state = self.state.borrow_mut();
                state.decoding_options = options;
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::SetProcessingOptions(options) => {
                let mut state = self.state.borrow_mut();
                state.processing_options = options;
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::ClearFrameCache(element_id) => {
                let mut state = self.state.borrow_mut();
                if let Some(id) = element_id {
                    state.frame_cache.remove(&id);
                } else {
                    state.frame_cache.clear();
                }
                drop(state);
                self.notify_listeners();
            }
            VideoStoreAction::ClearThumbnailCache(element_id) => {
                let mut state = self.state.borrow_mut();
                if let Some(id) = element_id {
                    state.thumbnail_cache.remove(&id);
                } else {
                    state.thumbnail_cache.clear();
                }
                drop(state);
                self.notify_listeners();
            }
            _ => {
                // Action already handled in main dispatch
            }
        }
    }

    fn initialize_video_system(&self) {
        let mut state = self.state.borrow_mut();
        state.is_initializing = true;
        state.error_message = None;
        drop(state);
        self.notify_listeners();

        // Initialize video decoder
        let mut decoder = VideoDecoder::new();

        // Initialize video processor
        let processor = VideoProcessor::new();

        // Store the instances
        *self.video_decoder.borrow_mut() = Some(decoder.clone());
        *self.video_processor.borrow_mut() = Some(processor);

        // Initialize decoder asynchronously
        let store_clone = self.clone();
        wasm_bindgen_futures::spawn_local(async move {
            match decoder.initialize().await {
                Ok(_) => {
                    store_clone.dispatch(VideoStoreAction::VideoSystemInitialized);
                }
                Err(e) => {
                    let error_msg = format!("Failed to initialize video decoder: {:?}", e);
                    store_clone.dispatch(VideoStoreAction::VideoSystemError(error_msg));
                }
            }
        });
    }

    fn load_video_file(&self, element_id: Uuid, file: File) {
        let file_name = file.name();

        // Add element to state
        let mut state = self.state.borrow_mut();
        let element = VideoElement {
            id: element_id,
            file_name: file_name.clone(),
            metadata: None,
            is_loaded: false,
            is_processing: true,
            thumbnail_url: None,
            error_message: None,
        };
        state.loaded_video_elements.insert(element_id, element);
        drop(state);
        self.notify_listeners();

        // Load video asynchronously
        let decoder_opt = self.video_decoder.borrow().clone();
        if let Some(mut decoder) = decoder_opt {
            let store_clone = self.clone();
            wasm_bindgen_futures::spawn_local(async move {
                match decoder.load_video(element_id, &file).await {
                    Ok(metadata) => {
                        store_clone.dispatch(VideoStoreAction::VideoFileLoaded(element_id, metadata));
                    }
                    Err(e) => {
                        let error_msg = format!("Failed to load video file: {:?}", e);
                        store_clone.dispatch(VideoStoreAction::VideoFileError(element_id, error_msg));
                    }
                }
            });
        } else {
            self.dispatch(VideoStoreAction::VideoFileError(
                element_id,
                "Video decoder not initialized".to_string(),
            ));
        }
    }

    fn unload_video_element(&self, element_id: Uuid) {
        // Remove from decoder
        if let Some(decoder) = self.video_decoder.borrow_mut().as_mut() {
            decoder.remove_video(&element_id);
        }

        // Remove from state
        let mut state = self.state.borrow_mut();
        state.loaded_video_elements.remove(&element_id);
        state.frame_cache.remove(&element_id);
        state.thumbnail_cache.remove(&element_id);

        // Clear active element if it was the unloaded one
        if state.active_video_element == Some(element_id) {
            state.active_video_element = None;
            state.playback_state.duration = 0.0;
            state.current_frame = None;
        }
        drop(state);
        self.notify_listeners();
    }

    fn decode_frame_at_time(&self, element_id: Uuid, timestamp: f64) {
        let decoder_opt = self.video_decoder.borrow().clone();
        if let Some(mut decoder) = decoder_opt {
            let store_clone = self.clone();
            wasm_bindgen_futures::spawn_local(async move {
                match decoder.decode_frame_at_time(&element_id, timestamp).await {
                    Ok(frame) => {
                        store_clone.dispatch(VideoStoreAction::FrameDecoded(element_id, frame));
                    }
                    Err(e) => {
                        let error_msg = format!("Failed to decode frame: {:?}", e);
                        store_clone.dispatch(VideoStoreAction::VideoFileError(element_id, error_msg));
                    }
                }
            });
        }
    }

    fn decode_frame_sequence(&self, element_id: Uuid, start_time: f64, end_time: f64, frame_rate: f64) {
        if let Some(decoder) = self.video_decoder.borrow_mut().as_mut() {
            let store_clone = self.clone();
            wasm_bindgen_futures::spawn_local(async move {
                match decoder.decode_frame_sequence(&element_id, start_time, end_time, frame_rate).await {
                    Ok(frames) => {
                        store_clone.dispatch(VideoStoreAction::FrameSequenceDecoded(element_id, frames));
                    }
                    Err(e) => {
                        let error_msg = format!("Failed to decode frame sequence: {:?}", e);
                        store_clone.dispatch(VideoStoreAction::VideoFileError(element_id, error_msg));
                    }
                }
            });
        }
    }

    fn generate_thumbnail(&self, element_id: Uuid, options: ThumbnailOptions) {
        if let Some(processor) = self.video_processor.borrow().as_ref() {
            // Get the file from the loaded elements
            let state = self.state.borrow();
            if let Some(element) = state.loaded_video_elements.get(&element_id) {
                // For now, we'll use a placeholder implementation
                // In a real implementation, we would need access to the original file
                let thumbnail_url = format!("data:image/jpeg;base64,placeholder_for_{}", element_id);
                drop(state);
                self.dispatch(VideoStoreAction::ThumbnailGenerated(element_id, thumbnail_url));
            }
        }
    }
}

impl Clone for VideoStore {
    fn clone(&self) -> Self {
        Self {
            state: self.state.clone(),
            video_decoder: self.video_decoder.clone(),
            video_processor: self.video_processor.clone(),
            listeners: self.listeners.clone(),
        }
    }
}

impl Default for VideoStore {
    fn default() -> Self {
        Self::new()
    }
}

// Global video store instance
thread_local! {
    pub static VIDEO_STORE: VideoStore = VideoStore::new();
}

pub fn use_video_store() -> (VideoStoreState, Callback<VideoStoreAction>) {
    let state = VIDEO_STORE.with(|store| store.get_state());

    let dispatch = Callback::from(move |action: VideoStoreAction| {
        VIDEO_STORE.with(|store| {
            store.dispatch(action);
        });
    });

    (state, dispatch)
}

// Helper functions for common video operations
pub fn initialize_video_system() {
    VIDEO_STORE.with(|store| {
        store.dispatch(VideoStoreAction::InitializeVideoSystem);
    });
}

pub fn load_video_file(element_id: Uuid, file: File) {
    VIDEO_STORE.with(|store| {
        store.dispatch(VideoStoreAction::LoadVideoFile(element_id, file));
    });
}

pub fn set_active_video(element_id: Option<Uuid>) {
    VIDEO_STORE.with(|store| {
        store.dispatch(VideoStoreAction::SetActiveVideoElement(element_id));
    });
}

pub fn toggle_video_playback() {
    VIDEO_STORE.with(|store| {
        let state = store.get_state();
        if state.playback_state.is_playing {
            store.dispatch(VideoStoreAction::Pause);
        } else {
            store.dispatch(VideoStoreAction::Play);
        }
    });
}

pub fn seek_video_to_time(time: f64) {
    VIDEO_STORE.with(|store| {
        store.dispatch(VideoStoreAction::Seek(time));
    });
}

pub fn set_video_volume(volume: f64) {
    VIDEO_STORE.with(|store| {
        store.dispatch(VideoStoreAction::SetVolume(volume));
    });
}

pub fn set_video_muted(muted: bool) {
    VIDEO_STORE.with(|store| {
        store.dispatch(VideoStoreAction::SetMuted(muted));
    });
}

pub fn load_timeline_video(timeline: Timeline) {
    VIDEO_STORE.with(|store| {
        store.dispatch(VideoStoreAction::LoadTimeline(timeline));
    });
}

pub fn generate_video_thumbnail(element_id: Uuid, options: ThumbnailOptions) {
    VIDEO_STORE.with(|store| {
        store.dispatch(VideoStoreAction::GenerateThumbnail(element_id, options));
    });
}

pub fn decode_video_frame_at_time(element_id: Uuid, timestamp: f64) {
    VIDEO_STORE.with(|store| {
        store.dispatch(VideoStoreAction::DecodeFrameAtTime(element_id, timestamp));
    });
}
