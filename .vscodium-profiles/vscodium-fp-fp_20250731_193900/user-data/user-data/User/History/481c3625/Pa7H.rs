
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};

/// Progress tracking for long-running operations
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ProgressTracker {
    pub id: Uuid,
    pub operation_type: OperationType,
    pub status: ProgressStatus,
    pub progress_percentage: f32,
    pub current_step: String,
    pub total_steps: u32,
    pub completed_steps: u32,
    pub start_time: DateTime<Utc>,
    pub estimated_completion: Option<DateTime<Utc>>,
    pub error_message: Option<String>,
    pub metadata: HashMap<String, serde_json::Value>,
    pub cancellable: bool,
    pub can_pause: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OperationType {
    VideoExport,
    AudioExport,
    MediaImport,
    ThumbnailGeneration,
    WaveformGeneration,
    ProjectSave,
    ProjectLoad,
    CacheCleanup,
    DatabaseMigration,
    BatchOperation,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub enum ProgressStatus {
    Pending,
    Running,
    Paused,
    Completed,
    Failed,
    Cancelled,
}

/// Progress update event
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProgressUpdate {
    pub tracker_id: Uuid,
    pub progress_percentage: f32,
    pub current_step: String,
    pub completed_steps: u32,
    pub estimated_completion: Option<DateTime<Utc>>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Background task manager for handling long-running operations
pub struct BackgroundTaskManager {
    active_tasks: HashMap<Uuid, ProgressTracker>,
    completed_tasks: Vec<ProgressTracker>,
    max_completed_history: usize,
    progress_callbacks: HashMap<Uuid, Box<dyn Fn(ProgressUpdate)>>,
}

impl BackgroundTaskManager {
    pub fn new() -> Self {
        Self {
            active_tasks: HashMap::new(),
            completed_tasks: Vec::new(),
            max_completed_history: 100,
            progress_callbacks: HashMap::new(),
        }
    }

    /// Start a new background task
    pub fn start_task(
        &mut self,
        operation_type: OperationType,
        total_steps: u32,
        cancellable: bool,
        can_pause: bool,
    ) -> Uuid {
        let task_id = Uuid::new_v4();
        let tracker = ProgressTracker {
            id: task_id,
            operation_type,
            status: ProgressStatus::Pending,
            progress_percentage: 0.0,
            current_step: "Initializing...".to_string(),
            total_steps,
            completed_steps: 0,
            start_time: Utc::now(),
            estimated_completion: None,
            error_message: None,
            metadata: HashMap::new(),
            cancellable,
            can_pause,
        };

        self.active_tasks.insert(task_id, tracker);
        task_id
    }

    /// Update task progress
    pub fn update_progress(
        &mut self,
        task_id: &Uuid,
        progress_percentage: f32,
        current_step: String,
        completed_steps: u32,
    ) -> Result<(), String> {
        if let Some(tracker) = self.active_tasks.get_mut(task_id) {
            tracker.progress_percentage = progress_percentage.clamp(0.0, 100.0);
            tracker.current_step = current_step.clone();
            tracker.completed_steps = completed_steps;
            tracker.status = ProgressStatus::Running;

            // Calculate estimated completion time
            if tracker.completed_steps > 0 && tracker.total_steps > tracker.completed_steps {
                let elapsed = Utc::now().signed_duration_since(tracker.start_time);
                let avg_time_per_step = elapsed.num_milliseconds() as f64 / tracker.completed_steps as f64;
                let remaining_steps = tracker.total_steps - tracker.completed_steps;
                let estimated_remaining_ms = avg_time_per_step * remaining_steps as f64;
                
                tracker.estimated_completion = Some(
                    Utc::now() + chrono::Duration::milliseconds(estimated_remaining_ms as i64)
                );
            }

            // Emit progress update
            let update = ProgressUpdate {
                tracker_id: *task_id,
                progress_percentage,
                current_step,
                completed_steps,
                estimated_completion: tracker.estimated_completion,
                metadata: tracker.metadata.clone(),
            };

            if let Some(callback) = self.progress_callbacks.get(task_id) {
                callback(update);
            }

            Ok(())
        } else {
            Err(format!("Task {} not found", task_id))
        }
    }

    /// Complete a task successfully
    pub fn complete_task(&mut self, task_id: &Uuid) -> Result<(), String> {
        if let Some(mut tracker) = self.active_tasks.remove(task_id) {
            tracker.status = ProgressStatus::Completed;
            tracker.progress_percentage = 100.0;
            tracker.completed_steps = tracker.total_steps;
            
            self.add_to_history(tracker);
            self.progress_callbacks.remove(task_id);
            Ok(())
        } else {
            Err(format!("Task {} not found", task_id))
        }
    }

    /// Fail a task with error message
    pub fn fail_task(&mut self, task_id: &Uuid, error_message: String) -> Result<(), String> {
        if let Some(mut tracker) = self.active_tasks.remove(task_id) {
            tracker.status = ProgressStatus::Failed;
            tracker.error_message = Some(error_message);
            
            self.add_to_history(tracker);
            self.progress_callbacks.remove(task_id);
            Ok(())
        } else {
            Err(format!("Task {} not found", task_id))
        }
    }

    /// Cancel a task
    pub fn cancel_task(&mut self, task_id: &Uuid) -> Result<(), String> {
        if let Some(mut tracker) = self.active_tasks.remove(task_id) {
            if !tracker.cancellable {
                return Err("Task is not cancellable".to_string());
            }
            
            tracker.status = ProgressStatus::Cancelled;
            self.add_to_history(tracker);
            self.progress_callbacks.remove(task_id);
            Ok(())
        } else {
            Err(format!("Task {} not found", task_id))
        }
    }

    /// Pause a task
    pub fn pause_task(&mut self, task_id: &Uuid) -> Result<(), String> {
        if let Some(tracker) = self.active_tasks.get_mut(task_id) {
            if !tracker.can_pause {
                return Err("Task cannot be paused".to_string());
            }
            
            tracker.status = ProgressStatus::Paused;
            Ok(())
        } else {
            Err(format!("Task {} not found", task_id))
        }
    }

    /// Resume a paused task
    pub fn resume_task(&mut self, task_id: &Uuid) -> Result<(), String> {
        if let Some(tracker) = self.active_tasks.get_mut(task_id) {
            if tracker.status != ProgressStatus::Paused {
                return Err("Task is not paused".to_string());
            }
            
            tracker.status = ProgressStatus::Running;
            Ok(())
        } else {
            Err(format!("Task {} not found", task_id))
        }
    }

    /// Set progress callback for a task
    pub fn set_progress_callback<F>(&mut self, task_id: Uuid, callback: F)
    where
        F: Fn(ProgressUpdate) + 'static,
    {
        self.progress_callbacks.insert(task_id, Box::new(callback));
    }

    /// Get active tasks
    pub fn get_active_tasks(&self) -> Vec<&ProgressTracker> {
        self.active_tasks.values().collect()
    }

    /// Get task by ID
    pub fn get_task(&self, task_id: &Uuid) -> Option<&ProgressTracker> {
        self.active_tasks.get(task_id)
    }

    /// Get completed tasks history
    pub fn get_completed_tasks(&self) -> &Vec<ProgressTracker> {
        &self.completed_tasks
    }

    /// Clear completed tasks history
    pub fn clear_history(&mut self) {
        self.completed_tasks.clear();
    }

    /// Add task to completed history
    fn add_to_history(&mut self, tracker: ProgressTracker) {
        self.completed_tasks.push(tracker);
        
        // Maintain history size limit
        if self.completed_tasks.len() > self.max_completed_history {
            self.completed_tasks.remove(0);
        }
    }

    /// Get summary statistics
    pub fn get_statistics(&self) -> TaskStatistics {
        let active_count = self.active_tasks.len();
        let completed_count = self.completed_tasks.iter()
            .filter(|t| t.status == ProgressStatus::Completed)
            .count();
        let failed_count = self.completed_tasks.iter()
            .filter(|t| t.status == ProgressStatus::Failed)
            .count();
        let cancelled_count = self.completed_tasks.iter()
            .filter(|t| t.status == ProgressStatus::Cancelled)
            .count();

        TaskStatistics {
            active_tasks: active_count,
            completed_tasks: completed_count,
            failed_tasks: failed_count,
            cancelled_tasks: cancelled_count,
            total_tasks: active_count + self.completed_tasks.len(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskStatistics {
    pub active_tasks: usize,
    pub completed_tasks: usize,
    pub failed_tasks: usize,
    pub cancelled_tasks: usize,
    pub total_tasks: usize,
}

impl Default for BackgroundTaskManager {
    fn default() -> Self {
        Self::new()
    }
}
