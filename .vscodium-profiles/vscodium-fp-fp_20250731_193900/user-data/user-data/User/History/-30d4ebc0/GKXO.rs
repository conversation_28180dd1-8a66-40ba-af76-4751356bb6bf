use wasm_bindgen::prelude::*;
use web_sys::{FontFace, FontFaceSet};
use js_sys::Promise;
use wasm_bindgen_futures::JsFuture;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::rc::Rc;
use std::cell::RefCell;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FontInfo {
    pub family: String,
    pub style: FontStyle,
    pub weight: FontWeight,
    pub source: FontSource,
    pub loaded: bool,
    pub loading: bool,
    pub error: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FontStyle {
    Normal,
    Italic,
    Oblique,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FontWeight {
    Thin,       // 100
    ExtraLight, // 200
    Light,      // 300
    Normal,     // 400
    Medium,     // 500
    SemiBold,   // 600
    Bold,       // 700
    ExtraBold,  // 800
    Black,      // 900
    Custom(u32),
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FontSource {
    System,
    WebFont { url: String },
    GoogleFonts { family: String },
    CustomUpload { data: Vec<u8> },
}

impl FontWeight {
    pub fn to_css_value(&self) -> String {
        match self {
            FontWeight::Thin => "100".to_string(),
            FontWeight::ExtraLight => "200".to_string(),
            FontWeight::Light => "300".to_string(),
            FontWeight::Normal => "400".to_string(),
            FontWeight::Medium => "500".to_string(),
            FontWeight::SemiBold => "600".to_string(),
            FontWeight::Bold => "700".to_string(),
            FontWeight::ExtraBold => "800".to_string(),
            FontWeight::Black => "900".to_string(),
            FontWeight::Custom(weight) => weight.to_string(),
        }
    }
}

impl FontStyle {
    pub fn to_css_value(&self) -> String {
        match self {
            FontStyle::Normal => "normal".to_string(),
            FontStyle::Italic => "italic".to_string(),
            FontStyle::Oblique => "oblique".to_string(),
        }
    }
}

#[derive(Clone)]
pub struct FontManager {
    fonts: Rc<RefCell<HashMap<String, FontInfo>>>,
    font_faces: Rc<RefCell<HashMap<String, FontFace>>>,
    loading_promises: Rc<RefCell<HashMap<String, Promise>>>,
    fallback_stack: Vec<String>,
}

impl FontManager {
    pub fn new() -> Self {
        let mut manager = Self {
            fonts: Rc::new(RefCell::new(HashMap::new())),
            font_faces: Rc::new(RefCell::new(HashMap::new())),
            loading_promises: Rc::new(RefCell::new(HashMap::new())),
            fallback_stack: vec![
                "Arial".to_string(),
                "Helvetica".to_string(),
                "sans-serif".to_string(),
            ],
        };

        // Load system fonts
        manager.load_system_fonts();
        manager
    }

    /// Initialize the font manager with default settings
    pub fn initialize(&mut self) -> Result<(), JsValue> {
        // This method can be used for any additional initialization
        // For now, it's a no-op since initialization is done in new()
        Ok(())
    }

    /// Load common system fonts
    fn load_system_fonts(&mut self) {
        let system_fonts = vec![
            ("Arial", FontWeight::Normal, FontStyle::Normal),
            ("Arial", FontWeight::Bold, FontStyle::Normal),
            ("Arial", FontWeight::Normal, FontStyle::Italic),
            ("Helvetica", FontWeight::Normal, FontStyle::Normal),
            ("Times New Roman", FontWeight::Normal, FontStyle::Normal),
            ("Courier New", FontWeight::Normal, FontStyle::Normal),
            ("Georgia", FontWeight::Normal, FontStyle::Normal),
            ("Verdana", FontWeight::Normal, FontStyle::Normal),
        ];

        for (family, weight, style) in system_fonts {
            let font_key = self.generate_font_key(family, &weight, &style);
            let font_info = FontInfo {
                family: family.to_string(),
                style,
                weight,
                source: FontSource::System,
                loaded: true,
                loading: false,
                error: None,
            };
            self.fonts.borrow_mut().insert(font_key, font_info);
        }
    }

    /// Load a web font from URL
    pub async fn load_web_font(
        &mut self,
        family: &str,
        weight: FontWeight,
        style: FontStyle,
        url: &str,
    ) -> Result<(), JsValue> {
        let font_key = self.generate_font_key(family, &weight, &style);
        
        // Check if already loaded or loading
        if let Some(font_info) = self.fonts.borrow().get(&font_key) {
            if font_info.loaded {
                return Ok(());
            }
            if font_info.loading {
                // Wait for existing loading promise
                if let Some(promise) = self.loading_promises.borrow().get(&font_key) {
                    JsFuture::from(promise.clone()).await?;
                    return Ok(());
                }
            }
        }

        // Mark as loading
        let font_info = FontInfo {
            family: family.to_string(),
            style: style.clone(),
            weight: weight.clone(),
            source: FontSource::WebFont { url: url.to_string() },
            loaded: false,
            loading: true,
            error: None,
        };
        self.fonts.borrow_mut().insert(font_key.clone(), font_info);

        // Create FontFace
        let font_face = FontFace::new_with_str(&family, &format!("url({})", url))?;
        
        // Set font properties
        font_face.set_weight(&weight.to_css_value());
        font_face.set_style(&style.to_css_value());

        // Load the font
        let load_promise = font_face.load()?;
        self.loading_promises.borrow_mut().insert(font_key.clone(), load_promise.clone());

        // Wait for loading to complete
        match JsFuture::from(load_promise).await {
            Ok(_) => {
                // Add to document fonts
                if let Some(window) = web_sys::window() {
                    if let Some(document) = window.document() {
                        if let Ok(fonts) = Reflect::get(&document, &JsValue::from_str("fonts")) {
                            let font_face_set: FontFaceSet = fonts.dyn_into()?;
                            font_face_set.add(&font_face)?;
                        }
                    }
                }

                // Update font info
                if let Some(font_info) = self.fonts.borrow_mut().get_mut(&font_key) {
                    font_info.loaded = true;
                    font_info.loading = false;
                }

                // Store font face
                self.font_faces.borrow_mut().insert(font_key.clone(), font_face);
                
                // Remove loading promise
                self.loading_promises.borrow_mut().remove(&font_key);

                Ok(())
            }
            Err(error) => {
                // Update font info with error
                if let Some(font_info) = self.fonts.borrow_mut().get_mut(&font_key) {
                    font_info.loading = false;
                    font_info.error = Some(format!("{:?}", error));
                }

                // Remove loading promise
                self.loading_promises.borrow_mut().remove(&font_key);

                Err(error)
            }
        }
    }

    /// Load Google Fonts
    pub async fn load_google_font(
        &mut self,
        family: &str,
        weights: Vec<FontWeight>,
        styles: Vec<FontStyle>,
    ) -> Result<(), JsValue> {
        // Build Google Fonts URL
        let mut font_specs = Vec::new();
        for weight in &weights {
            for style in &styles {
                let style_suffix = match style {
                    FontStyle::Italic => "i",
                    _ => "",
                };
                font_specs.push(format!("{}{}", weight.to_css_value(), style_suffix));
            }
        }
        
        let font_url = format!(
            "https://fonts.googleapis.com/css2?family={}:wght@{}&display=swap",
            family.replace(" ", "+"),
            font_specs.join(";")
        );

        // Load CSS
        self.load_font_css(&font_url).await?;

        // Mark fonts as loaded
        for weight in weights {
            for style in styles.clone() {
                let font_key = self.generate_font_key(family, &weight, &style);
                let font_info = FontInfo {
                    family: family.to_string(),
                    style,
                    weight: weight.clone(),
                    source: FontSource::GoogleFonts { family: family.to_string() },
                    loaded: true,
                    loading: false,
                    error: None,
                };
                self.fonts.borrow_mut().insert(font_key, font_info);
            }
        }

        Ok(())
    }

    /// Load font CSS from URL
    async fn load_font_css(&self, url: &str) -> Result<(), JsValue> {
        if let Some(window) = web_sys::window() {
            if let Some(document) = window.document() {
                let link = document.create_element("link")?;
                link.set_attribute("rel", "stylesheet")?;
                link.set_attribute("href", url)?;

                if let Some(head) = document.head() {
                    head.append_child(&link)?;
                }

                // Wait for CSS to load
                let link_element = link.dyn_into::<web_sys::HtmlLinkElement>()?;
                let promise = Promise::new(&mut |resolve, _reject| {
                    let closure = Closure::wrap(Box::new(move || {
                        resolve.call0(&JsValue::NULL).unwrap();
                    }) as Box<dyn FnMut()>);

                    link_element.set_onload(Some(closure.as_ref().unchecked_ref()));
                    closure.forget();
                });

                JsFuture::from(promise).await?;
            }
        }
        Ok(())
    }

    /// Upload and load custom font
    pub async fn load_custom_font(
        &mut self,
        family: &str,
        weight: FontWeight,
        style: FontStyle,
        font_data: Vec<u8>,
    ) -> Result<(), JsValue> {
        let font_key = self.generate_font_key(family, &weight, &style);

        // Create blob URL from font data
        let uint8_array = js_sys::Uint8Array::new_with_length(font_data.len() as u32);
        uint8_array.copy_from(&font_data);
        
        let array = js_sys::Array::new();
        array.push(&uint8_array);
        
        let blob = web_sys::Blob::new_with_u8_array_sequence(&array)?;
        let blob_url = web_sys::Url::create_object_url_with_blob(&blob)?;

        // Load as web font
        self.load_web_font(family, weight, style, &blob_url).await?;

        // Update source type
        if let Some(font_info) = self.fonts.borrow_mut().get_mut(&font_key) {
            font_info.source = FontSource::CustomUpload { data: font_data };
        }

        Ok(())
    }

    /// Check if font is loaded
    pub fn is_font_loaded(&self, family: &str, weight: &FontWeight, style: &FontStyle) -> bool {
        let font_key = self.generate_font_key(family, weight, style);
        self.fonts.borrow()
            .get(&font_key)
            .map(|info| info.loaded)
            .unwrap_or(false)
    }

    /// Get font with fallback
    pub fn get_font_stack(&self, family: &str, weight: &FontWeight, style: &FontStyle) -> String {
        let mut stack = vec![family.to_string()];
        
        // Add fallbacks if primary font is not loaded
        if !self.is_font_loaded(family, weight, style) {
            stack.extend(self.fallback_stack.clone());
        }
        
        stack.join(", ")
    }

    /// Generate unique key for font
    fn generate_font_key(&self, family: &str, weight: &FontWeight, style: &FontStyle) -> String {
        format!("{}:{}:{}", family, weight.to_css_value(), style.to_css_value())
    }

    /// Get all loaded fonts
    pub fn get_loaded_fonts(&self) -> Vec<FontInfo> {
        self.fonts.borrow()
            .values()
            .filter(|info| info.loaded)
            .cloned()
            .collect()
    }

    /// Get font families
    pub fn get_font_families(&self) -> Vec<String> {
        let mut families: Vec<String> = self.fonts.borrow()
            .values()
            .map(|info| info.family.clone())
            .collect();
        families.sort();
        families.dedup();
        families
    }

    /// Clear font cache
    pub fn clear_cache(&mut self) {
        self.fonts.borrow_mut().clear();
        self.font_faces.borrow_mut().clear();
        self.loading_promises.borrow_mut().clear();
        self.load_system_fonts();
    }
}

impl Default for FontManager {
    fn default() -> Self {
        Self::new()
    }
}

// Font Family Management System
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct FontFamily {
    pub name: String,
    pub variants: Vec<FontVariant>,
    pub category: FontCategory,
    pub source: FontSource,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct FontVariant {
    pub weight: FontWeight,
    pub style: FontStyle,
    pub loaded: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FontCategory {
    Serif,
    SansSerif,
    Monospace,
    Display,
    Handwriting,
    Script,
}

#[derive(Clone)]
pub struct FontFamilyManager {
    families: HashMap<String, FontFamily>,
    font_manager: FontManager,
    popular_fonts: Vec<String>,
}

impl FontFamilyManager {
    pub fn new() -> Self {
        let mut manager = Self {
            families: HashMap::new(),
            font_manager: FontManager::new(),
            popular_fonts: vec![
                "Arial".to_string(),
                "Helvetica".to_string(),
                "Times New Roman".to_string(),
                "Georgia".to_string(),
                "Verdana".to_string(),
                "Courier New".to_string(),
                "Impact".to_string(),
                "Comic Sans MS".to_string(),
            ],
        };

        manager.initialize_system_fonts();
        manager
    }

    /// Initialize system font families
    fn initialize_system_fonts(&mut self) {
        let system_families = vec![
            ("Arial", FontCategory::SansSerif, vec![
                (FontWeight::Normal, FontStyle::Normal),
                (FontWeight::Bold, FontStyle::Normal),
                (FontWeight::Normal, FontStyle::Italic),
                (FontWeight::Bold, FontStyle::Italic),
            ]),
            ("Helvetica", FontCategory::SansSerif, vec![
                (FontWeight::Normal, FontStyle::Normal),
                (FontWeight::Bold, FontStyle::Normal),
                (FontWeight::Normal, FontStyle::Italic),
            ]),
            ("Times New Roman", FontCategory::Serif, vec![
                (FontWeight::Normal, FontStyle::Normal),
                (FontWeight::Bold, FontStyle::Normal),
                (FontWeight::Normal, FontStyle::Italic),
                (FontWeight::Bold, FontStyle::Italic),
            ]),
            ("Georgia", FontCategory::Serif, vec![
                (FontWeight::Normal, FontStyle::Normal),
                (FontWeight::Bold, FontStyle::Normal),
                (FontWeight::Normal, FontStyle::Italic),
            ]),
            ("Courier New", FontCategory::Monospace, vec![
                (FontWeight::Normal, FontStyle::Normal),
                (FontWeight::Bold, FontStyle::Normal),
            ]),
        ];

        for (name, category, variants) in system_families {
            let font_variants: Vec<FontVariant> = variants
                .into_iter()
                .map(|(weight, style)| FontVariant {
                    weight,
                    style,
                    loaded: true, // System fonts are always loaded
                })
                .collect();

            let family = FontFamily {
                name: name.to_string(),
                variants: font_variants,
                category,
                source: FontSource::System,
            };

            self.families.insert(name.to_string(), family);
        }
    }

    /// Add Google Fonts family
    pub async fn add_google_fonts_family(
        &mut self,
        name: &str,
        category: FontCategory,
        variants: Vec<(FontWeight, FontStyle)>,
    ) -> Result<(), JsValue> {
        // Load the fonts
        let weights: Vec<FontWeight> = variants.iter().map(|(w, _)| w.clone()).collect();
        let styles: Vec<FontStyle> = variants.iter().map(|(_, s)| s.clone()).collect();

        self.font_manager.load_google_font(name, weights, styles).await?;

        // Create family entry
        let font_variants: Vec<FontVariant> = variants
            .into_iter()
            .map(|(weight, style)| FontVariant {
                weight,
                style,
                loaded: true,
            })
            .collect();

        let family = FontFamily {
            name: name.to_string(),
            variants: font_variants,
            category,
            source: FontSource::GoogleFonts { family: name.to_string() },
        };

        self.families.insert(name.to_string(), family);
        Ok(())
    }

    /// Add custom font family
    pub async fn add_custom_font_family(
        &mut self,
        name: &str,
        category: FontCategory,
        font_files: Vec<(FontWeight, FontStyle, Vec<u8>)>,
    ) -> Result<(), JsValue> {
        let mut variants = Vec::new();

        for (weight, style, data) in font_files {
            self.font_manager.load_custom_font(name, weight.clone(), style.clone(), data.clone()).await?;

            variants.push(FontVariant {
                weight,
                style,
                loaded: true,
            });
        }

        let family = FontFamily {
            name: name.to_string(),
            variants,
            category,
            source: FontSource::CustomUpload { data: Vec::new() }, // Placeholder
        };

        self.families.insert(name.to_string(), family);
        Ok(())
    }

    /// Get font families by category
    pub fn get_families_by_category(&self, category: &FontCategory) -> Vec<&FontFamily> {
        self.families
            .values()
            .filter(|family| std::mem::discriminant(&family.category) == std::mem::discriminant(category))
            .collect()
    }

    /// Get popular fonts
    pub fn get_popular_fonts(&self) -> Vec<&FontFamily> {
        self.popular_fonts
            .iter()
            .filter_map(|name| self.families.get(name))
            .collect()
    }

    /// Search fonts by name
    pub fn search_fonts(&self, query: &str) -> Vec<&FontFamily> {
        let query_lower = query.to_lowercase();
        self.families
            .values()
            .filter(|family| family.name.to_lowercase().contains(&query_lower))
            .collect()
    }

    /// Get font with fallback stack
    pub fn get_font_stack(&self, family_name: &str, weight: &FontWeight, style: &FontStyle) -> String {
        self.font_manager.get_font_stack(family_name, weight, style)
    }

    /// Check if font variant is available
    pub fn is_variant_available(&self, family_name: &str, weight: &FontWeight, style: &FontStyle) -> bool {
        if let Some(family) = self.families.get(family_name) {
            family.variants.iter().any(|variant| {
                std::mem::discriminant(&variant.weight) == std::mem::discriminant(weight) &&
                std::mem::discriminant(&variant.style) == std::mem::discriminant(style) &&
                variant.loaded
            })
        } else {
            false
        }
    }

    /// Get available variants for a family
    pub fn get_available_variants(&self, family_name: &str) -> Vec<&FontVariant> {
        if let Some(family) = self.families.get(family_name) {
            family.variants.iter().filter(|variant| variant.loaded).collect()
        } else {
            Vec::new()
        }
    }

    /// Get all font families
    pub fn get_all_families(&self) -> Vec<&FontFamily> {
        self.families.values().collect()
    }

    /// Get font family by name
    pub fn get_family(&self, name: &str) -> Option<&FontFamily> {
        self.families.get(name)
    }

    /// Load popular fonts
    pub async fn load_popular_fonts(&mut self) -> Result<(), JsValue> {
        for font_name in &self.popular_fonts.clone() {
            // Try to load as Google Font if not already available
            if !self.families.contains_key(font_name) {
                let _ = self.add_google_fonts_family(
                    font_name,
                    FontCategory::SansSerif, // Default category
                    vec![(FontWeight::Normal, FontStyle::Normal)]
                ).await;
            }
        }
        Ok(())
    }

    /// Load a specific font family
    pub async fn load_font_family(&mut self, family_name: &str) -> Result<(), JsValue> {
        if !self.families.contains_key(family_name) {
            // Try to load as Google Font
            self.add_google_fonts_family(
                family_name,
                FontCategory::SansSerif, // Default category
                vec![(FontWeight::Normal, FontStyle::Normal)]
            ).await?;
        }
        Ok(())
    }
}

impl Default for FontFamilyManager {
    fn default() -> Self {
        Self::new()
    }
}

// Font Metrics Calculation System
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FontMetrics {
    pub font_size: f32,
    pub line_height: f32,
    pub ascent: f32,
    pub descent: f32,
    pub x_height: f32,
    pub cap_height: f32,
    pub baseline: f32,
    pub em_width: f32,
    pub space_width: f32,
}

pub struct FontMetricsCalculator {
    canvas: web_sys::HtmlCanvasElement,
    context: web_sys::CanvasRenderingContext2d,
    metrics_cache: HashMap<String, FontMetrics>,
}

impl FontMetricsCalculator {
    pub fn new() -> Result<Self, JsValue> {
        let window = web_sys::window().ok_or("No window object")?;
        let document = window.document().ok_or("No document object")?;

        let canvas = document
            .create_element("canvas")?
            .dyn_into::<web_sys::HtmlCanvasElement>()?;

        canvas.set_width(100);
        canvas.set_height(100);

        let context = canvas
            .get_context("2d")?
            .ok_or("No 2d context")?
            .dyn_into::<web_sys::CanvasRenderingContext2d>()?;

        Ok(Self {
            canvas,
            context,
            metrics_cache: HashMap::new(),
        })
    }

    /// Calculate comprehensive font metrics
    pub fn calculate_metrics(
        &mut self,
        family: &str,
        size: f32,
        weight: &FontWeight,
        style: &FontStyle,
    ) -> Result<FontMetrics, JsValue> {
        let cache_key = format!("{}:{}:{}:{}", family, size, weight.to_css_value(), style.to_css_value());

        // Check cache first
        if let Some(cached_metrics) = self.metrics_cache.get(&cache_key) {
            return Ok(cached_metrics.clone());
        }

        // Set font
        let font_string = format!(
            "{} {} {}px {}",
            style.to_css_value(),
            weight.to_css_value(),
            size,
            family
        );
        self.context.set_font(&font_string);

        // Measure basic metrics
        let em_metrics = self.context.measure_text("M")?;
        let space_metrics = self.context.measure_text(" ")?;
        let x_metrics = self.context.measure_text("x")?;

        // Calculate metrics
        let em_width = em_metrics.width() as f32;
        let space_width = space_metrics.width() as f32;

        // Estimate ascent and descent based on font size
        let ascent = size * 0.8; // Typical ascent is about 80% of font size
        let descent = size * 0.2; // Typical descent is about 20% of font size
        let line_height = size * 1.2; // Default line height

        // Estimate x-height and cap-height
        let x_height = size * 0.5; // Typical x-height is about 50% of font size
        let cap_height = size * 0.7; // Typical cap-height is about 70% of font size

        let baseline = ascent; // Baseline is at ascent distance from top

        let metrics = FontMetrics {
            font_size: size,
            line_height,
            ascent,
            descent,
            x_height,
            cap_height,
            baseline,
            em_width,
            space_width,
        };

        // Cache the metrics
        self.metrics_cache.insert(cache_key, metrics.clone());

        Ok(metrics)
    }

    /// Calculate text width
    pub fn calculate_text_width(
        &mut self,
        text: &str,
        family: &str,
        size: f32,
        weight: &FontWeight,
        style: &FontStyle,
    ) -> Result<f32, JsValue> {
        let font_string = format!(
            "{} {} {}px {}",
            style.to_css_value(),
            weight.to_css_value(),
            size,
            family
        );
        self.context.set_font(&font_string);

        let metrics = self.context.measure_text(text)?;
        Ok(metrics.width() as f32)
    }

    /// Calculate text bounding box
    pub fn calculate_text_bounds(
        &mut self,
        text: &str,
        family: &str,
        size: f32,
        weight: &FontWeight,
        style: &FontStyle,
    ) -> Result<(f32, f32, f32, f32), JsValue> {
        let font_string = format!(
            "{} {} {}px {}",
            style.to_css_value(),
            weight.to_css_value(),
            size,
            family
        );
        self.context.set_font(&font_string);

        let metrics = self.context.measure_text(text)?;
        let font_metrics = self.calculate_metrics(family, size, weight, style)?;

        let width = metrics.width() as f32;
        let height = font_metrics.line_height;

        // Calculate bounding box (x, y, width, height)
        Ok((0.0, -font_metrics.ascent, width, height))
    }

    /// Calculate optimal line height for readability
    pub fn calculate_optimal_line_height(&self, font_size: f32) -> f32 {
        // Golden ratio-based line height calculation
        font_size * 1.618
    }

    /// Calculate character spacing for improved readability
    pub fn calculate_optimal_letter_spacing(&self, font_size: f32) -> f32 {
        // Subtle letter spacing based on font size
        font_size * 0.02
    }

    /// Clear metrics cache
    pub fn clear_cache(&mut self) {
        self.metrics_cache.clear();
    }

    /// Get cached metrics count
    pub fn get_cache_size(&self) -> usize {
        self.metrics_cache.len()
    }
}

impl Default for FontMetricsCalculator {
    fn default() -> Self {
        Self::new().expect("Failed to create FontMetricsCalculator")
    }
}

// Custom Font Upload System
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomFontUpload {
    pub name: String,
    pub family: String,
    pub weight: FontWeight,
    pub style: FontStyle,
    pub format: FontFormat,
    pub size: usize,
    pub uploaded_at: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FontFormat {
    TrueType,
    OpenType,
    Woff,
    Woff2,
    Eot,
}

impl FontFormat {
    pub fn from_mime_type(mime_type: &str) -> Option<Self> {
        match mime_type {
            "font/ttf" | "application/x-font-ttf" => Some(FontFormat::TrueType),
            "font/otf" | "application/x-font-opentype" => Some(FontFormat::OpenType),
            "font/woff" | "application/font-woff" => Some(FontFormat::Woff),
            "font/woff2" | "application/font-woff2" => Some(FontFormat::Woff2),
            "application/vnd.ms-fontobject" => Some(FontFormat::Eot),
            _ => None,
        }
    }

    pub fn from_extension(extension: &str) -> Option<Self> {
        match extension.to_lowercase().as_str() {
            "ttf" => Some(FontFormat::TrueType),
            "otf" => Some(FontFormat::OpenType),
            "woff" => Some(FontFormat::Woff),
            "woff2" => Some(FontFormat::Woff2),
            "eot" => Some(FontFormat::Eot),
            _ => None,
        }
    }

    pub fn to_css_format(&self) -> &'static str {
        match self {
            FontFormat::TrueType => "truetype",
            FontFormat::OpenType => "opentype",
            FontFormat::Woff => "woff",
            FontFormat::Woff2 => "woff2",
            FontFormat::Eot => "embedded-opentype",
        }
    }
}

pub struct CustomFontUploader {
    uploaded_fonts: HashMap<String, CustomFontUpload>,
    font_manager: FontManager,
    max_file_size: usize,
    allowed_formats: Vec<FontFormat>,
}

impl CustomFontUploader {
    pub fn new() -> Self {
        Self {
            uploaded_fonts: HashMap::new(),
            font_manager: FontManager::new(),
            max_file_size: 5 * 1024 * 1024, // 5MB
            allowed_formats: vec![
                FontFormat::TrueType,
                FontFormat::OpenType,
                FontFormat::Woff,
                FontFormat::Woff2,
            ],
        }
    }

    /// Upload and validate font file
    pub async fn upload_font(
        &mut self,
        file_name: &str,
        font_data: Vec<u8>,
        family_name: Option<String>,
    ) -> Result<String, String> {
        // Validate file size
        if font_data.len() > self.max_file_size {
            return Err(format!(
                "Font file too large: {} bytes (max: {} bytes)",
                font_data.len(),
                self.max_file_size
            ));
        }

        // Determine format from file extension
        let extension = file_name.split('.').last().unwrap_or("");
        let format = FontFormat::from_extension(extension)
            .ok_or_else(|| format!("Unsupported font format: {}", extension))?;

        // Check if format is allowed
        if !self.allowed_formats.iter().any(|f| std::mem::discriminant(f) == std::mem::discriminant(&format)) {
            return Err(format!("Font format not allowed: {:?}", format));
        }

        // Validate font data (basic validation)
        if !self.validate_font_data(&font_data, &format) {
            return Err("Invalid font file data".to_string());
        }

        // Extract font metadata
        let (family, weight, style) = self.extract_font_metadata(&font_data, &format)
            .unwrap_or_else(|| {
                (
                    family_name.unwrap_or_else(|| file_name.trim_end_matches(&format!(".{}", extension)).to_string()),
                    FontWeight::Normal,
                    FontStyle::Normal,
                )
            });

        // Generate unique ID
        let font_id = format!("custom_{}_{}", family, chrono::Utc::now().timestamp());

        // Load font
        self.font_manager
            .load_custom_font(&family, weight.clone(), style.clone(), font_data.clone())
            .await
            .map_err(|e| format!("Failed to load font: {:?}", e))?;

        // Store upload info
        let upload = CustomFontUpload {
            name: file_name.to_string(),
            family: family.clone(),
            weight,
            style,
            format,
            size: font_data.len(),
            uploaded_at: chrono::Utc::now().to_rfc3339(),
        };

        self.uploaded_fonts.insert(font_id.clone(), upload);

        Ok(font_id)
    }

    /// Validate font file data
    fn validate_font_data(&self, data: &[u8], format: &FontFormat) -> bool {
        if data.len() < 4 {
            return false;
        }

        // Check magic bytes for different font formats
        match format {
            FontFormat::TrueType => {
                // TTF files start with 0x00010000 or "true"
                data.starts_with(&[0x00, 0x01, 0x00, 0x00]) || data.starts_with(b"true")
            }
            FontFormat::OpenType => {
                // OTF files start with "OTTO"
                data.starts_with(b"OTTO")
            }
            FontFormat::Woff => {
                // WOFF files start with "wOFF"
                data.starts_with(b"wOFF")
            }
            FontFormat::Woff2 => {
                // WOFF2 files start with "wOF2"
                data.starts_with(b"wOF2")
            }
            FontFormat::Eot => {
                // EOT files have a specific header structure
                data.len() > 34 && data[34..38] == [0x4C, 0x50, 0x00, 0x00]
            }
        }
    }

    /// Extract font metadata (simplified version)
    fn extract_font_metadata(&self, _data: &[u8], _format: &FontFormat) -> Option<(String, FontWeight, FontStyle)> {
        // This is a simplified implementation
        // In a real implementation, you would parse the font file to extract:
        // - Font family name from the 'name' table
        // - Font weight from the 'OS/2' table
        // - Font style from various tables
        None
    }

    /// Get uploaded fonts
    pub fn get_uploaded_fonts(&self) -> Vec<&CustomFontUpload> {
        self.uploaded_fonts.values().collect()
    }

    /// Remove uploaded font
    pub fn remove_font(&mut self, font_id: &str) -> bool {
        self.uploaded_fonts.remove(font_id).is_some()
    }

    /// Get font by ID
    pub fn get_font(&self, font_id: &str) -> Option<&CustomFontUpload> {
        self.uploaded_fonts.get(font_id)
    }

    /// Set maximum file size
    pub fn set_max_file_size(&mut self, size: usize) {
        self.max_file_size = size;
    }

    /// Set allowed formats
    pub fn set_allowed_formats(&mut self, formats: Vec<FontFormat>) {
        self.allowed_formats = formats;
    }

    /// Clear all uploaded fonts
    pub fn clear_all(&mut self) {
        self.uploaded_fonts.clear();
        self.font_manager.clear_cache();
    }
}

impl Default for CustomFontUploader {
    fn default() -> Self {
        Self::new()
    }
}

impl PartialEq for FontFamilyManager {
    fn eq(&self, other: &Self) -> bool {
        self.families == other.families && self.popular_fonts == other.popular_fonts
    }
}
