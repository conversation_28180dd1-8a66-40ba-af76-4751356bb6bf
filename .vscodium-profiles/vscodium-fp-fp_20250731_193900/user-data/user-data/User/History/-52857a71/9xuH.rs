use wasm_bindgen::prelude::*;
use web_sys::{AudioContext, GainNode, AnalyserNode, HtmlVideoElement};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

use uuid::Uuid;

use crate::utils::audio::{AudioEngine};
use crate::utils::audio_effects_system::{VolumeControlSettings, FadeSettings};
use crate::utils::web_audio::{WebAudioManager, AudioContextConfig};
use crate::types::timeline::{TimelineElement, ElementType};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AudioTrackSettings {
    pub id: Uuid,
    pub name: String,
    pub volume: VolumeControlSettings,
    pub fade: FadeSettings,
    pub effects_enabled: bool,
    pub monitoring: bool,
}

impl Default for AudioTrackSettings {
    fn default() -> Self {
        Self {
            id: Uuid::new_v4(),
            name: "Audio Track".to_string(),
            volume: VolumeControlSettings::default(),
            fade: FadeSettings::default(),
            effects_enabled: true,
            monitoring: false,
        }
    }
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct AudioPlaybackState {
    pub is_playing: bool,
    pub current_time: f64,
    pub duration: f64,
    pub playback_rate: f64,
    pub loop_enabled: bool,
    pub muted: bool,
    pub master_volume: f64,
    pub buffering: bool,
    pub seeking: bool,
    pub loop_start: Option<f64>,
    pub loop_end: Option<f64>,
    pub playback_mode: PlaybackMode,
    pub transport_state: TransportState,
    pub last_update_time: f64,
    pub audio_latency: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PlaybackMode {
    Normal,
    Loop,
    PingPong,
    Scrub,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TransportState {
    Stopped,
    Playing,
    Paused,
    Seeking,
    Buffering,
    Error(String),
}

impl Default for AudioPlaybackState {
    fn default() -> Self {
        Self {
            is_playing: false,
            current_time: 0.0,
            duration: 0.0,
            playback_rate: 1.0,
            loop_enabled: false,
            muted: false,
            master_volume: 1.0,
            buffering: false,
            seeking: false,
            loop_start: None,
            loop_end: None,
            playback_mode: PlaybackMode::Normal,
            transport_state: TransportState::Stopped,
            last_update_time: 0.0,
            audio_latency: 0.0,
        }
    }
}

pub struct AudioManager {
    audio_engine: AudioEngine,
    web_audio_manager: WebAudioManager,
    tracks: HashMap<Uuid, AudioTrackSettings>,
    playback_state: AudioPlaybackState,
    timeline_elements: Vec<TimelineElement>,
    master_gain: Option<GainNode>,
    analyser: Option<AnalyserNode>,
    context: Option<AudioContext>,
}

impl AudioManager {
    pub fn new() -> Result<Self, String> {
        let audio_engine = AudioEngine::new()?;
        let config = AudioContextConfig::default();
        let web_audio_manager = WebAudioManager::new(config);
        
        Ok(Self {
            audio_engine,
            web_audio_manager,
            tracks: HashMap::new(),
            playback_state: AudioPlaybackState::default(),
            timeline_elements: Vec::new(),
            master_gain: None,
            analyser: None,
            context: None,
        })
    }

    pub async fn initialize(&mut self) -> Result<(), JsValue> {
        self.web_audio_manager.initialize().await?;
        
        if let Some(context) = self.web_audio_manager.get_context() {
            self.context = Some(context.clone());
            
            // Create master gain node
            let master_gain = context.create_gain()?;
            master_gain.connect_with_audio_node(&context.destination())?;
            self.master_gain = Some(master_gain);
            
            // Create analyser for visualization
            let analyser = context.create_analyser()?;
            analyser.set_fft_size(2048);
            if let Some(ref gain) = self.master_gain {
                gain.connect_with_audio_node(&analyser)?;
            }
            self.analyser = Some(analyser);
        }
        
        Ok(())
    }

    pub fn add_track(&mut self, settings: AudioTrackSettings) {
        self.tracks.insert(settings.id, settings);
    }

    pub fn remove_track(&mut self, track_id: &Uuid) {
        self.tracks.remove(track_id);
    }

    pub fn update_track_settings(&mut self, track_id: &Uuid, settings: AudioTrackSettings) {
        self.tracks.insert(*track_id, settings);
    }

    pub fn get_track_settings(&self, track_id: &Uuid) -> Option<&AudioTrackSettings> {
        self.tracks.get(track_id)
    }

    pub fn set_master_volume(&mut self, volume: f64) -> Result<(), String> {
        self.playback_state.master_volume = volume.clamp(0.0, 2.0);
        
        if let Some(ref gain) = self.master_gain {
            gain.gain().set_value(volume as f32);
        }
        
        Ok(())
    }

    pub fn set_muted(&mut self, muted: bool) -> Result<(), String> {
        self.playback_state.muted = muted;
        
        if let Some(ref gain) = self.master_gain {
            let volume = if muted { 0.0 } else { self.playback_state.master_volume as f32 };
            gain.gain().set_value(volume);
        }
        
        Ok(())
    }

    pub fn play(&mut self) -> Result<(), String> {
        self.playback_state.is_playing = true;
        
        if let Some(ref context) = self.context {
            if context.state() == web_sys::AudioContextState::Suspended {
                let _ = context.resume();
            }
        }
        
        Ok(())
    }

    pub fn pause(&mut self) -> Result<(), String> {
        self.playback_state.is_playing = false;
        Ok(())
    }

    pub fn stop(&mut self) -> Result<(), String> {
        self.playback_state.is_playing = false;
        self.playback_state.current_time = 0.0;
        Ok(())
    }

    pub fn seek(&mut self, time: f64) -> Result<(), String> {
        self.playback_state.current_time = time.clamp(0.0, self.playback_state.duration);
        Ok(())
    }

    pub fn set_playback_rate(&mut self, rate: f64) -> Result<(), String> {
        self.playback_state.playback_rate = rate.clamp(0.1, 4.0);
        Ok(())
    }

    pub fn update_timeline(&mut self, elements: Vec<TimelineElement>) {
        self.timeline_elements = elements.into_iter()
            .filter(|e| e.element_type == ElementType::Audio)
            .collect();
    }

    pub fn get_playback_state(&self) -> &AudioPlaybackState {
        &self.playback_state
    }

    pub fn get_frequency_data(&self) -> Option<Vec<u8>> {
        if let Some(ref analyser) = self.analyser {
            let buffer_length = analyser.frequency_bin_count();
            let mut data = vec![0u8; buffer_length as usize];
            analyser.get_byte_frequency_data(&mut data);
            Some(data)
        } else {
            None
        }
    }

    pub fn get_time_domain_data(&self) -> Option<Vec<u8>> {
        if let Some(ref analyser) = self.analyser {
            let buffer_length = analyser.frequency_bin_count();
            let mut data = vec![0u8; buffer_length as usize];
            analyser.get_byte_time_domain_data(&mut data);
            Some(data)
        } else {
            None
        }
    }

    pub fn connect_media_element(&mut self, element: &HtmlVideoElement) -> Result<(), String> {
        self.audio_engine.connect_video_element(element)
    }

    pub fn get_active_audio_elements(&self, current_time: f64) -> Vec<&TimelineElement> {
        self.timeline_elements.iter()
            .filter(|element| {
                current_time >= element.start_time &&
                current_time < element.start_time + element.duration
            })
            .collect()
    }

    pub async fn load_audio_buffer(&mut self, url: &str) -> Result<web_sys::AudioBuffer, JsValue> {
        if let Some(ref context) = self.context {
            // Fetch the audio file
            let window = web_sys::window().unwrap();
            let response = wasm_bindgen_futures::JsFuture::from(window.fetch_with_str(url)).await?;
            let response: web_sys::Response = response.dyn_into()?;

            // Get array buffer
            let array_buffer = wasm_bindgen_futures::JsFuture::from(response.array_buffer()?).await?;
            let array_buffer: js_sys::ArrayBuffer = array_buffer.dyn_into()?;

            // Decode audio data
            let audio_buffer = wasm_bindgen_futures::JsFuture::from(
                context.decode_audio_data(&array_buffer)?
            ).await?;

            Ok(audio_buffer.dyn_into()?)
        } else {
            Err(JsValue::from_str("Audio context not initialized"))
        }
    }

    pub fn create_buffer_source(&self, buffer: &web_sys::AudioBuffer) -> Result<web_sys::AudioBufferSourceNode, JsValue> {
        if let Some(ref context) = self.context {
            let source = context.create_buffer_source()?;
            source.set_buffer(Some(buffer));

            if let Some(ref master_gain) = self.master_gain {
                source.connect_with_audio_node(master_gain)?;
            }

            Ok(source)
        } else {
            Err(JsValue::from_str("Audio context not initialized"))
        }
    }

    pub fn schedule_audio_playback(&self, source: &web_sys::AudioBufferSourceNode, when: f64, offset: f64, duration: Option<f64>) -> Result<(), JsValue> {
        if let Some(duration) = duration {
            source.start_with_when_and_grain_offset_and_grain_duration(when, offset, duration)?;
        } else {
            source.start_with_when_and_grain_offset(when, offset)?;
        }
        Ok(())
    }

    pub fn get_current_audio_time(&self) -> Option<f64> {
        self.context.as_ref().map(|ctx| ctx.current_time())
    }

    pub fn set_timeline_position(&mut self, position: f64) {
        self.playback_state.current_time = position;
    }

    pub fn get_master_gain_node(&self) -> Option<&GainNode> {
        self.master_gain.as_ref()
    }

    pub fn get_analyser_node(&self) -> Option<&AnalyserNode> {
        self.analyser.as_ref()
    }

    pub fn update_playback_state(&mut self) -> Result<(), JsValue> {
        // Update current time from audio context
        if let Some(ref context) = self.context {
            let current_audio_time = context.current_time();
            self.playback_state.last_update_time = current_audio_time;

            // Calculate audio latency
            self.playback_state.audio_latency = self.calculate_audio_latency();

            // Update transport state based on current conditions
            self.update_transport_state();
        }
        Ok(())
    }

    fn calculate_audio_latency(&self) -> f64 {
        // Estimate audio latency based on buffer size and sample rate
        if let Some(ref context) = self.context {
            // Use estimated latency since base_latency and output_latency are not available in web-sys
            let sample_rate = context.sample_rate() as f64;
            let estimated_buffer_size = 128.0; // Common buffer size for web audio
            estimated_buffer_size / sample_rate
        } else {
            0.0
        }
    }

    fn update_transport_state(&mut self) {
        if self.playback_state.seeking {
            self.playback_state.transport_state = TransportState::Seeking;
        } else if self.playback_state.buffering {
            self.playback_state.transport_state = TransportState::Buffering;
        } else if self.playback_state.is_playing {
            self.playback_state.transport_state = TransportState::Playing;
        } else {
            self.playback_state.transport_state = TransportState::Stopped;
        }
    }

    pub fn set_playback_mode(&mut self, mode: PlaybackMode) {
        // Configure loop settings based on mode
        match &mode {
            PlaybackMode::Loop => {
                self.playback_state.loop_enabled = true;
            },
            PlaybackMode::Normal | PlaybackMode::Scrub => {
                self.playback_state.loop_enabled = false;
            },
            PlaybackMode::PingPong => {
                self.playback_state.loop_enabled = true;
                // PingPong mode would require additional logic for reverse playback
            }
        }

        // Set the mode after using it
        self.playback_state.playback_mode = mode;
    }

    pub fn set_loop_region(&mut self, start: Option<f64>, end: Option<f64>) {
        self.playback_state.loop_start = start;
        self.playback_state.loop_end = end;

        if start.is_some() && end.is_some() {
            self.playback_state.loop_enabled = true;
        }
    }

    pub fn start_seeking(&mut self) {
        self.playback_state.seeking = true;
        self.playback_state.transport_state = TransportState::Seeking;
    }

    pub fn finish_seeking(&mut self) {
        self.playback_state.seeking = false;
        self.update_transport_state();
    }

    pub fn set_buffering(&mut self, buffering: bool) {
        self.playback_state.buffering = buffering;
        self.update_transport_state();
    }

    pub fn get_playback_progress(&self) -> f64 {
        if self.playback_state.duration > 0.0 {
            self.playback_state.current_time / self.playback_state.duration
        } else {
            0.0
        }
    }

    pub fn get_time_remaining(&self) -> f64 {
        (self.playback_state.duration - self.playback_state.current_time).max(0.0)
    }

    pub fn is_in_loop_region(&self) -> bool {
        if let (Some(start), Some(end)) = (self.playback_state.loop_start, self.playback_state.loop_end) {
            self.playback_state.current_time >= start && self.playback_state.current_time <= end
        } else {
            false
        }
    }

    pub fn get_detailed_playback_state(&self) -> DetailedPlaybackState {
        DetailedPlaybackState {
            basic_state: self.playback_state.clone(),
            progress: self.get_playback_progress(),
            time_remaining: self.get_time_remaining(),
            in_loop_region: self.is_in_loop_region(),
            active_tracks: self.tracks.len(),
            timeline_elements: self.timeline_elements.len(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetailedPlaybackState {
    pub basic_state: AudioPlaybackState,
    pub progress: f64,
    pub time_remaining: f64,
    pub in_loop_region: bool,
    pub active_tracks: usize,
    pub timeline_elements: usize,
}

impl Default for AudioManager {
    fn default() -> Self {
        Self::new().unwrap_or_else(|_| {
            // Fallback implementation if audio context creation fails
            Self {
                audio_engine: AudioEngine::new().unwrap(),
                web_audio_manager: WebAudioManager::new(AudioContextConfig::default()),
                tracks: HashMap::new(),
                playback_state: AudioPlaybackState::default(),
                timeline_elements: Vec::new(),
                master_gain: None,
                analyser: None,
                context: None,
            }
        })
    }
}
