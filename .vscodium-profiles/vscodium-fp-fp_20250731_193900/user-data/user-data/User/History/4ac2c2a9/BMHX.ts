import { FFmpeg } from "@ffmpeg/ffmpeg";
import { toBlobURL } from "@ffmpeg/util";

let ffmpeg: FFmpeg | null = null;
let initializationPromise: Promise<FFmpeg> | null = null;

export const initFFmpeg = async (): Promise<FFmpeg> => {
  if (ffmpeg) {
    console.log("✅ FFmpeg already initialized, returning existing instance");
    return ffmpeg;
  }

  // Prevent multiple simultaneous initialization attempts
  if (initializationPromise) {
    console.log("⏳ FFmpeg initialization already in progress, waiting...");
    return initializationPromise;
  }

  console.log("🚀 Initializing FFmpeg...");

  initializationPromise = (async () => {
    try {
      ffmpeg = new FFmpeg();

      // Try local files first, fallback to CDN
      let baseURL = "/ffmpeg";
      let useLocalFiles = true;

      // Test if local files are accessible first
      console.log("🔍 Testing local FFmpeg file accessibility...");
      try {
        const coreResponse = await fetch(`${baseURL}/ffmpeg-core.js`);
        const wasmResponse = await fetch(`${baseURL}/ffmpeg-core.wasm`);

        console.log("📊 Local file accessibility results:", {
          coreStatus: coreResponse.status,
          coreOk: coreResponse.ok,
          wasmStatus: wasmResponse.status,
          wasmOk: wasmResponse.ok,
          coreSize: coreResponse.headers.get('content-length'),
          wasmSize: wasmResponse.headers.get('content-length')
        });

        if (!coreResponse.ok || !wasmResponse.ok) {
          throw new Error(`Local files not accessible: core ${coreResponse.status}, wasm ${wasmResponse.status}`);
        }

        console.log("✅ Local FFmpeg files are accessible");
      } catch (localError) {
        console.warn("⚠️ Local FFmpeg files not accessible, falling back to CDN:", localError);
        useLocalFiles = false;
        baseURL = "https://unpkg.com/@ffmpeg/core@0.12.10/dist/umd";
      }

      console.log(`🔧 Using ${useLocalFiles ? 'local' : 'CDN'} FFmpeg files from: ${baseURL}`);

      const coreURL = await toBlobURL(`${baseURL}/ffmpeg-core.js`, "text/javascript");
      const wasmURL = await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, "application/wasm");

      console.log("🔧 Loading FFmpeg with URLs:", {
        source: useLocalFiles ? 'local' : 'CDN',
        coreURL: coreURL.substring(0, 50) + "...",
        wasmURL: wasmURL.substring(0, 50) + "...",
        coreBlobSize: coreURL.length,
        wasmBlobSize: wasmURL.length
      });

      console.log("🚀 Calling ffmpeg.load()...");
      await ffmpeg.load({
        coreURL,
        wasmURL,
      });

      console.log("🎉 FFmpeg initialized successfully!");
      return ffmpeg;
    } catch (error) {
      console.error("❌ FFmpeg initialization failed:", error);
      console.error("❌ Error details:", {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : 'No stack trace'
      });

      // Reset ffmpeg instance and promise on failure
      ffmpeg = null;
      initializationPromise = null;

      // Provide detailed error information
      if (error instanceof Error) {
        if (error.message.includes('fetch')) {
          throw new Error(`FFmpeg files not accessible. Tried both local files and CDN. Original error: ${error.message}`);
        } else if (error.message.includes('CORS')) {
          throw new Error(`CORS error loading FFmpeg. This might be due to browser security restrictions. Original error: ${error.message}`);
        } else {
          throw new Error(`FFmpeg initialization failed: ${error.message}`);
        }
      }

      throw error;
    }
  })();

  return initializationPromise;
};

export const generateThumbnail = async (
  videoFile: File,
  timeInSeconds = 1,
  width = 320,
  height = 240
): Promise<string> => {
  console.log("🎬 generateThumbnail called, initializing FFmpeg...");
  const ffmpeg = await initFFmpeg();
  console.log("✅ FFmpeg instance obtained:", !!ffmpeg);

  const inputName = `input_${Date.now()}.mp4`;
  const outputName = `thumbnail_${timeInSeconds.toFixed(2)}_${Date.now()}.jpg`;

  // Write input file
  await ffmpeg.writeFile(
    inputName,
    new Uint8Array(await videoFile.arrayBuffer())
  );

  console.log(`🎬 FFmpeg: Generating thumbnail at ${timeInSeconds}s with size ${width}x${height}`);

  // Generate thumbnail at specific time with custom dimensions
  await ffmpeg.exec([
    "-i",
    inputName,
    "-ss",
    timeInSeconds.toString(),
    "-vframes",
    "1",
    "-vf",
    `scale=${width}:${height}`,
    "-q:v",
    "2",
    outputName,
  ]);

  // Read output file
  const data = await ffmpeg.readFile(outputName);
  const blob = new Blob([data], { type: "image/jpeg" });

  // Cleanup
  await ffmpeg.deleteFile(inputName);
  await ffmpeg.deleteFile(outputName);

  const url = URL.createObjectURL(blob);
  console.log(`✅ FFmpeg: Generated thumbnail at ${timeInSeconds}s - ${url.substring(0, 50)}...`);

  return url;
};

export const trimVideo = async (
  videoFile: File,
  startTime: number,
  endTime: number,
  onProgress?: (progress: number) => void
): Promise<Blob> => {
  const ffmpeg = await initFFmpeg();

  const inputName = "input.mp4";
  const outputName = "output.mp4";

  // Set up progress callback
  if (onProgress) {
    ffmpeg.on("progress", ({ progress }) => {
      onProgress(progress * 100);
    });
  }

  // Write input file
  await ffmpeg.writeFile(
    inputName,
    new Uint8Array(await videoFile.arrayBuffer())
  );

  const duration = endTime - startTime;

  // Trim video
  await ffmpeg.exec([
    "-i",
    inputName,
    "-ss",
    startTime.toString(),
    "-t",
    duration.toString(),
    "-c",
    "copy", // Use stream copy for faster processing
    outputName,
  ]);

  // Read output file
  const data = await ffmpeg.readFile(outputName);
  const blob = new Blob([data], { type: "video/mp4" });

  // Cleanup
  await ffmpeg.deleteFile(inputName);
  await ffmpeg.deleteFile(outputName);

  return blob;
};

export const getVideoInfo = async (
  videoFile: File
): Promise<{
  duration: number;
  width: number;
  height: number;
  fps: number;
}> => {
  const ffmpeg = await initFFmpeg();

  const inputName = "input.mp4";

  // Write input file
  await ffmpeg.writeFile(
    inputName,
    new Uint8Array(await videoFile.arrayBuffer())
  );

  // Capture FFmpeg stderr output with a one-time listener pattern
  let ffmpegOutput = "";
  let listening = true;
  const listener = (data: string) => {
    if (listening) ffmpegOutput += data;
  };
  ffmpeg.on("log", ({ message }) => listener(message));

  // Run ffmpeg to get info (stderr will contain the info)
  try {
    await ffmpeg.exec(["-i", inputName, "-f", "null", "-"]);
  } catch (error) {
    listening = false;
    await ffmpeg.deleteFile(inputName);
    console.error("FFmpeg execution failed:", error);
    throw new Error(
      "Failed to extract video info. The file may be corrupted or in an unsupported format."
    );
  }

  // Disable listener after exec completes
  listening = false;

  // Cleanup
  await ffmpeg.deleteFile(inputName);

  // Parse output for duration, resolution, and fps
  // Example: Duration: 00:00:10.00, start: 0.000000, bitrate: 1234 kb/s
  // Example: Stream #0:0: Video: h264 (High), yuv420p(progressive), 1920x1080 [SAR 1:1 DAR 16:9], 30 fps, 30 tbr, 90k tbn, 60 tbc

  const durationMatch = ffmpegOutput.match(/Duration: (\d+):(\d+):([\d.]+)/);
  let duration = 0;
  if (durationMatch) {
    const [, h, m, s] = durationMatch;
    duration = parseInt(h) * 3600 + parseInt(m) * 60 + parseFloat(s);
  }

  const videoStreamMatch = ffmpegOutput.match(
    /Video:.* (\d+)x(\d+)[^,]*, ([\d.]+) fps/
  );
  let width = 0,
    height = 0,
    fps = 0;
  if (videoStreamMatch) {
    width = parseInt(videoStreamMatch[1]);
    height = parseInt(videoStreamMatch[2]);
    fps = parseFloat(videoStreamMatch[3]);
  }

  return {
    duration,
    width,
    height,
    fps,
  };
};

export const convertToWebM = async (
  videoFile: File,
  onProgress?: (progress: number) => void
): Promise<Blob> => {
  const ffmpeg = await initFFmpeg();

  const inputName = "input.mp4";
  const outputName = "output.webm";

  // Set up progress callback
  if (onProgress) {
    ffmpeg.on("progress", ({ progress }) => {
      onProgress(progress * 100);
    });
  }

  // Write input file
  await ffmpeg.writeFile(
    inputName,
    new Uint8Array(await videoFile.arrayBuffer())
  );

  // Convert to WebM
  await ffmpeg.exec([
    "-i",
    inputName,
    "-c:v",
    "libvpx-vp9",
    "-crf",
    "30",
    "-b:v",
    "0",
    "-c:a",
    "libopus",
    outputName,
  ]);

  // Read output file
  const data = await ffmpeg.readFile(outputName);
  const blob = new Blob([data], { type: "video/webm" });

  // Cleanup
  await ffmpeg.deleteFile(inputName);
  await ffmpeg.deleteFile(outputName);

  return blob;
};

export const extractAudio = async (
  videoFile: File,
  format: "mp3" | "wav" = "mp3"
): Promise<Blob> => {
  const ffmpeg = await initFFmpeg();

  const inputName = "input.mp4";
  const outputName = `output.${format}`;

  // Write input file
  await ffmpeg.writeFile(
    inputName,
    new Uint8Array(await videoFile.arrayBuffer())
  );

  // Extract audio
  await ffmpeg.exec([
    "-i",
    inputName,
    "-vn", // Disable video
    "-acodec",
    format === "mp3" ? "libmp3lame" : "pcm_s16le",
    outputName,
  ]);

  // Read output file
  const data = await ffmpeg.readFile(outputName);
  const blob = new Blob([data], { type: `audio/${format}` });

  // Cleanup
  await ffmpeg.deleteFile(inputName);
  await ffmpeg.deleteFile(outputName);

  return blob;
};
