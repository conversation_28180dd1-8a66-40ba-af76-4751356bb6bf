use wasm_bindgen::prelude::*;
use web_sys::{AudioContext, AudioBuffer, AudioBufferSourceNode};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use uuid::Uuid;

use crate::utils::audio_manager::{AudioManager, AudioPlaybackState};
use crate::utils::audio_effects_system::{AudioEffectsProcessor, AudioElementEffects, AudioProcessingStats};
use crate::utils::audio_timeline_sync::{AudioTimelineSync, AudioSyncSettings, AudioSyncStats};
use crate::types::timeline::{TimelineElement, ElementType};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AudioSystemConfig {
    pub sample_rate: f32,
    pub buffer_size: u32,
    pub max_channels: u32,
    pub enable_effects: bool,
    pub enable_sync: bool,
    pub sync_settings: AudioSyncSettings,
}

impl Default for AudioSystemConfig {
    fn default() -> Self {
        Self {
            sample_rate: 44100.0,
            buffer_size: 256,
            max_channels: 2,
            enable_effects: true,
            enable_sync: true,
            sync_settings: AudioSyncSettings::default(),
        }
    }
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize, PartialEq)]
pub struct AudioSystemStatus {
    pub is_initialized: bool,
    pub playback_state: AudioPlaybackState,
    pub loaded_buffers: usize,
    pub active_sources: usize,
    pub effects_enabled: bool,
    pub sync_enabled: bool,
    pub effects_stats: Option<AudioProcessingStats>,
    pub sync_stats: Option<AudioSyncStats>,
}

pub struct AudioSystemCoordinator {
    audio_manager: AudioManager,
    effects_processor: Option<AudioEffectsProcessor>,
    timeline_sync: Option<AudioTimelineSync>,
    config: AudioSystemConfig,
    audio_buffers: HashMap<String, AudioBuffer>,
    active_sources: HashMap<Uuid, AudioBufferSourceNode>,
    is_initialized: bool,
}

impl AudioSystemCoordinator {
    pub fn new(config: AudioSystemConfig) -> Result<Self, String> {
        let audio_manager = AudioManager::new()?;
        
        Ok(Self {
            audio_manager,
            effects_processor: None,
            timeline_sync: None,
            config,
            audio_buffers: HashMap::new(),
            active_sources: HashMap::new(),
            is_initialized: false,
        })
    }

    pub async fn initialize(&mut self) -> Result<(), JsValue> {
        // Initialize audio manager
        self.audio_manager.initialize().await?;

        // Create shared audio context for all components
        let shared_context = AudioContext::new()?;

        // Initialize effects processor if enabled
        if self.config.enable_effects {
            self.effects_processor = Some(AudioEffectsProcessor::new(shared_context.clone()));
        }

        // Initialize timeline sync if enabled
        if self.config.enable_sync {
            let master_gain = self.audio_manager.get_master_gain_node().cloned();
            let mut timeline_sync = AudioTimelineSync::new(Some(shared_context.clone()), master_gain);
            timeline_sync.set_sync_settings(self.config.sync_settings.clone());
            self.timeline_sync = Some(timeline_sync);
        }

        self.is_initialized = true;
        Ok(())
    }

    pub fn get_shared_audio_context(&self) -> Option<AudioContext> {
        // Return the shared audio context if available
        if let Some(ref processor) = self.effects_processor {
            // In practice, we'd expose the context from the processor
            // For now, create a new one (not ideal but functional)
            AudioContext::new().ok()
        } else {
            None
        }
    }

    pub async fn load_audio_file(&mut self, id: String, url: &str) -> Result<(), JsValue> {
        let buffer = self.audio_manager.load_audio_buffer(url).await?;
        self.audio_buffers.insert(id, buffer);
        Ok(())
    }

    pub fn add_timeline_elements(&mut self, elements: &[TimelineElement]) -> Result<(), JsValue> {
        // Filter audio elements
        let audio_elements: Vec<_> = elements.iter()
            .filter(|e| e.element_type == ElementType::Audio)
            .cloned()
            .collect();

        // Update audio manager
        self.audio_manager.update_timeline(audio_elements.clone());

        // Add to effects processor
        if let Some(ref mut effects) = self.effects_processor {
            for element in &audio_elements {
                effects.add_audio_element(element.id.to_string())?;
            }
        }

        // Add to timeline sync
        if let Some(ref mut sync) = self.timeline_sync {
            sync.add_timeline_elements(&audio_elements);
        }

        Ok(())
    }

    pub fn play(&mut self) -> Result<(), String> {
        self.audio_manager.play()?;
        
        if let Some(ref mut sync) = self.timeline_sync {
            sync.start_sync();
        }
        
        Ok(())
    }

    pub fn pause(&mut self) -> Result<(), String> {
        self.audio_manager.pause()?;
        
        if let Some(ref mut sync) = self.timeline_sync {
            sync.stop_sync();
        }
        
        Ok(())
    }

    pub fn stop(&mut self) -> Result<(), String> {
        self.audio_manager.stop()?;
        
        if let Some(ref mut sync) = self.timeline_sync {
            sync.stop_sync();
        }
        
        // Stop all active sources
        for (_, source) in self.active_sources.drain() {
            let _ = source.stop();
        }
        
        Ok(())
    }

    pub fn seek(&mut self, time: f64) -> Result<(), String> {
        self.audio_manager.seek(time)?;
        self.audio_manager.set_timeline_position(time);
        
        if let Some(ref mut sync) = self.timeline_sync {
            sync.seek_to_position(time);
        }
        
        Ok(())
    }

    pub fn set_master_volume(&mut self, volume: f64) -> Result<(), String> {
        self.audio_manager.set_master_volume(volume)
    }

    pub fn set_muted(&mut self, muted: bool) -> Result<(), String> {
        self.audio_manager.set_muted(muted)
    }

    pub fn update_timeline_position(&mut self, position: f64) {
        self.audio_manager.set_timeline_position(position);
        
        if let Some(ref mut sync) = self.timeline_sync {
            sync.update_timeline_position(position);
        }
    }

    pub fn get_playback_state(&self) -> &AudioPlaybackState {
        self.audio_manager.get_playback_state()
    }

    pub fn get_frequency_data(&self) -> Option<Vec<u8>> {
        self.audio_manager.get_frequency_data()
    }

    pub fn get_time_domain_data(&self) -> Option<Vec<u8>> {
        self.audio_manager.get_time_domain_data()
    }

    pub fn update_element_effects(&mut self, element_id: &str, effects: AudioElementEffects) -> Result<(), JsValue> {
        if let Some(ref mut processor) = self.effects_processor {
            processor.update_element_effects(element_id, effects)?;
        }
        Ok(())
    }

    pub fn set_element_volume(&mut self, element_id: &str, volume: f32) -> Result<(), JsValue> {
        if let Some(ref mut processor) = self.effects_processor {
            processor.set_volume(element_id, volume)?;
        }
        Ok(())
    }

    pub fn set_element_pan(&mut self, element_id: &str, pan: f32) -> Result<(), JsValue> {
        if let Some(ref mut processor) = self.effects_processor {
            processor.set_pan(element_id, pan)?;
        }
        Ok(())
    }

    pub fn apply_fade(&mut self, element_id: &str, start_time: f64, total_duration: f32) -> Result<(), JsValue> {
        if let Some(ref mut processor) = self.effects_processor {
            processor.apply_fade_automation(element_id, start_time, total_duration)?;
        }
        Ok(())
    }

    pub fn is_initialized(&self) -> bool {
        self.is_initialized
    }

    pub fn get_config(&self) -> &AudioSystemConfig {
        &self.config
    }

    pub fn update_config(&mut self, config: AudioSystemConfig) {
        self.config = config;
        
        // Update sync settings if timeline sync is active
        if let Some(ref mut sync) = self.timeline_sync {
            sync.set_sync_settings(self.config.sync_settings.clone());
        }
    }

    pub fn get_active_elements_count(&self) -> usize {
        if let Some(ref sync) = self.timeline_sync {
            sync.get_active_elements_count()
        } else {
            0
        }
    }

    pub fn cleanup(&mut self) -> Result<(), JsValue> {
        // Stop all audio
        let _ = self.stop();

        // Clear buffers
        self.audio_buffers.clear();

        // Reset components
        if let Some(ref mut processor) = self.effects_processor {
            processor.reset_all_effects()?;
        }

        if let Some(ref mut sync) = self.timeline_sync {
            sync.clear_all_elements();
        }

        Ok(())
    }

    pub fn get_system_status(&self) -> AudioSystemStatus {
        let effects_stats = self.effects_processor.as_ref()
            .map(|p| p.get_audio_processing_stats());

        let sync_stats = self.timeline_sync.as_ref()
            .map(|s| s.get_sync_stats());

        AudioSystemStatus {
            is_initialized: self.is_initialized,
            playback_state: self.audio_manager.get_playback_state().clone(),
            loaded_buffers: self.audio_buffers.len(),
            active_sources: self.active_sources.len(),
            effects_enabled: self.config.enable_effects,
            sync_enabled: self.config.enable_sync,
            effects_stats,
            sync_stats,
        }
    }

    pub fn synchronize_all_systems(&mut self) -> Result<(), JsValue> {
        // Ensure all systems are synchronized with current state
        let current_position = self.audio_manager.get_playback_state().current_time;

        // Update timeline sync
        if let Some(ref mut sync) = self.timeline_sync {
            sync.update_timeline_position(current_position);
        }

        // Update effects processor timing
        if let Some(ref mut processor) = self.effects_processor {
            processor.cleanup_inactive_processors();
        }

        Ok(())
    }

    pub fn get_real_time_audio_levels(&self) -> HashMap<String, f32> {
        let mut levels = HashMap::new();

        if let Some(ref processor) = self.effects_processor {
            // Get levels for all active elements
            for (id, _) in &self.active_sources {
                if let Some(level) = processor.get_real_time_level(&id.to_string()) {
                    levels.insert(id.to_string(), level);
                }
            }
        }

        levels
    }

    pub fn optimize_performance(&mut self) -> Result<(), JsValue> {
        // Cleanup inactive processors
        if let Some(ref mut processor) = self.effects_processor {
            processor.cleanup_inactive_processors();
        }

        // Remove finished audio sources
        self.active_sources.retain(|_, source| {
            // Check if source is still playing (this is a simplified check)
            true // In practice, we'd check the source state
        });

        // Synchronize systems
        self.synchronize_all_systems()?;

        Ok(())
    }

    // Audio Effects Integration Methods
    pub fn set_element_volume(&mut self, element_id: Uuid, volume: f32) -> Result<(), JsValue> {
        if let Some(ref mut processor) = self.effects_processor {
            processor.set_element_volume(&element_id.to_string(), volume)?;
        }
        Ok(())
    }

    pub fn set_element_muted(&mut self, element_id: Uuid, muted: bool) -> Result<(), JsValue> {
        if let Some(ref mut processor) = self.effects_processor {
            processor.set_element_muted(&element_id.to_string(), muted)?;
        }
        Ok(())
    }

    pub fn set_element_pan(&mut self, element_id: Uuid, pan: f32) -> Result<(), JsValue> {
        if let Some(ref mut processor) = self.effects_processor {
            processor.set_element_pan(&element_id.to_string(), pan)?;
        }
        Ok(())
    }

    pub fn apply_element_filter(&mut self, element_id: Uuid, filter_settings: crate::utils::audio_effects_system::FilterSettings) -> Result<(), JsValue> {
        if let Some(ref mut processor) = self.effects_processor {
            processor.apply_element_filter(&element_id.to_string(), filter_settings)?;
        }
        Ok(())
    }

    pub fn update_element_effects(&mut self, element_id: Uuid, effects: crate::utils::audio_effects_system::AudioElementEffects) -> Result<(), JsValue> {
        if let Some(ref mut processor) = self.effects_processor {
            processor.update_element_effects(&element_id.to_string(), effects)?;
        }
        Ok(())
    }
}

impl Default for AudioSystemCoordinator {
    fn default() -> Self {
        Self::new(AudioSystemConfig::default()).unwrap()
    }
}
