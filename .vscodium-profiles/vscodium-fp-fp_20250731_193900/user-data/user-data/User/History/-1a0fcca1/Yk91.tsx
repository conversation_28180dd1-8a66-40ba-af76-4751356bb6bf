"use client";

import React, { useRef, useEffect, useState } from 'react';
import { TIMELINE_CONSTANTS } from '@/constants/timeline-constants';
import { useTimelineZoom } from '@/hooks/use-timeline-zoom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useMediaStore } from '@/stores/media-store';
import { useProjectStore } from '@/stores/project-store';
import { useTimelineStore } from '@/stores/timeline-store';
import { processMediaFiles } from '@/lib/media-processing';
import { toast } from 'sonner';
import { Upload, Loader2, Image, Video, Music } from 'lucide-react';

interface DebugData {
  timestamp: string;
  containerWidth: number | null;
  fallbackUsed: boolean;
  dynamicWidth: number;
  zoomLevel: number;
  duration: number;
  currentTime: number;
}

export function TimelineDebugTest() {
  const timelineRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [debugData, setDebugData] = useState<DebugData[]>([]);
  const [duration, setDuration] = useState(10);
  const [currentTime] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Store hooks
  const { mediaItems, addMediaItem } = useMediaStore();
  const { activeProject } = useProjectStore();
  const { addMediaToNewTrack } = useTimelineStore();

  // Test Assumption 2: Use actual zoom hook to track state changes
  const { zoomLevel, setZoomLevel } = useTimelineZoom({
    containerRef: timelineRef,
    isInTimeline: true
  });

  const calculateDynamicWidth = () => {
    const containerWidth = timelineRef.current?.clientWidth;
    const dynamicTimelineWidth = Math.max(
      (duration || 0) * TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel,
      (currentTime + 30) * TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel,
      containerWidth || 1000 // This is the potential issue
    );

    const newDebugData: DebugData = {
      timestamp: new Date().toISOString(),
      containerWidth: containerWidth || null,
      fallbackUsed: !containerWidth,
      dynamicWidth: dynamicTimelineWidth,
      zoomLevel,
      duration,
      currentTime
    };

    setDebugData(prev => [...prev.slice(-9), newDebugData]); // Keep last 10 entries
    
    console.log('🔍 Timeline Debug Test:', newDebugData);
    
    return dynamicTimelineWidth;
  };

  useEffect(() => {
    calculateDynamicWidth();
  }, [zoomLevel, duration, currentTime]);

  useEffect(() => {
    const resizeObserver = new ResizeObserver(() => {
      console.log('🔍 Container Resized');
      calculateDynamicWidth();
    });

    if (timelineRef.current) {
      resizeObserver.observe(timelineRef.current);
    }

    return () => resizeObserver.disconnect();
  }, []);

  const triggerResize = () => {
    if (timelineRef.current) {
      timelineRef.current.style.width = timelineRef.current.style.width === '50%' ? '100%' : '50%';
    }
  };

  const testZoomReset = () => {
    console.log('🔍 Testing Zoom Reset - Before:', zoomLevel);
    setZoomLevel(1); // Reset to default
    console.log('🔍 Testing Zoom Reset - After reset to 1');
  };

  const testZoomChange = () => {
    const newZoom = zoomLevel === 1 ? 2 : 1;
    console.log('🔍 Testing Zoom Change - From:', zoomLevel, 'To:', newZoom);
    setZoomLevel(newZoom);
  };

  const testDurationChange = () => {
    const newDuration = duration === 10 ? 20 : 10;
    console.log('🔍 Testing Duration Change - From:', duration, 'To:', newDuration);
    setDuration(newDuration);
  };

  const simulateTrackChange = () => {
    // Simulate what happens when tracks change and duration is recalculated
    const randomDuration = Math.floor(Math.random() * 30) + 5; // 5-35 seconds
    console.log('🔍 Simulating Track Change - New Duration:', randomDuration);
    setDuration(randomDuration);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Timeline Container Width Debug Test</CardTitle>
        <CardDescription>
          Testing timeline width calculation and container reference issues
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={triggerResize}
            variant="outline"
            size="sm"
          >
            Toggle Container Width
          </Button>
          <Button
            onClick={testZoomChange}
            variant="outline"
            size="sm"
          >
            Toggle Zoom Level
          </Button>
          <Button
            onClick={testZoomReset}
            variant="destructive"
            size="sm"
          >
            Reset Zoom to 1x
          </Button>
          <Button
            onClick={testDurationChange}
            variant="secondary"
            size="sm"
          >
            Toggle Duration
          </Button>
          <Button
            onClick={simulateTrackChange}
            variant="secondary"
            size="sm"
          >
            Simulate Track Change
          </Button>
        </div>

        <div className="p-4 bg-muted/50 rounded-lg border">
          <div className="text-sm font-medium mb-2">Current State:</div>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>Zoom Level: <Badge variant="outline">{zoomLevel}x</Badge></div>
            <div>Duration: <Badge variant="outline">{duration}s</Badge></div>
          </div>
        </div>

        <div
          ref={timelineRef}
          className="border-2 border-dashed border-muted h-20 transition-all duration-300 rounded-lg bg-muted/20"
          style={{ width: '100%' }}
        >
          <div className="p-2 text-sm text-muted-foreground">
            Timeline Container (resize me to test)
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">Debug Data (Last 10 calculations):</h4>
          <div className="max-h-60 overflow-y-auto space-y-2">
            {debugData.map((data, index) => (
              <div
                key={index}
                className={`text-xs p-3 rounded-lg border ${
                  data.fallbackUsed
                    ? 'bg-destructive/10 border-destructive/20'
                    : 'bg-green-500/10 border-green-500/20'
                }`}
              >
                <div className="grid grid-cols-2 gap-2">
                  <div><strong>Time:</strong> {new Date(data.timestamp).toLocaleTimeString()}</div>
                  <div><strong>Container Width:</strong> {data.containerWidth || 'NULL (using fallback!)'}</div>
                  <div><strong>Dynamic Width:</strong> {data.dynamicWidth}px</div>
                  <div><strong>Fallback Used:</strong> {data.fallbackUsed ? 'YES ⚠️' : 'NO ✅'}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
