"use client";

import React, { useRef, useEffect, useState } from 'react';
import { TIMELINE_CONSTANTS } from '@/constants/timeline-constants';
import { useTimelineZoom } from '@/hooks/use-timeline-zoom';

interface DebugData {
  timestamp: string;
  containerWidth: number | null;
  fallbackUsed: boolean;
  dynamicWidth: number;
  zoomLevel: number;
  duration: number;
  currentTime: number;
}

export function TimelineDebugTest() {
  const timelineRef = useRef<HTMLDivElement>(null);
  const [debugData, setDebugData] = useState<DebugData[]>([]);
  const [duration, setDuration] = useState(10);
  const [currentTime] = useState(0);

  // Test Assumption 2: Use actual zoom hook to track state changes
  const { zoomLevel, setZoomLevel } = useTimelineZoom({
    containerRef: timelineRef,
    isInTimeline: true
  });

  const calculateDynamicWidth = () => {
    const containerWidth = timelineRef.current?.clientWidth;
    const dynamicTimelineWidth = Math.max(
      (duration || 0) * TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel,
      (currentTime + 30) * TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel,
      containerWidth || 1000 // This is the potential issue
    );

    const newDebugData: DebugData = {
      timestamp: new Date().toISOString(),
      containerWidth: containerWidth || null,
      fallbackUsed: !containerWidth,
      dynamicWidth: dynamicTimelineWidth,
      zoomLevel,
      duration,
      currentTime
    };

    setDebugData(prev => [...prev.slice(-9), newDebugData]); // Keep last 10 entries
    
    console.log('🔍 Timeline Debug Test:', newDebugData);
    
    return dynamicTimelineWidth;
  };

  useEffect(() => {
    calculateDynamicWidth();
  }, [zoomLevel, duration, currentTime]);

  useEffect(() => {
    const resizeObserver = new ResizeObserver(() => {
      console.log('🔍 Container Resized');
      calculateDynamicWidth();
    });

    if (timelineRef.current) {
      resizeObserver.observe(timelineRef.current);
    }

    return () => resizeObserver.disconnect();
  }, []);

  const triggerResize = () => {
    if (timelineRef.current) {
      timelineRef.current.style.width = timelineRef.current.style.width === '50%' ? '100%' : '50%';
    }
  };

  const testZoomReset = () => {
    console.log('🔍 Testing Zoom Reset - Before:', zoomLevel);
    setZoomLevel(1); // Reset to default
    console.log('🔍 Testing Zoom Reset - After reset to 1');
  };

  const testZoomChange = () => {
    const newZoom = zoomLevel === 1 ? 2 : 1;
    console.log('🔍 Testing Zoom Change - From:', zoomLevel, 'To:', newZoom);
    setZoomLevel(newZoom);
  };

  return (
    <div className="p-4 border rounded-lg bg-background">
      <h3 className="text-lg font-semibold mb-4">Timeline Container Width Debug Test</h3>
      
      <div className="mb-4 space-x-2">
        <button
          onClick={triggerResize}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Toggle Container Width (Test Assumption 1)
        </button>
        <button
          onClick={testZoomChange}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Toggle Zoom Level (Test Assumption 2)
        </button>
        <button
          onClick={testZoomReset}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          Reset Zoom to 1x
        </button>
      </div>

      <div className="mb-4 p-3 bg-gray-50 rounded border">
        <div className="text-sm font-medium">Current State:</div>
        <div className="text-sm text-gray-600">Zoom Level: <span className="font-mono">{zoomLevel}x</span></div>
        <div className="text-sm text-gray-600">Duration: <span className="font-mono">{duration}s</span></div>
      </div>

      <div
        ref={timelineRef}
        className="border-2 border-dashed border-gray-400 h-20 mb-4 transition-all duration-300"
        style={{ width: '100%' }}
      >
        <div className="p-2 text-sm text-gray-600">
          Timeline Container (resize me to test)
        </div>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium">Debug Data (Last 10 calculations):</h4>
        <div className="max-h-60 overflow-y-auto space-y-1">
          {debugData.map((data, index) => (
            <div 
              key={index} 
              className={`text-xs p-2 rounded ${data.fallbackUsed ? 'bg-red-100 border border-red-300' : 'bg-green-100 border border-green-300'}`}
            >
              <div><strong>Time:</strong> {new Date(data.timestamp).toLocaleTimeString()}</div>
              <div><strong>Container Width:</strong> {data.containerWidth || 'NULL (using fallback!)'}</div>
              <div><strong>Dynamic Width:</strong> {data.dynamicWidth}px</div>
              <div><strong>Fallback Used:</strong> {data.fallbackUsed ? 'YES ⚠️' : 'NO ✅'}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
