---
alwaysApply: true
---

<?xml version="1.0" encoding="UTF-8"?>
<llvm_developer_policy>

    <table_of_contents>
        <section name="Introduction"/>
        <section name="Developer Policies">
            <subsection name="Communication Channels"/>
            <subsection name="Making and Submitting a Patch"/>
            <subsection name="Email Addresses"/>
            <subsection name="Code Reviews"/>
            <subsection name="Maintainers"/>
            <subsection name="Test Cases"/>
            <subsection name="Release Notes"/>
            <subsection name="Quality"/>
            <subsection name="Commit messages"/>
            <subsection name="Patch reversion policy"/>
            <subsection name="Obtaining Commit Access"/>
            <subsection name="Obtaining Other Access or Permissions"/>
            <subsection name="Proposing Major Changes (RFCs)"/>
            <subsection name="Incremental Development"/>
            <subsection name="Making Potentially Breaking Changes"/>
            <subsection name="Attribution of Changes"/>
            <subsection name="Bans"/>
            <subsection name="IR Backwards Compatibility"/>
            <subsection name="C API Changes"/>
            <subsection name="Updating Toolchain Requirements"/>
            <subsection name="Working with the CI system"/>
        </section>
        <section name="Introducing New Components into LLVM">
            <subsection name="Adding a New Target"/>
            <subsection name="Adding an Established Project To the LLVM Monorepo"/>
            <subsection name="Incubating New Projects"/>
        </section>
        <section name="Copyright, License, and Patents">
            <subsection name="Copyright">
                <subsubsection name="Embedded Copyright or 'Contributed by' Statements"/>
            </subsection>
            <subsection name="Relicensing"/>
            <subsection name="New LLVM Project License Framework"/>
            <subsection name="Patents"/>
            <subsection name="Legacy License Structure"/>
            <subsection name="AI generated contributions"/>
        </section>
    </table_of_contents>

    <introduction>
        <purpose>
            This document contains the LLVM Developer Policy which defines the project's policy towards developers and their contributions. The intent of this policy is to eliminate miscommunication, rework, and confusion that might arise from the distributed nature of LLVM's development.
        </purpose>
        
        <scope>
            This policy covers all llvm.org subprojects, including Clang, LLDB, libc++, MLIR, etc.
        </scope>

        <objectives>
            <objective>Attract both users and new contributors to the LLVM project</objective>
            <objective>Help people contribute to LLVM by documenting our development practices</objective>
            <objective>Maintain the stability, performance, and quality of the main branch</objective>
            <objective>Establish the project's copyright, license, and patent policies</objective>
        </objectives>
    </introduction>

    <developer_policies>
        <communication_channels>
            <description>
                LLVM is a large project with many subcomponents, and it has a wide array of communication channels that you can use to keep track of recent developments, upcoming projects, new designs, enhancements, and other community business.
            </description>
            
            <primary_channels>
                <channel name="LLVM Discourse forums" type="primary">
                    <description>The successor to former mailing lists (llvm-dev@, cfe-dev@, lldb-dev@, etc). Most vital and active communication channel for the distributed open source project.</description>
                    <features>
                        <feature>Long-form asynchronous text communication</feature>
                        <feature>Major change proposals and RFCs (Request For Comment)</feature>
                        <feature>Public and archived discussions</feature>
                    </features>
                    <note>Notices of confidentiality or non-disclosure cannot be respected</note>
                </channel>
                
                <channel name="GitHub Pull Requests" type="code_contributions">
                    <description>Code contributions are accepted as GitHub Pull Requests</description>
                    <notification_system>
                        <team_prefix>pr-subscribers-*</team_prefix>
                        <purpose>Notifications for specific code paths</purpose>
                    </notification_system>
                </channel>
                
                <channel name="GitHub Issue Tracker" type="bug_tracking">
                    <description>Missing features and bugs are tracked through GitHub issue tracker</description>
                    <notification_system>
                        <team_prefix>issue-subscribers-*</team_prefix>
                        <email_list>llvm-bugs</email_list>
                        <purpose>Component-specific or all issue notifications</purpose>
                    </notification_system>
                </channel>
                
                <channel name="Discord Server" type="real_time">
                    <description>Real-time chat communication</description>
                </channel>
                
                <channel name="Community Calendar" type="meetings">
                    <description>Regular workgroup video calls and office hours</description>
                </channel>
            </primary_channels>
        </communication_channels>

        <patch_submission>
            <process>
                <step>Patches are submitted to GitHub and reviewed using Pull Requests</step>
                <step>Follow the Getting Started Guide to check out sources and make a patch</step>
                <step>Follow the GitHub Pull Request guide to upload a pull request</step>
            </process>
            
            <success_tips>
                <tip>Include a test - this tends to be one of the first things a reviewer will ask for</tip>
                <tip>Identify 2-3 individuals to review the patch by looking through Maintainers file or git blame</tip>
                <tip>Base patches on recent commit from main to avoid precommit CI failures</tip>
                <tip>For release branch changes, land in main first then follow backporting instructions</tip>
            </success_tips>
            
            <restrictions>
                <restriction>Do not add confidentiality or non-disclosure notices to patches</restriction>
                <reason>These notices conflict with LLVM licensing terms and may result in contribution exclusion</reason>
            </restrictions>
        </patch_submission>

        <email_requirements>
            <requirement>Contributors must have a public email address associated with their GitHub commits</requirement>
            <setting>Ensure "Keep my email addresses private" is disabled in GitHub account settings</setting>
            <purpose>LLVM project uses email to communicate about past contributions, primarily for buildbot infrastructure notifications</purpose>
            <privacy_option>Free email forwarding services available for identity privacy</privacy_option>
        </email_requirements>

        <code_reviews>
            <policy_reference>See LLVM Code-Review Policy and Practices for detailed information</policy_reference>
            <rationale>Code review is a generally accepted software engineering best practice for maintaining high code quality</rationale>
        </code_reviews>

        <maintainers>
            <purpose>
                The LLVM Project aims to evolve features quickly while continually being in a release-ready state. Maintainers are volunteers who do the less glamorous work to ensure robust, high-quality products.
            </purpose>

            <definition>
                Maintainers are regular contributors who volunteer to take on additional community responsibilities beyond code contributions.
            </definition>

            <responsibilities>
                <responsibility>Ensure commits receive high-quality review, either by the maintainer or by someone else</responsibility>
                <responsibility>Help to confirm and comment on issues</responsibility>
                <responsibility>Mediate code review disagreements through collaboration with other maintainers</responsibility>
                <responsibility>Actively engage with relevant RFCs</responsibility>
                <responsibility>Aid release managers with backporting and other release-related activities</responsibility>
                <responsibility>Be a point of contact for contributors who need help</responsibility>
            </responsibilities>

            <lead_maintainers>
                <description>Each top-level project specifies one or more lead maintainers responsible for ensuring community needs are met for that project</description>
                <scope>Responsibilities span the project rather than a limited area within the project</scope>
                <fallback>If a project has no active lead maintainers, it may be a candidate for removal from the monorepo</fallback>
            </lead_maintainers>

            <eligibility>
                <requirement>All contributors with commit access to the LLVM Project are eligible to be a maintainer</requirement>
                <commitments>
                    <commitment>Engaging in responsibilities the majority of days in a month</commitment>
                    <commitment>Ensuring they and community members abide by the LLVM Community Code of Conduct</commitment>
                    <commitment>Performing duties for at least three months</commitment>
                </commitments>
            </eligibility>

            <becoming_maintainer>
                <method>Volunteer yourself by posting a PR adding yourself to the area(s)</method>
                <method>Be nominated by existing maintainer via PR (nominee must explicitly accept)</method>
                <approval_criteria>At least one maintainer in same project vouches for ability and no explicit objections from community</approval_criteria>
            </becoming_maintainer>

            <stepping_down>
                <voluntary>Move name to "inactive maintainers" section or remove entirely - no PR review necessary</voluntary>
                <involuntary>Any maintainer not actively performing responsibilities can be moved to inactive by another active maintainer with agreement from one other active maintainer</involuntary>
                <resuming>Post PR moving name from inactive to active maintainers list</resuming>
            </stepping_down>
        </maintainers>

        <test_cases>
            <requirement>Developers are required to create test cases for any bugs fixed and any new features added</requirement>

            <guidelines>
                <guideline>All feature and regression test cases are added to the test subdirectory of each LLVM subproject</guideline>
                <guideline>Prefer functional changes tested using FileCheck and appropriate tool (opt for IR transformations, llc for backend changes, clang for frontend changes)</guideline>
                <guideline>Some subprojects have project-specific testing tools (clang -verify flag, clangd -lit-test flag)</guideline>
                <guideline>Changes to libraries not directly observable through tool invocations are best tested with unit tests in unittests subdirectory</guideline>
                <guideline>Test cases should be targeted - large inputs should be reduced with tools like llvm-reduce</guideline>
                <guideline>Avoid links to resources not available to entire community</guideline>
            </guidelines>

            <test_separation>
                <in_tree>Small in-tree tests preferred</in_tree>
                <out_of_tree>Large integration tests should be added to llvm-test-suite repository</out_of_tree>
                <purpose>llvm-test-suite is for integration and application testing, not feature or regression testing</purpose>
            </test_separation>
        </test_cases>

        <release_notes>
            <location>Typically found in docs/ReleaseNotes.rst for each project</location>
            <when_to_add>Changes that are user-facing or that users may wish to know about should be added at author's or code reviewer's discretion</when_to_add>

            <examples_warranting_notes>
                <example>Adding, removing, or modifying command-line options</example>
                <example>Adding, removing, or regrouping a diagnostic</example>
                <example>Fixing bugs with significant user-facing impact (link to issue in bug database)</example>
                <example>Adding or removing optimizations with widespread impact or enabling new programming paradigms</example>
                <example>Modifying a C stable API</example>
                <example>Notifying users about potentially disruptive changes expected in future releases</example>
            </examples_warranting_notes>

            <breaking_changes_section>
                <purpose>Potentially Breaking Changes section for disruptive future changes</purpose>
                <announcement_requirement>New entries should be announced in Announcements channel on Discourse</announcement_requirement>
            </breaking_changes_section>
        </release_notes>

        <quality_standards>
            <minimum_requirements>
                <requirement>Code must adhere to the LLVM Coding Standards</requirement>
                <requirement>Code must compile cleanly (no errors, no warnings) on at least one platform</requirement>
                <requirement>Bug fixes and new features should include a testcase</requirement>
                <requirement>Code must pass the llvm/test test suite</requirement>
                <requirement>Code must not cause regressions on reasonable subset of llvm-test</requirement>
                <requirement>Links in source code and test files must point to publicly available resources</requirement>
            </minimum_requirements>

            <committer_responsibilities>
                <responsibility>Code should compile cleanly on all supported platforms</responsibility>
                <responsibility>Changes should not cause correctness regressions in llvm-test suite</responsibility>
                <responsibility>Change set should not cause performance or correctness regressions for LLVM tools</responsibility>
                <responsibility>Changes should not cause regressions in code compiled by LLVM on all applicable targets</responsibility>
                <responsibility>Address any GitHub Issues that result from your change</responsibility>
            </committer_responsibilities>

            <monitoring>
                <recommendation>Check nightly testers for regressions the day after your change</recommendation>
                <notification>Build bots will directly email you if commits including yours caused a failure</notification>
                <action>Expected to check build bot messages and fix breakage if it's your fault</action>
            </monitoring>

            <reversion_policy>
                <condition>Commits that violate quality standards may be reverted</condition>
                <reason>Necessary when change blocks other developers from making progress</reason>
                <recovery>Developer is welcome to re-commit after problem has been fixed</recovery>
            </reversion_policy>
        </quality_standards>

        <commit_messages>
            <philosophy>
                <emphasis>Commit messages should communicate briefly what the change does, but should really emphasize why a change is being made and provide useful context</emphasis>
                <quality>Commit messages should be thoughtfully written and specific, rather than vague</quality>
                <example_bad>"bits were not set right" - leaves reviewer wondering about which bits and why</example_bad>
                <example_good>"Correctly set overflow bits in TargetInfo" - conveys almost all there is to the change</example_good>
            </philosophy>

            <format_guidelines>
                <guideline>Separate commit message into title and body separated by a blank line</guideline>
                <guideline>If not original author, ensure 'Author' property is set to original author and 'Committer' to yourself</guideline>
                <guideline>Use git tag 'Co-authored-by:' for multiple authors in rare situations</guideline>
                <guideline>Title should be concise - long titles are frowned upon and look bad in git log</guideline>
                <guideline>Add tags in square brackets for code-specific changes (e.g., "[SCEV] …" or "[OpenMP] …")</guideline>
                <guideline>Body should be concise but explanatory with complete reasoning</guideline>
                <guideline>Text formatting and spelling should follow documentation and in-code comment rules</guideline>
                <guideline>Include git commit hash for bug fixes, reverts, or reapplies of patches</guideline>
                <guideline>Add link to review page if patch has been reviewed</guideline>
                <guideline>Add reference to GitHub Issues if patch fixes a bug</guideline>
                <guideline>Acceptable to add metadata for automation, but should not replace self-explanatory commit message</guideline>
            </format_guidelines>

            <pull_request_workflow>
                <note>LLVM uses squash workflow for pull requests</note>
                <requirement>Update pull request description during review as it evolves</requirement>
                <process>GitHub uses initial commit message for PR description but ignores subsequent commit messages</process>
                <final_step>Authors and reviewers should make final editing pass over squashed commit description when merging</final_step>
            </pull_request_workflow>

            <enforcement>Minor violations result in reminding contributor of policy rather than reverting</enforcement>
        </commit_messages>

        <patch_reversion_policy>
            <community_values>
                <value>Strongly value having tip of tree in good state while allowing rapid iterative development</value>
                <approach>Make heavier use of reverts to keep tree healthy than some other open source projects</approach>
                <norm>Different norms from other projects - reverting is normal and healthy</norm>
            </community_values>

            <responding_to_revert>
                <mindset>Remember it is normal and healthy to have patches reverted</mindset>
                <mindset>Having patch reverted does not necessarily mean you did anything wrong</mindset>
                <courtesy>Explicitly thank the person who reverted the patch</courtesy>
                <follow_up>If you need more information, follow up in original commit thread with reverting patch author</follow_up>
            </responding_to_revert>

            <when_to_revert_own_change>
                <condition>Any time you learn of a serious problem with a change</condition>
                <philosophy>Strongly encourage "revert to green" as opposed to "fixing forward"</philosophy>
                <process>Revert first, investigate offline, then reapply fixed patch</process>
                <specific_cases>
                    <case>If you break a buildbot in a way which can't be quickly fixed</case>
                    <case>If a test case demonstrating a problem is reported in commit thread</case>
                    <case>If you receive substantial post-commit review feedback</case>
                    <case>If asked to revert by another contributor (unless would further destabilize tip of tree)</case>
                </specific_cases>
            </when_to_revert_own_change>

            <when_to_revert_others_change>
                <general_rule>If author themselves would revert per guidelines, other contributors encouraged to do so as courtesy</general_rule>
                <rationale>Contributors not expected to be always available - assurance that problematic patch will be reverted enables this</rationale>
            </when_to_revert_others_change>

            <revert_expectations>
                <judgment>Use best judgment - if uncertain, start email on commit thread asking for assistance</judgment>
                <stability>Be sure reverting improves stability of tip of tree</stability>
                <commit_message>Commit message for reverting commit should explain why patch is being reverted</commit_message>
                <notification>Customary to respond to original commit email mentioning the revert</notification>
                <test_case>Ideally have publicly reproducible test case ready to share</test_case>
                <collaboration>Not reasonable to revert without promise to provide means for patch author to debug</collaboration>
                <timing>Reverts should be reasonably timely - 2 hours ago can be reverted without discussion, 2 years ago should not</timing>
                <reapplication>When re-applying reverted patch, update commit message to indicate problem and how it was addressed</reapplication>
            </revert_expectations>
        </patch_reversion_policy>

        <commit_access>
            <eligibility>
                <requirement>3 or more merged pull requests</requirement>
                <process>Use provided link to file issue and request commit access</process>
                <approval>Need two current contributors to support request</approval>
                <reviewers>Reviewers of committed patches automatically CCed upon creating issue</reviewers>
                <criteria>Reviewers confirm three patches committed and no concerns about adhering to Developer Policy and Code of Conduct</criteria>
            </eligibility>

            <process>
                <step>GitHub invitation sent to account if approved</step>
                <step>Go to Invitation Link directly if no notification received</step>
                <step>Accept invitation to get commit access</step>
            </process>

            <pre_access_practice>
                <common_practice>Request someone with commit access to commit on your behalf</common_practice>
                <requirement>Provide name and email address for Author property of commit</requirement>
            </pre_access_practice>

            <tracking>
                <notification>Committed changes automatically reflected on commits mailing list</notification>
                <moderation>Mailing lists are moderated - large commits may require moderator approval</moderation>
            </tracking>

            <policies_for_new_committers>
                <policy>Granted commit-after-approval to all parts of LLVM</policy>
                <policy>Allowed to commit patches without approval which you think are obvious</policy>
                <policy>Allowed to commit patches without approval to portions you have contributed or maintain</policy>
                <policy>Multiple violations or single egregious violation may cause commit access to be revoked</policy>
            </policies_for_new_committers>

            <obvious_changes_examples>
                <example>Fixing build breakage</example>
                <example>Reverting obviously broken patches</example>
                <example>Documentation/comment changes</example>
                <example>Other minor changes</example>
            </obvious_changes_examples>

            <formatting_guidelines>
                <guideline>Avoid committing formatting- or whitespace-only changes outside of code you plan to make subsequent changes to</guideline>
                <guideline>Separate formatting or whitespace changes from functional changes</guideline>
                <guideline>Such changes should be highly localized</guideline>
                <guideline>Commit message should clearly state commit is not intended to change functionality (usually by stating NFC)</guideline>
            </formatting_guidelines>

            <ongoing_expectations>
                <expectation>Changes still subject to code review (before or after commit)</expectation>
                <encouragement>Encouraged to review other peoples' patches but not required</encouragement>
            </ongoing_expectations>
        </commit_access>

        <other_access_permissions>
            <process>Raise issue similar to commit access request</process>
            <evidence>Include evidence of need for specific type of access instead of authored PRs</evidence>
            <example>For issue triage access, include links to previously triaged issues and explain how ability would help</example>
        </other_access_permissions>

        <major_changes_rfcs>
            <purpose>LLVM is large community with many stakeholders - major changes require public discussion before landing</purpose>
            <process>Post Request For Comments (RFC) on LLVM Discourse forums</process>
            <rationale>Design of LLVM is carefully controlled to ensure pieces fit together well and are consistent</rationale>
            <recommendation>Get consensus with development community before investing significant effort in implementation</recommendation>
            <prototypes>Prototype implementations can help make design discussions more concrete</prototypes>

            <acceptance_suggestions>
                <suggestion>Make it targeted and avoid touching components irrelevant to the task</suggestion>
                <suggestion>Explain how change improves LLVM for other stakeholders rather than focusing on specific use case</suggestion>
                <suggestion>Periodically summarize current state of discussion and separate consensus points from areas needing further discussion</suggestion>
                <suggestion>High quality bar for compilers as foundational infrastructure - burden of proof is on proposer</suggestion>
            </acceptance_suggestions>

            <consensus_process>
                <goal>Aiming for "rough consensus" similar to IETF RFC7282</goal>
                <requirement>Consider and address all objections to RFC</requirement>
                <requirement>Confirm community can live with tradeoffs embodied in proposal</requirement>
            </consensus_process>

            <area_teams_role>
                <responsibility>LLVM Area Teams facilitate project decision making</responsibility>
                <intervention>Step in to restate perceived consensus when no obvious agreement</intervention>
                <deeper_disagreement>Identify next steps - gather more data, change proposal, or reject outright</deeper_disagreement>
                <moderation>Act as moderators by scheduling calls for participants to resolve disagreements</moderation>
            </area_teams_role>

            <implementation>Once design finalized, work should be done as series of incremental changes, not long-term development branch</implementation>
        </major_changes_rfcs>

        <incremental_development>
            <preference>LLVM project prefers incremental development approach where significant changes are developed in-tree incrementally</preference>
            <discouraged>Long-lived development branches or forks are discouraged</discouraged>

            <branch_drawbacks>
                <drawback>Branches must have mainline merged periodically - resolving merge conflicts takes time</drawback>
                <drawback>Other people in community tend to ignore work on branches</drawback>
                <drawback>Huge changes from merged branches are extremely difficult to code review</drawback>
                <drawback>Branches are not routinely tested by nightly tester infrastructure</drawback>
                <drawback>Monolithic large changes often don't work until entire set is done</drawback>
            </branch_drawbacks>

            <incremental_tips>
                <tip>Large/invasive changes usually have secondary changes required before big change (API cleanup, etc.)</tip>
                <tip>Secondary changes can often be done independently before major change</tip>
                <tip>Decompose remaining inter-related work into unrelated sets if possible</tip>
                <tip>Define first increment and get consensus on end goal</tip>
                <tip>Each change can be standalone or part of planned series working towards development goal</tip>
                <tip>Keep each change as small as possible</tip>
                <tip>Often add new API and slowly migrate clients to use new API</tip>
                <tip>Each change to use new API is often "obvious" and can be committed without review</tip>
                <tip>Once new API in place and used, easier to replace underlying implementation</tip>
            </incremental_tips>

            <guidance>If interested in making large change and this scares you, discuss change/gather consensus then ask about best way to proceed</guidance>
        </incremental_development>

        <potentially_breaking_changes>
            <purpose>Help notify users and vendors of potential disruptions when upgrading to newer version</purpose>

            <examples>
                <example>Deprecating feature expected to be removed in future</example>
                <example>Removing already-deprecated feature</example>
                <example>Upgrading diagnostic from warning to error</example>
                <example>Switching important default behavior</example>
                <example>Any other potentially disruptive situation worth raising awareness of</example>
            </examples>

            <required_actions>
                <action>
                    <step>During code review, add applicable "vendors" github team for awareness</step>
                    <purpose>Give vendors early notice of potentially disruptive changes being considered</purpose>
                    <feedback>Vendors can give early testing feedback to alert of unacceptable breakages</feedback>
                    <teams>
                        <team>Clang vendors</team>
                        <team>libc++ vendors</team>
                    </teams>
                    <joining>People can join vendors group by clicking "Join team" button on linked github pages</joining>
                </action>

                <action>
                    <step>When committing change, add information to Potentially Breaking Changes section of project's release notes</step>
                    <content>Information about what change is, what is potentially disruptive, code examples, links, and motivation</content>
                    <purpose>Help users learn about potential issues with upgrading to that release</purpose>
                </action>

                <action>
                    <step>After committing, post potentially disruptive changes to Announcements channel on Discourse</step>
                    <tagging>Tag with potentially-breaking label and project-specific label (clang, llvm, etc.)</tagging>
                    <purpose>Lower-traffic alternative to joining vendors group for pre-release notice</purpose>
                    <notification>Users can add potentially-breaking label to watch categories in Discourse preferences</notification>
                </action>
            </required_actions>
        </potentially_breaking_changes>

        <attribution_of_changes>
            <principle>Important to retain correct attribution when committing patches for others</principle>
            <avoid>Do not litter source code with random attributions "this code written by J. Random Hacker"</avoid>
            <tracking>Revision control system keeps perfect history of who changed what</tracking>
            <credits>CREDITS.txt file describes higher-level contributions</credits>
            <method>Follow attribution outlined in commit messages section</method>
            <rule>Do not add contributor names to source code</rule>

            <authorization>
                <requirement>Don't commit patches authored by others unless they have submitted patch to project or you have been authorized</requirement>
                <proper_channels>Author should first submit to relevant project's commit list, development list, or LLVM bug tracker</proper_channels>
                <private_patches>If someone sends patch privately, encourage them to submit to appropriate list first</private_patches>
            </authorization>

            <legacy_attribution>
                <context>Previous version control system (subversion) did not distinguish between author and committer like git</context>
                <old_method>Include "Patch by John Doe." in separate line of commit message</old_method>
                <automation>Automated processes rely on this format</automation>
            </legacy_attribution>
        </attribution_of_changes>

        <bans>
            <goal>Protect people in community from having to interact with people consistently not respecting LLVM Community Code of Conduct</goal>
            <direct_contributions>Do not accept any form of direct contribution from banned individual</direct_contributions>
            <indirect_contributions>Permissible only by someone taking full ownership and responsibility for all related community interactions</indirect_contributions>
            <evasion>Trying to evade non-permanent ban results in permanent ban</evasion>
            <guidance>When in doubt, reach <NAME_EMAIL> for advice</guidance>
        </bans>

        <ir_backwards_compatibility>
            <philosophy>When IR format has to be changed, maintain some backwards compatibility</philosophy>
            <balance>Rules intended as balance between convenience for LLVM users and not imposing big burden on LLVM developers</balance>

            <rules>
                <rule>
                    <scope>Textual format</scope>
                    <policy>Not backwards compatible - don't change too often but no specific promises</policy>
                </rule>

                <rule>
                    <scope>Additions and changes to IR</scope>
                    <policy>Should be reflected in test/Bitcode/compatibility.ll</policy>
                </rule>

                <rule>
                    <scope>Bitcode compatibility</scope>
                    <policy>Current LLVM version supports loading any bitcode since version 3.0</policy>
                </rule>

                <rule>
                    <scope>Release compatibility files</scope>
                    <policy>After each X.Y release, compatibility.ll must be copied to compatibility-X.Y.ll</policy>
                    <process>Corresponding bitcode file should be assembled using X.Y build and committed as compatibility-X.Y.ll.bc</process>
                </rule>

                <rule>
                    <scope>Feature handling</scope>
                    <policy>Newer releases can ignore features from older releases but cannot miscompile them</policy>
                    <example>If nsw is replaced, dropping it would be valid way to upgrade IR</example>
                </rule>

                <rule>
                    <scope>Debug metadata</scope>
                    <policy>Special case - currently dropped during upgrades</policy>
                </rule>

                <rule>
                    <scope>Non-debug metadata</scope>
                    <policy>Defined to be safe to drop - valid way to upgrade is to drop it</policy>
                    <note>Not very user friendly and more effort expected, but no promises made</note>
                </rule>
            </rules>
        </ir_backwards_compatibility>

        <c_api_changes>
            <stability_guarantees>
                <general>C API is "best effort" for stability</general>
                <approach>Make every attempt to keep C API stable, but stability limited by abstractness of interface and stability of C++ API it wraps</approach>
                <practical_meaning>Things like "create debug info" or "create this type of instruction" likely less stable than "take this IR file and JIT it for my current machine"</practical_meaning>
            </stability_guarantees>

            <release_stability>
                <policy>Won't break C API on release branch with patches that go on that branch</policy>
                <exception>Will fix unintentional C API break that keeps release consistent with both previous and next release</exception>
            </release_stability>

            <requirements>
                <testing>Patches to C API expected to come with tests like any other patch</testing>
                <expanding_existing>If LLVM subcomponent has C API already included, expanding that C API is acceptable</expanding_existing>
                <adding_new>Adding C API for subcomponents that don't currently have one needs discussion on LLVM Discourse forums for design and maintainability feedback prior to implementation</adding_new>
                <documentation>Any changes to C API required to be documented in release notes for external users</documentation>
            </requirements>
        </c_api_changes>

        <toolchain_requirements>
            <intent>Intend to require newer toolchains as time goes by to use newer versions of C++ as they get standardized</intent>
            <consideration>Requiring newer toolchains can be painful for those building LLVM</consideration>

            <process>
                <step number="1">
                    <guideline>General goal to support LLVM and GCC versions from last 3 years at minimum</guideline>
                    <flexibility>Time-based guideline not strict - may support much older compilers or decide to support fewer versions</flexibility>
                </step>

                <step number="2">
                    <action>Send RFC to LLVM Discourse forums</action>
                    <content>
                        <detail>Detail upsides of version increase (newer C++ language/library features, avoid miscompiles, etc.)</detail>
                        <detail>Detail downsides on important platforms (Ubuntu LTS status, etc.)</detail>
                    </content>
                </step>

                <step number="3">
                    <action>Once RFC reaches consensus, update CMake toolchain version checks and getting started guide</action>
                    <purpose>Provides softer transition path - error can be turned into warning using CMake flag</purpose>
                    <importance>Important step: LLVM doesn't have code requiring new toolchains yet, but soon will</importance>
                </step>

                <step number="4">
                    <requirement>Ensure at least one LLVM release has had this soft-error</requirement>
                    <rationale>Not all developers compile LLVM top-of-tree - release-bound developers should also be told about upcoming changes</rationale>
                </step>

                <step number="5">
                    <action>Turn soft-error into hard-error after said LLVM release has branched</action>
                </step>

                <step number="6">
                    <action>Update coding standards to allow new features explicitly approved in RFC</action>
                </step>

                <step number="7">
                    <action>Start using new features in LLVM's codebase</action>
                </step>
            </process>
        </toolchain_requirements>

        <ci_system>
            <main_tool>LLVM Buildbot is main continuous integration (CI) tool</main_tool>
            <coverage>Uses different builders to cover wide variety of sub-projects and configurations</coverage>
            <execution>Builds executed on different workers configured and provided by community members</execution>
            <tracking>Buildbot tracks commits on main branch and release branches</tracking>
            <testing_type>Post-merge testing - patches built and tested after merged to branches</testing_type>
            <expectation>Okay to break build occasionally as unreasonable to expect contributors to build and test with every possible configuration</expectation>

            <if_you_broke_build>
                <action>Fix build as soon as possible as this might block other contributors or downstream users</action>
                <fallback>If you need more time to analyze and fix bug, please revert your change to unblock others</fallback>
            </if_you_broke_build>

            <if_someone_else_broke_build>
                <action>Comment on code review in GitHub or email author, explain problem and impact</action>
                <information>Add link to broken build and error message so folks can understand problem</information>
                <escalation>Revert commit if this blocks your work</escalation>
            </if_someone_else_broke_build>

            <if_build_worker_permanently_broken>
                <step1>Contact owner of worker - find name and contact information in Worker tab of build page</step1>
                <step2>If owner does not respond or fix worker, escalate to Galina Kostanova (BuildBot master maintainer)</step2>
                <step3>If Galina could not help, escalate to Infrastructure Working Group</step3>
            </if_build_worker_permanently_broken>
        </ci_system>
    </developer_policies>

    <introducing_new_components>
        <philosophy>
            <inclusivity>LLVM community is vibrant and exciting place - look to be inclusive of new projects and foster new communities</inclusivity>
            <collaboration>Increase collaboration across industry and academia</collaboration>
            <balance>Strike balance between being inclusive of new ideas and people and cost of ongoing maintenance</balance>
        </philosophy>

        <support_policy>
            <core_projects>Need higher degree of scrutiny than peripheral projects</core_projects>
            <peripheral_projects>May have additional differences in requirements</peripheral_projects>
            <flexibility>Open to discussing unusual cases - start RFC thread on LLVM Discourse forums</flexibility>
        </support_policy>

        <adding_new_target>
            <receptivity>LLVM very receptive to new targets, even experimental ones</receptivity>
            <challenges>Number of problems can appear when adding new large portions of code</challenges>
            <support_level>New targets need same level of support as other core parts of compiler</support_level>
            <tier>Covered in core tier of support policy</tier>

            <experimental_vs_official>
                <experimental>
                    <characteristic>Not built by default (need to be explicitly enabled at CMake time)</characteristic>
                    <responsibility>Test failures, bugs, and build breakages when experimental target enabled are responsibility of community behind target to fix</responsibility>
                </experimental>

                <promotion_to_official>
                    <requirement>Must have addressed every minimum requirement and been stable in tree for at least 3 months</requirement>
                    <requirement>Target's code must be completely adapted to policy and coding standards</requirement>
                    <requirement>Test coverage needs to be broad and well written</requirement>
                    <requirement>Build target check-all must pass with new target built</requirement>
                    <requirement>Public buildbots need to be created and actively maintained</requirement>
                </promotion_to_official>
            </experimental_vs_official>

            <basic_rules_experimental>
                <rule>Every target must have at least one maintainer</rule>
                <rule>Must be active community behind target</rule>
                <rule>Code must be free of contentious issues</rule>
                <rule>Code conforms to all policies in developer policy document</rule>
                <rule>Target should have reasonable documentation or publicly available simulator/hardware</rule>
            </basic_rules_experimental>

            <continued_support>
                <requirement>Maintainer(s) must continue following rules throughout lifetime of target</requirement>
                <consequence>Continuous violations could lead to complete removal from code base</consequence>
                <degradation>Degradation in support, documentation or test coverage makes target candidate for deprecation and removal</degradation>
            </continued_support>
        </adding_new_target>
    </introducing_new_components>

    <copyright_license_patents>
        <copyright>
            <embedded_statements>
                <policy>Information about embedded copyright or 'Contributed by' statements</policy>
            </embedded_statements>
        </copyright>

        <relicensing>
            <policy>Information about relicensing procedures and policies</policy>
        </relicensing>

        <new_license_framework>
            <policy>New LLVM Project License Framework details</policy>
        </new_license_framework>

        <patents>
            <policy>Patent-related policies and procedures</policy>
        </patents>

        <legacy_license_structure>
            <policy>Information about legacy license structure</policy>
        </legacy_license_structure>

        <ai_generated_contributions>
            <policy>Policies regarding AI-generated contributions</policy>
        </ai_generated_contributions>
    </copyright_license_patents>
</llvm_developer_policy>
