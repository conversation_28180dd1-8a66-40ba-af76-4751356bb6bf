use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use crate::utils::text_system::{TextStyle, TextPosition};

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct KeyframeAnimationSystem {
    pub animations: HashMap<String, TextAnimationSequence>,
    pub active_animations: HashMap<String, AnimationInstance>,
    pub global_time: f64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct TextAnimationSequence {
    pub id: String,
    pub name: String,
    pub duration: f64,
    pub keyframes: Vec<TextKeyframe>,
    pub easing: EasingFunction,
    pub loop_mode: LoopMode,
    pub delay: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TextKeyframe {
    pub time: f64, // Time in seconds from start
    pub properties: KeyframeProperties,
    pub easing: Option<EasingFunction>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub struct KeyframeProperties {
    pub position: Option<TextPosition>,
    pub style: Option<TextStyle>,
    pub opacity: Option<f32>,
    pub scale: Option<(f32, f32)>,
    pub rotation: Option<f32>,
    pub color: Option<String>,
    pub font_size: Option<f32>,
    pub letter_spacing: Option<f32>,
    pub line_height: Option<f32>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum EasingFunction {
    Linear,
    EaseIn,
    EaseOut,
    EaseInOut,
    EaseInQuad,
    EaseOutQuad,
    EaseInOutQuad,
    EaseInCubic,
    EaseOutCubic,
    EaseInOutCubic,
    EaseInQuart,
    EaseOutQuart,
    EaseInOutQuart,
    EaseInQuint,
    EaseOutQuint,
    EaseInOutQuint,
    EaseInSine,
    EaseOutSine,
    EaseInOutSine,
    EaseInExpo,
    EaseOutExpo,
    EaseInOutExpo,
    EaseInCirc,
    EaseOutCirc,
    EaseInOutCirc,
    EaseInBack,
    EaseOutBack,
    EaseInOutBack,
    EaseInElastic,
    EaseOutElastic,
    EaseInOutElastic,
    EaseInBounce,
    EaseOutBounce,
    EaseInOutBounce,
    Custom(Vec<(f32, f32)>), // Bezier control points
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum LoopMode {
    None,
    Loop,
    PingPong,
    LoopCount(u32),
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct AnimationInstance {
    pub sequence_id: String,
    pub start_time: f64,
    pub current_time: f64,
    pub loop_count: u32,
    pub is_playing: bool,
    pub is_paused: bool,
    pub playback_speed: f32,
}

impl KeyframeAnimationSystem {
    pub fn new() -> Self {
        Self {
            animations: HashMap::new(),
            active_animations: HashMap::new(),
            global_time: 0.0,
        }
    }

    /// Add a new animation sequence
    pub fn add_animation(&mut self, sequence: TextAnimationSequence) {
        self.animations.insert(sequence.id.clone(), sequence);
    }

    /// Start playing an animation
    pub fn play_animation(&mut self, sequence_id: &str, element_id: &str) -> Result<(), String> {
        if !self.animations.contains_key(sequence_id) {
            return Err(format!("Animation sequence '{}' not found", sequence_id));
        }

        let instance = AnimationInstance {
            sequence_id: sequence_id.to_string(),
            start_time: self.global_time,
            current_time: 0.0,
            loop_count: 0,
            is_playing: true,
            is_paused: false,
            playback_speed: 1.0,
        };

        self.active_animations.insert(element_id.to_string(), instance);
        Ok(())
    }

    /// Stop an animation
    pub fn stop_animation(&mut self, element_id: &str) {
        self.active_animations.remove(element_id);
    }

    /// Pause an animation
    pub fn pause_animation(&mut self, element_id: &str) {
        if let Some(instance) = self.active_animations.get_mut(element_id) {
            instance.is_paused = true;
        }
    }

    /// Resume an animation
    pub fn resume_animation(&mut self, element_id: &str) {
        if let Some(instance) = self.active_animations.get_mut(element_id) {
            instance.is_paused = false;
        }
    }

    /// Update the animation system
    pub fn update(&mut self, delta_time: f64) {
        self.global_time += delta_time;
        
        let mut completed_animations = Vec::new();

        for (element_id, instance) in &mut self.active_animations {
            if !instance.is_playing || instance.is_paused {
                continue;
            }

            instance.current_time += delta_time * instance.playback_speed as f64;

            if let Some(sequence) = self.animations.get(&instance.sequence_id) {
                let adjusted_time = instance.current_time - sequence.delay;
                
                if adjusted_time >= 0.0 {
                    match sequence.loop_mode {
                        LoopMode::None => {
                            if adjusted_time >= sequence.duration {
                                completed_animations.push(element_id.clone());
                            }
                        }
                        LoopMode::Loop => {
                            if adjusted_time >= sequence.duration {
                                instance.current_time = sequence.delay;
                                instance.loop_count += 1;
                            }
                        }
                        LoopMode::PingPong => {
                            let cycle_duration = sequence.duration * 2.0;
                            if adjusted_time >= cycle_duration {
                                instance.current_time = sequence.delay;
                                instance.loop_count += 1;
                            }
                        }
                        LoopMode::LoopCount(max_loops) => {
                            if adjusted_time >= sequence.duration {
                                instance.loop_count += 1;
                                if instance.loop_count >= max_loops {
                                    completed_animations.push(element_id.clone());
                                } else {
                                    instance.current_time = sequence.delay;
                                }
                            }
                        }
                    }
                }
            }
        }

        // Remove completed animations
        for element_id in completed_animations {
            self.active_animations.remove(&element_id);
        }
    }

    /// Get the current animated properties for an element
    pub fn get_animated_properties(&self, element_id: &str) -> Option<KeyframeProperties> {
        let instance = self.active_animations.get(element_id)?;
        let sequence = self.animations.get(&instance.sequence_id)?;
        
        let adjusted_time = instance.current_time - sequence.delay;
        if adjusted_time < 0.0 {
            return None;
        }

        let animation_time = match sequence.loop_mode {
            LoopMode::PingPong => {
                let cycle_time = adjusted_time % (sequence.duration * 2.0);
                if cycle_time <= sequence.duration {
                    cycle_time
                } else {
                    sequence.duration * 2.0 - cycle_time
                }
            }
            _ => adjusted_time % sequence.duration,
        };

        Some(self.interpolate_keyframes(&sequence.keyframes, animation_time, &sequence.easing))
    }

    /// Interpolate between keyframes at a given time
    fn interpolate_keyframes(
        &self,
        keyframes: &[TextKeyframe],
        time: f64,
        default_easing: &EasingFunction,
    ) -> KeyframeProperties {
        if keyframes.is_empty() {
            return KeyframeProperties::default();
        }

        if keyframes.len() == 1 {
            return keyframes[0].properties.clone();
        }

        // Find the two keyframes to interpolate between
        let mut prev_keyframe = &keyframes[0];
        let mut next_keyframe = &keyframes[keyframes.len() - 1];

        for i in 0..keyframes.len() - 1 {
            if time >= keyframes[i].time && time <= keyframes[i + 1].time {
                prev_keyframe = &keyframes[i];
                next_keyframe = &keyframes[i + 1];
                break;
            }
        }

        if prev_keyframe.time == next_keyframe.time {
            return prev_keyframe.properties.clone();
        }

        // Calculate interpolation factor
        let t = (time - prev_keyframe.time) / (next_keyframe.time - prev_keyframe.time);
        let easing = next_keyframe.easing.as_ref().unwrap_or(default_easing);
        let eased_t = self.apply_easing(t as f32, easing);

        // Interpolate properties
        self.interpolate_properties(&prev_keyframe.properties, &next_keyframe.properties, eased_t)
    }

    /// Interpolate between two sets of properties
    fn interpolate_properties(
        &self,
        from: &KeyframeProperties,
        to: &KeyframeProperties,
        t: f32,
    ) -> KeyframeProperties {
        KeyframeProperties {
            position: self.interpolate_position(&from.position, &to.position, t),
            style: None, // Style interpolation is complex, handle separately
            opacity: self.interpolate_f32(&from.opacity, &to.opacity, t),
            scale: self.interpolate_scale(&from.scale, &to.scale, t),
            rotation: self.interpolate_f32(&from.rotation, &to.rotation, t),
            color: self.interpolate_color(&from.color, &to.color, t),
            font_size: self.interpolate_f32(&from.font_size, &to.font_size, t),
            letter_spacing: self.interpolate_f32(&from.letter_spacing, &to.letter_spacing, t),
            line_height: self.interpolate_f32(&from.line_height, &to.line_height, t),
        }
    }

    /// Apply easing function to interpolation factor
    fn apply_easing(&self, t: f32, easing: &EasingFunction) -> f32 {
        match easing {
            EasingFunction::Linear => t,
            EasingFunction::EaseIn => t * t,
            EasingFunction::EaseOut => 1.0 - (1.0 - t) * (1.0 - t),
            EasingFunction::EaseInOut => {
                if t < 0.5 {
                    2.0 * t * t
                } else {
                    1.0 - 2.0 * (1.0 - t) * (1.0 - t)
                }
            }
            EasingFunction::EaseInQuad => t * t,
            EasingFunction::EaseOutQuad => 1.0 - (1.0 - t) * (1.0 - t),
            EasingFunction::EaseInOutQuad => {
                if t < 0.5 {
                    2.0 * t * t
                } else {
                    1.0 - 2.0 * (1.0 - t) * (1.0 - t)
                }
            }
            EasingFunction::EaseInCubic => t * t * t,
            EasingFunction::EaseOutCubic => 1.0 - (1.0 - t).powi(3),
            EasingFunction::EaseInOutCubic => {
                if t < 0.5 {
                    4.0 * t * t * t
                } else {
                    1.0 - 4.0 * (1.0 - t).powi(3)
                }
            }
            EasingFunction::EaseInSine => 1.0 - (t * std::f32::consts::PI / 2.0).cos(),
            EasingFunction::EaseOutSine => (t * std::f32::consts::PI / 2.0).sin(),
            EasingFunction::EaseInOutSine => -(((t * std::f32::consts::PI).cos() - 1.0) / 2.0),
            EasingFunction::EaseOutBounce => {
                if t < 1.0 / 2.75 {
                    7.5625 * t * t
                } else if t < 2.0 / 2.75 {
                    let t = t - 1.5 / 2.75;
                    7.5625 * t * t + 0.75
                } else if t < 2.5 / 2.75 {
                    let t = t - 2.25 / 2.75;
                    7.5625 * t * t + 0.9375
                } else {
                    let t = t - 2.625 / 2.75;
                    7.5625 * t * t + 0.984375
                }
            }
            _ => t, // Simplified for other easing functions
        }
    }

    // Helper interpolation methods
    fn interpolate_f32(&self, from: &Option<f32>, to: &Option<f32>, t: f32) -> Option<f32> {
        match (from, to) {
            (Some(f), Some(t_val)) => Some(f + (t_val - f) * t),
            (Some(f), None) => Some(*f),
            (None, Some(t_val)) => Some(*t_val),
            (None, None) => None,
        }
    }

    fn interpolate_position(
        &self,
        from: &Option<TextPosition>,
        to: &Option<TextPosition>,
        t: f32,
    ) -> Option<TextPosition> {
        match (from, to) {
            (Some(f), Some(to_pos)) => {
                Some(TextPosition {
                    x: f.x + (to_pos.x - f.x) * t,
                    y: f.y + (to_pos.y - f.y) * t,
                    z: f.z + (to_pos.z - f.z) * t,
                    anchor_x: f.anchor_x + (to_pos.anchor_x - f.anchor_x) * t,
                    anchor_y: f.anchor_y + (to_pos.anchor_y - f.anchor_y) * t,
                    rotation: f.rotation + (to_pos.rotation - f.rotation) * t,
                    scale_x: f.scale_x + (to_pos.scale_x - f.scale_x) * t,
                    scale_y: f.scale_y + (to_pos.scale_y - f.scale_y) * t,
                })
            }
            (Some(f), None) => Some(f.clone()),
            (None, Some(to_pos)) => Some(to_pos.clone()),
            (None, None) => None,
        }
    }

    fn interpolate_scale(
        &self,
        from: &Option<(f32, f32)>,
        to: &Option<(f32, f32)>,
        t: f32,
    ) -> Option<(f32, f32)> {
        match (from, to) {
            (Some((fx, fy)), Some((tx, ty))) => {
                Some((fx + (tx - fx) * t, fy + (ty - fy) * t))
            }
            (Some(f), None) => Some(*f),
            (None, Some(to_scale)) => Some(*to_scale),
            (None, None) => None,
        }
    }

    fn interpolate_color(&self, from: &Option<String>, to: &Option<String>, t: f32) -> Option<String> {
        // Simplified color interpolation - in a full implementation,
        // this would parse colors and interpolate in RGB/HSL space
        match (from, to) {
            (Some(_), Some(to_color)) if t > 0.5 => Some(to_color.clone()),
            (Some(from_color), Some(_)) => Some(from_color.clone()),
            (Some(color), None) | (None, Some(color)) => Some(color.clone()),
            (None, None) => None,
        }
    }
}

impl Default for KeyframeProperties {
    fn default() -> Self {
        Self {
            position: None,
            style: None,
            opacity: None,
            scale: None,
            rotation: None,
            color: None,
            font_size: None,
            letter_spacing: None,
            line_height: None,
        }
    }
}

impl Default for KeyframeAnimationSystem {
    fn default() -> Self {
        Self::new()
    }
}

// Text Animation Presets Library
pub struct TextAnimationPresets;

impl TextAnimationPresets {
    /// Get all available animation presets
    pub fn get_all_presets() -> Vec<TextAnimationSequence> {
        vec![
            Self::fade_in(),
            Self::fade_out(),
            Self::slide_in_left(),
            Self::slide_in_right(),
            Self::slide_in_top(),
            Self::slide_in_bottom(),
            Self::scale_in(),
            Self::scale_out(),
            Self::rotate_in(),
            Self::bounce_in(),
            Self::elastic_in(),
            Self::typewriter(),
            Self::glow_pulse(),
            Self::shake(),
            Self::wave(),
            Self::zoom_blur(),
            Self::flip_in(),
            Self::roll_in(),
        ]
    }

    /// Fade in animation
    pub fn fade_in() -> TextAnimationSequence {
        TextAnimationSequence {
            id: "fade_in".to_string(),
            name: "Fade In".to_string(),
            duration: 1.0,
            keyframes: vec![
                TextKeyframe {
                    time: 0.0,
                    properties: KeyframeProperties {
                        opacity: Some(0.0),
                        ..Default::default()
                    },
                    easing: None,
                },
                TextKeyframe {
                    time: 1.0,
                    properties: KeyframeProperties {
                        opacity: Some(1.0),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseOut),
                },
            ],
            easing: EasingFunction::EaseOut,
            loop_mode: LoopMode::None,
            delay: 0.0,
        }
    }

    /// Fade out animation
    pub fn fade_out() -> TextAnimationSequence {
        TextAnimationSequence {
            id: "fade_out".to_string(),
            name: "Fade Out".to_string(),
            duration: 1.0,
            keyframes: vec![
                TextKeyframe {
                    time: 0.0,
                    properties: KeyframeProperties {
                        opacity: Some(1.0),
                        ..Default::default()
                    },
                    easing: None,
                },
                TextKeyframe {
                    time: 1.0,
                    properties: KeyframeProperties {
                        opacity: Some(0.0),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseIn),
                },
            ],
            easing: EasingFunction::EaseIn,
            loop_mode: LoopMode::None,
            delay: 0.0,
        }
    }

    /// Slide in from left
    pub fn slide_in_left() -> TextAnimationSequence {
        TextAnimationSequence {
            id: "slide_in_left".to_string(),
            name: "Slide In Left".to_string(),
            duration: 0.8,
            keyframes: vec![
                TextKeyframe {
                    time: 0.0,
                    properties: KeyframeProperties {
                        position: Some(TextPosition {
                            x: -100.0,
                            y: 0.0,
                            ..Default::default()
                        }),
                        opacity: Some(0.0),
                        ..Default::default()
                    },
                    easing: None,
                },
                TextKeyframe {
                    time: 0.8,
                    properties: KeyframeProperties {
                        position: Some(TextPosition {
                            x: 0.0,
                            y: 0.0,
                            ..Default::default()
                        }),
                        opacity: Some(1.0),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseOutBack),
                },
            ],
            easing: EasingFunction::EaseOutBack,
            loop_mode: LoopMode::None,
            delay: 0.0,
        }
    }

    /// Scale in animation
    pub fn scale_in() -> TextAnimationSequence {
        TextAnimationSequence {
            id: "scale_in".to_string(),
            name: "Scale In".to_string(),
            duration: 0.6,
            keyframes: vec![
                TextKeyframe {
                    time: 0.0,
                    properties: KeyframeProperties {
                        scale: Some((0.0, 0.0)),
                        opacity: Some(0.0),
                        ..Default::default()
                    },
                    easing: None,
                },
                TextKeyframe {
                    time: 0.6,
                    properties: KeyframeProperties {
                        scale: Some((1.0, 1.0)),
                        opacity: Some(1.0),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseOutElastic),
                },
            ],
            easing: EasingFunction::EaseOutElastic,
            loop_mode: LoopMode::None,
            delay: 0.0,
        }
    }

    /// Bounce in animation
    pub fn bounce_in() -> TextAnimationSequence {
        TextAnimationSequence {
            id: "bounce_in".to_string(),
            name: "Bounce In".to_string(),
            duration: 1.2,
            keyframes: vec![
                TextKeyframe {
                    time: 0.0,
                    properties: KeyframeProperties {
                        scale: Some((0.3, 0.3)),
                        opacity: Some(0.0),
                        ..Default::default()
                    },
                    easing: None,
                },
                TextKeyframe {
                    time: 0.2,
                    properties: KeyframeProperties {
                        scale: Some((1.1, 1.1)),
                        opacity: Some(1.0),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseOut),
                },
                TextKeyframe {
                    time: 0.4,
                    properties: KeyframeProperties {
                        scale: Some((0.9, 0.9)),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseInOut),
                },
                TextKeyframe {
                    time: 0.6,
                    properties: KeyframeProperties {
                        scale: Some((1.03, 1.03)),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseInOut),
                },
                TextKeyframe {
                    time: 0.8,
                    properties: KeyframeProperties {
                        scale: Some((0.97, 0.97)),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseInOut),
                },
                TextKeyframe {
                    time: 1.2,
                    properties: KeyframeProperties {
                        scale: Some((1.0, 1.0)),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseOut),
                },
            ],
            easing: EasingFunction::EaseOutBounce,
            loop_mode: LoopMode::None,
            delay: 0.0,
        }
    }

    /// Typewriter effect
    pub fn typewriter() -> TextAnimationSequence {
        TextAnimationSequence {
            id: "typewriter".to_string(),
            name: "Typewriter".to_string(),
            duration: 2.0,
            keyframes: vec![
                TextKeyframe {
                    time: 0.0,
                    properties: KeyframeProperties {
                        opacity: Some(0.0),
                        ..Default::default()
                    },
                    easing: None,
                },
                TextKeyframe {
                    time: 2.0,
                    properties: KeyframeProperties {
                        opacity: Some(1.0),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::Linear),
                },
            ],
            easing: EasingFunction::Linear,
            loop_mode: LoopMode::None,
            delay: 0.0,
        }
    }

    /// Glow pulse animation
    pub fn glow_pulse() -> TextAnimationSequence {
        TextAnimationSequence {
            id: "glow_pulse".to_string(),
            name: "Glow Pulse".to_string(),
            duration: 2.0,
            keyframes: vec![
                TextKeyframe {
                    time: 0.0,
                    properties: KeyframeProperties {
                        opacity: Some(0.7),
                        scale: Some((1.0, 1.0)),
                        ..Default::default()
                    },
                    easing: None,
                },
                TextKeyframe {
                    time: 1.0,
                    properties: KeyframeProperties {
                        opacity: Some(1.0),
                        scale: Some((1.05, 1.05)),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseInOut),
                },
                TextKeyframe {
                    time: 2.0,
                    properties: KeyframeProperties {
                        opacity: Some(0.7),
                        scale: Some((1.0, 1.0)),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseInOut),
                },
            ],
            easing: EasingFunction::EaseInOut,
            loop_mode: LoopMode::Loop,
            delay: 0.0,
        }
    }

    /// Create additional preset methods for other animations...
    pub fn slide_in_right() -> TextAnimationSequence {
        let mut preset = Self::slide_in_left();
        preset.id = "slide_in_right".to_string();
        preset.name = "Slide In Right".to_string();
        // Modify keyframes to slide from right
        if let Some(first_keyframe) = preset.keyframes.get_mut(0) {
            if let Some(ref mut position) = first_keyframe.properties.position {
                position.x = 100.0;
            }
        }
        preset
    }

    pub fn slide_in_top() -> TextAnimationSequence {
        let mut preset = Self::slide_in_left();
        preset.id = "slide_in_top".to_string();
        preset.name = "Slide In Top".to_string();
        // Modify keyframes to slide from top
        if let Some(first_keyframe) = preset.keyframes.get_mut(0) {
            if let Some(ref mut position) = first_keyframe.properties.position {
                position.x = 0.0;
                position.y = -100.0;
            }
        }
        preset
    }

    pub fn slide_in_bottom() -> TextAnimationSequence {
        let mut preset = Self::slide_in_left();
        preset.id = "slide_in_bottom".to_string();
        preset.name = "Slide In Bottom".to_string();
        // Modify keyframes to slide from bottom
        if let Some(first_keyframe) = preset.keyframes.get_mut(0) {
            if let Some(ref mut position) = first_keyframe.properties.position {
                position.x = 0.0;
                position.y = 100.0;
            }
        }
        preset
    }

    pub fn scale_out() -> TextAnimationSequence {
        let mut preset = Self::scale_in();
        preset.id = "scale_out".to_string();
        preset.name = "Scale Out".to_string();
        // Reverse the keyframes
        preset.keyframes.reverse();
        if let Some(first) = preset.keyframes.get_mut(0) {
            first.time = 0.0;
        }
        if let Some(last) = preset.keyframes.get_mut(1) {
            last.time = 0.6;
        }
        preset
    }

    pub fn rotate_in() -> TextAnimationSequence {
        TextAnimationSequence {
            id: "rotate_in".to_string(),
            name: "Rotate In".to_string(),
            duration: 1.0,
            keyframes: vec![
                TextKeyframe {
                    time: 0.0,
                    properties: KeyframeProperties {
                        rotation: Some(-180.0),
                        scale: Some((0.0, 0.0)),
                        opacity: Some(0.0),
                        ..Default::default()
                    },
                    easing: None,
                },
                TextKeyframe {
                    time: 1.0,
                    properties: KeyframeProperties {
                        rotation: Some(0.0),
                        scale: Some((1.0, 1.0)),
                        opacity: Some(1.0),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseOutBack),
                },
            ],
            easing: EasingFunction::EaseOutBack,
            loop_mode: LoopMode::None,
            delay: 0.0,
        }
    }

    pub fn elastic_in() -> TextAnimationSequence {
        let mut preset = Self::scale_in();
        preset.id = "elastic_in".to_string();
        preset.name = "Elastic In".to_string();
        preset.easing = EasingFunction::EaseOutElastic;
        preset
    }

    pub fn shake() -> TextAnimationSequence {
        TextAnimationSequence {
            id: "shake".to_string(),
            name: "Shake".to_string(),
            duration: 0.5,
            keyframes: vec![
                TextKeyframe {
                    time: 0.0,
                    properties: KeyframeProperties {
                        position: Some(TextPosition { x: 0.0, y: 0.0, ..Default::default() }),
                        ..Default::default()
                    },
                    easing: None,
                },
                TextKeyframe {
                    time: 0.1,
                    properties: KeyframeProperties {
                        position: Some(TextPosition { x: -10.0, y: 0.0, ..Default::default() }),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::Linear),
                },
                TextKeyframe {
                    time: 0.2,
                    properties: KeyframeProperties {
                        position: Some(TextPosition { x: 10.0, y: 0.0, ..Default::default() }),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::Linear),
                },
                TextKeyframe {
                    time: 0.3,
                    properties: KeyframeProperties {
                        position: Some(TextPosition { x: -10.0, y: 0.0, ..Default::default() }),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::Linear),
                },
                TextKeyframe {
                    time: 0.4,
                    properties: KeyframeProperties {
                        position: Some(TextPosition { x: 10.0, y: 0.0, ..Default::default() }),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::Linear),
                },
                TextKeyframe {
                    time: 0.5,
                    properties: KeyframeProperties {
                        position: Some(TextPosition { x: 0.0, y: 0.0, ..Default::default() }),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseOut),
                },
            ],
            easing: EasingFunction::Linear,
            loop_mode: LoopMode::None,
            delay: 0.0,
        }
    }

    pub fn wave() -> TextAnimationSequence {
        TextAnimationSequence {
            id: "wave".to_string(),
            name: "Wave".to_string(),
            duration: 2.0,
            keyframes: vec![
                TextKeyframe {
                    time: 0.0,
                    properties: KeyframeProperties {
                        position: Some(TextPosition { x: 0.0, y: 0.0, ..Default::default() }),
                        ..Default::default()
                    },
                    easing: None,
                },
                TextKeyframe {
                    time: 0.5,
                    properties: KeyframeProperties {
                        position: Some(TextPosition { x: 0.0, y: -20.0, ..Default::default() }),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseInOut),
                },
                TextKeyframe {
                    time: 1.0,
                    properties: KeyframeProperties {
                        position: Some(TextPosition { x: 0.0, y: 0.0, ..Default::default() }),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseInOut),
                },
                TextKeyframe {
                    time: 1.5,
                    properties: KeyframeProperties {
                        position: Some(TextPosition { x: 0.0, y: 20.0, ..Default::default() }),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseInOut),
                },
                TextKeyframe {
                    time: 2.0,
                    properties: KeyframeProperties {
                        position: Some(TextPosition { x: 0.0, y: 0.0, ..Default::default() }),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseInOut),
                },
            ],
            easing: EasingFunction::EaseInOut,
            loop_mode: LoopMode::Loop,
            delay: 0.0,
        }
    }

    pub fn zoom_blur() -> TextAnimationSequence {
        TextAnimationSequence {
            id: "zoom_blur".to_string(),
            name: "Zoom Blur".to_string(),
            duration: 1.0,
            keyframes: vec![
                TextKeyframe {
                    time: 0.0,
                    properties: KeyframeProperties {
                        scale: Some((3.0, 3.0)),
                        opacity: Some(0.0),
                        ..Default::default()
                    },
                    easing: None,
                },
                TextKeyframe {
                    time: 1.0,
                    properties: KeyframeProperties {
                        scale: Some((1.0, 1.0)),
                        opacity: Some(1.0),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseOut),
                },
            ],
            easing: EasingFunction::EaseOut,
            loop_mode: LoopMode::None,
            delay: 0.0,
        }
    }

    pub fn flip_in() -> TextAnimationSequence {
        TextAnimationSequence {
            id: "flip_in".to_string(),
            name: "Flip In".to_string(),
            duration: 0.8,
            keyframes: vec![
                TextKeyframe {
                    time: 0.0,
                    properties: KeyframeProperties {
                        rotation: Some(90.0),
                        scale: Some((0.0, 1.0)),
                        opacity: Some(0.0),
                        ..Default::default()
                    },
                    easing: None,
                },
                TextKeyframe {
                    time: 0.8,
                    properties: KeyframeProperties {
                        rotation: Some(0.0),
                        scale: Some((1.0, 1.0)),
                        opacity: Some(1.0),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseOutBack),
                },
            ],
            easing: EasingFunction::EaseOutBack,
            loop_mode: LoopMode::None,
            delay: 0.0,
        }
    }

    pub fn roll_in() -> TextAnimationSequence {
        TextAnimationSequence {
            id: "roll_in".to_string(),
            name: "Roll In".to_string(),
            duration: 1.0,
            keyframes: vec![
                TextKeyframe {
                    time: 0.0,
                    properties: KeyframeProperties {
                        position: Some(TextPosition { x: -100.0, y: 0.0, ..Default::default() }),
                        rotation: Some(-120.0),
                        opacity: Some(0.0),
                        ..Default::default()
                    },
                    easing: None,
                },
                TextKeyframe {
                    time: 1.0,
                    properties: KeyframeProperties {
                        position: Some(TextPosition { x: 0.0, y: 0.0, ..Default::default() }),
                        rotation: Some(0.0),
                        opacity: Some(1.0),
                        ..Default::default()
                    },
                    easing: Some(EasingFunction::EaseOut),
                },
            ],
            easing: EasingFunction::EaseOut,
            loop_mode: LoopMode::None,
            delay: 0.0,
        }
    }
}
