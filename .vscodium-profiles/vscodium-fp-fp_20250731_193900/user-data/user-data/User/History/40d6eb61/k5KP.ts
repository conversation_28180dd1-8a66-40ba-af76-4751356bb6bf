import { create } from "zustand";
import { persist } from "zustand/middleware";

const DEFAULT_PANEL_SIZES = {
  toolsPanel: 45,
  previewPanel: 75,
  propertiesPanel: 20,
  mainContent: 70,
  timeline: 30,
} as const;

interface PanelState {
  // Panel sizes as percentages
  toolsPanel: number;
  previewPanel: number;
  propertiesPanel: number;
  mainContent: number;
  timeline: number;

  // Actions
  setToolsPanel: (size: number) => void;
  setPreviewPanel: (size: number) => void;
  setPropertiesPanel: (size: number) => void;
  setMainContent: (size: number) => void;
  setTimeline: (size: number) => void;
}

export const usePanelStore = create<PanelState>()(
  persist(
    (set) => ({
      // Default sizes - optimized for responsiveness
      ...DEFAULT_PANEL_SIZES,

      // Actions
      setToolsPanel: (size) => {
        console.log('🔍 Panel Resize - Tools:', { timestamp: new Date().toISOString(), size });
        set({ toolsPanel: size });
      },
      setPreviewPanel: (size) => {
        console.log('🔍 Panel Resize - Preview:', { timestamp: new Date().toISOString(), size });
        set({ previewPanel: size });
      },
      setPropertiesPanel: (size) => {
        console.log('🔍 Panel Resize - Properties:', { timestamp: new Date().toISOString(), size });
        set({ propertiesPanel: size });
      },
      setMainContent: (size) => {
        console.log('🔍 Panel Resize - Main Content:', { timestamp: new Date().toISOString(), size });
        set({ mainContent: size });
      },
      setTimeline: (size) => {
        console.log('🔍 Panel Resize - Timeline:', { timestamp: new Date().toISOString(), size });
        set({ timeline: size });
      },
    }),
    {
      name: "panel-sizes",
    }
  )
);
