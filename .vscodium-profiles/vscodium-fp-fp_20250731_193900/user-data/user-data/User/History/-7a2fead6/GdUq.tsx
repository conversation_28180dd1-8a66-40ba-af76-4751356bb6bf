"use client";

import React, { useMemo, useEffect, useState } from "react";
import { usePlaybackStore } from "@/stores/playback-store";
import { useDynamicThumbnails } from "@/hooks/use-dynamic-thumbnails";
import { TIMELINE_CONSTANTS } from "@/constants/timeline-constants";
import type { MediaItem } from "@/types/media";
import type { TimelineTrack } from "@/types/timeline";

export interface DynamicThumbnailDisplayProps {
  mediaItem: MediaItem;
  track: TimelineTrack;
  elementStartTime: number;
  elementDuration: number;
  elementWidth: number;
  elementHeight: number;
  zoomLevel: number;
  className?: string;
}

interface ThumbnailTile {
  url: string;
  offsetX: number;
  width: number;
  timePosition: number;
}

/**
 * Component that displays dynamic thumbnails based on playhead position
 */
export function DynamicThumbnailDisplay({
  mediaItem,
  track,
  elementStartTime,
  elementDuration,
  elementWidth,
  elementHeight,
  zoomLevel,
  className = "",
}: DynamicThumbnailDisplayProps) {
  const { currentTime } = usePlaybackStore();
  const [thumbnailTiles, setThumbnailTiles] = useState<ThumbnailTile[]>([]);
  
  const {
    currentThumbnail,
    allThumbnails,
    isLoading,
    generateThumbnails,
    preload,
  } = useDynamicThumbnails({
    mediaId: mediaItem.id,
    elementStartTime,
    elementDuration,
    enabled: mediaItem.type === "video",
    options: {
      intervalSeconds: Math.max(0.5, 2 / zoomLevel), // More thumbnails at higher zoom
      maxThumbnails: Math.min(100, Math.ceil(elementDuration * zoomLevel)),
      thumbnailWidth: 320,
      thumbnailHeight: 240,
    },
  });

  // Calculate thumbnail tiles for the current view
  const calculateThumbnailTiles = useMemo(() => {
    if (!allThumbnails.length || !elementWidth) return [];
    
    const tiles: ThumbnailTile[] = [];
    const pixelsPerSecond = TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel;
    
    // Calculate how many pixels each thumbnail should cover
    const thumbnailDuration = elementDuration / allThumbnails.length;
    const thumbnailWidth = thumbnailDuration * pixelsPerSecond;
    
    // Generate tiles for visible area
    for (let i = 0; i < allThumbnails.length; i++) {
      const thumbnail = allThumbnails[i];
      const offsetX = thumbnail.timePosition * pixelsPerSecond;
      
      // Only include tiles that are visible in the current element
      if (offsetX < elementWidth) {
        tiles.push({
          url: thumbnail.url,
          offsetX,
          width: Math.min(thumbnailWidth, elementWidth - offsetX),
          timePosition: thumbnail.timePosition,
        });
      }
    }
    
    return tiles;
  }, [allThumbnails, elementWidth, elementDuration, zoomLevel]);

  // Update thumbnail tiles when calculation changes
  useEffect(() => {
    setThumbnailTiles(calculateThumbnailTiles);
  }, [calculateThumbnailTiles]);

  // Auto-generate thumbnails when component mounts
  useEffect(() => {
    if (mediaItem.type === "video" && allThumbnails.length === 0 && !isLoading) {
      // Use preload for better performance
      preload();
    }
  }, [mediaItem.type, allThumbnails.length, isLoading, preload]);

  // Calculate playhead position relative to this element
  const getPlayheadPosition = useMemo(() => {
    if (currentTime < elementStartTime || currentTime > elementStartTime + elementDuration) {
      return null; // Playhead is outside this element
    }
    
    const relativeTime = currentTime - elementStartTime;
    const pixelsPerSecond = TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel;
    return relativeTime * pixelsPerSecond;
  }, [currentTime, elementStartTime, elementDuration, zoomLevel]);

  // Render loading state
  if (isLoading && thumbnailTiles.length === 0) {
    return (
      <div className={`w-full h-full flex items-center justify-center bg-[#004D52] ${className}`}>
        <div className="text-xs text-white/60">Generating thumbnails...</div>
      </div>
    );
  }

  // Render fallback for non-video or when no thumbnails available
  if (mediaItem.type !== "video" || thumbnailTiles.length === 0) {
    return (
      <div className={`w-full h-full flex items-center justify-center bg-[#004D52] ${className}`}>
        <div className="text-xs text-white/60">{mediaItem.name}</div>
      </div>
    );
  }

  return (
    <div className={`relative w-full h-full bg-[#004D52] overflow-hidden ${className}`}>
      {/* Thumbnail tiles */}
      {thumbnailTiles.map((tile, index) => (
        <div
          key={`${tile.timePosition}-${index}`}
          className="absolute top-0 bottom-0"
          style={{
            left: `${tile.offsetX}px`,
            width: `${tile.width}px`,
            backgroundImage: `url(${tile.url})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
          }}
        />
      ))}
      
      {/* Playhead indicator overlay */}
      {getPlayheadPosition !== null && (
        <div
          className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10 pointer-events-none"
          style={{
            left: `${getPlayheadPosition}px`,
          }}
        />
      )}
      
      {/* Current frame highlight */}
      {currentThumbnail && getPlayheadPosition !== null && (
        <div
          className="absolute top-0 bottom-0 border-2 border-yellow-400 pointer-events-none z-20"
          style={{
            left: `${Math.max(0, getPlayheadPosition - 20)}px`,
            width: "40px",
            opacity: 0.7,
          }}
        />
      )}
      
      {/* Tile borders for visual separation */}
      <div
        className="absolute top-0 bottom-0 left-0 right-0 pointer-events-none"
        style={{
          backgroundImage: thumbnailTiles.length > 1 ? `repeating-linear-gradient(
            to right,
            transparent 0px,
            transparent ${thumbnailTiles[0]?.width - 1}px,
            rgba(255, 255, 255, 0.3) ${thumbnailTiles[0]?.width - 1}px,
            rgba(255, 255, 255, 0.3) ${thumbnailTiles[0]?.width}px
          )` : "none",
        }}
      />
    </div>
  );
}

/**
 * Simplified version for when dynamic thumbnails are disabled
 */
export function StaticThumbnailDisplay({
  mediaItem,
  track,
  elementWidth,
  elementHeight,
  className = "",
}: Omit<DynamicThumbnailDisplayProps, "elementStartTime" | "elementDuration" | "zoomLevel">) {
  if (mediaItem.type === "video" && mediaItem.thumbnailUrl) {
    const aspectRatio = mediaItem.width && mediaItem.height 
      ? mediaItem.width / mediaItem.height 
      : 16 / 9;
    const tileHeight = elementHeight - 8;
    const tileWidth = tileHeight * aspectRatio;

    return (
      <div className={`w-full h-full bg-[#004D52] py-1 ${className}`}>
        <div
          className="w-full h-full"
          style={{
            backgroundImage: `url(${mediaItem.thumbnailUrl})`,
            backgroundRepeat: "repeat-x",
            backgroundSize: `${tileWidth}px ${tileHeight}px`,
            backgroundPosition: "left center",
          }}
        />
      </div>
    );
  }

  return (
    <div className={`w-full h-full flex items-center justify-center bg-[#004D52] ${className}`}>
      <div className="text-xs text-white/60">{mediaItem.name}</div>
    </div>
  );
}
