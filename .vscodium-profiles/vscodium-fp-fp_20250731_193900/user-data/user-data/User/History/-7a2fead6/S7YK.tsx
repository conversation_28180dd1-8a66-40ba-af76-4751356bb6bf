"use client";

import React, { useMemo, useEffect, useState, useRef } from "react";
import { usePlaybackStore } from "@/stores/playback-store";
import { useDynamicThumbnails } from "@/hooks/use-dynamic-thumbnails";
import { TIMELINE_CONSTANTS } from "@/constants/timeline-constants";
import type { MediaItem } from "@/types/media";
import type { TimelineTrack } from "@/types/timeline";

export interface DynamicThumbnailDisplayProps {
  mediaItem: MediaItem;
  track: TimelineTrack;
  elementStartTime: number;
  elementDuration: number;
  elementWidth: number;
  elementHeight: number;
  zoomLevel: number;
  className?: string;
}

interface ThumbnailTile {
  url: string;
  offsetX: number;
  width: number;
  timePosition: number;
}

/**
 * Component that displays dynamic thumbnails based on playhead position
 */
export function DynamicThumbnailDisplay({
  mediaItem,
  track,
  elementStartTime,
  elementDuration,
  elementWidth,
  elementHeight,
  zoomLevel,
  className = "",
}: DynamicThumbnailDisplayProps) {
  const { currentTime } = usePlaybackStore();
  const [thumbnailTiles, setThumbnailTiles] = useState<ThumbnailTile[]>([]);

  console.log("🎭 DynamicThumbnailDisplay mounted for:", {
    mediaId: mediaItem.id,
    mediaName: mediaItem.name,
    hasFile: !!mediaItem.file,
    elementDuration,
    elementWidth,
    elementHeight
  });
  
  const {
    currentThumbnail,
    allThumbnails,
    isLoading,
    generateThumbnails,
    clearThumbnails,
    preload,
  } = useDynamicThumbnails({
    mediaId: mediaItem.id,
    elementStartTime,
    elementDuration,
    enabled: mediaItem.type === "video",
    zoomLevel,
    // Let the hook calculate optimal settings based on zoom and duration
  });

  // Calculate thumbnail tiles for the current view
  const calculateThumbnailTiles = useMemo(() => {
    if (!elementWidth) {
      console.log("❌ No element width:", { elementWidth });
      return [];
    }

    if (!allThumbnails.length) {
      if (isLoading) {
        console.log("⏳ Thumbnails loading...", { thumbnailCount: allThumbnails.length, isLoading });
      } else {
        console.log("❌ No thumbnails available:", { thumbnailCount: allThumbnails.length, isLoading });
      }
      return [];
    }

    console.log("🎯 Calculating thumbnail tiles:", {
      thumbnailCount: allThumbnails.length,
      elementWidth,
      elementDuration,
      zoomLevel,
      thumbnails: allThumbnails.map(t => ({ time: t.timePosition, url: t.url.substring(0, 30) + "..." }))
    });

    const tiles: ThumbnailTile[] = [];
    const pixelsPerSecond = TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel;

    // Sort thumbnails by time position to ensure proper ordering
    const sortedThumbnails = [...allThumbnails].sort((a, b) => a.timePosition - b.timePosition);

    // Smart tile generation that works well at any zoom level
    if (sortedThumbnails.length === 1) {
      // Single thumbnail: tile across entire width
      tiles.push({
        url: sortedThumbnails[0].url,
        offsetX: 0,
        width: elementWidth,
        timePosition: sortedThumbnails[0].timePosition,
      });
      console.log(`📍 Single tile: time=${sortedThumbnails[0].timePosition}s, width=${elementWidth}px`);
    } else if (sortedThumbnails.length === 2) {
      // Two thumbnails: split the width evenly for better coverage
      const tileWidth = elementWidth / 2;
      sortedThumbnails.forEach((thumbnail, i) => {
        tiles.push({
          url: thumbnail.url,
          offsetX: i * tileWidth,
          width: tileWidth,
          timePosition: thumbnail.timePosition,
        });
        console.log(`📍 Tile ${i}: time=${thumbnail.timePosition}s, offsetX=${i * tileWidth}px, width=${tileWidth}px`);
      });
    } else {
      // Multiple thumbnails: distribute evenly for consistent coverage
      const tileWidth = elementWidth / sortedThumbnails.length;
      sortedThumbnails.forEach((thumbnail, i) => {
        tiles.push({
          url: thumbnail.url,
          offsetX: i * tileWidth,
          width: tileWidth,
          timePosition: thumbnail.timePosition,
        });
        console.log(`📍 Tile ${i}: time=${thumbnail.timePosition}s, offsetX=${i * tileWidth}px, width=${tileWidth}px`);
      });
    }

    console.log("✅ Generated tiles:", tiles.length);
    return tiles;
  }, [allThumbnails, elementWidth, elementDuration, zoomLevel]);

  // Update thumbnail tiles when calculation changes
  useEffect(() => {
    console.log("🔄 Updating thumbnail tiles:", {
      calculatedTiles: calculateThumbnailTiles.length,
      allThumbnails: allThumbnails.length,
      isLoading,
      elementWidth,
      thumbnailDetails: allThumbnails.map(t => ({ time: t.timePosition, hasUrl: !!t.url }))
    });
    setThumbnailTiles(calculateThumbnailTiles);
  }, [calculateThumbnailTiles]);

  // Force immediate update when thumbnails become available
  useEffect(() => {
    if (allThumbnails.length > 0 && thumbnailTiles.length === 0) {
      console.log("🚀 Force updating tiles - thumbnails available but tiles not calculated");
      setThumbnailTiles(calculateThumbnailTiles);
    }
  }, [allThumbnails.length, thumbnailTiles.length, calculateThumbnailTiles]);

  // Smart thumbnail generation: responsive to zoom changes
  const lastZoomLevel = useRef(zoomLevel);
  const hasTriggeredGeneration = useRef(false);

  useEffect(() => {
    console.log("🚀 Smart thumbnail generation check:", {
      mediaType: mediaItem.type,
      thumbnailCount: allThumbnails.length,
      isLoading,
      hasStaticThumbnail: !!mediaItem.thumbnailUrl,
      preloadOnMount: !!preload,
      zoomLevel,
      lastZoomLevel: lastZoomLevel.current,
      elementWidth,
      hasTriggeredGeneration: hasTriggeredGeneration.current
    });

    if (mediaItem.type === "video" && !isLoading) {
      // Initial load
      if (allThumbnails.length === 0 && !hasTriggeredGeneration.current) {
        console.log("🎬 Generating initial thumbnails for", mediaItem.id, "at zoom", zoomLevel);
        hasTriggeredGeneration.current = true;
        lastZoomLevel.current = zoomLevel;
        preload();
      }
      // Zoom level increased significantly - need more thumbnails
      else if (zoomLevel > lastZoomLevel.current * 1.3 && zoomLevel > 1.5) {
        console.log("🔍 Zoom increased significantly, regenerating thumbnails:", {
          oldZoom: lastZoomLevel.current,
          newZoom: zoomLevel,
          currentThumbnails: allThumbnails.length
        });
        lastZoomLevel.current = zoomLevel;
        // Clear existing thumbnails and generate new ones optimized for current zoom
        clearThumbnails();
        setTimeout(() => generateThumbnails(), 100); // Small delay to ensure clear completes
      }
    }
  }, [mediaItem.type, mediaItem.id, allThumbnails.length, isLoading, zoomLevel, preload, generateThumbnails]);

  // Calculate playhead position relative to this element
  const getPlayheadPosition = useMemo(() => {
    if (currentTime < elementStartTime || currentTime > elementStartTime + elementDuration) {
      return null; // Playhead is outside this element
    }
    
    const relativeTime = currentTime - elementStartTime;
    const pixelsPerSecond = TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel;
    return relativeTime * pixelsPerSecond;
  }, [currentTime, elementStartTime, elementDuration, zoomLevel]);

  // Debug render state
  console.log("🎨 Render state:", {
    isLoading,
    thumbnailTilesLength: thumbnailTiles.length,
    allThumbnailsLength: allThumbnails.length,
    mediaType: mediaItem.type,
    elementWidth,
    hasTriggeredGeneration: hasTriggeredGeneration.current,
    thumbnailUrls: allThumbnails.map(t => t.url.substring(0, 30) + "...")
  });

  // Render loading state only if no thumbnails are available at all
  if (isLoading && allThumbnails.length === 0) {
    console.log("🔄 Rendering loading state");
    return (
      <div className={`w-full h-full flex items-center justify-center bg-[#004D52] ${className}`}>
        <div className="text-xs text-white/60">Generating thumbnails...</div>
      </div>
    );
  }

  // Render static thumbnail only when no dynamic thumbnails are available AND not loading
  if (mediaItem.type !== "video" || (thumbnailTiles.length === 0 && allThumbnails.length === 0 && !isLoading)) {
    console.log("🔄 Rendering static thumbnail fallback");

    // Use static thumbnail if available (like Premiere Pro, DaVinci Resolve)
    if (mediaItem.type === "video" && mediaItem.thumbnailUrl) {
      const aspectRatio = mediaItem.width && mediaItem.height
        ? mediaItem.width / mediaItem.height
        : 16 / 9;
      const tileHeight = elementHeight - 8;
      const tileWidth = tileHeight * aspectRatio;

      return (
        <div className={`w-full h-full bg-[#004D52] py-1 ${className}`}>
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `url(${mediaItem.thumbnailUrl})`,
              backgroundRepeat: "repeat-x",
              backgroundSize: `${tileWidth}px ${tileHeight}px`,
              backgroundPosition: "left center",
            }}
          />
        </div>
      );
    }

    // Fallback for non-video or no thumbnail
    return (
      <div className={`w-full h-full flex items-center justify-center bg-[#004D52] ${className}`}>
        <div className="text-xs text-white/60">{mediaItem.name}</div>
      </div>
    );
  }

  // Show thumbnails immediately when available, even if still loading more
  console.log("🎬 Rendering dynamic thumbnails:", {
    tilesCount: thumbnailTiles.length,
    isLoading,
    allThumbnailsCount: allThumbnails.length
  });

  return (
    <div className={`relative w-full h-full bg-[#004D52] overflow-hidden ${className}`}>
      {/* Loading indicator overlay if still generating more thumbnails */}
      {isLoading && thumbnailTiles.length > 0 && (
        <div className="absolute top-1 right-1 bg-black/70 text-white text-xs px-2 py-1 rounded">
          Generating...
        </div>
      )}

      {/* Thumbnail tiles */}
      {thumbnailTiles.map((tile, index) => (
        <div
          key={`${tile.timePosition}-${index}`}
          className="absolute top-0 bottom-0 border-r border-white/20"
          style={{
            left: `${tile.offsetX}px`,
            width: `${tile.width}px`,
            backgroundImage: `url(${tile.url})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
          }}
          title={`Thumbnail at ${tile.timePosition.toFixed(1)}s`}
        >
          {/* Debug overlay showing time position */}
          <div className="absolute top-1 left-1 bg-black/70 text-white text-xs px-1 rounded">
            {tile.timePosition.toFixed(1)}s
          </div>
        </div>
      ))}
      
      {/* Playhead indicator overlay */}
      {getPlayheadPosition !== null && (
        <div
          className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10 pointer-events-none"
          style={{
            left: `${getPlayheadPosition}px`,
          }}
        />
      )}
      
      {/* Current frame highlight */}
      {currentThumbnail && getPlayheadPosition !== null && (
        <div
          className="absolute top-0 bottom-0 border-2 border-yellow-400 pointer-events-none z-20"
          style={{
            left: `${Math.max(0, getPlayheadPosition - 20)}px`,
            width: "40px",
            opacity: 0.7,
          }}
        />
      )}
      
      {/* Tile borders for visual separation */}
      <div
        className="absolute top-0 bottom-0 left-0 right-0 pointer-events-none"
        style={{
          backgroundImage: thumbnailTiles.length > 1 ? `repeating-linear-gradient(
            to right,
            transparent 0px,
            transparent ${thumbnailTiles[0]?.width - 1}px,
            rgba(255, 255, 255, 0.3) ${thumbnailTiles[0]?.width - 1}px,
            rgba(255, 255, 255, 0.3) ${thumbnailTiles[0]?.width}px
          )` : "none",
        }}
      />
    </div>
  );
}

/**
 * Simplified version for when dynamic thumbnails are disabled
 */
export function StaticThumbnailDisplay({
  mediaItem,
  track,
  elementWidth,
  elementHeight,
  className = "",
}: {
  mediaItem: MediaItem;
  track: TimelineTrack;
  elementWidth: number;
  elementHeight: number;
  className?: string;
}) {
  if (mediaItem.type === "video" && mediaItem.thumbnailUrl) {
    const aspectRatio = mediaItem.width && mediaItem.height 
      ? mediaItem.width / mediaItem.height 
      : 16 / 9;
    const tileHeight = elementHeight - 8;
    const tileWidth = tileHeight * aspectRatio;

    return (
      <div className={`w-full h-full bg-[#004D52] py-1 ${className}`}>
        <div
          className="w-full h-full"
          style={{
            backgroundImage: `url(${mediaItem.thumbnailUrl})`,
            backgroundRepeat: "repeat-x",
            backgroundSize: `${tileWidth}px ${tileHeight}px`,
            backgroundPosition: "left center",
          }}
        />
      </div>
    );
  }

  return (
    <div className={`w-full h-full flex items-center justify-center bg-[#004D52] ${className}`}>
      <div className="text-xs text-white/60">{mediaItem.name}</div>
    </div>
  );
}
