
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use wasm_bindgen_futures::spawn_local;

use crate::utils::storage::{
    DatabaseManager, StoredProject, StoredMediaFile, ProjectSettings,
    MediaFileCache
};
use crate::types::{
    timeline::{Timeline as TimelineData, TimelineElement, Track},
    project::{Project, ProjectSettings as ExistingProjectSettings},
    media::{MediaFile, MediaLibrary, MediaType},
};

/// Project persistence manager for handling save/load operations
pub struct ProjectPersistenceManager {
    db_manager: DatabaseManager,
    media_cache: MediaFileCache,
    auto_save_enabled: bool,
    auto_save_interval: u32, // seconds
}

impl ProjectPersistenceManager {
    pub fn new() -> Self {
        Self {
            db_manager: DatabaseManager::new(),
            media_cache: MediaFileCache::new(500, 10000), // 500MB, 10k entries
            auto_save_enabled: true,
            auto_save_interval: 30,
        }
    }

    /// Initialize the persistence manager
    pub async fn init(&mut self) -> Result<(), String> {
        self.db_manager.init().await
            .map_err(|e| format!("Failed to initialize database: {:?}", e))?;

        self.media_cache.init().await
            .map_err(|e| format!("Failed to initialize media cache: {:?}", e))?;

        // Load user settings to configure auto-save
        if let Ok(Some(settings)) = self.db_manager.load_user_settings("default").await {
            self.auto_save_interval = settings.auto_save_interval;
        }

        Ok(())
    }

    /// Save a complete project to the database
    pub async fn save_project(&self, project: &Project) -> Result<(), String> {
        // Convert project to storage format
        let stored_project = self.project_to_stored_project(project)?;
        
        // Save project data
        self.db_manager.save_project(&stored_project).await
            .map_err(|e| format!("Failed to save project: {:?}", e))?;
        
        // Save associated media files
        for media_file in &project.media_library.files {
            let stored_media = self.media_file_to_stored_media(media_file)?;
            self.db_manager.save_media_file(&stored_media).await
                .map_err(|e| format!("Failed to save media file: {:?}", e))?;
        }
        
        Ok(())
    }

    /// Load a project from the database
    pub async fn load_project(&self, project_id: &Uuid) -> Result<Option<Project>, String> {
        // Load project data
        let stored_project = match self.db_manager.load_project(project_id).await {
            Ok(Some(project)) => project,
            Ok(None) => return Ok(None),
            Err(e) => return Err(format!("Failed to load project: {:?}", e)),
        };

        // Load associated media files
        let media_library = self.load_project_media_library(&stored_project).await?;

        // Convert to project format
        let project = self.stored_project_to_project(stored_project, media_library)?;
        
        Ok(Some(project))
    }

    /// List all projects with metadata
    pub async fn list_projects(&self) -> Result<Vec<ProjectMetadata>, String> {
        let stored_projects = self.db_manager.list_projects().await
            .map_err(|e| format!("Failed to list projects: {:?}", e))?;
        
        let mut projects = Vec::new();
        for stored_project in stored_projects {
            let duration = self.calculate_project_duration(&stored_project)?;
            let element_count = self.count_timeline_elements(&stored_project)?;

            projects.push(ProjectMetadata {
                id: stored_project.id,
                name: stored_project.name,
                description: stored_project.description,
                created_at: stored_project.created_at,
                updated_at: stored_project.updated_at,
                thumbnail: stored_project.thumbnail,
                duration,
                element_count,
            });
        }
        
        // Sort by updated_at descending (most recent first)
        projects.sort_by(|a, b| b.updated_at.cmp(&a.updated_at));
        
        Ok(projects)
    }

    /// Delete a project and its associated data
    pub async fn delete_project(&self, project_id: &Uuid) -> Result<(), String> {
        // Load project to get associated media files
        if let Some(project) = self.load_project(project_id).await? {
            // Delete associated cache entries
            for media_file in &project.media_library.files {
                self.delete_media_cache(&media_file.id).await?;
            }
        }
        
        // Delete the project
        self.db_manager.delete_project(project_id).await
            .map_err(|e| format!("Failed to delete project: {:?}", e))?;
        
        Ok(())
    }

    /// Create a new project with default settings
    pub async fn create_new_project(&self, name: String, description: Option<String>) -> Result<Project, String> {
        let mut project = Project::new(name);

        if let Some(desc) = description {
            project.description = desc;
        }

        // Convert timestamps to DateTime<Utc> for storage
        let now = Utc::now();
        project.created_at = now.timestamp_millis() as f64;
        project.updated_at = now.timestamp_millis() as f64;

        // Save the new project
        self.save_project(&project).await?;

        Ok(project)
    }

    /// Auto-save a project (non-blocking)
    pub fn auto_save_project(&self, project: Project) {
        if !self.auto_save_enabled {
            return;
        }
        
        let db_manager = self.db_manager.clone();
        spawn_local(async move {
            let stored_project = match Self::project_to_stored_project_static(&project) {
                Ok(stored) => stored,
                Err(e) => {
                    web_sys::console::error_1(&format!("Auto-save failed: {}", e).into());
                    return;
                }
            };
            
            if let Err(e) = db_manager.save_project(&stored_project).await {
                web_sys::console::error_1(&format!("Auto-save failed: {:?}", e).into());
            } else {
                web_sys::console::log_1(&format!("Auto-saved project: {}", project.name).into());
            }
        });
    }

    /// Export project data as JSON
    pub async fn export_project_json(&self, project_id: &Uuid) -> Result<String, String> {
        let project = self.load_project(project_id).await?
            .ok_or("Project not found")?;
        
        serde_json::to_string_pretty(&project)
            .map_err(|e| format!("Failed to serialize project: {}", e))
    }

    /// Import project from JSON
    pub async fn import_project_json(&self, json_data: &str) -> Result<Project, String> {
        let mut project: Project = serde_json::from_str(json_data)
            .map_err(|e| format!("Failed to parse project JSON: {}", e))?;
        
        // Generate new ID to avoid conflicts
        project.id = Uuid::new_v4();
        project.updated_at = Utc::now().timestamp() as f64;
        
        // Save the imported project
        self.save_project(&project).await?;
        
        Ok(project)
    }

    /// Get project statistics
    pub async fn get_project_statistics(&self, project_id: &Uuid) -> Result<ProjectStatistics, String> {
        let project = self.load_project(project_id).await?
            .ok_or("Project not found")?;
        
        let stats = ProjectStatistics {
            total_elements: project.timeline.elements.len(),
            video_elements: project.timeline.elements.iter()
                .filter(|e| matches!(e.element_type, crate::types::timeline::ElementType::Video))
                .count(),
            audio_elements: project.timeline.elements.iter()
                .filter(|e| matches!(e.element_type, crate::types::timeline::ElementType::Audio))
                .count(),
            text_elements: project.timeline.elements.iter()
                .filter(|e| matches!(e.element_type, crate::types::timeline::ElementType::Text))
                .count(),
            total_tracks: project.timeline.tracks.len(),
            total_duration: project.timeline.duration,
            media_files_count: project.media_library.files.len(),
            project_size_mb: self.calculate_project_size(&project).await?,
        };
        
        Ok(stats)
    }

    // Helper methods
    
    fn project_to_stored_project(&self, project: &Project) -> Result<StoredProject, String> {
        Self::project_to_stored_project_static(project)
    }
    
    fn project_to_stored_project_static(project: &Project) -> Result<StoredProject, String> {
        let timeline_data = serde_json::to_value(&project.timeline)
            .map_err(|e| format!("Failed to serialize timeline: {}", e))?;

        // Convert timestamps
        let created_at = DateTime::from_timestamp_millis(project.created_at as i64)
            .unwrap_or_else(|| Utc::now());
        let updated_at = DateTime::from_timestamp_millis(project.updated_at as i64)
            .unwrap_or_else(|| Utc::now());

        // Convert project settings to storage format
        let storage_settings = ProjectSettings {
            video_resolution: (project.settings.width, project.settings.height),
            frame_rate: project.settings.frame_rate,
            audio_sample_rate: project.settings.sample_rate,
            audio_channels: 2, // Default to stereo
            default_duration: 60.0, // Default duration
        };

        Ok(StoredProject {
            id: project.id,
            name: project.name.clone(),
            description: Some(project.description.clone()),
            created_at,
            updated_at,
            version: env!("CARGO_PKG_VERSION").to_string(),
            timeline_data,
            settings: storage_settings,
            thumbnail: None, // TODO: Generate thumbnail
        })
    }
    
    fn stored_project_to_project(&self, stored: StoredProject, media_library: MediaLibrary) -> Result<Project, String> {
        let timeline: TimelineData = serde_json::from_value(stored.timeline_data)
            .map_err(|e| format!("Failed to deserialize timeline: {}", e))?;

        // Convert storage settings to project settings
        let project_settings = ExistingProjectSettings {
            width: stored.settings.video_resolution.0,
            height: stored.settings.video_resolution.1,
            frame_rate: stored.settings.frame_rate,
            sample_rate: stored.settings.audio_sample_rate,
            background_color: "#000000".to_string(), // Default background
        };

        Ok(Project {
            id: stored.id,
            name: stored.name,
            description: stored.description.unwrap_or_default(),
            created_at: stored.created_at.timestamp_millis() as f64,
            updated_at: stored.updated_at.timestamp_millis() as f64,
            timeline,
            media_library,
            settings: project_settings,
        })
    }
    
    fn media_file_to_stored_media(&self, media: &MediaFile) -> Result<StoredMediaFile, String> {
        let file_type_str = match media.file_type {
            MediaType::Video => "video",
            MediaType::Audio => "audio",
            MediaType::Image => "image",
        }.to_string();

        let resolution = if let (Some(width), Some(height)) = (media.width, media.height) {
            Some((width, height))
        } else {
            None
        };

        let created_at = DateTime::from_timestamp_millis(media.created_at as i64)
            .unwrap_or_else(|| Utc::now());

        Ok(StoredMediaFile {
            id: media.id,
            name: media.name.clone(),
            file_type: file_type_str,
            mime_type: "application/octet-stream".to_string(), // Default MIME type
            size: media.size,
            duration: media.duration,
            resolution,
            created_at,
            last_accessed: Utc::now(),
            thumbnail: media.thumbnail.clone(),
            metadata: HashMap::new(), // TODO: Extract metadata
        })
    }
    
    async fn load_project_media_library(&self, stored_project: &StoredProject) -> Result<MediaLibrary, String> {
        // Extract media file IDs from timeline elements
        let timeline: TimelineData = serde_json::from_value(stored_project.timeline_data.clone())
            .map_err(|e| format!("Failed to deserialize timeline: {}", e))?;
        
        let mut media_files = Vec::new();
        let mut media_ids = std::collections::HashSet::new();
        
        // Collect unique media IDs from timeline elements
        for element in &timeline.elements {
            // TODO: Extract media ID from element properties
            // This would depend on how media references are stored in elements
        }
        
        // Load media files from database
        for media_id in media_ids {
            if let Ok(Some(stored_media)) = self.db_manager.load_media_file(&media_id).await {
                let media_type = match stored_media.file_type.as_str() {
                    "video" => MediaType::Video,
                    "audio" => MediaType::Audio,
                    "image" => MediaType::Image,
                    _ => MediaType::Video, // Default fallback
                };

                media_files.push(MediaFile {
                    id: stored_media.id,
                    name: stored_media.name,
                    file_type: media_type,
                    url: format!("blob:{}", stored_media.id), // Generate blob URL
                    duration: stored_media.duration,
                    width: stored_media.resolution.map(|(w, _)| w),
                    height: stored_media.resolution.map(|(_, h)| h),
                    size: stored_media.size,
                    created_at: stored_media.created_at.timestamp_millis() as f64,
                    thumbnail: stored_media.thumbnail,
                });
            }
        }

        Ok(MediaLibrary {
            files: media_files,
            selected: Vec::new(),
            search_query: String::new(),
            filter: crate::types::media::MediaFilter::All,
        })
    }
    
    fn calculate_project_duration(&self, stored_project: &StoredProject) -> Result<f64, String> {
        let timeline: TimelineData = serde_json::from_value(stored_project.timeline_data.clone())
            .map_err(|e| format!("Failed to deserialize timeline: {}", e))?;
        Ok(timeline.duration)
    }
    
    fn count_timeline_elements(&self, stored_project: &StoredProject) -> Result<usize, String> {
        let timeline: TimelineData = serde_json::from_value(stored_project.timeline_data.clone())
            .map_err(|e| format!("Failed to deserialize timeline: {}", e))?;
        Ok(timeline.elements.len())
    }
    
    async fn delete_media_cache(&self, media_id: &Uuid) -> Result<(), String> {
        self.media_cache.clear_media_cache(media_id).await
            .map_err(|e| format!("Failed to clear media cache: {:?}", e))?;
        Ok(())
    }

    /// Preload media cache for project
    pub async fn preload_project_cache(&self, project: &Project) -> Result<(), String> {
        let media_ids: Vec<Uuid> = project.media_library.files.iter()
            .map(|file| file.id)
            .collect();

        self.media_cache.preload_cache(&media_ids).await
            .map_err(|e| format!("Failed to preload cache: {:?}", e))?;

        Ok(())
    }

    /// Get cache usage statistics
    pub async fn get_cache_statistics(&self) -> Result<std::collections::HashMap<String, crate::utils::storage::CacheTypeStats>, String> {
        self.media_cache.get_cache_usage().await
            .map_err(|e| format!("Failed to get cache statistics: {:?}", e))
    }
    
    async fn calculate_project_size(&self, project: &Project) -> Result<f64, String> {
        // TODO: Calculate actual project size including media files and cache
        // For now, return estimated size based on timeline complexity
        let base_size = 0.1; // Base project size in MB
        let element_size = project.timeline.elements.len() as f64 * 0.01; // 10KB per element
        Ok(base_size + element_size)
    }
}

impl Default for ProjectPersistenceManager {
    fn default() -> Self {
        Self::new()
    }
}

/// Project metadata for listing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectMetadata {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub thumbnail: Option<String>,
    pub duration: f64,
    pub element_count: usize,
}

/// Project statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectStatistics {
    pub total_elements: usize,
    pub video_elements: usize,
    pub audio_elements: usize,
    pub text_elements: usize,
    pub total_tracks: usize,
    pub total_duration: f64,
    pub media_files_count: usize,
    pub project_size_mb: f64,
}
