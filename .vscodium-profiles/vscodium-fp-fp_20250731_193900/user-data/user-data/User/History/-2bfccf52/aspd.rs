use wasm_bindgen::prelude::*;
use web_sys::{
    CanvasRenderingContext2d, HtmlCanvasElement,
    AudioBuffer
};
use serde::{Serialize, Deserialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct WaveformConfig {
    pub width: u32,
    pub height: u32,
    pub samples_per_pixel: u32,
    pub channels: u32,
    pub color_scheme: WaveformColorScheme,
    pub style: WaveformStyle,
    pub show_grid: bool,
    pub grid_color: String,
    pub background_color: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct WaveformColorScheme {
    pub waveform_color: String,
    pub waveform_progress_color: String,
    pub selection_color: String,
    pub cursor_color: String,
    pub peak_color: String,
    pub rms_color: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum WaveformStyle {
    Bars,
    Line,
    Filled,
    Mirror,
    Frequency,
}

impl Default for WaveformConfig {
    fn default() -> Self {
        Self {
            width: 800,
            height: 200,
            samples_per_pixel: 128,
            channels: 2,
            color_scheme: WaveformColorScheme {
                waveform_color: "#4A90E2".to_string(),
                waveform_progress_color: "#7ED321".to_string(),
                selection_color: "rgba(255, 255, 0, 0.3)".to_string(),
                cursor_color: "#FF0000".to_string(),
                peak_color: "#FF6B6B".to_string(),
                rms_color: "#4ECDC4".to_string(),
            },
            style: WaveformStyle::Bars,
            show_grid: true,
            grid_color: "rgba(255, 255, 255, 0.1)".to_string(),
            background_color: "#2C3E50".to_string(),
        }
    }
}

#[derive(Debug, Clone)]
pub struct WaveformData {
    pub peaks: Vec<f32>,
    pub rms: Vec<f32>,
    pub sample_rate: f32,
    pub duration: f32,
    pub channels: u32,
}

#[derive(Debug, Clone)]
pub struct WaveformSelection {
    pub start_time: f32,
    pub end_time: f32,
    pub start_pixel: u32,
    pub end_pixel: u32,
}

pub struct WaveformRenderer {
    canvas: HtmlCanvasElement,
    context: CanvasRenderingContext2d,
    config: WaveformConfig,
    waveform_data: Option<WaveformData>,
    current_time: f32,
    selection: Option<WaveformSelection>,
    zoom_level: f32,
    scroll_offset: f32,
    cached_image_data: Option<web_sys::ImageData>,
    needs_redraw: bool,
}

impl WaveformRenderer {
    pub fn new(canvas: HtmlCanvasElement, config: WaveformConfig) -> Result<Self, JsValue> {
        let context = canvas
            .get_context("2d")?
            .ok_or("Failed to get 2D context")?
            .dyn_into::<CanvasRenderingContext2d>()?;

        canvas.set_width(config.width);
        canvas.set_height(config.height);

        Ok(Self {
            canvas,
            context,
            config,
            waveform_data: None,
            current_time: 0.0,
            selection: None,
            zoom_level: 1.0,
            scroll_offset: 0.0,
            cached_image_data: None,
            needs_redraw: true,
        })
    }

    pub async fn load_audio_buffer(&mut self, buffer: &AudioBuffer) -> Result<(), JsValue> {
        let sample_rate = buffer.sample_rate();
        let duration = buffer.duration() as f32;
        let channels = buffer.number_of_channels();
        let length = buffer.length();

        let mut all_peaks = Vec::new();
        let mut all_rms = Vec::new();

        // Process each channel separately for multi-channel display
        for channel in 0..channels {
            let mut samples = vec![0.0f32; length as usize];
            buffer.copy_from_channel(&mut samples, channel as i32)?;

            let mut channel_peaks = Vec::new();
            let mut channel_rms = Vec::new();

            // Calculate peaks and RMS for visualization
            let samples_per_pixel = self.config.samples_per_pixel as usize;
            let pixels_needed = (samples.len() + samples_per_pixel - 1) / samples_per_pixel;

            for i in 0..pixels_needed {
                let start_sample = i * samples_per_pixel;
                let end_sample = (start_sample + samples_per_pixel).min(samples.len());

                let mut max_peak = 0.0f32;
                let mut sum_squares = 0.0f32;
                let mut sample_count = 0;

                for &sample in &samples[start_sample..end_sample] {
                    max_peak = max_peak.max(sample.abs());
                    sum_squares += sample * sample;
                    sample_count += 1;
                }

                channel_peaks.push(max_peak);
                channel_rms.push((sum_squares / sample_count as f32).sqrt());
            }

            all_peaks.extend(channel_peaks);
            all_rms.extend(channel_rms);
        }

        self.waveform_data = Some(WaveformData {
            peaks: all_peaks,
            rms: all_rms,
            sample_rate,
            duration,
            channels,
        });

        self.needs_redraw = true;
        Ok(())
    }

    pub fn render(&mut self) -> Result<(), JsValue> {
        if !self.needs_redraw {
            return Ok(());
        }

        // Clear canvas
        self.context.set_fill_style(&self.config.background_color.clone().into());
        self.context.fill_rect(0.0, 0.0, self.config.width as f64, self.config.height as f64);

        if let Some(ref data) = self.waveform_data {
            // Draw grid if enabled
            if self.config.show_grid {
                self.draw_grid()?;
            }

            // Draw waveform based on style
            match self.config.style {
                WaveformStyle::Bars => self.draw_bars(data)?,
                WaveformStyle::Line => self.draw_line(data)?,
                WaveformStyle::Filled => self.draw_filled(data)?,
                WaveformStyle::Mirror => self.draw_mirror(data)?,
                WaveformStyle::Frequency => self.draw_frequency(data)?,
            }

            // Draw selection if present
            if let Some(ref selection) = self.selection {
                self.draw_selection(selection)?;
            }

            // Draw current time cursor
            self.draw_cursor()?;
        }

        self.needs_redraw = false;
        Ok(())
    }

    fn draw_grid(&self) -> Result<(), JsValue> {
        self.context.set_stroke_style(&self.config.grid_color.clone().into());
        self.context.set_line_width(1.0);

        // Vertical grid lines (time markers)
        let time_interval = 1.0; // 1 second intervals
        if let Some(ref data) = self.waveform_data {
            let pixels_per_second = self.config.width as f32 / data.duration * self.zoom_level;
            let mut time = 0.0;
            
            while time <= data.duration {
                let x = (time * pixels_per_second - self.scroll_offset) as f64;
                if x >= 0.0 && x <= self.config.width as f64 {
                    self.context.begin_path();
                    self.context.move_to(x, 0.0);
                    self.context.line_to(x, self.config.height as f64);
                    self.context.stroke();
                }
                time += time_interval;
            }
        }

        // Horizontal grid lines (amplitude markers)
        let amplitude_lines = 5;
        for i in 1..amplitude_lines {
            let y = (self.config.height as f32 / amplitude_lines as f32 * i as f32) as f64;
            self.context.begin_path();
            self.context.move_to(0.0, y);
            self.context.line_to(self.config.width as f64, y);
            self.context.stroke();
        }

        Ok(())
    }

    fn draw_bars(&self, data: &WaveformData) -> Result<(), JsValue> {
        let samples_per_channel = data.peaks.len() / data.channels as usize;
        let channel_height = self.config.height as f32 / data.channels as f32;
        let bar_width = self.config.width as f32 / samples_per_channel as f32 * self.zoom_level;

        for channel in 0..data.channels {
            // Set different colors for different channels
            let color = match channel {
                0 => &self.config.color_scheme.waveform_color,
                1 => &self.config.color_scheme.waveform_progress_color,
                _ => &self.config.color_scheme.peak_color,
            };
            self.context.set_fill_style(&color.into());

            let channel_start_y = channel as f32 * channel_height;
            let channel_center_y = channel_start_y + channel_height / 2.0;
            let max_height = channel_height * 0.4; // Leave margin between channels

            let channel_offset = channel as usize * samples_per_channel;

            for i in 0..samples_per_channel {
                if channel_offset + i >= data.peaks.len() {
                    break;
                }

                let x = (i as f32 * bar_width - self.scroll_offset) as f64;

                // Skip bars outside visible area
                if (x + bar_width as f64) < 0.0 || x > (self.config.width as f64) {
                    continue;
                }

                let peak = data.peaks[channel_offset + i];
                let bar_height = (peak * max_height) as f64;
                let y = channel_center_y as f64 - bar_height / 2.0;

                self.context.fill_rect(x, y, bar_width as f64, bar_height);
            }

            // Draw channel separator line
            if channel < data.channels - 1 {
                self.context.set_stroke_style(&"rgba(255, 255, 255, 0.2)".into());
                self.context.set_line_width(1.0);
                self.context.begin_path();
                let separator_y = (channel_start_y + channel_height) as f64;
                self.context.move_to(0.0, separator_y);
                self.context.line_to(self.config.width as f64, separator_y);
                self.context.stroke();
            }
        }

        Ok(())
    }

    fn draw_line(&self, data: &WaveformData) -> Result<(), JsValue> {
        self.context.set_stroke_style(&self.config.color_scheme.waveform_color.clone().into());
        self.context.set_line_width(2.0);
        self.context.begin_path();

        let pixel_width = self.config.width as f32 / data.peaks.len() as f32 * self.zoom_level;
        let center_y = self.config.height as f32 / 2.0;
        let max_height = center_y * 0.9;

        let mut first_point = true;
        for (i, &peak) in data.peaks.iter().enumerate() {
            let x = (i as f32 * pixel_width - self.scroll_offset) as f64;
            
            if x < -pixel_width as f64 || x > self.config.width as f64 + pixel_width as f64 {
                continue;
            }

            let y = (center_y - peak * max_height) as f64;

            if first_point {
                self.context.move_to(x, y);
                first_point = false;
            } else {
                self.context.line_to(x, y);
            }
        }

        self.context.stroke();
        Ok(())
    }

    fn draw_filled(&self, data: &WaveformData) -> Result<(), JsValue> {
        self.context.set_fill_style(&self.config.color_scheme.waveform_color.clone().into());
        self.context.begin_path();

        let pixel_width = self.config.width as f32 / data.peaks.len() as f32 * self.zoom_level;
        let center_y = self.config.height as f32 / 2.0;
        let max_height = center_y * 0.9;

        // Start from bottom
        self.context.move_to(0.0, center_y as f64);

        // Draw top line
        for (i, &peak) in data.peaks.iter().enumerate() {
            let x = (i as f32 * pixel_width - self.scroll_offset) as f64;
            if x < -pixel_width as f64 || x > self.config.width as f64 + pixel_width as f64 {
                continue;
            }
            let y = (center_y - peak * max_height) as f64;
            self.context.line_to(x, y);
        }

        // Close path back to bottom
        self.context.line_to(self.config.width as f64, center_y as f64);
        self.context.close_path();
        self.context.fill();

        Ok(())
    }

    fn draw_mirror(&self, data: &WaveformData) -> Result<(), JsValue> {
        self.context.set_fill_style(&self.config.color_scheme.waveform_color.clone().into());
        
        let pixel_width = self.config.width as f32 / data.peaks.len() as f32 * self.zoom_level;
        let center_y = self.config.height as f32 / 2.0;
        let max_height = center_y * 0.9;

        for (i, &peak) in data.peaks.iter().enumerate() {
            let x = (i as f32 * pixel_width - self.scroll_offset) as f64;
            
            if (x + pixel_width as f64) < 0.0 || x > (self.config.width as f64) {
                continue;
            }

            let bar_height = (peak * max_height) as f64;
            
            // Draw upper half
            self.context.fill_rect(x, center_y as f64 - bar_height, pixel_width as f64, bar_height);
            
            // Draw lower half (mirrored)
            self.context.fill_rect(x, center_y as f64, pixel_width as f64, bar_height);
        }

        Ok(())
    }

    fn draw_frequency(&self, data: &WaveformData) -> Result<(), JsValue> {
        // Simplified frequency visualization using RMS data
        let gradient = self.context.create_linear_gradient(0.0, 0.0, 0.0, self.config.height as f64);
        gradient.add_color_stop(0.0, "#FF6B6B")?; // High frequencies - red
        gradient.add_color_stop(0.5, "#4ECDC4")?; // Mid frequencies - teal
        gradient.add_color_stop(1.0, "#45B7D1")?; // Low frequencies - blue

        self.context.set_fill_style(&gradient);

        let pixel_width = self.config.width as f32 / data.rms.len() as f32 * self.zoom_level;
        let max_height = self.config.height as f32;

        for (i, &rms_value) in data.rms.iter().enumerate() {
            let x = (i as f32 * pixel_width - self.scroll_offset) as f64;
            
            if (x + pixel_width as f64) < 0.0 || x > (self.config.width as f64) {
                continue;
            }

            let bar_height = (rms_value * max_height) as f64;
            let y = max_height as f64 - bar_height;

            self.context.fill_rect(x, y, pixel_width as f64, bar_height);
        }

        Ok(())
    }

    fn draw_selection(&self, selection: &WaveformSelection) -> Result<(), JsValue> {
        self.context.set_fill_style(&self.config.color_scheme.selection_color.clone().into());
        
        let start_x = (selection.start_pixel as f32 - self.scroll_offset) as f64;
        let end_x = (selection.end_pixel as f32 - self.scroll_offset) as f64;
        let width = end_x - start_x;

        if width > 0.0 && start_x < self.config.width as f64 && end_x > 0.0 {
            self.context.fill_rect(
                start_x.max(0.0),
                0.0,
                width.min(self.config.width as f64 - start_x.max(0.0)),
                self.config.height as f64
            );
        }

        Ok(())
    }

    fn draw_cursor(&self) -> Result<(), JsValue> {
        if let Some(ref data) = self.waveform_data {
            let pixels_per_second = self.config.width as f32 / data.duration * self.zoom_level;
            let cursor_x = (self.current_time * pixels_per_second - self.scroll_offset) as f64;

            if cursor_x >= 0.0 && cursor_x <= self.config.width as f64 {
                self.context.set_stroke_style(&self.config.color_scheme.cursor_color.clone().into());
                self.context.set_line_width(2.0);
                self.context.begin_path();
                self.context.move_to(cursor_x, 0.0);
                self.context.line_to(cursor_x, self.config.height as f64);
                self.context.stroke();
            }
        }

        Ok(())
    }

    pub fn set_current_time(&mut self, time: f32) {
        if (self.current_time - time).abs() > 0.001 {
            self.current_time = time;
            self.needs_redraw = true;
        }
    }

    pub fn set_selection(&mut self, start_time: f32, end_time: f32) {
        if let Some(ref data) = self.waveform_data {
            let pixels_per_second = self.config.width as f32 / data.duration * self.zoom_level;
            let start_pixel = (start_time * pixels_per_second) as u32;
            let end_pixel = (end_time * pixels_per_second) as u32;

            self.selection = Some(WaveformSelection {
                start_time,
                end_time,
                start_pixel,
                end_pixel,
            });
            self.needs_redraw = true;
        }
    }

    pub fn clear_selection(&mut self) {
        if self.selection.is_some() {
            self.selection = None;
            self.needs_redraw = true;
        }
    }

    pub fn set_zoom(&mut self, zoom_level: f32) {
        self.zoom_level = zoom_level.max(0.1).min(100.0);
        self.needs_redraw = true;
    }

    pub fn set_scroll_offset(&mut self, offset: f32) {
        self.scroll_offset = offset.max(0.0);
        self.needs_redraw = true;
    }

    pub fn pixel_to_time(&self, pixel: f32) -> Option<f32> {
        self.waveform_data.as_ref().map(|data| {
            let adjusted_pixel = pixel + self.scroll_offset;
            let pixels_per_second = self.config.width as f32 / data.duration * self.zoom_level;
            adjusted_pixel / pixels_per_second
        })
    }

    pub fn time_to_pixel(&self, time: f32) -> Option<f32> {
        self.waveform_data.as_ref().map(|data| {
            let pixels_per_second = self.config.width as f32 / data.duration * self.zoom_level;
            time * pixels_per_second - self.scroll_offset
        })
    }

    pub fn get_visible_time_range(&self) -> Option<(f32, f32)> {
        self.waveform_data.as_ref().map(|data| {
            let start_time = self.scroll_offset / (self.config.width as f32 / data.duration * self.zoom_level);
            let end_time = start_time + (data.duration / self.zoom_level);
            (start_time, end_time.min(data.duration))
        })
    }

    pub fn update_config(&mut self, config: WaveformConfig) {
        self.config = config;
        self.canvas.set_width(self.config.width);
        self.canvas.set_height(self.config.height);
        self.needs_redraw = true;
    }

    pub fn get_canvas(&self) -> &HtmlCanvasElement {
        &self.canvas
    }

    pub fn force_redraw(&mut self) {
        self.needs_redraw = true;
    }
}

#[derive(Debug, Clone)]
pub struct WaveformInteractionState {
    pub is_dragging: bool,
    pub drag_start_x: f32,
    pub drag_start_offset: f32,
    pub is_selecting: bool,
    pub selection_start_x: f32,
    pub mouse_x: f32,
    pub mouse_y: f32,
}

impl Default for WaveformInteractionState {
    fn default() -> Self {
        Self {
            is_dragging: false,
            drag_start_x: 0.0,
            drag_start_offset: 0.0,
            is_selecting: false,
            selection_start_x: 0.0,
            mouse_x: 0.0,
            mouse_y: 0.0,
        }
    }
}

pub struct WaveformController {
    renderer: WaveformRenderer,
    interaction_state: WaveformInteractionState,
    min_zoom: f32,
    max_zoom: f32,
    zoom_sensitivity: f32,
    pan_sensitivity: f32,
}

impl WaveformController {
    pub fn new(canvas: HtmlCanvasElement, config: WaveformConfig) -> Result<Self, JsValue> {
        let renderer = WaveformRenderer::new(canvas, config)?;

        Ok(Self {
            renderer,
            interaction_state: WaveformInteractionState::default(),
            min_zoom: 0.1,
            max_zoom: 100.0,
            zoom_sensitivity: 0.1,
            pan_sensitivity: 1.0,
        })
    }

    pub fn handle_mouse_down(&mut self, x: f32, y: f32, button: u32, shift_key: bool) -> Result<(), JsValue> {
        self.interaction_state.mouse_x = x;
        self.interaction_state.mouse_y = y;

        match button {
            0 => { // Left mouse button
                if shift_key {
                    // Start selection
                    self.interaction_state.is_selecting = true;
                    self.interaction_state.selection_start_x = x;
                } else {
                    // Start dragging or seek
                    self.interaction_state.is_dragging = true;
                    self.interaction_state.drag_start_x = x;
                    self.interaction_state.drag_start_offset = self.renderer.scroll_offset;

                    // Seek to clicked position
                    if let Some(time) = self.renderer.pixel_to_time(x) {
                        self.renderer.set_current_time(time);
                    }
                }
            },
            1 => { // Middle mouse button - pan mode
                self.interaction_state.is_dragging = true;
                self.interaction_state.drag_start_x = x;
                self.interaction_state.drag_start_offset = self.renderer.scroll_offset;
            },
            _ => {}
        }

        Ok(())
    }

    pub fn handle_mouse_move(&mut self, x: f32, y: f32) -> Result<(), JsValue> {
        let prev_x = self.interaction_state.mouse_x;
        self.interaction_state.mouse_x = x;
        self.interaction_state.mouse_y = y;

        if self.interaction_state.is_dragging {
            // Pan the waveform
            let delta_x = x - self.interaction_state.drag_start_x;
            let new_offset = self.interaction_state.drag_start_offset - delta_x * self.pan_sensitivity;
            self.renderer.set_scroll_offset(new_offset);
        } else if self.interaction_state.is_selecting {
            // Update selection
            let start_x = self.interaction_state.selection_start_x.min(x);
            let end_x = self.interaction_state.selection_start_x.max(x);

            if let (Some(start_time), Some(end_time)) = (
                self.renderer.pixel_to_time(start_x),
                self.renderer.pixel_to_time(end_x)
            ) {
                self.renderer.set_selection(start_time, end_time);
            }
        }

        Ok(())
    }

    pub fn handle_mouse_up(&mut self, x: f32, y: f32, button: u32) -> Result<(), JsValue> {
        match button {
            0 => { // Left mouse button
                if self.interaction_state.is_selecting {
                    // Finalize selection
                    let start_x = self.interaction_state.selection_start_x.min(x);
                    let end_x = self.interaction_state.selection_start_x.max(x);

                    if (end_x - start_x).abs() < 5.0 {
                        // Too small, clear selection
                        self.renderer.clear_selection();
                    }

                    self.interaction_state.is_selecting = false;
                } else if self.interaction_state.is_dragging {
                    self.interaction_state.is_dragging = false;
                }
            },
            1 => { // Middle mouse button
                self.interaction_state.is_dragging = false;
            },
            _ => {}
        }

        Ok(())
    }

    pub fn handle_wheel(&mut self, delta_y: f32, ctrl_key: bool, x: f32) -> Result<(), JsValue> {
        if ctrl_key {
            // Zoom
            let zoom_factor = if delta_y > 0.0 { 1.0 - self.zoom_sensitivity } else { 1.0 + self.zoom_sensitivity };
            let new_zoom = (self.renderer.zoom_level * zoom_factor).max(self.min_zoom).min(self.max_zoom);

            // Zoom towards mouse position
            if let Some(time_at_mouse) = self.renderer.pixel_to_time(x) {
                self.renderer.set_zoom(new_zoom);

                // Adjust scroll to keep the same time under the mouse
                if let Some(new_pixel) = self.renderer.time_to_pixel(time_at_mouse) {
                    let offset_adjustment = new_pixel - x;
                    let new_offset = self.renderer.scroll_offset + offset_adjustment;
                    self.renderer.set_scroll_offset(new_offset);
                }
            } else {
                self.renderer.set_zoom(new_zoom);
            }
        } else {
            // Pan horizontally
            let pan_amount = delta_y * self.pan_sensitivity * 10.0;
            let new_offset = self.renderer.scroll_offset + pan_amount;
            self.renderer.set_scroll_offset(new_offset);
        }

        Ok(())
    }

    pub fn handle_double_click(&mut self, x: f32, y: f32) -> Result<(), JsValue> {
        // Zoom to fit or reset zoom
        if self.renderer.zoom_level > 1.0 {
            // Reset to fit
            self.renderer.set_zoom(1.0);
            self.renderer.set_scroll_offset(0.0);
        } else {
            // Zoom in to clicked position
            let new_zoom = 4.0;
            if let Some(time_at_click) = self.renderer.pixel_to_time(x) {
                self.renderer.set_zoom(new_zoom);

                // Center the clicked time
                if let Some(new_pixel) = self.renderer.time_to_pixel(time_at_click) {
                    let center_offset = new_pixel - self.renderer.config.width as f32 / 2.0;
                    self.renderer.set_scroll_offset(center_offset);
                }
            }
        }

        Ok(())
    }

    pub fn zoom_in(&mut self, center_x: Option<f32>) -> Result<(), JsValue> {
        let zoom_factor = 1.0 + self.zoom_sensitivity * 2.0;
        let new_zoom = (self.renderer.zoom_level * zoom_factor).min(self.max_zoom);

        if let Some(x) = center_x {
            if let Some(time_at_center) = self.renderer.pixel_to_time(x) {
                self.renderer.set_zoom(new_zoom);
                if let Some(new_pixel) = self.renderer.time_to_pixel(time_at_center) {
                    let offset_adjustment = new_pixel - x;
                    let new_offset = self.renderer.scroll_offset + offset_adjustment;
                    self.renderer.set_scroll_offset(new_offset);
                }
            }
        } else {
            self.renderer.set_zoom(new_zoom);
        }

        Ok(())
    }

    pub fn zoom_out(&mut self, center_x: Option<f32>) -> Result<(), JsValue> {
        let zoom_factor = 1.0 - self.zoom_sensitivity * 2.0;
        let new_zoom = (self.renderer.zoom_level * zoom_factor).max(self.min_zoom);

        if let Some(x) = center_x {
            if let Some(time_at_center) = self.renderer.pixel_to_time(x) {
                self.renderer.set_zoom(new_zoom);
                if let Some(new_pixel) = self.renderer.time_to_pixel(time_at_center) {
                    let offset_adjustment = new_pixel - x;
                    let new_offset = self.renderer.scroll_offset + offset_adjustment;
                    self.renderer.set_scroll_offset(new_offset);
                }
            }
        } else {
            self.renderer.set_zoom(new_zoom);
        }

        Ok(())
    }

    pub fn zoom_to_fit(&mut self) -> Result<(), JsValue> {
        self.renderer.set_zoom(1.0);
        self.renderer.set_scroll_offset(0.0);
        Ok(())
    }

    pub fn zoom_to_selection(&mut self) -> Result<(), JsValue> {
        if let Some(selection) = self.renderer.selection.clone() {
            let selection_duration = selection.end_time - selection.start_time;
            if selection_duration > 0.0 {
                if let Some(ref data) = self.renderer.waveform_data {
                    let zoom_level = data.duration / selection_duration;
                    self.renderer.set_zoom(zoom_level);

                    // Center the selection
                    let selection_center_time = (selection.start_time + selection.end_time) / 2.0;
                    if let Some(center_pixel) = self.renderer.time_to_pixel(selection_center_time) {
                        let center_offset = center_pixel - self.renderer.config.width as f32 / 2.0;
                        self.renderer.set_scroll_offset(center_offset);
                    }
                }
            }
        }
        Ok(())
    }

    pub fn pan_to_time(&mut self, time: f32) -> Result<(), JsValue> {
        if let Some(pixel) = self.renderer.time_to_pixel(time) {
            let center_offset = pixel - self.renderer.config.width as f32 / 2.0;
            self.renderer.set_scroll_offset(center_offset);
        }
        Ok(())
    }

    pub fn get_zoom_level(&self) -> f32 {
        self.renderer.zoom_level
    }

    pub fn get_scroll_offset(&self) -> f32 {
        self.renderer.scroll_offset
    }

    pub fn set_zoom_limits(&mut self, min_zoom: f32, max_zoom: f32) {
        self.min_zoom = min_zoom.max(0.01);
        self.max_zoom = max_zoom.min(1000.0);
    }

    pub fn set_sensitivity(&mut self, zoom_sensitivity: f32, pan_sensitivity: f32) {
        self.zoom_sensitivity = zoom_sensitivity.max(0.01).min(1.0);
        self.pan_sensitivity = pan_sensitivity.max(0.1).min(10.0);
    }

    pub fn get_renderer_mut(&mut self) -> &mut WaveformRenderer {
        &mut self.renderer
    }

    pub fn get_renderer(&self) -> &WaveformRenderer {
        &self.renderer
    }

    pub fn render(&mut self) -> Result<(), JsValue> {
        self.renderer.render()
    }
}
