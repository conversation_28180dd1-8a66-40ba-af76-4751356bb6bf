use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::rc::Rc;
use std::cell::RefCell;
use uuid::Uuid;

use crate::types::timeline::{Timeline, TimelineElement, ElementType, ElementProperties};
use crate::utils::audio_manager::AudioManager;
use crate::utils::video_decoder::VideoDecoder;
use crate::utils::audio_effects_system::{AudioEffectsProcessor, AudioElementEffects};
use crate::utils::audio_timeline_sync::AudioTimelineSync;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimelineState {
    pub current_time: f64,
    pub duration: f64,
    pub zoom_level: f64,
    pub scroll_position: f64,
    pub is_playing: bool,
    pub playback_rate: f64,
    pub loop_enabled: bool,
    pub snap_enabled: bool,
    pub snap_threshold: f64,
}

impl Default for TimelineState {
    fn default() -> Self {
        Self {
            current_time: 0.0,
            duration: 300.0, // 5 minutes default
            zoom_level: 1.0,
            scroll_position: 0.0,
            is_playing: false,
            playback_rate: 1.0,
            loop_enabled: false,
            snap_enabled: true,
            snap_threshold: 0.1, // 100ms snap threshold
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimelineEvent {
    pub event_type: TimelineEventType,
    pub timestamp: f64,
    pub element_id: Option<Uuid>,
    pub track_id: Option<Uuid>,
    pub data: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TimelineEventType {
    ElementAdded,
    ElementRemoved,
    ElementMoved,
    ElementResized,
    ElementPropertiesChanged,
    TrackAdded,
    TrackRemoved,
    TrackMuted,
    TrackSolo,
    PlaybackStarted,
    PlaybackStopped,
    TimeChanged,
    EffectsUpdated,
}

pub struct TimelineManager {
    timeline: Timeline,
    state: TimelineState,
    event_history: Vec<TimelineEvent>,
    undo_stack: Vec<Timeline>,
    redo_stack: Vec<Timeline>,
    max_undo_levels: usize,
    audio_manager: Option<Rc<RefCell<AudioManager>>>,
    video_decoder: Option<Rc<RefCell<VideoDecoder>>>,
    audio_effects_processor: Option<Rc<RefCell<AudioEffectsProcessor>>>,
    audio_timeline_sync: Option<Rc<RefCell<AudioTimelineSync>>>,
    change_listeners: Vec<Box<dyn Fn(&TimelineEvent)>>,
}

impl TimelineManager {
    pub fn new() -> Self {
        Self {
            timeline: Timeline::default(),
            state: TimelineState::default(),
            event_history: Vec::new(),
            undo_stack: Vec::new(),
            redo_stack: Vec::new(),
            max_undo_levels: 50,
            audio_manager: None,
            video_decoder: None,
            audio_effects_processor: None,
            audio_timeline_sync: None,
            change_listeners: Vec::new(),
        }
    }

    pub fn set_audio_manager(&mut self, audio_manager: Rc<RefCell<AudioManager>>) {
        self.audio_manager = Some(audio_manager);
    }

    pub fn set_video_decoder(&mut self, video_decoder: Rc<RefCell<VideoDecoder>>) {
        self.video_decoder = Some(video_decoder);
    }

    pub fn set_audio_effects_processor(&mut self, audio_effects_processor: Rc<RefCell<AudioEffectsProcessor>>) {
        self.audio_effects_processor = Some(audio_effects_processor);
    }

    pub fn set_audio_timeline_sync(&mut self, audio_timeline_sync: Rc<RefCell<AudioTimelineSync>>) {
        self.audio_timeline_sync = Some(audio_timeline_sync);
    }

    pub fn add_element(&mut self, element: TimelineElement) -> Result<(), String> {
        self.save_state_for_undo();
        
        // Validate element placement
        if element.start_time < 0.0 {
            return Err("Element start time cannot be negative".to_string());
        }

        if element.duration <= 0.0 {
            return Err("Element duration must be positive".to_string());
        }

        // Check for track existence
        if !self.timeline.tracks.iter().any(|t| t.id == element.track_id) {
            return Err("Track does not exist".to_string());
        }

        // Add element to timeline
        self.timeline.elements.push(element.clone());

        // Update timeline duration if necessary
        let element_end = element.start_time + element.duration;
        if element_end > self.state.duration {
            self.state.duration = element_end;
        }

        // Notify audio/video managers and effects processor
        self.notify_managers_element_added(&element);
        self.notify_effects_element_added(&element);

        // Record event
        let event = TimelineEvent {
            event_type: TimelineEventType::ElementAdded,
            timestamp: js_sys::Date::now(),
            element_id: Some(element.id),
            track_id: Some(element.track_id),
            data: None,
        };
        self.add_event(event);

        Ok(())
    }

    pub fn remove_element(&mut self, element_id: &Uuid) -> Result<(), String> {
        self.save_state_for_undo();

        let element_index = self.timeline.elements
            .iter()
            .position(|e| e.id == *element_id)
            .ok_or("Element not found")?;

        let element = self.timeline.elements.remove(element_index);

        // Notify managers and effects processor
        self.notify_managers_element_removed(&element);
        self.notify_effects_element_removed(&element);

        // Record event
        let event = TimelineEvent {
            event_type: TimelineEventType::ElementRemoved,
            timestamp: js_sys::Date::now(),
            element_id: Some(*element_id),
            track_id: Some(element.track_id),
            data: None,
        };
        self.add_event(event);

        Ok(())
    }

    pub fn move_element(&mut self, element_id: &Uuid, new_start_time: f64, new_track_id: Option<Uuid>) -> Result<(), String> {
        self.save_state_for_undo();

        // Apply snapping if enabled first
        let snapped_time = if self.state.snap_enabled {
            self.snap_to_grid(new_start_time)
        } else {
            new_start_time
        };

        let element = self.timeline.elements
            .iter_mut()
            .find(|e| e.id == *element_id)
            .ok_or("Element not found")?;

        element.start_time = snapped_time.max(0.0);

        if let Some(track_id) = new_track_id {
            if self.timeline.tracks.iter().any(|t| t.id == track_id) {
                element.track_id = track_id;
            }
        }

        // Update timeline duration if necessary
        let element_end = element.start_time + element.duration;
        if element_end > self.state.duration {
            self.state.duration = element_end;
        }

        // Record event
        let event = TimelineEvent {
            event_type: TimelineEventType::ElementMoved,
            timestamp: js_sys::Date::now(),
            element_id: Some(*element_id),
            track_id: Some(element.track_id),
            data: Some(format!("new_time:{}", element.start_time)),
        };
        self.add_event(event);

        Ok(())
    }

    pub fn resize_element(&mut self, element_id: &Uuid, new_duration: f64) -> Result<(), String> {
        self.save_state_for_undo();

        if new_duration <= 0.0 {
            return Err("Duration must be positive".to_string());
        }

        let element = self.timeline.elements
            .iter_mut()
            .find(|e| e.id == *element_id)
            .ok_or("Element not found")?;

        element.duration = new_duration;

        // Update timeline duration if necessary
        let element_end = element.start_time + element.duration;
        if element_end > self.state.duration {
            self.state.duration = element_end;
        }

        // Record event
        let event = TimelineEvent {
            event_type: TimelineEventType::ElementResized,
            timestamp: js_sys::Date::now(),
            element_id: Some(*element_id),
            track_id: Some(element.track_id),
            data: Some(format!("new_duration:{}", new_duration)),
        };
        self.add_event(event);

        Ok(())
    }

    pub fn set_current_time(&mut self, time: f64) -> Result<(), String> {
        let clamped_time = time.clamp(0.0, self.state.duration);
        self.state.current_time = clamped_time;

        // Notify audio/video managers of time change
        if let Some(ref audio_manager) = self.audio_manager {
            if let Ok(mut manager) = audio_manager.try_borrow_mut() {
                let _ = manager.seek(clamped_time);
            }
        }

        // Notify audio timeline sync and synchronize effects
        if let Some(ref audio_timeline_sync) = self.audio_timeline_sync {
            if let Ok(mut sync) = audio_timeline_sync.try_borrow_mut() {
                sync.update_timeline_position(clamped_time);
                sync.synchronize_effects_with_timeline();
            }
        }

        // Record event
        let event = TimelineEvent {
            event_type: TimelineEventType::TimeChanged,
            timestamp: js_sys::Date::now(),
            element_id: None,
            track_id: None,
            data: Some(format!("time:{}", clamped_time)),
        };
        self.add_event(event);

        Ok(())
    }

    pub fn get_current_time(&self) -> f64 {
        self.state.current_time
    }

    pub fn start_scrubbing(&mut self) {
        // Pause playback during scrubbing
        if self.state.is_playing {
            let _ = self.pause();
        }

        // Notify audio timeline sync
        if let Some(ref audio_timeline_sync) = self.audio_timeline_sync {
            if let Ok(mut sync) = audio_timeline_sync.try_borrow_mut() {
                sync.set_scrubbing_mode(true);
            }
        }
    }

    pub fn stop_scrubbing(&mut self) {
        // Notify audio timeline sync
        if let Some(ref audio_timeline_sync) = self.audio_timeline_sync {
            if let Ok(mut sync) = audio_timeline_sync.try_borrow_mut() {
                sync.set_scrubbing_mode(false);
            }
        }
    }

    pub fn scrub_to_position(&mut self, time: f64) {
        let clamped_time = time.clamp(0.0, self.state.duration);
        self.state.current_time = clamped_time;

        // Notify audio timeline sync for scrubbing
        if let Some(ref audio_timeline_sync) = self.audio_timeline_sync {
            if let Ok(mut sync) = audio_timeline_sync.try_borrow_mut() {
                sync.handle_scrub_position(clamped_time);
            }
        }
    }

    pub fn update_effect_parameter_live(&mut self, element_id: &Uuid, parameter: &str, value: f32) -> Result<(), String> {
        // Notify audio timeline sync for live parameter updates
        if let Some(ref audio_timeline_sync) = self.audio_timeline_sync {
            if let Ok(mut sync) = audio_timeline_sync.try_borrow_mut() {
                sync.update_realtime_parameter(element_id, parameter, value);
            }
        }

        Ok(())
    }

    pub fn play(&mut self) -> Result<(), String> {
        self.state.is_playing = true;

        // Notify audio manager
        if let Some(ref audio_manager) = self.audio_manager {
            if let Ok(mut manager) = audio_manager.try_borrow_mut() {
                manager.play()?;
            }
        }

        // Record event
        let event = TimelineEvent {
            event_type: TimelineEventType::PlaybackStarted,
            timestamp: js_sys::Date::now(),
            element_id: None,
            track_id: None,
            data: None,
        };
        self.add_event(event);

        Ok(())
    }

    pub fn pause(&mut self) -> Result<(), String> {
        self.state.is_playing = false;

        // Notify audio manager
        if let Some(ref audio_manager) = self.audio_manager {
            if let Ok(mut manager) = audio_manager.try_borrow_mut() {
                manager.pause()?;
            }
        }

        // Record event
        let event = TimelineEvent {
            event_type: TimelineEventType::PlaybackStopped,
            timestamp: js_sys::Date::now(),
            element_id: None,
            track_id: None,
            data: None,
        };
        self.add_event(event);

        Ok(())
    }

    pub fn undo(&mut self) -> Result<(), String> {
        if let Some(previous_state) = self.undo_stack.pop() {
            self.redo_stack.push(self.timeline.clone());
            self.timeline = previous_state;
            Ok(())
        } else {
            Err("Nothing to undo".to_string())
        }
    }

    pub fn redo(&mut self) -> Result<(), String> {
        if let Some(next_state) = self.redo_stack.pop() {
            self.undo_stack.push(self.timeline.clone());
            self.timeline = next_state;
            Ok(())
        } else {
            Err("Nothing to redo".to_string())
        }
    }

    fn save_state_for_undo(&mut self) {
        self.undo_stack.push(self.timeline.clone());
        self.redo_stack.clear(); // Clear redo stack when new action is performed

        // Limit undo stack size
        if self.undo_stack.len() > self.max_undo_levels {
            self.undo_stack.remove(0);
        }
    }

    fn snap_to_grid(&self, time: f64) -> f64 {
        let grid_size = 1.0 / self.state.zoom_level; // Adjust grid based on zoom
        let snapped = (time / grid_size).round() * grid_size;
        
        if (time - snapped).abs() <= self.state.snap_threshold {
            snapped
        } else {
            time
        }
    }

    fn notify_managers_element_added(&self, element: &TimelineElement) {
        match element.element_type {
            ElementType::Audio => {
                if let Some(ref audio_manager) = self.audio_manager {
                    if let Ok(mut manager) = audio_manager.try_borrow_mut() {
                        manager.update_timeline(vec![element.clone()]);
                    }
                }
            },
            ElementType::Video => {
                // Video elements would be handled by video decoder
            },
            _ => {}
        }
    }

    fn notify_managers_element_removed(&self, element: &TimelineElement) {
        // Notify managers about element removal
        // Implementation would depend on specific manager APIs
    }

    fn notify_effects_element_added(&self, element: &TimelineElement) {
        if element.element_type == ElementType::Audio {
            if let Some(ref effects_processor) = self.audio_effects_processor {
                if let Ok(mut processor) = effects_processor.try_borrow_mut() {
                    let element_id = element.id.to_string();
                    if let Err(e) = processor.add_audio_element(element_id) {
                        web_sys::console::warn_1(&format!("Failed to add audio element to effects processor: {:?}", e).into());
                    }
                }
            }
        }
    }

    fn notify_effects_element_removed(&self, element: &TimelineElement) {
        if element.element_type == ElementType::Audio {
            if let Some(ref effects_processor) = self.audio_effects_processor {
                if let Ok(mut processor) = effects_processor.try_borrow_mut() {
                    let element_id = element.id.to_string();
                    if let Err(e) = processor.remove_audio_element(&element_id) {
                        web_sys::console::warn_1(&format!("Failed to remove audio element from effects processor: {:?}", e).into());
                    }
                }
            }
        }
    }

    fn add_event(&mut self, event: TimelineEvent) {
        self.event_history.push(event.clone());
        
        // Notify listeners
        for listener in &self.change_listeners {
            listener(&event);
        }
    }

    // Getters
    pub fn get_timeline(&self) -> &Timeline {
        &self.timeline
    }

    pub fn get_state(&self) -> &TimelineState {
        &self.state
    }

    pub fn get_elements_at_time(&self, time: f64) -> Vec<&TimelineElement> {
        self.timeline.elements.iter()
            .filter(|e| time >= e.start_time && time < e.start_time + e.duration)
            .collect()
    }

    pub fn get_event_history(&self) -> &Vec<TimelineEvent> {
        &self.event_history
    }

    pub fn update_element_effects(&mut self, element_id: &Uuid, effects: AudioElementEffects) -> Result<(), String> {
        // Find the element in timeline
        let element = self.timeline.elements
            .iter()
            .find(|e| e.id == *element_id && e.element_type == ElementType::Audio)
            .ok_or("Audio element not found")?;

        // Update effects in processor
        if let Some(ref effects_processor) = self.audio_effects_processor {
            if let Ok(mut processor) = effects_processor.try_borrow_mut() {
                let element_id_str = element_id.to_string();
                processor.update_element_effects(&element_id_str, effects)
                    .map_err(|e| format!("Failed to update effects: {:?}", e))?;
            }
        }

        Ok(())
    }

    pub fn get_element_effects(&self, element_id: &Uuid) -> Option<AudioElementEffects> {
        if let Some(ref effects_processor) = self.audio_effects_processor {
            if let Ok(processor) = effects_processor.try_borrow() {
                let element_id_str = element_id.to_string();
                return processor.get_element_effects(&element_id_str).cloned();
            }
        }
        None
    }

    pub fn update_element_properties(&mut self, element_id: &Uuid, properties: ElementProperties) -> Result<(), String> {
        // Find and update the element
        let (element_type, track_id) = {
            let element = self.timeline.elements
                .iter_mut()
                .find(|e| e.id == *element_id)
                .ok_or("Element not found")?;

            let element_type = element.element_type.clone();
            let track_id = element.track_id;
            element.properties = properties.clone();
            (element_type, track_id)
        };

        // If this is an audio element, propagate volume changes to effects
        if element_type == ElementType::Audio {
            self.propagate_audio_property_changes(element_id, &properties)?;
        }

        // Record event
        let event = TimelineEvent {
            event_type: TimelineEventType::ElementPropertiesChanged,
            timestamp: js_sys::Date::now(),
            element_id: Some(*element_id),
            track_id: Some(track_id),
            data: None,
        };
        self.add_event(event);

        Ok(())
    }

    fn propagate_audio_property_changes(&self, element_id: &Uuid, properties: &ElementProperties) -> Result<(), String> {
        if let Some(ref effects_processor) = self.audio_effects_processor {
            if let Ok(mut processor) = effects_processor.try_borrow_mut() {
                let element_id_str = element_id.to_string();

                // Update volume if it changed
                if let Err(e) = processor.set_volume(&element_id_str, properties.volume as f32) {
                    web_sys::console::warn_1(&format!("Failed to update volume: {:?}", e).into());
                }

                // Update opacity (could affect audio gain)
                if let Err(e) = processor.set_volume(&element_id_str, (properties.volume * properties.opacity) as f32) {
                    web_sys::console::warn_1(&format!("Failed to update opacity-adjusted volume: {:?}", e).into());
                }
            }
        }
        Ok(())
    }

    pub fn set_playback_state(&mut self, is_playing: bool) -> Result<(), String> {
        self.state.is_playing = is_playing;

        // Synchronize effects with playback state
        if let Some(ref audio_timeline_sync) = self.audio_timeline_sync {
            if let Ok(mut sync) = audio_timeline_sync.try_borrow_mut() {
                if is_playing {
                    sync.start_sync();
                    sync.synchronize_effects_with_timeline();
                } else {
                    sync.stop_sync();
                }
            }
        }

        // Record event
        let event = TimelineEvent {
            event_type: if is_playing { TimelineEventType::PlaybackStarted } else { TimelineEventType::PlaybackStopped },
            timestamp: js_sys::Date::now(),
            element_id: None,
            track_id: None,
            data: None,
        };
        self.add_event(event);

        Ok(())
    }


}

impl Default for TimelineManager {
    fn default() -> Self {
        Self::new()
    }
}
