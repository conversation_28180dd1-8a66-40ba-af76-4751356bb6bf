use wasm_bindgen::prelude::*;
use web_sys::{CanvasRenderingContext2d, HtmlCanvasElement, TextMetrics, CanvasGradient};
use js_sys::Array;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextElement {
    pub id: String,
    pub text: String,
    pub position: TextPosition,
    pub style: TextStyle,
    pub animation: Option<TextAnimation>,
    pub start_time: f64,
    pub duration: f64,
    pub layer: u32,
    pub visible: bool,
    pub locked: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TextPosition {
    pub x: f32,
    pub y: f32,
    pub z: f32, // For 3D effects
    pub anchor_x: f32, // 0.0 = left, 0.5 = center, 1.0 = right
    pub anchor_y: f32, // 0.0 = top, 0.5 = middle, 1.0 = bottom
    pub rotation: f32, // degrees
    pub scale_x: f32,
    pub scale_y: f32,
}

impl Default for TextPosition {
    fn default() -> Self {
        Self {
            x: 0.0,
            y: 0.0,
            z: 0.0,
            anchor_x: 0.5,
            anchor_y: 0.5,
            rotation: 0.0,
            scale_x: 1.0,
            scale_y: 1.0,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TextStyle {
    pub font_family: String,
    pub font_size: f32,
    pub font_weight: FontWeight,
    pub font_style: FontStyle,
    pub color: String,
    pub alignment: TextAlignment,
    pub line_height: f32,
    pub letter_spacing: f32,
    pub word_spacing: f32,
    pub text_decoration: TextDecoration,
    pub text_transform: TextTransform,
    pub effects: Vec<TextEffect>,
    pub background: Option<TextBackground>,
    pub border: Option<TextBorder>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FontWeight {
    Thin,       // 100
    ExtraLight, // 200
    Light,      // 300
    Normal,     // 400
    Medium,     // 500
    SemiBold,   // 600
    Bold,       // 700
    ExtraBold,  // 800
    Black,      // 900
    Custom(u32),
    // Additional variants for compatibility
    Bolder,
    Lighter,
    W100,
    W200,
    W300,
    W400,
    W500,
    W600,
    W700,
    W800,
    W900,
}

impl FontWeight {
    pub fn to_css_value(&self) -> String {
        match self {
            FontWeight::Thin => "100".to_string(),
            FontWeight::ExtraLight => "200".to_string(),
            FontWeight::Light => "300".to_string(),
            FontWeight::Normal => "400".to_string(),
            FontWeight::Medium => "500".to_string(),
            FontWeight::SemiBold => "600".to_string(),
            FontWeight::Bold => "700".to_string(),
            FontWeight::ExtraBold => "800".to_string(),
            FontWeight::Black => "900".to_string(),
            FontWeight::Custom(weight) => weight.to_string(),
            FontWeight::Bolder => "bolder".to_string(),
            FontWeight::Lighter => "lighter".to_string(),
            FontWeight::W100 => "100".to_string(),
            FontWeight::W200 => "200".to_string(),
            FontWeight::W300 => "300".to_string(),
            FontWeight::W400 => "400".to_string(),
            FontWeight::W500 => "500".to_string(),
            FontWeight::W600 => "600".to_string(),
            FontWeight::W700 => "700".to_string(),
            FontWeight::W800 => "800".to_string(),
            FontWeight::W900 => "900".to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FontStyle {
    Normal,
    Italic,
    Oblique(f32), // angle in degrees
}

impl FontStyle {
    pub fn to_css_value(&self) -> String {
        match self {
            FontStyle::Normal => "normal".to_string(),
            FontStyle::Italic => "italic".to_string(),
            FontStyle::Oblique(angle) => format!("oblique {}deg", angle),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TextAlignment {
    Left,
    Center,
    Right,
    Justify,
    Start,
    End,
}

impl TextAlignment {
    pub fn to_css_value(&self) -> String {
        match self {
            TextAlignment::Left => "left".to_string(),
            TextAlignment::Center => "center".to_string(),
            TextAlignment::Right => "right".to_string(),
            TextAlignment::Justify => "justify".to_string(),
            TextAlignment::Start => "start".to_string(),
            TextAlignment::End => "end".to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TextDecoration {
    pub underline: bool,
    pub overline: bool,
    pub line_through: bool,
    pub color: Option<String>,
    pub style: TextDecorationStyle,
    pub thickness: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TextDecorationStyle {
    Solid,
    Double,
    Dotted,
    Dashed,
    Wavy,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TextTransform {
    None,
    Uppercase,
    Lowercase,
    Capitalize,
    FullWidth,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TextEffect {
    pub effect_type: TextEffectType,
    pub enabled: bool,
    pub opacity: f32,
    pub blend_mode: BlendMode,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TextEffectType {
    Shadow {
        offset_x: f32,
        offset_y: f32,
        blur_radius: f32,
        color: String,
    },
    Outline {
        width: f32,
        color: String,
        style: OutlineStyle,
    },
    Glow {
        radius: f32,
        color: String,
        intensity: f32,
    },
    Gradient {
        gradient_type: GradientType,
        colors: Vec<ColorStop>,
        angle: f32, // for linear gradients
    },
    Stroke {
        width: f32,
        color: String,
        position: StrokePosition,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum OutlineStyle {
    Solid,
    Dashed,
    Dotted,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum GradientType {
    Linear,
    Radial,
    Conic,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ColorStop {
    pub color: String,
    pub position: f32, // 0.0 to 1.0
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum StrokePosition {
    Outside,
    Inside,
    Center,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum BlendMode {
    Normal,
    Multiply,
    Screen,
    Overlay,
    SoftLight,
    HardLight,
    ColorDodge,
    ColorBurn,
    Darken,
    Lighten,
    Difference,
    Exclusion,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TextBackground {
    pub color: String,
    pub opacity: f32,
    pub padding: TextPadding,
    pub border_radius: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TextPadding {
    pub top: f32,
    pub right: f32,
    pub bottom: f32,
    pub left: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TextBorder {
    pub width: f32,
    pub color: String,
    pub style: BorderStyle,
    pub radius: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum BorderStyle {
    Solid,
    Dashed,
    Dotted,
    Double,
    Groove,
    Ridge,
    Inset,
    Outset,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TextAnimation {
    pub animation_type: TextAnimationType,
    pub duration: f64,
    pub delay: f64,
    pub easing: EasingFunction,
    pub repeat_count: RepeatCount,
    pub direction: AnimationDirection,
    pub fill_mode: FillMode,
    pub keyframes: Vec<TextKeyframe>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TextAnimationType {
    FadeIn,
    FadeOut,
    SlideIn { direction: SlideDirection },
    SlideOut { direction: SlideDirection },
    Scale { from: f32, to: f32 },
    Rotate { from: f32, to: f32 },
    Typewriter { speed: f32 },
    Bounce,
    Pulse,
    Shake,
    Custom { name: String },
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum SlideDirection {
    Left,
    Right,
    Up,
    Down,
    TopLeft,
    TopRight,
    BottomLeft,
    BottomRight,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum EasingFunction {
    Linear,
    EaseIn,
    EaseOut,
    EaseInOut,
    CubicBezier(f32, f32, f32, f32),
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum RepeatCount {
    Once,
    Infinite,
    Count(u32),
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AnimationDirection {
    Normal,
    Reverse,
    Alternate,
    AlternateReverse,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FillMode {
    None,
    Forwards,
    Backwards,
    Both,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TextKeyframe {
    pub time: f32, // 0.0 to 1.0
    pub position: Option<TextPosition>,
    pub style: Option<TextStyle>,
    pub opacity: Option<f32>,
    pub easing: Option<EasingFunction>,
}

impl Default for TextStyle {
    fn default() -> Self {
        Self {
            font_family: "Arial, sans-serif".to_string(),
            font_size: 24.0,
            font_weight: FontWeight::Normal,
            font_style: FontStyle::Normal,
            color: "#FFFFFF".to_string(),
            alignment: TextAlignment::Center,
            line_height: 1.2,
            letter_spacing: 0.0,
            word_spacing: 0.0,
            text_decoration: TextDecoration {
                underline: false,
                overline: false,
                line_through: false,
                color: None,
                style: TextDecorationStyle::Solid,
                thickness: 1.0,
            },
            text_transform: TextTransform::None,
            effects: Vec::new(),
            background: None,
            border: None,
        }
    }
}

impl TextElement {
    pub fn new(id: String, text: String) -> Self {
        Self {
            id,
            text,
            position: TextPosition::default(),
            style: TextStyle::default(),
            animation: None,
            start_time: 0.0,
            duration: 5.0, // Default 5 seconds
            layer: 0,
            visible: true,
            locked: false,
        }
    }

    pub fn with_position(mut self, x: f32, y: f32) -> Self {
        self.position.x = x;
        self.position.y = y;
        self
    }

    pub fn with_style(mut self, style: TextStyle) -> Self {
        self.style = style;
        self
    }

    pub fn with_animation(mut self, animation: TextAnimation) -> Self {
        self.animation = Some(animation);
        self
    }

    pub fn with_timing(mut self, start_time: f64, duration: f64) -> Self {
        self.start_time = start_time;
        self.duration = duration;
        self
    }

    pub fn is_active_at_time(&self, time: f64) -> bool {
        time >= self.start_time && time <= self.start_time + self.duration
    }

    pub fn get_progress_at_time(&self, time: f64) -> f32 {
        if !self.is_active_at_time(time) {
            return if time < self.start_time { 0.0 } else { 1.0 };
        }
        
        ((time - self.start_time) / self.duration) as f32
    }

    pub fn clone_with_id(&self, new_id: String) -> Self {
        let mut cloned = self.clone();
        cloned.id = new_id;
        cloned
    }

    pub fn get_bounding_box(&self, context: &CanvasRenderingContext2d) -> Result<TextBoundingBox, JsValue> {
        // Set font for measurement
        let font_string = format!(
            "{} {} {}px {}",
            self.style.font_style.to_css_value(),
            self.style.font_weight.to_css_value(),
            self.style.font_size,
            self.style.font_family
        );
        context.set_font(&font_string);

        let metrics = context.measure_text(&self.text)?;
        let width = metrics.width() as f32;
        
        // Estimate height based on font size and line height
        let height = self.style.font_size * self.style.line_height;
        
        // Calculate actual position based on anchor
        let actual_x = self.position.x - (width * self.position.anchor_x);
        let actual_y = self.position.y - (height * self.position.anchor_y);

        Ok(TextBoundingBox {
            x: actual_x,
            y: actual_y,
            width,
            height,
        })
    }
}

#[derive(Debug, Clone)]
pub struct TextBoundingBox {
    pub x: f32,
    pub y: f32,
    pub width: f32,
    pub height: f32,
}

impl TextBoundingBox {
    pub fn contains_point(&self, x: f32, y: f32) -> bool {
        x >= self.x && x <= self.x + self.width && y >= self.y && y <= self.y + self.height
    }

    pub fn intersects(&self, other: &TextBoundingBox) -> bool {
        !(self.x + self.width < other.x || 
          other.x + other.width < self.x || 
          self.y + self.height < other.y || 
          other.y + other.height < self.y)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextPreset {
    pub id: String,
    pub name: String,
    pub description: String,
    pub category: String,
    pub style: TextStyle,
    pub animation: Option<TextAnimation>,
    pub thumbnail: Option<String>, // Base64 encoded image
}

impl TextPreset {
    pub fn apply_to_element(&self, element: &mut TextElement) {
        element.style = self.style.clone();
        if let Some(animation) = &self.animation {
            element.animation = Some(animation.clone());
        }
    }
}

pub struct TextPresetLibrary {
    presets: HashMap<String, TextPreset>,
    categories: Vec<String>,
}

impl TextPresetLibrary {
    pub fn new() -> Self {
        let mut library = Self {
            presets: HashMap::new(),
            categories: Vec::new(),
        };
        library.load_default_presets();
        library
    }

    fn load_default_presets(&mut self) {
        // Add some default presets
        self.add_category("Basic".to_string());
        self.add_category("Animated".to_string());
        self.add_category("Effects".to_string());

        // Basic title preset
        let title_preset = TextPreset {
            id: "basic_title".to_string(),
            name: "Basic Title".to_string(),
            description: "Simple white title text".to_string(),
            category: "Basic".to_string(),
            style: TextStyle {
                font_size: 48.0,
                font_weight: FontWeight::Bold,
                color: "#FFFFFF".to_string(),
                alignment: TextAlignment::Center,
                ..Default::default()
            },
            animation: None,
            thumbnail: None,
        };
        self.add_preset(title_preset);

        // Subtitle preset
        let subtitle_preset = TextPreset {
            id: "basic_subtitle".to_string(),
            name: "Subtitle".to_string(),
            description: "Smaller subtitle text".to_string(),
            category: "Basic".to_string(),
            style: TextStyle {
                font_size: 24.0,
                font_weight: FontWeight::Normal,
                color: "#CCCCCC".to_string(),
                alignment: TextAlignment::Center,
                ..Default::default()
            },
            animation: None,
            thumbnail: None,
        };
        self.add_preset(subtitle_preset);
    }

    pub fn add_preset(&mut self, preset: TextPreset) {
        if !self.categories.contains(&preset.category) {
            self.categories.push(preset.category.clone());
        }
        self.presets.insert(preset.id.clone(), preset);
    }

    pub fn remove_preset(&mut self, id: &str) -> Option<TextPreset> {
        self.presets.remove(id)
    }

    pub fn get_preset(&self, id: &str) -> Option<&TextPreset> {
        self.presets.get(id)
    }

    pub fn get_presets_by_category(&self, category: &str) -> Vec<&TextPreset> {
        self.presets.values().filter(|p| p.category == category).collect()
    }

    pub fn get_all_presets(&self) -> Vec<&TextPreset> {
        self.presets.values().collect()
    }

    pub fn get_categories(&self) -> &Vec<String> {
        &self.categories
    }

    pub fn add_category(&mut self, category: String) {
        if !self.categories.contains(&category) {
            self.categories.push(category);
        }
    }

    pub fn search_presets(&self, query: &str) -> Vec<&TextPreset> {
        let query_lower = query.to_lowercase();
        self.presets.values()
            .filter(|p| {
                p.name.to_lowercase().contains(&query_lower) ||
                p.description.to_lowercase().contains(&query_lower) ||
                p.category.to_lowercase().contains(&query_lower)
            })
            .collect()
    }
}

// Text Rendering Engine Implementation
pub struct TextRenderingEngine {
    canvas_cache: HashMap<String, HtmlCanvasElement>,
    font_cache: HashMap<String, bool>, // font_family -> loaded
    metrics_cache: HashMap<String, TextMetrics>,
}

impl TextRenderingEngine {
    pub fn new() -> Self {
        Self {
            canvas_cache: HashMap::new(),
            font_cache: HashMap::new(),
            metrics_cache: HashMap::new(),
        }
    }

    /// Render a text element to the given canvas context
    pub fn render_text_element(
        &mut self,
        context: &CanvasRenderingContext2d,
        element: &TextElement,
        current_time: f64,
        canvas_width: f32,
        canvas_height: f32,
    ) -> Result<(), JsValue> {
        // Check if element is active at current time
        if !element.is_active_at_time(current_time) || !element.visible {
            return Ok(());
        }

        // Calculate animation progress
        let progress = element.get_progress_at_time(current_time);

        // Apply animation transformations if present
        let (animated_position, animated_style, animated_opacity) =
            self.calculate_animated_properties(element, progress);

        // Save context state
        context.save();

        // Set up transformations
        self.apply_transformations(context, &animated_position, canvas_width, canvas_height)?;

        // Set up text styling
        self.apply_text_style(context, &animated_style)?;

        // Apply opacity
        context.set_global_alpha(animated_opacity as f64);

        // Render text effects (shadows, outlines, etc.)
        self.render_text_effects(context, element, &animated_style)?;

        // Render background if present
        if let Some(background) = &animated_style.background {
            self.render_text_background(context, element, background, &animated_style)?;
        }

        // Render the main text
        self.render_main_text(context, element, &animated_style)?;

        // Render border if present
        if let Some(border) = &animated_style.border {
            self.render_text_border(context, element, border, &animated_style)?;
        }

        // Restore context state
        context.restore();

        Ok(())
    }

    /// Calculate animated properties based on current progress
    fn calculate_animated_properties(
        &self,
        element: &TextElement,
        progress: f32,
    ) -> (TextPosition, TextStyle, f32) {
        let mut position = element.position.clone();
        let mut style = element.style.clone();
        let mut opacity = 1.0f32;

        if let Some(animation) = &element.animation {
            // Apply keyframe animations
            for keyframe in &animation.keyframes {
                if progress >= keyframe.time {
                    if let Some(kf_position) = &keyframe.position {
                        position = self.interpolate_position(&position, kf_position, progress - keyframe.time);
                    }
                    if let Some(kf_style) = &keyframe.style {
                        style = self.interpolate_style(&style, kf_style, progress - keyframe.time);
                    }
                    if let Some(kf_opacity) = keyframe.opacity {
                        opacity = kf_opacity;
                    }
                }
            }

            // Apply built-in animation types
            match &animation.animation_type {
                TextAnimationType::FadeIn => {
                    opacity = progress;
                }
                TextAnimationType::FadeOut => {
                    opacity = 1.0 - progress;
                }
                TextAnimationType::SlideIn { direction } => {
                    let (offset_x, offset_y) = self.get_slide_offset(direction, progress);
                    position.x += offset_x;
                    position.y += offset_y;
                }
                TextAnimationType::SlideOut { direction } => {
                    let (offset_x, offset_y) = self.get_slide_offset(direction, 1.0 - progress);
                    position.x += offset_x;
                    position.y += offset_y;
                }
                TextAnimationType::Scale { from, to } => {
                    let scale = from + (to - from) * progress;
                    position.scale_x = scale;
                    position.scale_y = scale;
                }
                TextAnimationType::Rotate { from, to } => {
                    position.rotation = from + (to - from) * progress;
                }
                TextAnimationType::Typewriter { speed: _ } => {
                    // This would be handled in text rendering by limiting visible characters
                }
                TextAnimationType::Bounce => {
                    let bounce_offset = (progress * std::f32::consts::PI * 4.0).sin() * 10.0 * (1.0 - progress);
                    position.y -= bounce_offset;
                }
                TextAnimationType::Pulse => {
                    let pulse_scale = 1.0 + (progress * std::f32::consts::PI * 2.0).sin() * 0.1;
                    position.scale_x = pulse_scale;
                    position.scale_y = pulse_scale;
                }
                TextAnimationType::Shake => {
                    let shake_x = (progress * std::f32::consts::PI * 20.0).sin() * 5.0;
                    let shake_y = (progress * std::f32::consts::PI * 25.0).cos() * 3.0;
                    position.x += shake_x;
                    position.y += shake_y;
                }
                TextAnimationType::Custom { name: _ } => {
                    // Custom animations would be handled by external plugins
                }
            }
        }

        (position, style, opacity)
    }

    /// Apply transformations to the canvas context
    fn apply_transformations(
        &self,
        context: &CanvasRenderingContext2d,
        position: &TextPosition,
        canvas_width: f32,
        canvas_height: f32,
    ) -> Result<(), JsValue> {
        // Translate to position
        context.translate(position.x as f64, position.y as f64)?;

        // Apply rotation
        if position.rotation != 0.0 {
            context.rotate((position.rotation * std::f32::consts::PI / 180.0) as f64)?;
        }

        // Apply scaling
        if position.scale_x != 1.0 || position.scale_y != 1.0 {
            context.scale(position.scale_x as f64, position.scale_y as f64)?;
        }

        Ok(())
    }

    /// Apply text styling to the canvas context
    fn apply_text_style(
        &self,
        context: &CanvasRenderingContext2d,
        style: &TextStyle,
    ) -> Result<(), JsValue> {
        // Set font
        let font_string = format!(
            "{} {} {}px {}",
            style.font_style.to_css_value(),
            style.font_weight.to_css_value(),
            style.font_size,
            style.font_family
        );
        context.set_font(&font_string);

        // Set text alignment
        context.set_text_align(&style.alignment.to_css_value());

        // Set text baseline
        context.set_text_baseline("middle");

        // Set fill color
        context.set_fill_style_str(&style.color);

        Ok(())
    }

    /// Render text effects (shadows, outlines, glows)
    fn render_text_effects(
        &self,
        context: &CanvasRenderingContext2d,
        element: &TextElement,
        style: &TextStyle,
    ) -> Result<(), JsValue> {
        for effect in &style.effects {
            if !effect.enabled {
                continue;
            }

            context.save();
            context.set_global_alpha(effect.opacity as f64);

            match &effect.effect_type {
                TextEffectType::Shadow { offset_x, offset_y, blur_radius, color } => {
                    context.set_shadow_offset_x(*offset_x as f64);
                    context.set_shadow_offset_y(*offset_y as f64);
                    context.set_shadow_blur(*blur_radius as f64);
                    context.set_shadow_color(color);

                    // Render text with shadow
                    context.fill_text(&element.text, 0.0, 0.0)?;
                }
                TextEffectType::Outline { width, color, style: _ } => {
                    context.set_stroke_style_str(color);
                    context.set_line_width(*width as f64);
                    context.stroke_text(&element.text, 0.0, 0.0)?;
                }
                TextEffectType::Glow { radius, color, intensity } => {
                    // Create glow effect by rendering multiple shadows
                    for i in 1..=(*radius as i32) {
                        let alpha = (intensity / *radius) * (1.0 - (i as f32 / *radius));
                        context.set_global_alpha(alpha as f64);
                        context.set_shadow_blur(i as f64);
                        context.set_shadow_color(color);
                        context.fill_text(&element.text, 0.0, 0.0)?;
                    }
                }
                TextEffectType::Gradient { gradient_type, colors, angle } => {
                    let gradient = self.create_gradient(context, gradient_type, colors, *angle, element)?;
                    context.set_fill_style(&gradient);
                    context.fill_text(&element.text, 0.0, 0.0)?;
                }
                TextEffectType::Stroke { width, color, position: _ } => {
                    context.set_stroke_style_str(color);
                    context.set_line_width(*width as f64);
                    context.stroke_text(&element.text, 0.0, 0.0)?;
                }
            }

            context.restore();
        }

        Ok(())
    }

    /// Create a gradient for text effects
    fn create_gradient(
        &self,
        context: &CanvasRenderingContext2d,
        gradient_type: &GradientType,
        colors: &[ColorStop],
        angle: f32,
        element: &TextElement,
    ) -> Result<CanvasGradient, JsValue> {
        let bbox = element.get_bounding_box(context)?;

        let gradient = match gradient_type {
            GradientType::Linear => {
                let angle_rad = angle * std::f32::consts::PI / 180.0;
                let x1 = bbox.x - (bbox.width / 2.0) * angle_rad.cos();
                let y1 = bbox.y - (bbox.height / 2.0) * angle_rad.sin();
                let x2 = bbox.x + (bbox.width / 2.0) * angle_rad.cos();
                let y2 = bbox.y + (bbox.height / 2.0) * angle_rad.sin();

                context.create_linear_gradient(x1 as f64, y1 as f64, x2 as f64, y2 as f64)
            }
            GradientType::Radial => {
                let center_x = bbox.x + bbox.width / 2.0;
                let center_y = bbox.y + bbox.height / 2.0;
                let radius = (bbox.width.max(bbox.height)) / 2.0;

                context.create_radial_gradient(
                    center_x as f64, center_y as f64, 0.0,
                    center_x as f64, center_y as f64, radius as f64
                )?
            }
            GradientType::Conic => {
                // Conic gradients are not directly supported in Canvas 2D
                // Fall back to radial gradient
                let center_x = bbox.x + bbox.width / 2.0;
                let center_y = bbox.y + bbox.height / 2.0;
                let radius = (bbox.width.max(bbox.height)) / 2.0;

                context.create_radial_gradient(
                    center_x as f64, center_y as f64, 0.0,
                    center_x as f64, center_y as f64, radius as f64
                )?
            }
        };

        // Add color stops
        for color_stop in colors {
            gradient.add_color_stop(color_stop.position, &color_stop.color)?;
        }

        Ok(gradient)
    }

    /// Render text background
    fn render_text_background(
        &self,
        context: &CanvasRenderingContext2d,
        element: &TextElement,
        background: &TextBackground,
        style: &TextStyle,
    ) -> Result<(), JsValue> {
        let bbox = element.get_bounding_box(context)?;

        context.save();
        context.set_global_alpha(background.opacity as f64);
        context.set_fill_style_str(&background.color);

        // Calculate background rectangle with padding
        let bg_x = bbox.x - background.padding.left;
        let bg_y = bbox.y - background.padding.top;
        let bg_width = bbox.width + background.padding.left + background.padding.right;
        let bg_height = bbox.height + background.padding.top + background.padding.bottom;

        if background.border_radius > 0.0 {
            // Draw rounded rectangle
            self.draw_rounded_rect(context, bg_x, bg_y, bg_width, bg_height, background.border_radius)?;
            context.fill();
        } else {
            // Draw regular rectangle
            context.fill_rect(bg_x as f64, bg_y as f64, bg_width as f64, bg_height as f64);
        }

        context.restore();
        Ok(())
    }

    /// Render text border
    fn render_text_border(
        &self,
        context: &CanvasRenderingContext2d,
        element: &TextElement,
        border: &TextBorder,
        style: &TextStyle,
    ) -> Result<(), JsValue> {
        let bbox = element.get_bounding_box(context)?;

        context.save();
        context.set_stroke_style_str(&border.color);
        context.set_line_width(border.width as f64);

        // Set line dash pattern based on border style
        match border.style {
            BorderStyle::Dashed => {
                let dash_array = Array::new();
                dash_array.push(&JsValue::from(5.0));
                dash_array.push(&JsValue::from(5.0));
                context.set_line_dash(&dash_array)?;
            }
            BorderStyle::Dotted => {
                let dash_array = Array::new();
                dash_array.push(&JsValue::from(2.0));
                dash_array.push(&JsValue::from(3.0));
                context.set_line_dash(&dash_array)?;
            }
            _ => {
                // Solid and other styles use default line
            }
        }

        if border.radius > 0.0 {
            // Draw rounded rectangle border
            self.draw_rounded_rect(context, bbox.x, bbox.y, bbox.width, bbox.height, border.radius)?;
            context.stroke();
        } else {
            // Draw regular rectangle border
            context.stroke_rect(bbox.x as f64, bbox.y as f64, bbox.width as f64, bbox.height as f64);
        }

        context.restore();
        Ok(())
    }

    /// Render the main text content
    fn render_main_text(
        &self,
        context: &CanvasRenderingContext2d,
        element: &TextElement,
        style: &TextStyle,
    ) -> Result<(), JsValue> {
        // Handle text transformation
        let transformed_text = self.apply_text_transform(&element.text, &style.text_transform);

        // Handle multi-line text
        let lines = self.split_text_into_lines(&transformed_text, style);
        let line_height = style.font_size * style.line_height;

        for (i, line) in lines.iter().enumerate() {
            let y_offset = (i as f32 - (lines.len() as f32 - 1.0) / 2.0) * line_height;

            // Apply text decorations
            if style.text_decoration.underline || style.text_decoration.overline || style.text_decoration.line_through {
                self.render_text_decorations(context, line, y_offset, style)?;
            }

            // Render the text line
            context.fill_text(line, 0.0, y_offset as f64)?;
        }

        Ok(())
    }

    /// Apply text transformations
    fn apply_text_transform(&self, text: &str, transform: &TextTransform) -> String {
        match transform {
            TextTransform::None => text.to_string(),
            TextTransform::Uppercase => text.to_uppercase(),
            TextTransform::Lowercase => text.to_lowercase(),
            TextTransform::Capitalize => {
                text.split_whitespace()
                    .map(|word| {
                        let mut chars = word.chars();
                        match chars.next() {
                            None => String::new(),
                            Some(first) => first.to_uppercase().collect::<String>() + chars.as_str(),
                        }
                    })
                    .collect::<Vec<_>>()
                    .join(" ")
            }
            TextTransform::FullWidth => {
                // Convert to full-width characters (mainly for CJK text)
                text.chars()
                    .map(|c| match c {
                        ' ' => '\u{3000}', // Full-width space
                        '!' => '\u{FF01}',
                        '"' => '\u{FF02}',
                        // Add more mappings as needed
                        _ => c,
                    })
                    .collect()
            }
        }
    }

    /// Split text into lines for multi-line rendering
    fn split_text_into_lines(&self, text: &str, style: &TextStyle) -> Vec<String> {
        // For now, just split on newlines
        // In a more advanced implementation, this would handle word wrapping
        text.lines().map(|line| line.to_string()).collect()
    }

    /// Render text decorations (underline, overline, line-through)
    fn render_text_decorations(
        &self,
        context: &CanvasRenderingContext2d,
        text: &str,
        y_offset: f32,
        style: &TextStyle,
    ) -> Result<(), JsValue> {
        let metrics = context.measure_text(text)?;
        let text_width = metrics.width() as f32;
        let font_size = style.font_size;

        context.save();

        // Set decoration color
        if let Some(color) = &style.text_decoration.color {
            context.set_stroke_style_str(color);
        }

        context.set_line_width(style.text_decoration.thickness as f64);

        // Set line style based on decoration style
        match style.text_decoration.style {
            TextDecorationStyle::Dashed => {
                let dash_array = Array::new();
                dash_array.push(&JsValue::from(4.0));
                dash_array.push(&JsValue::from(2.0));
                context.set_line_dash(&dash_array)?;
            }
            TextDecorationStyle::Dotted => {
                let dash_array = Array::new();
                dash_array.push(&JsValue::from(1.0));
                dash_array.push(&JsValue::from(2.0));
                context.set_line_dash(&dash_array)?;
            }
            _ => {}
        }

        let start_x = -text_width / 2.0;
        let end_x = text_width / 2.0;

        // Underline
        if style.text_decoration.underline {
            let underline_y = y_offset + font_size * 0.1;
            context.begin_path();
            context.move_to(start_x as f64, underline_y as f64);
            context.line_to(end_x as f64, underline_y as f64);
            context.stroke();
        }

        // Overline
        if style.text_decoration.overline {
            let overline_y = y_offset - font_size * 0.8;
            context.begin_path();
            context.move_to(start_x as f64, overline_y as f64);
            context.line_to(end_x as f64, overline_y as f64);
            context.stroke();
        }

        // Line-through
        if style.text_decoration.line_through {
            let line_through_y = y_offset - font_size * 0.3;
            context.begin_path();
            context.move_to(start_x as f64, line_through_y as f64);
            context.line_to(end_x as f64, line_through_y as f64);
            context.stroke();
        }

        context.restore();
        Ok(())
    }

    /// Draw a rounded rectangle path
    fn draw_rounded_rect(
        &self,
        context: &CanvasRenderingContext2d,
        x: f32,
        y: f32,
        width: f32,
        height: f32,
        radius: f32,
    ) -> Result<(), JsValue> {
        let r = radius.min(width / 2.0).min(height / 2.0);

        context.begin_path();
        context.move_to((x + r) as f64, y as f64);
        context.line_to((x + width - r) as f64, y as f64);
        context.arc_to((x + width) as f64, y as f64, (x + width) as f64, (y + r) as f64, r as f64)?;
        context.line_to((x + width) as f64, (y + height - r) as f64);
        context.arc_to((x + width) as f64, (y + height) as f64, (x + width - r) as f64, (y + height) as f64, r as f64)?;
        context.line_to((x + r) as f64, (y + height) as f64);
        context.arc_to(x as f64, (y + height) as f64, x as f64, (y + height - r) as f64, r as f64)?;
        context.line_to(x as f64, (y + r) as f64);
        context.arc_to(x as f64, y as f64, (x + r) as f64, y as f64, r as f64)?;
        context.close_path();

        Ok(())
    }

    /// Get slide animation offset based on direction and progress
    fn get_slide_offset(&self, direction: &SlideDirection, progress: f32) -> (f32, f32) {
        let distance = 100.0 * (1.0 - progress);

        match direction {
            SlideDirection::Left => (-distance, 0.0),
            SlideDirection::Right => (distance, 0.0),
            SlideDirection::Up => (0.0, -distance),
            SlideDirection::Down => (0.0, distance),
            SlideDirection::TopLeft => (-distance * 0.707, -distance * 0.707),
            SlideDirection::TopRight => (distance * 0.707, -distance * 0.707),
            SlideDirection::BottomLeft => (-distance * 0.707, distance * 0.707),
            SlideDirection::BottomRight => (distance * 0.707, distance * 0.707),
        }
    }

    /// Interpolate between two positions
    fn interpolate_position(&self, from: &TextPosition, to: &TextPosition, t: f32) -> TextPosition {
        TextPosition {
            x: from.x + (to.x - from.x) * t,
            y: from.y + (to.y - from.y) * t,
            z: from.z + (to.z - from.z) * t,
            anchor_x: from.anchor_x + (to.anchor_x - from.anchor_x) * t,
            anchor_y: from.anchor_y + (to.anchor_y - from.anchor_y) * t,
            rotation: from.rotation + (to.rotation - from.rotation) * t,
            scale_x: from.scale_x + (to.scale_x - from.scale_x) * t,
            scale_y: from.scale_y + (to.scale_y - from.scale_y) * t,
        }
    }

    /// Interpolate between two styles (simplified version)
    fn interpolate_style(&self, from: &TextStyle, to: &TextStyle, t: f32) -> TextStyle {
        // For now, just return the target style
        // In a full implementation, this would interpolate colors, sizes, etc.
        to.clone()
    }

    /// Clear the rendering cache
    pub fn clear_cache(&mut self) {
        self.canvas_cache.clear();
        self.font_cache.clear();
        self.metrics_cache.clear();
    }

    /// Pre-load a font for better performance
    pub fn preload_font(&mut self, font_family: &str) -> Result<(), JsValue> {
        // This would use the Font Loading API to preload fonts
        // For now, just mark as loaded
        self.font_cache.insert(font_family.to_string(), true);
        Ok(())
    }

    /// Check if a font is loaded
    pub fn is_font_loaded(&self, font_family: &str) -> bool {
        self.font_cache.get(font_family).copied().unwrap_or(false)
    }

    /// Get cached text metrics
    pub fn get_cached_metrics(&self, cache_key: &str) -> Option<&TextMetrics> {
        self.metrics_cache.get(cache_key)
    }

    /// Cache text metrics for performance
    pub fn cache_metrics(&mut self, cache_key: String, metrics: TextMetrics) {
        self.metrics_cache.insert(cache_key, metrics);
    }

    /// Generate a cache key for text metrics
    pub fn generate_metrics_cache_key(&self, text: &str, style: &TextStyle) -> String {
        format!(
            "{}:{}:{}:{}:{}",
            text,
            style.font_family,
            style.font_size,
            style.font_weight.to_css_value(),
            style.font_style.to_css_value()
        )
    }
}

impl Default for TextRenderingEngine {
    fn default() -> Self {
        Self::new()
    }
}

// Text Positioning System Implementation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextPositioningSystem {
    pub snap_to_grid: bool,
    pub grid_size: f32,
    pub snap_threshold: f32,
    pub alignment_guides: bool,
    pub safe_area_guides: bool,
    pub safe_area_margin: f32,
}

impl Default for TextPositioningSystem {
    fn default() -> Self {
        Self {
            snap_to_grid: false,
            grid_size: 10.0,
            snap_threshold: 5.0,
            alignment_guides: true,
            safe_area_guides: true,
            safe_area_margin: 0.1, // 10% margin
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PositionConstraints {
    pub min_x: Option<f32>,
    pub max_x: Option<f32>,
    pub min_y: Option<f32>,
    pub max_y: Option<f32>,
    pub aspect_ratio: Option<f32>,
    pub maintain_aspect_ratio: bool,
}

impl Default for PositionConstraints {
    fn default() -> Self {
        Self {
            min_x: None,
            max_x: None,
            min_y: None,
            max_y: None,
            aspect_ratio: None,
            maintain_aspect_ratio: false,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlignmentGuide {
    pub guide_type: AlignmentGuideType,
    pub position: f32,
    pub active: bool,
    pub color: String,
    pub thickness: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlignmentGuideType {
    Horizontal,
    Vertical,
    Center,
    ThirdsHorizontal,
    ThirdsVertical,
    GoldenRatio,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PositionSnap {
    pub snap_type: SnapType,
    pub position: TextPosition,
    pub distance: f32,
    pub guide: Option<AlignmentGuide>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SnapType {
    Grid,
    Guide,
    Element,
    SafeArea,
    Center,
}

impl TextPositioningSystem {
    pub fn new() -> Self {
        Self::default()
    }

    /// Calculate snapped position based on current position and constraints
    pub fn calculate_snapped_position(
        &self,
        current_position: &TextPosition,
        canvas_width: f32,
        canvas_height: f32,
        constraints: &PositionConstraints,
        other_elements: &[TextElement],
    ) -> (TextPosition, Vec<PositionSnap>) {
        let mut snapped_position = current_position.clone();
        let mut snaps = Vec::new();

        // Apply grid snapping
        if self.snap_to_grid {
            let (grid_snapped, grid_snap) = self.snap_to_grid_position(&snapped_position);
            if let Some(snap) = grid_snap {
                snapped_position = grid_snapped;
                snaps.push(snap);
            }
        }

        // Apply alignment guide snapping
        if self.alignment_guides {
            let guides = self.generate_alignment_guides(canvas_width, canvas_height);
            let (guide_snapped, guide_snaps) = self.snap_to_guides(&snapped_position, &guides);
            snapped_position = guide_snapped;
            snaps.extend(guide_snaps);
        }

        // Apply element-to-element snapping
        let (element_snapped, element_snaps) = self.snap_to_elements(&snapped_position, other_elements);
        snapped_position = element_snapped;
        snaps.extend(element_snaps);

        // Apply safe area constraints
        if self.safe_area_guides {
            snapped_position = self.apply_safe_area_constraints(&snapped_position, canvas_width, canvas_height);
        }

        // Apply position constraints
        snapped_position = self.apply_position_constraints(&snapped_position, constraints);

        (snapped_position, snaps)
    }

    /// Snap position to grid
    fn snap_to_grid_position(&self, position: &TextPosition) -> (TextPosition, Option<PositionSnap>) {
        let grid_x = (position.x / self.grid_size).round() * self.grid_size;
        let grid_y = (position.y / self.grid_size).round() * self.grid_size;

        let distance_x = (position.x - grid_x).abs();
        let distance_y = (position.y - grid_y).abs();

        if distance_x <= self.snap_threshold || distance_y <= self.snap_threshold {
            let mut snapped = position.clone();

            if distance_x <= self.snap_threshold {
                snapped.x = grid_x;
            }
            if distance_y <= self.snap_threshold {
                snapped.y = grid_y;
            }

            let snap = PositionSnap {
                snap_type: SnapType::Grid,
                position: snapped.clone(),
                distance: distance_x.min(distance_y),
                guide: None,
            };

            (snapped, Some(snap))
        } else {
            (position.clone(), None)
        }
    }

    /// Generate alignment guides for the canvas
    fn generate_alignment_guides(&self, canvas_width: f32, canvas_height: f32) -> Vec<AlignmentGuide> {
        let mut guides = Vec::new();

        // Center guides
        guides.push(AlignmentGuide {
            guide_type: AlignmentGuideType::Center,
            position: canvas_width / 2.0,
            active: true,
            color: "#FF0000".to_string(),
            thickness: 1.0,
        });

        guides.push(AlignmentGuide {
            guide_type: AlignmentGuideType::Center,
            position: canvas_height / 2.0,
            active: true,
            color: "#FF0000".to_string(),
            thickness: 1.0,
        });

        // Rule of thirds guides
        for i in 1..3 {
            guides.push(AlignmentGuide {
                guide_type: AlignmentGuideType::ThirdsVertical,
                position: canvas_width * (i as f32) / 3.0,
                active: true,
                color: "#00FF00".to_string(),
                thickness: 0.5,
            });

            guides.push(AlignmentGuide {
                guide_type: AlignmentGuideType::ThirdsHorizontal,
                position: canvas_height * (i as f32) / 3.0,
                active: true,
                color: "#00FF00".to_string(),
                thickness: 0.5,
            });
        }

        // Golden ratio guides
        let golden_ratio = 1.618;
        let golden_x = canvas_width / golden_ratio;
        let golden_y = canvas_height / golden_ratio;

        guides.push(AlignmentGuide {
            guide_type: AlignmentGuideType::GoldenRatio,
            position: golden_x,
            active: true,
            color: "#FFD700".to_string(),
            thickness: 0.5,
        });

        guides.push(AlignmentGuide {
            guide_type: AlignmentGuideType::GoldenRatio,
            position: golden_y,
            active: true,
            color: "#FFD700".to_string(),
            thickness: 0.5,
        });

        guides
    }

    /// Snap position to alignment guides
    fn snap_to_guides(&self, position: &TextPosition, guides: &[AlignmentGuide]) -> (TextPosition, Vec<PositionSnap>) {
        let mut snapped = position.clone();
        let mut snaps = Vec::new();

        for guide in guides {
            if !guide.active {
                continue;
            }

            let distance = match guide.guide_type {
                AlignmentGuideType::Vertical |
                AlignmentGuideType::Center |
                AlignmentGuideType::ThirdsVertical |
                AlignmentGuideType::GoldenRatio => {
                    (position.x - guide.position).abs()
                }
                AlignmentGuideType::Horizontal |
                AlignmentGuideType::ThirdsHorizontal => {
                    (position.y - guide.position).abs()
                }
            };

            if distance <= self.snap_threshold {
                match guide.guide_type {
                    AlignmentGuideType::Vertical |
                    AlignmentGuideType::Center |
                    AlignmentGuideType::ThirdsVertical |
                    AlignmentGuideType::GoldenRatio => {
                        snapped.x = guide.position;
                    }
                    AlignmentGuideType::Horizontal |
                    AlignmentGuideType::ThirdsHorizontal => {
                        snapped.y = guide.position;
                    }
                }

                snaps.push(PositionSnap {
                    snap_type: SnapType::Guide,
                    position: snapped.clone(),
                    distance,
                    guide: Some(guide.clone()),
                });
            }
        }

        (snapped, snaps)
    }

    /// Snap position to other text elements
    fn snap_to_elements(&self, position: &TextPosition, other_elements: &[TextElement]) -> (TextPosition, Vec<PositionSnap>) {
        let mut snapped = position.clone();
        let mut snaps = Vec::new();

        for element in other_elements {
            // Calculate distances to element edges
            let distance_x = (position.x - element.position.x).abs();
            let distance_y = (position.y - element.position.y).abs();

            // Snap to horizontal alignment
            if distance_y <= self.snap_threshold {
                snapped.y = element.position.y;
                snaps.push(PositionSnap {
                    snap_type: SnapType::Element,
                    position: snapped.clone(),
                    distance: distance_y,
                    guide: None,
                });
            }

            // Snap to vertical alignment
            if distance_x <= self.snap_threshold {
                snapped.x = element.position.x;
                snaps.push(PositionSnap {
                    snap_type: SnapType::Element,
                    position: snapped.clone(),
                    distance: distance_x,
                    guide: None,
                });
            }
        }

        (snapped, snaps)
    }

    /// Apply safe area constraints
    fn apply_safe_area_constraints(&self, position: &TextPosition, canvas_width: f32, canvas_height: f32) -> TextPosition {
        let mut constrained = position.clone();

        let margin_x = canvas_width * self.safe_area_margin;
        let margin_y = canvas_height * self.safe_area_margin;

        // Constrain to safe area
        constrained.x = constrained.x.max(margin_x).min(canvas_width - margin_x);
        constrained.y = constrained.y.max(margin_y).min(canvas_height - margin_y);

        constrained
    }

    /// Apply position constraints
    fn apply_position_constraints(&self, position: &TextPosition, constraints: &PositionConstraints) -> TextPosition {
        let mut constrained = position.clone();

        if let Some(min_x) = constraints.min_x {
            constrained.x = constrained.x.max(min_x);
        }
        if let Some(max_x) = constraints.max_x {
            constrained.x = constrained.x.min(max_x);
        }
        if let Some(min_y) = constraints.min_y {
            constrained.y = constrained.y.max(min_y);
        }
        if let Some(max_y) = constraints.max_y {
            constrained.y = constrained.y.min(max_y);
        }

        constrained
    }

    /// Calculate precise text alignment within bounds
    pub fn calculate_text_alignment(
        &self,
        element: &TextElement,
        bounds_width: f32,
        bounds_height: f32,
        context: &CanvasRenderingContext2d,
    ) -> Result<(f32, f32), JsValue> {
        // Get text metrics
        let font_string = format!(
            "{} {} {}px {}",
            element.style.font_style.to_css_value(),
            element.style.font_weight.to_css_value(),
            element.style.font_size,
            element.style.font_family
        );
        context.set_font(&font_string);

        let metrics = context.measure_text(&element.text)?;
        let text_width = metrics.width() as f32;
        let text_height = element.style.font_size * element.style.line_height;

        // Calculate alignment offsets
        let x_offset = match element.style.alignment {
            TextAlignment::Left | TextAlignment::Start => 0.0,
            TextAlignment::Center => (bounds_width - text_width) / 2.0,
            TextAlignment::Right | TextAlignment::End => bounds_width - text_width,
            TextAlignment::Justify => 0.0, // Justify handled differently
        };

        let y_offset = (bounds_height - text_height) / 2.0;

        Ok((x_offset, y_offset))
    }

    /// Generate positioning presets for common layouts
    pub fn generate_positioning_presets(&self, canvas_width: f32, canvas_height: f32) -> Vec<TextPositionPreset> {
        vec![
            TextPositionPreset {
                name: "Top Left".to_string(),
                description: "Position text in top-left corner".to_string(),
                position: TextPosition {
                    x: canvas_width * 0.1,
                    y: canvas_height * 0.1,
                    anchor_x: 0.0,
                    anchor_y: 0.0,
                    ..Default::default()
                },
            },
            TextPositionPreset {
                name: "Top Center".to_string(),
                description: "Position text at top center".to_string(),
                position: TextPosition {
                    x: canvas_width * 0.5,
                    y: canvas_height * 0.1,
                    anchor_x: 0.5,
                    anchor_y: 0.0,
                    ..Default::default()
                },
            },
            TextPositionPreset {
                name: "Top Right".to_string(),
                description: "Position text in top-right corner".to_string(),
                position: TextPosition {
                    x: canvas_width * 0.9,
                    y: canvas_height * 0.1,
                    anchor_x: 1.0,
                    anchor_y: 0.0,
                    ..Default::default()
                },
            },
            TextPositionPreset {
                name: "Center".to_string(),
                description: "Position text at center".to_string(),
                position: TextPosition {
                    x: canvas_width * 0.5,
                    y: canvas_height * 0.5,
                    anchor_x: 0.5,
                    anchor_y: 0.5,
                    ..Default::default()
                },
            },
            TextPositionPreset {
                name: "Bottom Left".to_string(),
                description: "Position text in bottom-left corner".to_string(),
                position: TextPosition {
                    x: canvas_width * 0.1,
                    y: canvas_height * 0.9,
                    anchor_x: 0.0,
                    anchor_y: 1.0,
                    ..Default::default()
                },
            },
            TextPositionPreset {
                name: "Bottom Center".to_string(),
                description: "Position text at bottom center".to_string(),
                position: TextPosition {
                    x: canvas_width * 0.5,
                    y: canvas_height * 0.9,
                    anchor_x: 0.5,
                    anchor_y: 1.0,
                    ..Default::default()
                },
            },
            TextPositionPreset {
                name: "Bottom Right".to_string(),
                description: "Position text in bottom-right corner".to_string(),
                position: TextPosition {
                    x: canvas_width * 0.9,
                    y: canvas_height * 0.9,
                    anchor_x: 1.0,
                    anchor_y: 1.0,
                    ..Default::default()
                },
            },
        ]
    }

    /// Render alignment guides on canvas
    pub fn render_alignment_guides(
        &self,
        context: &CanvasRenderingContext2d,
        canvas_width: f32,
        canvas_height: f32,
        active_snaps: &[PositionSnap],
    ) -> Result<(), JsValue> {
        if !self.alignment_guides {
            return Ok(());
        }

        let guides = self.generate_alignment_guides(canvas_width, canvas_height);

        context.save();

        for guide in &guides {
            if !guide.active {
                continue;
            }

            // Check if this guide is currently being snapped to
            let is_active = active_snaps.iter().any(|snap| {
                if let Some(snap_guide) = &snap.guide {
                    snap_guide.position == guide.position &&
                    std::mem::discriminant(&snap_guide.guide_type) == std::mem::discriminant(&guide.guide_type)
                } else {
                    false
                }
            });

            // Set guide appearance
            context.set_stroke_style_str(&guide.color);
            context.set_line_width(if is_active { guide.thickness * 2.0 } else { guide.thickness } as f64);
            context.set_global_alpha(if is_active { 0.8 } else { 0.3 });

            // Draw guide line
            context.begin_path();
            match guide.guide_type {
                AlignmentGuideType::Vertical |
                AlignmentGuideType::Center |
                AlignmentGuideType::ThirdsVertical |
                AlignmentGuideType::GoldenRatio => {
                    context.move_to(guide.position as f64, 0.0);
                    context.line_to(guide.position as f64, canvas_height as f64);
                }
                AlignmentGuideType::Horizontal |
                AlignmentGuideType::ThirdsHorizontal => {
                    context.move_to(0.0, guide.position as f64);
                    context.line_to(canvas_width as f64, guide.position as f64);
                }
            }
            context.stroke();
        }

        context.restore();
        Ok(())
    }

    /// Render grid if enabled
    pub fn render_grid(&self, context: &CanvasRenderingContext2d, canvas_width: f32, canvas_height: f32) -> Result<(), JsValue> {
        if !self.snap_to_grid {
            return Ok(());
        }

        context.save();
        context.set_stroke_style_str("#CCCCCC");
        context.set_line_width(0.5);
        context.set_global_alpha(0.2);

        // Draw vertical grid lines
        let mut x = self.grid_size;
        while x < canvas_width {
            context.begin_path();
            context.move_to(x as f64, 0.0);
            context.line_to(x as f64, canvas_height as f64);
            context.stroke();
            x += self.grid_size;
        }

        // Draw horizontal grid lines
        let mut y = self.grid_size;
        while y < canvas_height {
            context.begin_path();
            context.move_to(0.0, y as f64);
            context.line_to(canvas_width as f64, y as f64);
            context.stroke();
            y += self.grid_size;
        }

        context.restore();
        Ok(())
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextPositionPreset {
    pub name: String,
    pub description: String,
    pub position: TextPosition,
}
