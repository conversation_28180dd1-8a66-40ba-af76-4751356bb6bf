use wasm_bindgen::prelude::*;
use wasm_bindgen_futures::JsFuture;
use web_sys::{File, Blob, Url, HtmlVideoElement};
use js_sys::Promise;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use crate::utils::ffmpeg_bindings::{FFmpegInstance, FFmpegConfig};
use crate::utils::thumbnail_cache::{ThumbnailCache, ThumbnailMetadata};

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct VideoProcessingOptions {
    pub format: String,
    pub quality: String,
    pub resolution: Option<(u32, u32)>,
    pub bitrate: Option<u32>,
    pub fps: Option<f32>,
    pub video_codec: Option<String>,
    pub audio_codec: Option<String>,
    pub preset: Option<String>,
    pub profile: Option<String>,
    pub level: Option<String>,
    pub pixel_format: Option<String>,
    pub color_space: Option<String>,
    pub color_range: Option<String>,
    pub two_pass: bool,
    pub hardware_acceleration: bool,
    pub custom_filters: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThumbnailOptions {
    pub width: u32,
    pub height: u32,
    pub time: f64, // Time in seconds
    pub format: String, // "jpeg" or "png"
}

impl Default for ThumbnailOptions {
    fn default() -> Self {
        Self {
            width: 320,
            height: 180,
            time: 0.0,
            format: "jpeg".to_string(),
        }
    }
}

#[derive(Debug)]
pub struct VideoProcessor {
    ffmpeg: FFmpegInstance,
    thumbnail_cache: Option<ThumbnailCache>,
}



impl VideoProcessor {
    pub fn new() -> Self {
        let config = FFmpegConfig::default();
        Self {
            ffmpeg: FFmpegInstance::new(config),
            thumbnail_cache: None,
        }
    }

    pub fn new_with_multithreading() -> Self {
        let config = FFmpegConfig {
            use_multithreading: true,
            ..Default::default()
        };
        Self {
            ffmpeg: FFmpegInstance::new(config),
            thumbnail_cache: None,
        }
    }

    pub fn new_with_config(config: FFmpegConfig) -> Self {
        Self {
            ffmpeg: FFmpegInstance::new(config),
            thumbnail_cache: None,
        }
    }

    pub fn new_with_cache(config: FFmpegConfig, cache: ThumbnailCache) -> Self {
        Self {
            ffmpeg: FFmpegInstance::new(config),
            thumbnail_cache: Some(cache),
        }
    }

    pub async fn initialize(&mut self) -> Result<(), JsValue> {
        self.ffmpeg.initialize().await
    }

    pub fn is_initialized(&self) -> bool {
        self.ffmpeg.is_loaded()
    }

    /// Encode frames to video using FFmpeg
    pub async fn encode_frames_to_video(
        &self,
        frames: Vec<Vec<u8>>,
        width: u32,
        height: u32,
        fps: f32,
        codec: &str,
        bitrate: u32,
    ) -> Result<Blob, JsValue> {
        if !self.ffmpeg.is_loaded() {
            return Err(JsValue::from_str("FFmpeg not initialized"));
        }

        // Write frames to FFmpeg virtual filesystem
        for (i, frame_data) in frames.iter().enumerate() {
            let frame_name = format!("frame_{:06}.png", i);
            let uint8_array = js_sys::Uint8Array::new_with_length(frame_data.len() as u32);
            uint8_array.copy_from(frame_data);
            self.ffmpeg.write_file(&frame_name, &uint8_array).await?;
        }

        // Build FFmpeg command for video encoding
        let output_name = "output.mp4";
        let mut command = vec![
            "-framerate".to_string(),
            fps.to_string(),
            "-i".to_string(),
            "frame_%06d.png".to_string(),
            "-c:v".to_string(),
            codec.to_string(),
            "-b:v".to_string(),
            format!("{}k", bitrate),
            "-pix_fmt".to_string(),
            "yuv420p".to_string(),
            "-s".to_string(),
            format!("{}x{}", width, height),
            output_name.to_string(),
        ];

        // Execute FFmpeg command
        self.ffmpeg.exec(command).await?;

        // Read output file
        let output_data = self.ffmpeg.read_file(output_name).await?;

        // Clean up temporary files
        for i in 0..frames.len() {
            let frame_name = format!("frame_{:06}.png", i);
            let _ = self.ffmpeg.delete_file(&frame_name).await;
        }
        let _ = self.ffmpeg.delete_file(output_name).await;

        // Convert to Blob - output_data is already a Uint8Array
        let uint8_array = output_data;

        let blob_parts = js_sys::Array::new();
        blob_parts.push(&uint8_array);

        web_sys::Blob::new_with_u8_array_sequence(&blob_parts)
    }



    pub async fn generate_thumbnail(&self, file: &File, options: ThumbnailOptions) -> Result<Blob, JsValue> {
        if !self.ffmpeg.is_loaded() {
            return Err(JsValue::from_str("FFmpeg not initialized"));
        }

        // Get file data
        let file_data = self.ffmpeg.file_to_uint8_array(file).await?;
        let input_name = file.name();

        // Write input file to FFmpeg virtual filesystem
        self.ffmpeg.write_file(&input_name, &file_data).await?;

        // Build FFmpeg command for thumbnail generation
        let output_format = match options.format.as_str() {
            "png" => "png",
            _ => "mjpeg", // Default to JPEG
        };

        let output_name = format!("thumbnail.{}", if output_format == "png" { "png" } else { "jpg" });

        let command = vec![
            "-i".to_string(),
            input_name,
            "-ss".to_string(),
            options.time.to_string(), // Seek to specific time
            "-vframes".to_string(),
            "1".to_string(), // Extract only one frame
            "-f".to_string(),
            output_format.to_string(),
            "-s".to_string(),
            format!("{}x{}", options.width, options.height), // Set size
            "-q:v".to_string(),
            "2".to_string(), // High quality for thumbnails
            output_name.clone(),
        ];

        // Execute FFmpeg command
        self.ffmpeg.exec(command).await?;

        // Read output file
        let output_data = self.ffmpeg.read_file(&output_name).await?;

        // Convert to Blob
        let mime_type = match options.format.as_str() {
            "png" => "image/png",
            _ => "image/jpeg",
        };

        let blob = self.ffmpeg.uint8_array_to_blob(&output_data, mime_type)?;
        Ok(blob)
    }

    pub async fn extract_frame_at_time(&self, file: &File, time: f64, width: u32, height: u32) -> Result<Blob, JsValue> {
        let options = ThumbnailOptions {
            width,
            height,
            time,
            format: "jpeg".to_string(),
        };
        self.generate_thumbnail(file, options).await
    }

    pub async fn extract_multiple_frames(&self, file: &File, times: Vec<f64>, width: u32, height: u32) -> Result<Vec<Blob>, JsValue> {
        let mut frames = Vec::new();

        for time in times {
            match self.extract_frame_at_time(file, time, width, height).await {
                Ok(frame) => frames.push(frame),
                Err(e) => {
                    web_sys::console::warn_2(&JsValue::from_str("Failed to extract frame at time"), &JsValue::from_f64(time));
                    web_sys::console::warn_1(&e);
                    // Continue with other frames
                }
            }
        }

        Ok(frames)
    }

    pub async fn generate_thumbnail_strip(&self, file: &File, count: u32, width: u32, height: u32) -> Result<Vec<Blob>, JsValue> {
        if !self.ffmpeg.is_loaded() {
            return Err(JsValue::from_str("FFmpeg not initialized"));
        }

        // First get video duration
        let video_info = self.get_video_info(file).await?;
        let duration = video_info.duration;

        if duration <= 0.0 {
            return Err(JsValue::from_str("Invalid video duration"));
        }

        // Calculate time intervals
        let interval = duration / (count + 1) as f64;
        let times: Vec<f64> = (1..=count).map(|i| i as f64 * interval).collect();

        self.extract_multiple_frames(file, times, width, height).await
    }

    pub async fn generate_smart_thumbnail(&self, file: &File, options: ThumbnailOptions) -> Result<Blob, JsValue> {
        if !self.ffmpeg.is_loaded() {
            return Err(JsValue::from_str("FFmpeg not initialized"));
        }

        // Get file data
        let file_data = self.ffmpeg.file_to_uint8_array(file).await?;
        let input_name = file.name();

        // Write input file to FFmpeg virtual filesystem
        self.ffmpeg.write_file(&input_name, &file_data).await?;

        let output_format = match options.format.as_str() {
            "png" => "png",
            _ => "mjpeg",
        };

        let output_name = format!("smart_thumbnail.{}", if output_format == "png" { "png" } else { "jpg" });

        // Use smart frame selection to avoid black frames
        let command = vec![
            "-i".to_string(),
            input_name,
            "-ss".to_string(),
            options.time.to_string(),
            "-vf".to_string(),
            format!("select=gt(scene\\,0.3),scale={}:{}", options.width, options.height),
            "-vframes".to_string(),
            "1".to_string(),
            "-f".to_string(),
            output_format.to_string(),
            "-q:v".to_string(),
            "2".to_string(),
            output_name.clone(),
        ];

        // Execute FFmpeg command
        self.ffmpeg.exec(command).await?;

        // Read output file
        let output_data = self.ffmpeg.read_file(&output_name).await?;

        // Convert to Blob
        let mime_type = match options.format.as_str() {
            "png" => "image/png",
            _ => "image/jpeg",
        };

        let blob = self.ffmpeg.uint8_array_to_blob(&output_data, mime_type)?;
        Ok(blob)
    }

    pub async fn convert_video(&self, file: &File, options: VideoProcessingOptions) -> Result<Blob, JsValue> {
        if !self.ffmpeg.is_loaded() {
            return Err(JsValue::from_str("FFmpeg not initialized"));
        }

        // Get file data
        let file_data = self.ffmpeg.file_to_uint8_array(file).await?;
        let input_name = file.name();

        // Write input file to FFmpeg virtual filesystem
        self.ffmpeg.write_file(&input_name, &file_data).await?;

        // Build FFmpeg command
        let mut command = vec!["-i".to_string(), input_name];

        // Add format-specific options
        match options.format.as_str() {
            "mp4" => {
                command.extend_from_slice(&["-c:v".to_string(), "libx264".to_string()]);
                command.extend_from_slice(&["-c:a".to_string(), "aac".to_string()]);
            },
            "webm" => {
                command.extend_from_slice(&["-c:v".to_string(), "libvpx-vp9".to_string()]);
                command.extend_from_slice(&["-c:a".to_string(), "libopus".to_string()]);
            },
            _ => {
                // Default to mp4
                command.extend_from_slice(&["-c:v".to_string(), "libx264".to_string()]);
                command.extend_from_slice(&["-c:a".to_string(), "aac".to_string()]);
            }
        }

        // Add quality settings
        match options.quality.as_str() {
            "high" => command.extend_from_slice(&["-crf".to_string(), "18".to_string()]),
            "medium" => command.extend_from_slice(&["-crf".to_string(), "23".to_string()]),
            "low" => command.extend_from_slice(&["-crf".to_string(), "28".to_string()]),
            _ => command.extend_from_slice(&["-crf".to_string(), "23".to_string()]),
        }

        // Add resolution if specified
        if let Some((width, height)) = options.resolution {
            command.extend_from_slice(&["-s".to_string(), format!("{}x{}", width, height)]);
        }

        // Add bitrate if specified
        if let Some(bitrate) = options.bitrate {
            command.extend_from_slice(&["-b:v".to_string(), format!("{}k", bitrate)]);
        }

        // Add framerate if specified
        if let Some(fps) = options.fps {
            command.extend_from_slice(&["-r".to_string(), fps.to_string()]);
        }

        let output_name = format!("output.{}", options.format);
        command.push(output_name.clone());

        // Execute FFmpeg command
        self.ffmpeg.exec(command).await?;

        // Read output file
        let output_data = self.ffmpeg.read_file(&output_name).await?;

        // Convert to Blob
        let mime_type = match options.format.as_str() {
            "mp4" => "video/mp4",
            "webm" => "video/webm",
            "avi" => "video/avi",
            _ => "video/mp4",
        };

        let blob = self.ffmpeg.uint8_array_to_blob(&output_data, mime_type)?;
        Ok(blob)
    }

    pub async fn extract_audio(&self, file: &File) -> Result<Blob, JsValue> {
        if !self.ffmpeg.is_loaded() {
            return Err(JsValue::from_str("FFmpeg not initialized"));
        }

        // Get file data
        let file_data = self.ffmpeg.file_to_uint8_array(file).await?;
        let input_name = file.name();

        // Write input file to FFmpeg virtual filesystem
        self.ffmpeg.write_file(&input_name, &file_data).await?;

        // Build FFmpeg command to extract audio
        let command = vec![
            "-i".to_string(),
            input_name,
            "-vn".to_string(), // No video
            "-acodec".to_string(),
            "libmp3lame".to_string(), // Use MP3 codec
            "-ab".to_string(),
            "192k".to_string(), // Audio bitrate
            "output.mp3".to_string(),
        ];

        // Execute FFmpeg command
        self.ffmpeg.exec(command).await?;

        // Read output file
        let output_data = self.ffmpeg.read_file("output.mp3").await?;

        // Convert to Blob
        let blob = self.ffmpeg.uint8_array_to_blob(&output_data, "audio/mp3")?;
        Ok(blob)
    }



    pub async fn get_video_info(&self, file: &File) -> Result<VideoInfo, JsValue> {
        // Create video element to get metadata
        let document = web_sys::window().unwrap().document().unwrap();
        let video = document.create_element("video")?.dyn_into::<HtmlVideoElement>()?;
        let url = Url::create_object_url_with_blob(file)?;
        video.set_src(&url);

        // Wait for metadata to load
        let metadata_promise = Promise::new(&mut |resolve, _reject| {
            let video_clone = video.clone();
            let onloadedmetadata = Closure::wrap(Box::new(move || {
                resolve.call0(&JsValue::NULL).unwrap();
            }) as Box<dyn Fn()>);
            
            video_clone.set_onloadedmetadata(Some(onloadedmetadata.as_ref().unchecked_ref()));
            onloadedmetadata.forget();
        });
        
        JsFuture::from(metadata_promise).await?;

        let info = VideoInfo {
            duration: video.duration(),
            width: video.video_width(),
            height: video.video_height(),
            format: "unknown".to_string(), // Would be detected by FFmpeg
            bitrate: None, // Would be detected by FFmpeg
            fps: None, // Would be detected by FFmpeg
        };

        // Cleanup
        Url::revoke_object_url(&url)?;
        
        Ok(info)
    }



    pub async fn get_detailed_video_info(&self, file: &File) -> Result<DetailedVideoInfo, JsValue> {
        if !self.ffmpeg.is_loaded() {
            return Err(JsValue::from_str("FFmpeg not initialized"));
        }

        // Get file data
        let file_data = self.ffmpeg.file_to_uint8_array(file).await?;
        let input_name = file.name();

        // Write input file to FFmpeg virtual filesystem
        self.ffmpeg.write_file(&input_name, &file_data).await?;

        // Use ffprobe-like functionality to get detailed info
        let command = vec![
            "-i".to_string(),
            input_name,
            "-f".to_string(),
            "null".to_string(),
            "-".to_string(),
        ];

        // Execute FFmpeg command (this will output info to logs)
        // Note: This is a simplified approach. In a real implementation,
        // you would capture the FFmpeg output and parse it for detailed info
        let _ = self.ffmpeg.exec(command).await;

        // For now, return basic info (this would be enhanced with actual parsing)
        let basic_info = self.get_video_info(file).await?;

        Ok(DetailedVideoInfo {
            duration: basic_info.duration,
            width: basic_info.width,
            height: basic_info.height,
            format: self.detect_format_from_filename(&file.name()),
            video_codec: "unknown".to_string(), // Would be parsed from FFmpeg output
            audio_codec: "unknown".to_string(), // Would be parsed from FFmpeg output
            bitrate: None,
            fps: None,
            audio_channels: None,
            audio_sample_rate: None,
        })
    }

    pub fn detect_format_from_filename(&self, filename: &str) -> String {
        let extension = filename.split('.').last().unwrap_or("").to_lowercase();
        match extension.as_str() {
            "mp4" | "m4v" | "m4a" => "mp4".to_string(),
            "webm" => "webm".to_string(),
            "avi" => "avi".to_string(),
            "mov" | "qt" => "mov".to_string(),
            "mkv" | "mka" => "mkv".to_string(),
            "flv" => "flv".to_string(),
            "wmv" | "asf" => "wmv".to_string(),
            "3gp" | "3g2" => "3gp".to_string(),
            "ogv" | "ogg" => "ogg".to_string(),
            "ts" | "mts" | "m2ts" => "mpegts".to_string(),
            "vob" => "vob".to_string(),
            "rm" | "rmvb" => "rm".to_string(),
            "f4v" => "f4v".to_string(),
            "divx" => "avi".to_string(), // DivX is typically in AVI container
            "xvid" => "avi".to_string(), // XviD is typically in AVI container
            _ => "unknown".to_string(),
        }
    }

    pub fn get_format_info(&self, format: &str) -> FormatInfo {
        match format {
            "mp4" => FormatInfo {
                name: "MP4".to_string(),
                description: "MPEG-4 Part 14 multimedia container".to_string(),
                supported_video_codecs: vec!["h264".to_string(), "h265".to_string(), "av1".to_string()],
                supported_audio_codecs: vec!["aac".to_string(), "mp3".to_string()],
                file_extensions: vec!["mp4".to_string(), "m4v".to_string()],
                mime_type: "video/mp4".to_string(),
                is_streamable: true,
                max_resolution: Some((7680, 4320)), // 8K
            },
            "webm" => FormatInfo {
                name: "WebM".to_string(),
                description: "WebM multimedia container".to_string(),
                supported_video_codecs: vec!["vp8".to_string(), "vp9".to_string(), "av1".to_string()],
                supported_audio_codecs: vec!["vorbis".to_string(), "opus".to_string()],
                file_extensions: vec!["webm".to_string()],
                mime_type: "video/webm".to_string(),
                is_streamable: true,
                max_resolution: Some((7680, 4320)), // 8K
            },
            "avi" => FormatInfo {
                name: "AVI".to_string(),
                description: "Audio Video Interleave container".to_string(),
                supported_video_codecs: vec!["h264".to_string(), "xvid".to_string(), "divx".to_string()],
                supported_audio_codecs: vec!["mp3".to_string(), "ac3".to_string(), "pcm".to_string()],
                file_extensions: vec!["avi".to_string()],
                mime_type: "video/avi".to_string(),
                is_streamable: false,
                max_resolution: Some((1920, 1080)), // Limited by format
            },
            "mov" => FormatInfo {
                name: "QuickTime".to_string(),
                description: "QuickTime multimedia container".to_string(),
                supported_video_codecs: vec!["h264".to_string(), "h265".to_string(), "prores".to_string()],
                supported_audio_codecs: vec!["aac".to_string(), "pcm".to_string()],
                file_extensions: vec!["mov".to_string(), "qt".to_string()],
                mime_type: "video/quicktime".to_string(),
                is_streamable: true,
                max_resolution: Some((7680, 4320)), // 8K
            },
            "mkv" => FormatInfo {
                name: "Matroska".to_string(),
                description: "Matroska multimedia container".to_string(),
                supported_video_codecs: vec!["h264".to_string(), "h265".to_string(), "vp9".to_string(), "av1".to_string()],
                supported_audio_codecs: vec!["aac".to_string(), "ac3".to_string(), "dts".to_string(), "flac".to_string()],
                file_extensions: vec!["mkv".to_string(), "mka".to_string()],
                mime_type: "video/x-matroska".to_string(),
                is_streamable: true,
                max_resolution: None, // No inherent limit
            },
            _ => FormatInfo {
                name: "Unknown".to_string(),
                description: "Unknown or unsupported format".to_string(),
                supported_video_codecs: vec![],
                supported_audio_codecs: vec![],
                file_extensions: vec![],
                mime_type: "application/octet-stream".to_string(),
                is_streamable: false,
                max_resolution: None,
            },
        }
    }

    pub fn get_supported_codecs(&self) -> HashMap<String, Vec<CodecInfo>> {
        let mut codecs = HashMap::new();

        codecs.insert("video".to_string(), vec![
            CodecInfo {
                name: "libx264".to_string(),
                display_name: "H.264/AVC".to_string(),
                description: "Advanced Video Coding - widely compatible".to_string(),
                quality_range: (1, 51),
                default_quality: 23,
                supports_hardware_acceleration: true,
                file_formats: vec!["mp4".to_string(), "mkv".to_string(), "avi".to_string()],
                profile_options: vec!["baseline".to_string(), "main".to_string(), "high".to_string()],
            },
            CodecInfo {
                name: "libx265".to_string(),
                display_name: "H.265/HEVC".to_string(),
                description: "High Efficiency Video Coding - better compression".to_string(),
                quality_range: (1, 51),
                default_quality: 28,
                supports_hardware_acceleration: true,
                file_formats: vec!["mp4".to_string(), "mkv".to_string()],
                profile_options: vec!["main".to_string(), "main10".to_string()],
            },
            CodecInfo {
                name: "libvpx".to_string(),
                display_name: "VP8".to_string(),
                description: "Google VP8 - open source web codec".to_string(),
                quality_range: (4, 63),
                default_quality: 10,
                supports_hardware_acceleration: false,
                file_formats: vec!["webm".to_string()],
                profile_options: vec![],
            },
            CodecInfo {
                name: "libvpx-vp9".to_string(),
                display_name: "VP9".to_string(),
                description: "Google VP9 - efficient web codec".to_string(),
                quality_range: (0, 63),
                default_quality: 30,
                supports_hardware_acceleration: true,
                file_formats: vec!["webm".to_string(), "mkv".to_string()],
                profile_options: vec!["profile-0".to_string(), "profile-1".to_string()],
            },
            CodecInfo {
                name: "libaom-av1".to_string(),
                display_name: "AV1".to_string(),
                description: "AOMedia Video 1 - next-gen codec".to_string(),
                quality_range: (0, 63),
                default_quality: 30,
                supports_hardware_acceleration: true,
                file_formats: vec!["mp4".to_string(), "webm".to_string(), "mkv".to_string()],
                profile_options: vec!["main".to_string()],
            },
        ]);

        codecs.insert("audio".to_string(), vec![
            CodecInfo {
                name: "aac".to_string(),
                display_name: "AAC".to_string(),
                description: "Advanced Audio Coding - standard for MP4".to_string(),
                quality_range: (64, 320),
                default_quality: 128,
                supports_hardware_acceleration: false,
                file_formats: vec!["mp4".to_string(), "mkv".to_string()],
                profile_options: vec!["lc".to_string(), "he".to_string(), "he-v2".to_string()],
            },
            CodecInfo {
                name: "libmp3lame".to_string(),
                display_name: "MP3".to_string(),
                description: "MPEG Audio Layer III - widely compatible".to_string(),
                quality_range: (64, 320),
                default_quality: 192,
                supports_hardware_acceleration: false,
                file_formats: vec!["mp3".to_string(), "avi".to_string()],
                profile_options: vec![],
            },
            CodecInfo {
                name: "libopus".to_string(),
                display_name: "Opus".to_string(),
                description: "Modern audio codec - excellent quality".to_string(),
                quality_range: (32, 512),
                default_quality: 128,
                supports_hardware_acceleration: false,
                file_formats: vec!["webm".to_string(), "mkv".to_string()],
                profile_options: vec![],
            },
            CodecInfo {
                name: "libvorbis".to_string(),
                display_name: "Vorbis".to_string(),
                description: "Open source audio codec".to_string(),
                quality_range: (64, 500),
                default_quality: 192,
                supports_hardware_acceleration: false,
                file_formats: vec!["ogg".to_string(), "webm".to_string(), "mkv".to_string()],
                profile_options: vec![],
            },
            CodecInfo {
                name: "flac".to_string(),
                display_name: "FLAC".to_string(),
                description: "Free Lossless Audio Codec".to_string(),
                quality_range: (0, 8),
                default_quality: 5,
                supports_hardware_acceleration: false,
                file_formats: vec!["flac".to_string(), "mkv".to_string()],
                profile_options: vec![],
            },
        ]);

        codecs
    }

    pub async fn batch_process_videos(&self, files: Vec<File>, options: VideoProcessingOptions) -> Result<Vec<Blob>, JsValue> {
        let mut results = Vec::new();

        for file in files {
            match self.convert_video(&file, options.clone()).await {
                Ok(blob) => results.push(blob),
                Err(e) => {
                    web_sys::console::error_2(&JsValue::from_str("Failed to process file:"), &e);
                    // Continue with other files
                }
            }
        }

        Ok(results)
    }

    pub fn is_format_supported(&self, format: &str) -> bool {
        matches!(format, "mp4" | "webm" | "avi" | "mov" | "mkv" | "flv" | "wmv" | "3gp" | "ogg" | "mpegts" | "vob" | "rm" | "f4v")
    }

    pub fn get_optimal_format_for_web(&self) -> String {
        // Return the most web-compatible format
        "mp4".to_string()
    }

    pub fn get_conversion_recommendations(&self, input_format: &str, target_use: &str) -> ConversionRecommendation {
        match target_use {
            "web" => ConversionRecommendation {
                recommended_format: "mp4".to_string(),
                video_codec: "libx264".to_string(),
                audio_codec: "aac".to_string(),
                quality_preset: "medium".to_string(),
                reason: "MP4 with H.264/AAC provides best web compatibility".to_string(),
            },
            "streaming" => ConversionRecommendation {
                recommended_format: "webm".to_string(),
                video_codec: "libvpx-vp9".to_string(),
                audio_codec: "libopus".to_string(),
                quality_preset: "medium".to_string(),
                reason: "WebM with VP9/Opus optimized for streaming".to_string(),
            },
            "archive" => ConversionRecommendation {
                recommended_format: "mkv".to_string(),
                video_codec: "libx265".to_string(),
                audio_codec: "flac".to_string(),
                quality_preset: "high".to_string(),
                reason: "MKV with H.265/FLAC for high quality archival".to_string(),
            },
            _ => ConversionRecommendation {
                recommended_format: input_format.to_string(),
                video_codec: "copy".to_string(),
                audio_codec: "copy".to_string(),
                quality_preset: "medium".to_string(),
                reason: "No conversion needed".to_string(),
            },
        }
    }

    pub async fn validate_file_format(&self, file: &File) -> Result<FormatValidationResult, JsValue> {
        let filename = file.name();
        let detected_format = self.detect_format_from_filename(&filename);
        let format_info = self.get_format_info(&detected_format);

        let is_supported = self.is_format_supported(&detected_format);
        let file_size = file.size() as u64;

        // Basic validation
        let mut issues = Vec::new();
        let mut warnings = Vec::new();

        if !is_supported {
            issues.push("Unsupported file format".to_string());
        }

        if file_size > 2_000_000_000 { // 2GB limit for web processing
            warnings.push("Large file size may cause performance issues".to_string());
        }

        if detected_format == "unknown" {
            issues.push("Cannot determine file format from extension".to_string());
        }

        Ok(FormatValidationResult {
            is_valid: issues.is_empty(),
            detected_format,
            format_info,
            file_size,
            issues,
            warnings,
        })
    }

    pub async fn detect_codecs(&self, file: &File) -> Result<CodecDetectionResult, JsValue> {
        if !self.ffmpeg.is_loaded() {
            return Err(JsValue::from_str("FFmpeg not initialized"));
        }

        // Get file data
        let file_data = self.ffmpeg.file_to_uint8_array(file).await?;
        let input_name = file.name();

        // Write input file to FFmpeg virtual filesystem
        self.ffmpeg.write_file(&input_name, &file_data).await?;

        // Use FFprobe-like functionality to detect codecs
        let command = vec![
            "-i".to_string(),
            input_name,
            "-c".to_string(),
            "copy".to_string(),
            "-f".to_string(),
            "null".to_string(),
            "-".to_string(),
        ];

        // Execute FFmpeg command (codec info will be in logs)
        let _ = self.ffmpeg.exec(command).await;

        // For now, return basic detection based on format
        // In a real implementation, you would parse FFmpeg output
        let format = self.detect_format_from_filename(&file.name());
        let (video_codec, audio_codec) = self.guess_codecs_from_format(&format);

        Ok(CodecDetectionResult {
            video_codec: video_codec.map(|c| c.to_string()),
            audio_codec: audio_codec.map(|c| c.to_string()),
            video_bitrate: None,
            audio_bitrate: None,
            video_profile: None,
            audio_profile: None,
            has_video: video_codec.is_some(),
            has_audio: audio_codec.is_some(),
            duration: None,
        })
    }

    fn guess_codecs_from_format(&self, format: &str) -> (Option<&str>, Option<&str>) {
        match format {
            "mp4" => (Some("h264"), Some("aac")),
            "webm" => (Some("vp9"), Some("opus")),
            "avi" => (Some("h264"), Some("mp3")),
            "mov" => (Some("h264"), Some("aac")),
            "mkv" => (Some("h264"), Some("aac")),
            "flv" => (Some("h264"), Some("aac")),
            "wmv" => (Some("wmv"), Some("wma")),
            "3gp" => (Some("h263"), Some("amr")),
            _ => (None, None),
        }
    }

    pub fn get_optimal_codec_for_format(&self, format: &str, use_case: &str) -> CodecRecommendation {
        match (format, use_case) {
            ("mp4", "web") => CodecRecommendation {
                video_codec: "libx264".to_string(),
                audio_codec: "aac".to_string(),
                video_quality: 23,
                audio_quality: 128,
                reason: "H.264/AAC provides best web compatibility for MP4".to_string(),
            },
            ("webm", "web") => CodecRecommendation {
                video_codec: "libvpx-vp9".to_string(),
                audio_codec: "libopus".to_string(),
                video_quality: 30,
                audio_quality: 128,
                reason: "VP9/Opus optimized for web streaming".to_string(),
            },
            ("mp4", "archive") => CodecRecommendation {
                video_codec: "libx265".to_string(),
                audio_codec: "flac".to_string(),
                video_quality: 18,
                audio_quality: 5,
                reason: "H.265/FLAC for high quality archival".to_string(),
            },
            ("mkv", "archive") => CodecRecommendation {
                video_codec: "libx265".to_string(),
                audio_codec: "flac".to_string(),
                video_quality: 18,
                audio_quality: 5,
                reason: "H.265/FLAC in MKV for maximum quality".to_string(),
            },
            _ => CodecRecommendation {
                video_codec: "libx264".to_string(),
                audio_codec: "aac".to_string(),
                video_quality: 23,
                audio_quality: 128,
                reason: "Default H.264/AAC for broad compatibility".to_string(),
            },
        }
    }

    pub fn validate_codec_compatibility(&self, video_codec: &str, audio_codec: &str, format: &str) -> CodecCompatibilityResult {
        let format_info = self.get_format_info(format);
        let video_supported = format_info.supported_video_codecs.contains(&video_codec.to_string());
        let audio_supported = format_info.supported_audio_codecs.contains(&audio_codec.to_string());

        let mut issues = Vec::new();
        let mut warnings = Vec::new();

        if !video_supported {
            issues.push(format!("Video codec '{}' not supported in {} format", video_codec, format));
        }

        if !audio_supported {
            issues.push(format!("Audio codec '{}' not supported in {} format", audio_codec, format));
        }

        // Check for known compatibility issues
        if format == "mp4" && video_codec == "libvpx-vp9" {
            warnings.push("VP9 in MP4 has limited browser support".to_string());
        }

        if format == "webm" && audio_codec == "aac" {
            warnings.push("AAC in WebM not supported by all browsers".to_string());
        }

        CodecCompatibilityResult {
            is_compatible: issues.is_empty(),
            video_supported,
            audio_supported,
            issues,
            warnings,
        }
    }

    pub async fn convert_with_codec_optimization(&self, file: &File, target_format: &str, use_case: &str) -> Result<Blob, JsValue> {
        if !self.ffmpeg.is_loaded() {
            return Err(JsValue::from_str("FFmpeg not initialized"));
        }

        // Get optimal codec recommendation
        let codec_rec = self.get_optimal_codec_for_format(target_format, use_case);

        // Validate codec compatibility
        let compatibility = self.validate_codec_compatibility(&codec_rec.video_codec, &codec_rec.audio_codec, target_format);
        if !compatibility.is_compatible {
            return Err(JsValue::from_str(&format!("Codec incompatibility: {:?}", compatibility.issues)));
        }

        // Get file data
        let file_data = self.ffmpeg.file_to_uint8_array(file).await?;
        let input_name = file.name();

        // Write input file to FFmpeg virtual filesystem
        self.ffmpeg.write_file(&input_name, &file_data).await?;

        // Build optimized FFmpeg command
        let mut command = vec!["-i".to_string(), input_name];

        // Video codec settings
        command.extend_from_slice(&["-c:v".to_string(), codec_rec.video_codec.clone()]);

        // Video quality settings based on codec
        match codec_rec.video_codec.as_str() {
            "libx264" | "libx265" => {
                command.extend_from_slice(&["-crf".to_string(), codec_rec.video_quality.to_string()]);
                command.extend_from_slice(&["-preset".to_string(), "medium".to_string()]);
            },
            "libvpx-vp9" => {
                command.extend_from_slice(&["-crf".to_string(), codec_rec.video_quality.to_string()]);
                command.extend_from_slice(&["-b:v".to_string(), "0".to_string()]); // VBR mode
            },
            "libaom-av1" => {
                command.extend_from_slice(&["-crf".to_string(), codec_rec.video_quality.to_string()]);
                command.extend_from_slice(&["-cpu-used".to_string(), "4".to_string()]); // Speed preset
            },
            _ => {
                command.extend_from_slice(&["-crf".to_string(), codec_rec.video_quality.to_string()]);
            }
        }

        // Audio codec settings
        command.extend_from_slice(&["-c:a".to_string(), codec_rec.audio_codec.clone()]);

        // Audio quality settings based on codec
        match codec_rec.audio_codec.as_str() {
            "aac" | "libmp3lame" => {
                command.extend_from_slice(&["-b:a".to_string(), format!("{}k", codec_rec.audio_quality)]);
            },
            "libopus" | "libvorbis" => {
                command.extend_from_slice(&["-b:a".to_string(), format!("{}k", codec_rec.audio_quality)]);
            },
            "flac" => {
                command.extend_from_slice(&["-compression_level".to_string(), codec_rec.audio_quality.to_string()]);
            },
            _ => {
                command.extend_from_slice(&["-b:a".to_string(), format!("{}k", codec_rec.audio_quality)]);
            }
        }

        let output_name = format!("output.{}", target_format);
        command.push(output_name.clone());

        // Execute FFmpeg command
        self.ffmpeg.exec(command).await?;

        // Read output file
        let output_data = self.ffmpeg.read_file(&output_name).await?;

        // Convert to Blob
        let format_info = self.get_format_info(target_format);
        let blob = self.ffmpeg.uint8_array_to_blob(&output_data, &format_info.mime_type)?;

        Ok(blob)
    }

    pub fn create_quality_preset(&self, preset_name: &str, target_use: &str) -> VideoProcessingOptions {
        match (preset_name, target_use) {
            ("ultra_high", "archive") => VideoProcessingOptions {
                format: "mkv".to_string(),
                quality: "ultra_high".to_string(),
                video_codec: Some("libx265".to_string()),
                audio_codec: Some("flac".to_string()),
                preset: Some("slow".to_string()),
                profile: Some("main".to_string()),
                two_pass: true,
                ..Default::default()
            },
            ("high", "web") => VideoProcessingOptions {
                format: "mp4".to_string(),
                quality: "high".to_string(),
                video_codec: Some("libx264".to_string()),
                audio_codec: Some("aac".to_string()),
                preset: Some("medium".to_string()),
                profile: Some("high".to_string()),
                level: Some("4.0".to_string()),
                ..Default::default()
            },
            ("medium", "web") => VideoProcessingOptions {
                format: "mp4".to_string(),
                quality: "medium".to_string(),
                video_codec: Some("libx264".to_string()),
                audio_codec: Some("aac".to_string()),
                preset: Some("medium".to_string()),
                profile: Some("main".to_string()),
                ..Default::default()
            },
            ("low", "web") => VideoProcessingOptions {
                format: "mp4".to_string(),
                quality: "low".to_string(),
                video_codec: Some("libx264".to_string()),
                audio_codec: Some("aac".to_string()),
                preset: Some("fast".to_string()),
                profile: Some("baseline".to_string()),
                ..Default::default()
            },
            ("streaming", "web") => VideoProcessingOptions {
                format: "webm".to_string(),
                quality: "medium".to_string(),
                video_codec: Some("libvpx-vp9".to_string()),
                audio_codec: Some("libopus".to_string()),
                preset: Some("medium".to_string()),
                two_pass: true,
                ..Default::default()
            },
            _ => VideoProcessingOptions::default(),
        }
    }

    pub fn validate_quality_settings(&self, options: &VideoProcessingOptions) -> QualityValidationResult {
        let mut issues = Vec::new();
        let mut warnings = Vec::new();
        let mut recommendations = Vec::new();

        // Validate codec compatibility
        if let (Some(video_codec), Some(audio_codec)) = (&options.video_codec, &options.audio_codec) {
            let compatibility = self.validate_codec_compatibility(video_codec, audio_codec, &options.format);
            if !compatibility.is_compatible {
                issues.extend(compatibility.issues);
            }
            warnings.extend(compatibility.warnings);
        }

        // Validate resolution settings
        if let Some((width, height)) = options.resolution {
            if width % 2 != 0 || height % 2 != 0 {
                issues.push("Resolution dimensions must be even numbers for most codecs".to_string());
            }

            if width > 7680 || height > 4320 {
                warnings.push("8K+ resolution may cause performance issues".to_string());
            }

            if width < 128 || height < 128 {
                warnings.push("Very low resolution may result in poor quality".to_string());
            }
        }

        // Validate bitrate settings
        if let Some(bitrate) = options.bitrate {
            if let Some((width, height)) = options.resolution {
                let pixel_count = width * height;
                let recommended_bitrate = self.calculate_recommended_bitrate(pixel_count, &options.quality);

                if bitrate < recommended_bitrate / 4 {
                    warnings.push("Bitrate may be too low for selected resolution".to_string());
                } else if bitrate > recommended_bitrate * 4 {
                    warnings.push("Bitrate may be unnecessarily high".to_string());
                }
            }
        }

        // Validate FPS settings
        if let Some(fps) = options.fps {
            if fps < 1.0 || fps > 120.0 {
                issues.push("FPS must be between 1 and 120".to_string());
            } else if fps > 60.0 {
                warnings.push("High frame rates may increase file size significantly".to_string());
            }
        }

        // Generate recommendations
        if options.two_pass && options.preset.as_deref() == Some("ultrafast") {
            recommendations.push("Two-pass encoding with ultrafast preset may not provide benefits".to_string());
        }

        if options.hardware_acceleration && options.video_codec.as_deref() == Some("libaom-av1") {
            recommendations.push("AV1 hardware acceleration has limited support".to_string());
        }

        QualityValidationResult {
            is_valid: issues.is_empty(),
            issues,
            warnings,
            recommendations,
            estimated_file_size: self.estimate_file_size(options),
            estimated_encoding_time: self.estimate_encoding_time_from_options(options),
        }
    }

    fn calculate_recommended_bitrate(&self, pixel_count: u32, quality: &str) -> u32 {
        let base_bitrate = match quality {
            "ultra_high" => pixel_count / 1000,
            "high" => pixel_count / 1500,
            "medium" => pixel_count / 2000,
            "low" => pixel_count / 3000,
            _ => pixel_count / 2000,
        };

        // Minimum 500kbps, maximum 50Mbps
        base_bitrate.max(500).min(50000)
    }

    fn estimate_file_size(&self, options: &VideoProcessingOptions) -> Option<u64> {
        if let (Some(bitrate), Some(duration)) = (options.bitrate, self.get_estimated_duration()) {
            // Rough estimate: (video_bitrate + audio_bitrate) * duration / 8
            let audio_bitrate = 128; // Default audio bitrate in kbps
            let total_bitrate = bitrate + audio_bitrate;
            Some((total_bitrate as u64 * duration as u64 * 1000) / 8) // Size in bytes
        } else {
            None
        }
    }

    fn estimate_encoding_time_from_options(&self, options: &VideoProcessingOptions) -> Option<f64> {
        if let (Some(codec), Some(duration)) = (&options.video_codec, self.get_estimated_duration()) {
            let quality = self.parse_quality_to_numeric(&options.quality);
            Some(estimate_encoding_time(duration, codec, quality))
        } else {
            None
        }
    }

    fn parse_quality_to_numeric(&self, quality: &str) -> u32 {
        match quality {
            "ultra_high" => 15,
            "high" => 20,
            "medium" => 25,
            "low" => 30,
            _ => 25,
        }
    }

    fn get_estimated_duration(&self) -> Option<f64> {
        // This would be set from actual video analysis
        // For now, return None as placeholder
        None
    }

    pub fn get_quality_presets(&self) -> Vec<QualityPreset> {
        vec![
            QualityPreset {
                name: "Ultra High".to_string(),
                description: "Maximum quality for archival purposes".to_string(),
                target_bitrate_factor: 1.5,
                crf_value: 15,
                preset: "slow".to_string(),
                recommended_for: vec!["archive".to_string(), "professional".to_string()],
            },
            QualityPreset {
                name: "High".to_string(),
                description: "High quality for web distribution".to_string(),
                target_bitrate_factor: 1.0,
                crf_value: 20,
                preset: "medium".to_string(),
                recommended_for: vec!["web".to_string(), "streaming".to_string()],
            },
            QualityPreset {
                name: "Medium".to_string(),
                description: "Balanced quality and file size".to_string(),
                target_bitrate_factor: 0.7,
                crf_value: 25,
                preset: "medium".to_string(),
                recommended_for: vec!["web".to_string(), "mobile".to_string()],
            },
            QualityPreset {
                name: "Low".to_string(),
                description: "Smaller file size for bandwidth-limited scenarios".to_string(),
                target_bitrate_factor: 0.4,
                crf_value: 30,
                preset: "fast".to_string(),
                recommended_for: vec!["mobile".to_string(), "preview".to_string()],
            },
        ]
    }

    pub fn get_resolution_presets(&self) -> Vec<ResolutionPreset> {
        vec![
            ResolutionPreset {
                name: "8K UHD".to_string(),
                width: 7680,
                height: 4320,
                aspect_ratio: "16:9".to_string(),
                recommended_bitrate_range: (25000, 100000),
                description: "Ultra High Definition 8K".to_string(),
            },
            ResolutionPreset {
                name: "4K UHD".to_string(),
                width: 3840,
                height: 2160,
                aspect_ratio: "16:9".to_string(),
                recommended_bitrate_range: (8000, 40000),
                description: "Ultra High Definition 4K".to_string(),
            },
            ResolutionPreset {
                name: "1440p QHD".to_string(),
                width: 2560,
                height: 1440,
                aspect_ratio: "16:9".to_string(),
                recommended_bitrate_range: (4000, 20000),
                description: "Quad High Definition".to_string(),
            },
            ResolutionPreset {
                name: "1080p FHD".to_string(),
                width: 1920,
                height: 1080,
                aspect_ratio: "16:9".to_string(),
                recommended_bitrate_range: (2000, 10000),
                description: "Full High Definition".to_string(),
            },
            ResolutionPreset {
                name: "720p HD".to_string(),
                width: 1280,
                height: 720,
                aspect_ratio: "16:9".to_string(),
                recommended_bitrate_range: (1000, 5000),
                description: "High Definition".to_string(),
            },
            ResolutionPreset {
                name: "480p SD".to_string(),
                width: 854,
                height: 480,
                aspect_ratio: "16:9".to_string(),
                recommended_bitrate_range: (500, 2000),
                description: "Standard Definition".to_string(),
            },
        ]
    }

    pub fn optimize_settings_for_target(&self, mut options: VideoProcessingOptions, target_file_size: u64, duration: f64) -> VideoProcessingOptions {
        if duration <= 0.0 {
            return options;
        }

        // Calculate target bitrate from file size
        let target_bitrate_bps = (target_file_size * 8) as f64 / duration; // bits per second
        let target_bitrate_kbps = (target_bitrate_bps / 1000.0) as u32;

        // Reserve 10-20% for audio
        let audio_bitrate = target_bitrate_kbps.min(320).max(64); // Audio bitrate between 64-320 kbps
        let video_bitrate = target_bitrate_kbps.saturating_sub(audio_bitrate);

        options.bitrate = Some(video_bitrate);

        // Adjust quality based on bitrate
        if video_bitrate < 1000 {
            options.quality = "low".to_string();
            options.preset = Some("fast".to_string());
        } else if video_bitrate < 3000 {
            options.quality = "medium".to_string();
            options.preset = Some("medium".to_string());
        } else {
            options.quality = "high".to_string();
            options.preset = Some("slow".to_string());
        }

        options
    }

    pub fn get_adaptive_quality_settings(&self, input_resolution: (u32, u32), target_bandwidth: u32) -> Vec<VideoProcessingOptions> {
        let mut variants = Vec::new();
        let (input_width, input_height) = input_resolution;

        // Generate multiple quality variants for adaptive streaming
        let resolutions = vec![
            (1920, 1080, 5000),  // 1080p
            (1280, 720, 2500),   // 720p
            (854, 480, 1000),    // 480p
            (640, 360, 600),     // 360p
        ];

        for (width, height, base_bitrate) in resolutions {
            if width <= input_width && height <= input_height {
                let adjusted_bitrate = (base_bitrate * target_bandwidth / 5000).max(300);

                variants.push(VideoProcessingOptions {
                    format: "mp4".to_string(),
                    quality: if adjusted_bitrate > 3000 { "high" } else if adjusted_bitrate > 1500 { "medium" } else { "low" }.to_string(),
                    resolution: Some((width, height)),
                    bitrate: Some(adjusted_bitrate),
                    video_codec: Some("libx264".to_string()),
                    audio_codec: Some("aac".to_string()),
                    preset: Some("medium".to_string()),
                    profile: Some("main".to_string()),
                    ..Default::default()
                });
            }
        }

        variants
    }

    pub async fn batch_generate_thumbnails(&self, files: Vec<File>, options: ThumbnailOptions) -> Result<Vec<BatchThumbnailResult>, JsValue> {
        let mut results = Vec::new();

        for file in files {
            let file_name = file.name();
            match self.generate_thumbnail_with_cache(&file, options.clone()).await {
                Ok(blob) => {
                    results.push(BatchThumbnailResult {
                        file_name,
                        success: true,
                        thumbnail: Some(blob),
                        error: None,
                    });
                },
                Err(e) => {
                    web_sys::console::error_2(&JsValue::from_str("Failed to generate thumbnail for file:"), &JsValue::from_str(&file_name));
                    web_sys::console::error_1(&e);
                    results.push(BatchThumbnailResult {
                        file_name,
                        success: false,
                        thumbnail: None,
                        error: Some(format!("{:?}", e)),
                    });
                }
            }
        }

        Ok(results)
    }

    pub async fn generate_thumbnail_with_cache(&self, file: &File, options: ThumbnailOptions) -> Result<Blob, JsValue> {
        if let Some(cache) = &self.thumbnail_cache {
            // Generate file hash (simplified - in production use proper hashing)
            let file_hash = format!("{}_{}", file.name(), file.size());
            let cache_key = cache.generate_cache_key(&file_hash, options.time, options.width, options.height, &options.format);

            // Check cache first
            if let Some(cached_blob) = cache.get(&cache_key).await? {
                return Ok(cached_blob);
            }

            // Generate thumbnail
            let blob = self.generate_thumbnail(file, options.clone()).await?;

            // Cache the result
            let metadata = ThumbnailMetadata {
                file_hash,
                timestamp: options.time,
                width: options.width,
                height: options.height,
                format: options.format,
            };

            cache.set(&cache_key, &blob, metadata).await?;
            Ok(blob)
        } else {
            // No cache, generate directly
            self.generate_thumbnail(file, options).await
        }
    }

    pub async fn batch_generate_timeline_thumbnails(&self, file: &File, count: u32, width: u32, height: u32) -> Result<TimelineThumbnailResult, JsValue> {
        if !self.ffmpeg.is_loaded() {
            return Err(JsValue::from_str("FFmpeg not initialized"));
        }

        // Get video info
        let video_info = self.get_video_info(file).await?;
        let duration = video_info.duration;

        if duration <= 0.0 {
            return Err(JsValue::from_str("Invalid video duration"));
        }

        // Calculate time intervals
        let interval = duration / (count + 1) as f64;
        let times: Vec<f64> = (1..=count).map(|i| i as f64 * interval).collect();

        let mut thumbnails = Vec::new();
        let mut failed_times = Vec::new();

        for time in times {
            let options = ThumbnailOptions {
                width,
                height,
                time,
                format: "jpeg".to_string(),
            };

            match self.generate_thumbnail_with_cache(file, options).await {
                Ok(blob) => {
                    thumbnails.push(TimelineThumbnail {
                        timestamp: time,
                        blob,
                    });
                },
                Err(e) => {
                    web_sys::console::warn_2(&JsValue::from_str("Failed to generate thumbnail at time"), &JsValue::from_f64(time));
                    web_sys::console::warn_1(&e);
                    failed_times.push(time);
                }
            }
        }

        let successful_count = thumbnails.len() as u32;
        Ok(TimelineThumbnailResult {
            duration,
            thumbnails,
            failed_times,
            total_requested: count,
            successful_count,
        })
    }

    pub async fn generate_adaptive_thumbnails(&self, file: &File, target_width: u32) -> Result<Vec<AdaptiveThumbnail>, JsValue> {
        let video_info = self.get_video_info(file).await?;
        let aspect_ratio = video_info.width as f32 / video_info.height as f32;

        // Generate multiple sizes for responsive display
        let sizes = vec![
            (target_width, (target_width as f32 / aspect_ratio) as u32),
            (target_width / 2, (target_width as f32 / aspect_ratio / 2.0) as u32),
            (target_width / 4, (target_width as f32 / aspect_ratio / 4.0) as u32),
        ];

        let mut adaptive_thumbnails = Vec::new();
        let mid_time = video_info.duration / 2.0;

        for (width, height) in sizes {
            let options = ThumbnailOptions {
                width,
                height,
                time: mid_time,
                format: "jpeg".to_string(),
            };

            match self.generate_thumbnail_with_cache(file, options).await {
                Ok(blob) => {
                    adaptive_thumbnails.push(AdaptiveThumbnail {
                        width,
                        height,
                        blob,
                    });
                },
                Err(e) => {
                    web_sys::console::warn_2(&JsValue::from_str("Failed to generate adaptive thumbnail"), &JsValue::from_str(&format!("{}x{}", width, height)));
                    web_sys::console::warn_1(&e);
                }
            }
        }

        Ok(adaptive_thumbnails)
    }

    pub async fn preload_thumbnails(&self, files: Vec<File>, options: ThumbnailOptions) -> Result<PreloadResult, JsValue> {
        let start_time = js_sys::Date::now();
        let mut successful = 0;
        let mut failed = 0;
        let mut total_size = 0u64;

        for file in files {
            match self.generate_thumbnail_with_cache(&file, options.clone()).await {
                Ok(blob) => {
                    successful += 1;
                    total_size += blob.size() as u64;
                },
                Err(_) => {
                    failed += 1;
                }
            }
        }

        let end_time = js_sys::Date::now();

        Ok(PreloadResult {
            successful_count: successful,
            failed_count: failed,
            total_size_bytes: total_size,
            duration_ms: end_time - start_time,
        })
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VideoInfo {
    pub duration: f64,
    pub width: u32,
    pub height: u32,
    pub format: String,
    pub bitrate: Option<u32>,
    pub fps: Option<f32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetailedVideoInfo {
    pub duration: f64,
    pub width: u32,
    pub height: u32,
    pub format: String,
    pub video_codec: String,
    pub audio_codec: String,
    pub bitrate: Option<u32>,
    pub fps: Option<f32>,
    pub audio_channels: Option<u32>,
    pub audio_sample_rate: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormatInfo {
    pub name: String,
    pub description: String,
    pub supported_video_codecs: Vec<String>,
    pub supported_audio_codecs: Vec<String>,
    pub file_extensions: Vec<String>,
    pub mime_type: String,
    pub is_streamable: bool,
    pub max_resolution: Option<(u32, u32)>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversionRecommendation {
    pub recommended_format: String,
    pub video_codec: String,
    pub audio_codec: String,
    pub quality_preset: String,
    pub reason: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormatValidationResult {
    pub is_valid: bool,
    pub detected_format: String,
    pub format_info: FormatInfo,
    pub file_size: u64,
    pub issues: Vec<String>,
    pub warnings: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodecInfo {
    pub name: String,
    pub display_name: String,
    pub description: String,
    pub quality_range: (u32, u32),
    pub default_quality: u32,
    pub supports_hardware_acceleration: bool,
    pub file_formats: Vec<String>,
    pub profile_options: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodecDetectionResult {
    pub video_codec: Option<String>,
    pub audio_codec: Option<String>,
    pub video_bitrate: Option<u32>,
    pub audio_bitrate: Option<u32>,
    pub video_profile: Option<String>,
    pub audio_profile: Option<String>,
    pub has_video: bool,
    pub has_audio: bool,
    pub duration: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodecRecommendation {
    pub video_codec: String,
    pub audio_codec: String,
    pub video_quality: u32,
    pub audio_quality: u32,
    pub reason: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodecCompatibilityResult {
    pub is_compatible: bool,
    pub video_supported: bool,
    pub audio_supported: bool,
    pub issues: Vec<String>,
    pub warnings: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityValidationResult {
    pub is_valid: bool,
    pub issues: Vec<String>,
    pub warnings: Vec<String>,
    pub recommendations: Vec<String>,
    pub estimated_file_size: Option<u64>,
    pub estimated_encoding_time: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityPreset {
    pub name: String,
    pub description: String,
    pub target_bitrate_factor: f64,
    pub crf_value: u32,
    pub preset: String,
    pub recommended_for: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResolutionPreset {
    pub name: String,
    pub width: u32,
    pub height: u32,
    pub aspect_ratio: String,
    pub recommended_bitrate_range: (u32, u32),
    pub description: String,
}

#[derive(Debug, Clone)]
pub struct BatchThumbnailResult {
    pub file_name: String,
    pub success: bool,
    pub thumbnail: Option<Blob>,
    pub error: Option<String>,
}

#[derive(Debug, Clone)]
pub struct TimelineThumbnail {
    pub timestamp: f64,
    pub blob: Blob,
}

#[derive(Debug, Clone)]
pub struct TimelineThumbnailResult {
    pub duration: f64,
    pub thumbnails: Vec<TimelineThumbnail>,
    pub failed_times: Vec<f64>,
    pub total_requested: u32,
    pub successful_count: u32,
}

#[derive(Debug, Clone)]
pub struct AdaptiveThumbnail {
    pub width: u32,
    pub height: u32,
    pub blob: Blob,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PreloadResult {
    pub successful_count: u32,
    pub failed_count: u32,
    pub total_size_bytes: u64,
    pub duration_ms: f64,
}

impl Default for VideoProcessingOptions {
    fn default() -> Self {
        Self {
            format: "mp4".to_string(),
            quality: "medium".to_string(),
            resolution: None,
            bitrate: None,
            fps: None,
            video_codec: None,
            audio_codec: None,
            preset: Some("medium".to_string()),
            profile: None,
            level: None,
            pixel_format: None,
            color_space: None,
            color_range: None,
            two_pass: false,
            hardware_acceleration: false,
            custom_filters: Vec::new(),
        }
    }
}

impl Default for ThumbnailOptions {
    fn default() -> Self {
        Self {
            width: 320,
            height: 240,
            time: 0.0,
            format: "jpeg".to_string(),
        }
    }
}

// Export formats supported
pub fn get_supported_export_formats() -> Vec<&'static str> {
    vec!["mp4", "webm", "avi", "mov", "mkv", "flv", "wmv", "3gp", "ogg"]
}

pub fn get_supported_audio_formats() -> Vec<&'static str> {
    vec!["mp3", "wav", "aac", "ogg", "flac", "opus", "ac3", "dts"]
}

pub fn get_supported_import_formats() -> Vec<&'static str> {
    vec![
        "mp4", "m4v", "webm", "avi", "mov", "qt", "mkv", "mka",
        "flv", "wmv", "asf", "3gp", "3g2", "ogv", "ogg",
        "ts", "mts", "m2ts", "vob", "rm", "rmvb", "f4v",
        "divx", "xvid"
    ]
}

pub fn get_format_category(format: &str) -> &'static str {
    match format {
        "mp4" | "m4v" => "modern",
        "webm" => "web",
        "avi" | "divx" | "xvid" => "legacy",
        "mov" | "qt" => "professional",
        "mkv" | "mka" => "open",
        "flv" | "f4v" => "flash",
        "wmv" | "asf" => "microsoft",
        "3gp" | "3g2" => "mobile",
        "ogv" | "ogg" => "open",
        "ts" | "mts" | "m2ts" => "broadcast",
        "vob" => "dvd",
        "rm" | "rmvb" => "realmedia",
        _ => "unknown",
    }
}

pub fn get_codec_category(codec: &str) -> &'static str {
    match codec {
        "libx264" | "h264" => "modern",
        "libx265" | "h265" | "hevc" => "next-gen",
        "libvpx" | "vp8" => "web-legacy",
        "libvpx-vp9" | "vp9" => "web-modern",
        "libaom-av1" | "av1" => "future",
        "aac" => "modern-audio",
        "libmp3lame" | "mp3" => "legacy-audio",
        "libopus" | "opus" => "web-audio",
        "libvorbis" | "vorbis" => "open-audio",
        "flac" => "lossless-audio",
        _ => "unknown",
    }
}

pub fn estimate_encoding_time(duration: f64, codec: &str, quality: u32) -> f64 {
    // Base encoding speed (seconds of video per second of real time)
    let base_speed = match codec {
        "libx264" => 2.0,
        "libx265" => 0.5, // Much slower
        "libvpx" => 1.5,
        "libvpx-vp9" => 0.8,
        "libaom-av1" => 0.2, // Very slow
        _ => 1.0,
    };

    // Quality factor (higher quality = slower encoding)
    let quality_factor = match codec {
        "libx264" | "libx265" => {
            if quality < 18 { 0.5 } else if quality > 35 { 2.0 } else { 1.0 }
        },
        "libvpx-vp9" => {
            if quality < 20 { 0.4 } else if quality > 40 { 1.5 } else { 1.0 }
        },
        _ => 1.0,
    };

    duration / (base_speed * quality_factor)
}

pub fn get_hardware_acceleration_options(codec: &str) -> Vec<String> {
    match codec {
        "libx264" => vec![
            "h264_videotoolbox".to_string(), // macOS
            "h264_nvenc".to_string(),        // NVIDIA
            "h264_qsv".to_string(),          // Intel
            "h264_vaapi".to_string(),        // Linux
        ],
        "libx265" => vec![
            "hevc_videotoolbox".to_string(), // macOS
            "hevc_nvenc".to_string(),        // NVIDIA
            "hevc_qsv".to_string(),          // Intel
            "hevc_vaapi".to_string(),        // Linux
        ],
        "libvpx-vp9" => vec![
            "vp9_vaapi".to_string(),         // Linux
        ],
        _ => vec![],
    }
}
