use wasm_bindgen::prelude::*;
use web_sys::{
    HtmlCanvasElement, CanvasRenderingContext2d, ImageData
};
use js_sys::Uint8ClampedArray;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use uuid::Uuid;

use crate::utils::video_decoder::DecodedFrame;
use crate::utils::video_pipeline::{VideoRenderSettings, VideoRenderingContext};

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct VideoRenderConfig {
    pub canvas_width: u32,
    pub canvas_height: u32,
    pub pixel_ratio: f64,
    pub enable_hardware_acceleration: bool,
    pub enable_alpha: bool,
    pub color_space: String,
    pub premultiplied_alpha: bool,
    pub preserve_drawing_buffer: bool,
    pub antialias: bool,
    pub depth: bool,
    pub stencil: bool,
    pub fail_if_major_performance_caveat: bool,
}

impl Default for VideoRenderConfig {
    fn default() -> Self {
        Self {
            canvas_width: 1920,
            canvas_height: 1080,
            pixel_ratio: 1.0,
            enable_hardware_acceleration: true,
            enable_alpha: true,
            color_space: "srgb".to_string(),
            premultiplied_alpha: true,
            preserve_drawing_buffer: false,
            antialias: true,
            depth: false,
            stencil: false,
            fail_if_major_performance_caveat: false,
        }
    }
}

#[derive(Debug, Clone)]
pub struct VideoFrame {
    pub timestamp: f64,
    pub duration: f64,
    pub width: u32,
    pub height: u32,
    pub data: Vec<u8>, // RGBA pixel data
    pub format: VideoFrameFormat,
}

#[derive(Debug, Clone, PartialEq)]
pub enum VideoFrameFormat {
    RGBA8,
    RGB8,
    YUV420P,
    YUV422P,
    YUV444P,
}

#[derive(Debug, Clone)]
pub struct RenderLayer {
    pub id: Uuid,
    pub z_index: i32,
    pub opacity: f64,
    pub blend_mode: BlendMode,
    pub transform: Transform2D,
    pub visible: bool,
    pub content: LayerContent,
}

#[derive(Debug, Clone)]
pub enum LayerContent {
    VideoFrame(VideoFrame),
    Canvas(HtmlCanvasElement),
    Text(String),
}

#[derive(Debug, Clone, PartialEq)]
pub enum BlendMode {
    Normal,
    Multiply,
    Screen,
    Overlay,
    SoftLight,
    HardLight,
    ColorDodge,
    ColorBurn,
    Darken,
    Lighten,
    Difference,
    Exclusion,
}

#[derive(Debug, Clone)]
pub struct Transform2D {
    pub translate_x: f64,
    pub translate_y: f64,
    pub scale_x: f64,
    pub scale_y: f64,
    pub rotation: f64, // radians
    pub skew_x: f64,
    pub skew_y: f64,
}

impl Default for Transform2D {
    fn default() -> Self {
        Self {
            translate_x: 0.0,
            translate_y: 0.0,
            scale_x: 1.0,
            scale_y: 1.0,
            rotation: 0.0,
            skew_x: 0.0,
            skew_y: 0.0,
        }
    }
}

pub struct VideoRenderer {
    config: VideoRenderConfig,
    canvas: HtmlCanvasElement,
    context: CanvasRenderingContext2d,
    layers: Vec<RenderLayer>,
    background_color: String,
    current_frame: Option<VideoFrame>,
    frame_cache: HashMap<u64, VideoFrame>, // timestamp -> frame
    render_settings: VideoRenderSettings,
    performance_stats: RenderPerformanceStats,
    is_rendering: bool,
}

#[derive(Debug, Clone, Default)]
pub struct RenderPerformanceStats {
    pub frames_rendered: u64,
    pub total_render_time: f64,
    pub average_render_time: f64,
    pub last_render_time: f64,
    pub dropped_frames: u64,
    pub cache_hits: u64,
    pub cache_misses: u64,
}

impl VideoRenderer {
    pub fn new(canvas: HtmlCanvasElement, config: VideoRenderConfig) -> Result<Self, JsValue> {
        let context = canvas
            .get_context("2d")?
            .unwrap()
            .dyn_into::<CanvasRenderingContext2d>()?;

        // Set canvas size
        canvas.set_width(config.canvas_width);
        canvas.set_height(config.canvas_height);

        // Configure context
        context.set_image_smoothing_enabled(config.antialias);

        Ok(Self {
            config,
            canvas,
            context,
            layers: Vec::new(),
            background_color: "#000000".to_string(),
            current_frame: None,
            frame_cache: HashMap::new(),
            render_settings: VideoRenderSettings::default(),
            performance_stats: RenderPerformanceStats::default(),
            is_rendering: false,
        })
    }



    pub fn set_background_color(&mut self, color: &str) {
        self.background_color = color.to_string();
    }

    pub fn add_layer(&mut self, layer: RenderLayer) {
        self.layers.push(layer);
        self.sort_layers();
    }

    pub fn remove_layer(&mut self, layer_id: &Uuid) {
        self.layers.retain(|layer| layer.id != *layer_id);
    }

    pub fn update_layer(&mut self, layer_id: &Uuid, updated_layer: RenderLayer) {
        if let Some(layer) = self.layers.iter_mut().find(|l| l.id == *layer_id) {
            *layer = updated_layer;
            self.sort_layers();
        }
    }

    fn sort_layers(&mut self) {
        self.layers.sort_by(|a, b| a.z_index.cmp(&b.z_index));
    }

    pub fn set_current_frame(&mut self, frame: VideoFrame) {
        let timestamp_key = (frame.timestamp * 1000.0) as u64; // Convert to milliseconds
        self.frame_cache.insert(timestamp_key, frame.clone());
        self.current_frame = Some(frame);
    }

    pub fn get_cached_frame(&self, timestamp: f64) -> Option<&VideoFrame> {
        let timestamp_key = (timestamp * 1000.0) as u64;
        self.frame_cache.get(&timestamp_key)
    }

    pub fn clear_frame_cache(&mut self) {
        self.frame_cache.clear();
    }

    pub fn render(&mut self) -> Result<(), JsValue> {
        if self.is_rendering {
            return Ok(()); // Prevent concurrent rendering
        }

        self.is_rendering = true;
        let start_time = web_sys::js_sys::Date::now();

        // Clear canvas
        self.clear_canvas()?;

        // Render background
        self.render_background()?;

        // Render all layers
        for layer in &self.layers {
            if layer.visible {
                self.render_layer(layer)?;
            }
        }

        // Update performance stats
        let render_time = web_sys::js_sys::Date::now() - start_time;
        self.update_performance_stats(render_time);

        self.is_rendering = false;
        Ok(())
    }

    fn clear_canvas(&self) -> Result<(), JsValue> {
        self.context.clear_rect(
            0.0,
            0.0,
            self.config.canvas_width as f64,
            self.config.canvas_height as f64,
        );
        Ok(())
    }

    fn render_background(&self) -> Result<(), JsValue> {
        self.context.set_fill_style(&JsValue::from_str(&self.background_color));
        self.context.fill_rect(
            0.0,
            0.0,
            self.config.canvas_width as f64,
            self.config.canvas_height as f64,
        );
        Ok(())
    }

    fn render_layer(&self, layer: &RenderLayer) -> Result<(), JsValue> {
        // Save context state
        self.context.save();

        // Apply transform
        self.apply_transform(&layer.transform)?;

        // Set opacity
        self.context.set_global_alpha(layer.opacity);

        // Set blend mode
        self.set_blend_mode(&layer.blend_mode)?;

        // Render layer content
        match &layer.content {
            LayerContent::VideoFrame(frame) => self.render_video_frame(frame)?,
            LayerContent::Canvas(canvas) => self.render_canvas(canvas)?,
            LayerContent::Text(text) => self.render_text(text)?,
        }

        // Restore context state
        self.context.restore();

        Ok(())
    }

    fn apply_transform(&self, transform: &Transform2D) -> Result<(), JsValue> {
        self.context.translate(transform.translate_x, transform.translate_y)?;
        self.context.scale(transform.scale_x, transform.scale_y)?;
        self.context.rotate(transform.rotation)?;
        // Note: Canvas 2D doesn't have native skew, would need matrix transformation
        Ok(())
    }

    fn set_blend_mode(&self, blend_mode: &BlendMode) -> Result<(), JsValue> {
        let mode_str = match blend_mode {
            BlendMode::Normal => "source-over",
            BlendMode::Multiply => "multiply",
            BlendMode::Screen => "screen",
            BlendMode::Overlay => "overlay",
            BlendMode::SoftLight => "soft-light",
            BlendMode::HardLight => "hard-light",
            BlendMode::ColorDodge => "color-dodge",
            BlendMode::ColorBurn => "color-burn",
            BlendMode::Darken => "darken",
            BlendMode::Lighten => "lighten",
            BlendMode::Difference => "difference",
            BlendMode::Exclusion => "exclusion",
        };
        self.context.set_global_composite_operation(mode_str)?;
        Ok(())
    }

    fn render_video_frame(&self, frame: &VideoFrame) -> Result<(), JsValue> {
        // Convert frame data to ImageData
        let image_data = self.create_image_data_from_frame(frame)?;
        
        // Draw the image data to canvas
        self.context.put_image_data(&image_data, 0.0, 0.0)?;
        
        Ok(())
    }

    fn create_image_data_from_frame(&self, frame: &VideoFrame) -> Result<ImageData, JsValue> {
        let data = Uint8ClampedArray::from(&frame.data[..]);
        ImageData::new_with_u8_clamped_array_and_sh(
            wasm_bindgen::Clamped(&frame.data[..]),
            frame.width,
            frame.height
        )
    }



    fn render_canvas(&self, canvas: &HtmlCanvasElement) -> Result<(), JsValue> {
        self.context.draw_image_with_html_canvas_element(canvas, 0.0, 0.0)?;
        Ok(())
    }

    fn render_text(&self, text: &str) -> Result<(), JsValue> {
        self.context.set_font("16px Arial");
        self.context.set_fill_style(&JsValue::from_str("#FFFFFF"));
        self.context.fill_text(text, 10.0, 30.0)?;
        Ok(())
    }

    fn update_performance_stats(&mut self, render_time: f64) {
        self.performance_stats.frames_rendered += 1;
        self.performance_stats.total_render_time += render_time;
        self.performance_stats.last_render_time = render_time;
        self.performance_stats.average_render_time = 
            self.performance_stats.total_render_time / self.performance_stats.frames_rendered as f64;
    }

    pub fn get_performance_stats(&self) -> &RenderPerformanceStats {
        &self.performance_stats
    }

    pub fn resize(&mut self, width: u32, height: u32) -> Result<(), JsValue> {
        self.config.canvas_width = width;
        self.config.canvas_height = height;

        self.canvas.set_width(width);
        self.canvas.set_height(height);

        Ok(())
    }

    pub fn export_frame(&self) -> Result<String, JsValue> {
        self.canvas.to_data_url()
    }

    pub fn get_canvas(&self) -> &HtmlCanvasElement {
        &self.canvas
    }

    pub fn get_config(&self) -> &VideoRenderConfig {
        &self.config
    }

    pub fn set_render_settings(&mut self, settings: VideoRenderSettings) {
        self.render_settings = settings;
    }
}

// Video Rendering Coordinator - integrates renderer with pipeline and store
pub struct VideoRenderingCoordinator {
    renderer: VideoRenderer,
    render_queue: Vec<RenderRequest>,
    is_processing: bool,
    frame_rate: f64,
    last_frame_time: f64,
}

#[derive(Debug, Clone)]
pub struct RenderRequest {
    pub id: Uuid,
    pub timestamp: f64,
    pub layers: Vec<RenderLayer>,
    pub priority: u8,
    pub callback: Option<js_sys::Function>,
}

impl VideoRenderingCoordinator {
    pub fn new(canvas: HtmlCanvasElement, config: VideoRenderConfig) -> Result<Self, JsValue> {
        let renderer = VideoRenderer::new(canvas, config)?;

        Ok(Self {
            renderer,
            render_queue: Vec::new(),
            is_processing: false,
            frame_rate: 60.0,
            last_frame_time: 0.0,
        })
    }

    pub fn queue_render(&mut self, request: RenderRequest) {
        self.render_queue.push(request);
        self.render_queue.sort_by(|a, b| b.priority.cmp(&a.priority));
    }

    pub fn process_render_queue(&mut self) -> Result<(), JsValue> {
        if self.is_processing || self.render_queue.is_empty() {
            return Ok(());
        }

        let current_time = web_sys::js_sys::Date::now();
        let frame_interval = 1000.0 / self.frame_rate;

        if current_time - self.last_frame_time < frame_interval {
            return Ok(()); // Not time for next frame yet
        }

        self.is_processing = true;

        if let Some(request) = self.render_queue.pop() {
            // Clear existing layers
            self.renderer.layers.clear();

            // Add layers from request
            for layer in request.layers {
                self.renderer.add_layer(layer);
            }

            // Render frame
            self.renderer.render()?;

            // Execute callback if provided
            if let Some(callback) = request.callback {
                let _ = callback.call0(&JsValue::NULL);
            }

            self.last_frame_time = current_time;
        }

        self.is_processing = false;
        Ok(())
    }

    pub fn set_frame_rate(&mut self, fps: f64) {
        self.frame_rate = fps.max(1.0).min(120.0);
    }

    pub fn get_renderer(&self) -> &VideoRenderer {
        &self.renderer
    }

    pub fn get_renderer_mut(&mut self) -> &mut VideoRenderer {
        &mut self.renderer
    }

    pub fn clear_queue(&mut self) {
        self.render_queue.clear();
    }

    pub fn get_queue_length(&self) -> usize {
        self.render_queue.len()
    }
}

// Helper functions for creating common render layers
pub fn create_video_layer(frame: VideoFrame, z_index: i32) -> RenderLayer {
    RenderLayer {
        id: Uuid::new_v4(),
        z_index,
        opacity: 1.0,
        blend_mode: BlendMode::Normal,
        transform: Transform2D::default(),
        visible: true,
        content: LayerContent::VideoFrame(frame),
    }
}

pub fn create_text_layer(text: String, z_index: i32) -> RenderLayer {
    RenderLayer {
        id: Uuid::new_v4(),
        z_index,
        opacity: 1.0,
        blend_mode: BlendMode::Normal,
        transform: Transform2D::default(),
        visible: true,
        content: LayerContent::Text(text),
    }
}



// Convert DecodedFrame to VideoFrame
impl From<DecodedFrame> for VideoFrame {
    fn from(decoded_frame: DecodedFrame) -> Self {
        // This is a simplified conversion - in a real implementation,
        // you would extract the actual pixel data from the canvas
        Self {
            timestamp: decoded_frame.timestamp,
            duration: 1.0 / 30.0, // Default to 30fps
            width: decoded_frame.width,
            height: decoded_frame.height,
            data: vec![0; (decoded_frame.width * decoded_frame.height * 4) as usize], // Placeholder RGBA data
            format: VideoFrameFormat::RGBA8,
        }
    }
}
