use wasm_bindgen::prelude::*;
use serde::{Serialize, Deserialize};
use chrono::Utc;

use crate::utils::storage::{DatabaseManager, MigrationRecord, DB_VERSION};

/// Migration step definition
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MigrationStep {
    pub version: u32,
    pub description: String,
    pub up_sql: Vec<String>,
    pub down_sql: Vec<String>,
    pub requires_data_migration: bool,
}

/// Migration result
#[derive(Debug, Clone)]
pub struct MigrationResult {
    pub success: bool,
    pub applied_migrations: Vec<u32>,
    pub failed_migration: Option<u32>,
    pub error_message: Option<String>,
    pub total_steps: u32,
    pub completed_steps: u32,
    pub duration_ms: u64,
}

/// Migration progress callback
pub type MigrationProgressCallback = Box<dyn Fn(u32, u32, &str)>;

/// Migration validation result
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
}

/// Database migration manager
pub struct DatabaseMigrationManager {
    db_manager: DatabaseManager,
    migrations: Vec<MigrationStep>,
}

impl DatabaseMigrationManager {
    pub fn new() -> Self {
        Self {
            db_manager: DatabaseManager::new(),
            migrations: Self::get_all_migrations(),
        }
    }

    /// Initialize the migration manager
    pub async fn init(&mut self) -> Result<(), JsValue> {
        self.db_manager.init().await
    }

    /// Check if migrations are needed
    pub async fn needs_migration(&self) -> Result<bool, JsValue> {
        let current_version = self.get_current_database_version().await?;
        Ok(current_version < DB_VERSION)
    }

    /// Get current database version
    pub async fn get_current_database_version(&self) -> Result<u32, JsValue> {
        let transaction = self.db_manager.transaction(&["migrations"], "readonly")?;
        let store = transaction.object_store("migrations")?;
        let request = store.get_all()?;
        let result = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;

        let migrations_array: Array = result.dyn_into()?;
        let mut max_version = 0u32;

        for i in 0..migrations_array.length() {
            if let Ok(migration_value) = migrations_array.get(i).dyn_into::<JsValue>() {
                if let Ok(migration) = serde_wasm_bindgen::from_value::<MigrationRecord>(migration_value) {
                    max_version = max_version.max(migration.version);
                }
            }
        }

        Ok(max_version)
    }

    /// Run all pending migrations with progress tracking
    pub async fn migrate(&self) -> Result<MigrationResult, JsValue> {
        let start_time = js_sys::Date::now() as u64;
        let current_version = self.get_current_database_version().await?;
        let mut applied_migrations = Vec::new();

        let pending_migrations: Vec<_> = self.migrations.iter()
            .filter(|m| m.version > current_version)
            .collect();

        let total_steps = pending_migrations.len() as u32;
        let mut completed_steps = 0u32;

        // Validate before starting migrations
        let validation = self.validate_migration_path(&pending_migrations).await?;
        if !validation.is_valid {
            return Ok(MigrationResult {
                success: false,
                applied_migrations,
                failed_migration: None,
                error_message: Some(format!("Migration validation failed: {:?}", validation.errors)),
                total_steps,
                completed_steps,
                duration_ms: js_sys::Date::now() as u64 - start_time,
            });
        }

        for migration in pending_migrations {
            web_sys::console::log_1(&format!("Applying migration {} of {}: {}",
                completed_steps + 1, total_steps, migration.description).into());

            match self.apply_migration(migration).await {
                Ok(_) => {
                    applied_migrations.push(migration.version);
                    self.record_migration(migration).await?;
                    completed_steps += 1;
                }
                Err(e) => {
                    return Ok(MigrationResult {
                        success: false,
                        applied_migrations,
                        failed_migration: Some(migration.version),
                        error_message: Some(format!("Migration {} failed: {:?}", migration.version, e)),
                        total_steps,
                        completed_steps,
                        duration_ms: js_sys::Date::now() as u64 - start_time,
                    });
                }
            }
        }

        Ok(MigrationResult {
            success: true,
            applied_migrations,
            failed_migration: None,
            error_message: None,
            total_steps,
            completed_steps,
            duration_ms: js_sys::Date::now() as u64 - start_time,
        })
    }

    /// Apply a single migration
    async fn apply_migration(&self, migration: &MigrationStep) -> Result<(), JsValue> {
        web_sys::console::log_1(&format!("Applying migration {}: {}", migration.version, migration.description).into());

        // For IndexedDB, we need to handle schema changes differently
        // Most schema changes require a version upgrade which triggers onupgradeneeded
        if migration.requires_data_migration {
            self.apply_data_migration(migration).await?;
        }

        Ok(())
    }

    /// Apply data migration (for data transformations without schema changes)
    async fn apply_data_migration(&self, migration: &MigrationStep) -> Result<(), JsValue> {
        match migration.version {
            2 => self.migrate_v1_to_v2().await?,
            3 => self.migrate_v2_to_v3().await?,
            _ => {
                web_sys::console::log_1(&format!("No data migration needed for version {}", migration.version).into());
            }
        }
        Ok(())
    }

    /// Record successful migration
    async fn record_migration(&self, migration: &MigrationStep) -> Result<(), JsValue> {
        let migration_record = MigrationRecord {
            version: migration.version,
            applied_at: Utc::now(),
            description: migration.description.clone(),
        };

        let transaction = self.db_manager.transaction(&["migrations"], "readwrite")?;
        let store = transaction.object_store("migrations")?;
        let migration_value = serde_wasm_bindgen::to_value(&migration_record)?;
        let request = store.put(&migration_value)?;
        wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;

        Ok(())
    }

    /// Rollback to a specific version
    pub async fn rollback_to_version(&self, target_version: u32) -> Result<MigrationResult, JsValue> {
        let current_version = self.get_current_database_version().await?;
        
        if target_version >= current_version {
            return Ok(MigrationResult {
                success: false,
                applied_migrations: Vec::new(),
                failed_migration: None,
                error_message: Some("Target version must be lower than current version".to_string()),
                total_steps: 0,
                completed_steps: 0,
                duration_ms: 0,
            });
        }

        // Find migrations to rollback (in reverse order)
        let mut rollback_migrations = Vec::new();
        for migration in self.migrations.iter().rev() {
            if migration.version > target_version && migration.version <= current_version {
                rollback_migrations.push(migration);
            }
        }

        let mut rolled_back = Vec::new();
        let total_migrations = rollback_migrations.len();
        for migration in &rollback_migrations {
            match self.rollback_migration(migration).await {
                Ok(_) => {
                    rolled_back.push(migration.version);
                    self.remove_migration_record(migration.version).await?;
                }
                Err(e) => {
                    return Ok(MigrationResult {
                        success: false,
                        applied_migrations: rolled_back.clone(),
                        failed_migration: Some(migration.version),
                        error_message: Some(format!("Rollback of migration {} failed: {:?}", migration.version, e)),
                        total_steps: total_migrations as u32,
                        completed_steps: rolled_back.len() as u32,
                        duration_ms: 0,
                    });
                }
            }
        }

        Ok(MigrationResult {
            success: true,
            applied_migrations: rolled_back.clone(),
            failed_migration: None,
            error_message: None,
            total_steps: total_migrations as u32,
            completed_steps: rolled_back.len() as u32,
            duration_ms: 0,
        })
    }

    /// Rollback a single migration
    async fn rollback_migration(&self, migration: &MigrationStep) -> Result<(), JsValue> {
        web_sys::console::log_1(&format!("Rolling back migration {}: {}", migration.version, migration.description).into());

        if migration.requires_data_migration {
            self.rollback_data_migration(migration).await?;
        }

        Ok(())
    }

    /// Rollback data migration
    async fn rollback_data_migration(&self, migration: &MigrationStep) -> Result<(), JsValue> {
        match migration.version {
            2 => self.rollback_v2_to_v1().await?,
            3 => self.rollback_v3_to_v2().await?,
            _ => {
                web_sys::console::log_1(&format!("No data rollback needed for version {}", migration.version).into());
            }
        }
        Ok(())
    }

    /// Remove migration record
    async fn remove_migration_record(&self, version: u32) -> Result<(), JsValue> {
        let transaction = self.db_manager.transaction(&["migrations"], "readwrite")?;
        let store = transaction.object_store("migrations")?;
        let key = JsValue::from_f64(version as f64);
        let request = store.delete(&key)?;
        wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;
        Ok(())
    }

    /// Get all migration definitions
    fn get_all_migrations() -> Vec<MigrationStep> {
        vec![
            MigrationStep {
                version: 2,
                description: "Add user preferences and settings".to_string(),
                up_sql: vec![],
                down_sql: vec![],
                requires_data_migration: true,
            },
            MigrationStep {
                version: 3,
                description: "Add media file metadata indexing".to_string(),
                up_sql: vec![],
                down_sql: vec![],
                requires_data_migration: true,
            },
        ]
    }

    /// Migration v1 to v2: Add user preferences
    async fn migrate_v1_to_v2(&self) -> Result<(), JsValue> {
        web_sys::console::log_1(&"Migrating v1 to v2: Adding user preferences".into());

        use crate::utils::storage::UserSettings;

        // Check if default user settings exist
        if let Ok(None) = self.db_manager.load_user_settings("default").await {
            // Create default user settings
            let default_settings = UserSettings::default();
            self.db_manager.save_user_settings(&default_settings).await?;
            web_sys::console::log_1(&"Created default user settings".into());
        }

        // Migrate any existing projects to include new metadata fields
        let projects = self.db_manager.list_projects().await?;
        for mut project in projects {
            // Add version field if missing
            if project.version.is_empty() {
                project.version = "1.0.0".to_string();
                project.updated_at = chrono::Utc::now();
                self.db_manager.save_project(&project).await?;
            }
        }

        web_sys::console::log_1(&"Completed v1 to v2 migration".into());
        Ok(())
    }

    /// Migration v2 to v3: Add media metadata indexing
    async fn migrate_v2_to_v3(&self) -> Result<(), JsValue> {
        web_sys::console::log_1(&"Migrating v2 to v3: Adding media metadata indexing".into());

        // Update all media files to include enhanced metadata
        let media_files = self.db_manager.list_media_files().await?;
        let media_files_count = media_files.len();
        for mut media_file in media_files {
            // Add missing metadata fields
            if media_file.metadata.is_empty() {
                // Initialize basic metadata
                media_file.metadata.insert("format_version".to_string(), serde_json::Value::String("3.0".to_string()));
                media_file.metadata.insert("indexed_at".to_string(), serde_json::Value::String(chrono::Utc::now().to_rfc3339()));

                // Add file-type specific metadata
                match media_file.file_type.as_str() {
                    "video" => {
                        media_file.metadata.insert("has_audio".to_string(), serde_json::Value::Bool(true));
                        media_file.metadata.insert("codec".to_string(), serde_json::Value::String("unknown".to_string()));
                    }
                    "audio" => {
                        media_file.metadata.insert("channels".to_string(), serde_json::Value::Number(serde_json::Number::from(2)));
                        media_file.metadata.insert("sample_rate".to_string(), serde_json::Value::Number(serde_json::Number::from(48000)));
                    }
                    "image" => {
                        media_file.metadata.insert("color_space".to_string(), serde_json::Value::String("sRGB".to_string()));
                    }
                    _ => {}
                }

                // Update last_accessed for cache tracking
                media_file.last_accessed = chrono::Utc::now();

                // Save updated media file
                self.db_manager.save_media_file(&media_file).await?;
            }
        }

        web_sys::console::log_1(&format!("Updated {} media files with enhanced metadata", media_files_count).into());
        Ok(())
    }

    /// Rollback v2 to v1
    async fn rollback_v2_to_v1(&self) -> Result<(), JsValue> {
        web_sys::console::log_1(&"Rolling back v2 to v1: Removing user preferences".into());

        // Remove user settings
        let transaction = self.db_manager.transaction(&["user_settings"], "readwrite")?;
        let store = transaction.object_store("user_settings")?;
        let request = store.clear()?;
        wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;

        // Revert project version fields
        let projects = self.db_manager.list_projects().await?;
        for mut project in projects {
            project.version = String::new(); // Remove version field
            self.db_manager.save_project(&project).await?;
        }

        web_sys::console::log_1(&"Completed v2 to v1 rollback".into());
        Ok(())
    }

    /// Rollback v3 to v2
    async fn rollback_v3_to_v2(&self) -> Result<(), JsValue> {
        web_sys::console::log_1(&"Rolling back v3 to v2: Removing media metadata indexing".into());

        // Remove enhanced metadata from media files
        let media_files = self.db_manager.list_media_files().await?;
        let media_files_count = media_files.len();
        for mut media_file in media_files {
            // Clear enhanced metadata but keep basic fields
            media_file.metadata.clear();
            self.db_manager.save_media_file(&media_file).await?;
        }

        web_sys::console::log_1(&format!("Removed enhanced metadata from {} media files", media_files_count).into());
        Ok(())
    }

    /// Get migration history
    pub async fn get_migration_history(&self) -> Result<Vec<MigrationRecord>, JsValue> {
        let transaction = self.db_manager.transaction(&["migrations"], "readonly")?;
        let store = transaction.object_store("migrations")?;
        let request = store.get_all()?;
        let result = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::resolve(&request)).await?;

        let migrations_array: Array = result.dyn_into()?;
        let mut migrations = Vec::new();

        for i in 0..migrations_array.length() {
            if let Ok(migration_value) = migrations_array.get(i).dyn_into::<JsValue>() {
                if let Ok(migration) = serde_wasm_bindgen::from_value::<MigrationRecord>(migration_value) {
                    migrations.push(migration);
                }
            }
        }

        // Sort by version
        migrations.sort_by_key(|m| m.version);
        Ok(migrations)
    }

    /// Validate migration path before execution
    async fn validate_migration_path(&self, migrations: &[&MigrationStep]) -> Result<ValidationResult, JsValue> {
        let mut result = ValidationResult {
            is_valid: true,
            errors: Vec::new(),
            warnings: Vec::new(),
        };

        // Check for version gaps
        let mut expected_version = self.get_current_database_version().await? + 1;
        for migration in migrations {
            if migration.version != expected_version {
                result.errors.push(format!(
                    "Migration version gap: expected {}, found {}",
                    expected_version, migration.version
                ));
                result.is_valid = false;
            }
            expected_version = migration.version + 1;
        }

        // Check for duplicate versions
        let mut versions = std::collections::HashSet::new();
        for migration in migrations {
            if !versions.insert(migration.version) {
                result.errors.push(format!("Duplicate migration version: {}", migration.version));
                result.is_valid = false;
            }
        }

        // Validate database state
        if let Err(e) = self.validate_database_state().await {
            result.errors.push(format!("Database state validation failed: {:?}", e));
            result.is_valid = false;
        }

        // Add warnings for potentially risky migrations
        for migration in migrations {
            if migration.requires_data_migration {
                result.warnings.push(format!(
                    "Migration {} requires data transformation - ensure backup exists",
                    migration.version
                ));
            }
        }

        Ok(result)
    }

    /// Validate current database state
    async fn validate_database_state(&self) -> Result<(), JsValue> {
        // Check if database is accessible
        let _db = self.db_manager.get_db().map_err(|e| JsValue::from_str(&e))?;

        // Verify core tables exist
        let transaction = self.db_manager.transaction(&["projects", "media_files"], "readonly")?;
        let _projects_store = transaction.object_store("projects")?;
        let _media_store = transaction.object_store("media_files")?;

        Ok(())
    }

    /// Create database backup before migration
    pub async fn create_backup(&self) -> Result<String, JsValue> {
        let backup_id = format!("backup_{}", chrono::Utc::now().timestamp());

        // In a real implementation, this would export all data
        web_sys::console::log_1(&format!("Creating database backup: {}", backup_id).into());

        // For now, just log the backup creation
        Ok(backup_id)
    }

    /// Restore database from backup
    pub async fn restore_backup(&self, backup_id: &str) -> Result<(), JsValue> {
        web_sys::console::log_1(&format!("Restoring database from backup: {}", backup_id).into());

        // In a real implementation, this would restore all data
        Ok(())
    }
}

impl Default for DatabaseMigrationManager {
    fn default() -> Self {
        Self::new()
    }
}
