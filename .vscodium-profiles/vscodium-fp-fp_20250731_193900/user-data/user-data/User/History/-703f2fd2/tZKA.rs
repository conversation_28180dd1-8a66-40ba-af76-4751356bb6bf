use yew::prelude::*;
use std::rc::Rc;
use std::cell::RefCell;
use serde::{Serialize, Deserialize};
use uuid::Uuid;

use crate::utils::audio_system_coordinator::{AudioSystemCoordinator, AudioSystemConfig, AudioSystemStatus};
use crate::utils::audio_manager::AudioPlaybackState;
use crate::utils::audio_effects_system::{AudioElementEffects, VolumeControlSettings, FilterSettings};
use crate::utils::audio_timeline_sync::{AudioSyncStats, RealtimeStats, PerformanceReport};
use crate::types::timeline::{Timeline, TimelineElement};

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct AudioStoreState {
    pub playback_state: AudioPlaybackState,
    pub system_status: Option<AudioSystemStatus>,
    pub master_volume: f64,
    pub is_muted: bool,
    pub current_timeline: Option<Timeline>,
    pub loaded_audio_elements: Vec<Uuid>,
    pub audio_effects: std::collections::HashMap<Uuid, AudioElementEffects>,
    pub is_initializing: bool,
    pub error_message: Option<String>,
    // Sync communication fields
    pub sync_stats: Option<AudioSyncStats>,
    pub realtime_stats: Option<RealtimeStats>,
    pub realtime_position_updates_enabled: bool,
    pub sync_quality_score: f64,
}

impl Default for AudioStoreState {
    fn default() -> Self {
        Self {
            playback_state: AudioPlaybackState::default(),
            system_status: None,
            master_volume: 1.0,
            is_muted: false,
            current_timeline: None,
            loaded_audio_elements: Vec::new(),
            audio_effects: std::collections::HashMap::new(),
            is_initializing: false,
            error_message: None,
            // Initialize sync communication fields
            sync_stats: None,
            realtime_stats: None,
            realtime_position_updates_enabled: false,
            sync_quality_score: 1.0,
        }
    }
}

#[derive(Debug, Clone)]
pub enum AudioStoreAction {
    InitializeAudioSystem,
    AudioSystemInitialized(AudioSystemStatus),
    AudioSystemError(String),
    PlayPause,
    Play,
    Pause,
    Stop,
    Seek(f64),
    SetVolume(f64),
    SetMuted(bool),
    LoadTimeline(Timeline),
    LoadAudioElement(Uuid),
    UnloadAudioElement(Uuid),
    UpdatePlaybackState(AudioPlaybackState),
    UpdateSystemStatus(AudioSystemStatus),
    // Audio Effects Actions
    SetElementVolume(Uuid, f32),
    SetElementMuted(Uuid, bool),
    SetElementPan(Uuid, f32),
    ApplyElementFilter(Uuid, FilterSettings),
    UpdateElementEffects(Uuid, AudioElementEffects),
    // Sync Communication Actions
    EnableRealtimePositionUpdates,
    DisableRealtimePositionUpdates,
    UpdateSyncStats(AudioSyncStats),
    UpdateRealtimeStats(RealtimeStats),
    UpdateTimelinePosition(f64),
    RequestSyncQualityUpdate,
    // Enhanced seek action
    SeekToPosition(f64, bool), // position, is_playing
}

pub struct AudioStore {
    state: Rc<RefCell<AudioStoreState>>,
    audio_coordinator: Rc<RefCell<Option<AudioSystemCoordinator>>>,
    listeners: Rc<RefCell<Vec<Callback<AudioStoreState>>>>,
}

impl AudioStore {
    pub fn new() -> Self {
        Self {
            state: Rc::new(RefCell::new(AudioStoreState::default())),
            audio_coordinator: Rc::new(RefCell::new(None)),
            listeners: Rc::new(RefCell::new(Vec::new())),
        }
    }

    pub fn get_state(&self) -> AudioStoreState {
        self.state.borrow().clone()
    }

    pub fn subscribe(&self, callback: Callback<AudioStoreState>) {
        self.listeners.borrow_mut().push(callback);
    }

    pub fn dispatch(&self, action: AudioStoreAction) {
        let mut state = self.state.borrow_mut();
        let mut coordinator = self.audio_coordinator.borrow_mut();

        match action {
            AudioStoreAction::InitializeAudioSystem => {
                state.is_initializing = true;
                state.error_message = None;
                
                // Initialize audio system coordinator
                match AudioSystemCoordinator::new(AudioSystemConfig::default()) {
                    Ok(audio_coord) => {
                        *coordinator = Some(audio_coord);
                        // In a real implementation, we'd initialize the audio context here
                        // For now, we'll simulate successful initialization
                        let status = AudioSystemStatus {
                            is_initialized: true,
                            playback_state: state.playback_state.clone(),
                            loaded_buffers: 0,
                            active_sources: 0,
                            effects_enabled: true,
                            sync_enabled: true,
                            effects_stats: None,
                            sync_stats: None,
                        };
                        state.system_status = Some(status.clone());
                        state.is_initializing = false;
                    }
                    Err(e) => {
                        state.error_message = Some(format!("Failed to initialize audio system: {:?}", e));
                        state.is_initializing = false;
                    }
                }
            }

            AudioStoreAction::AudioSystemInitialized(status) => {
                state.system_status = Some(status);
                state.is_initializing = false;
                state.error_message = None;
            }

            AudioStoreAction::AudioSystemError(error) => {
                state.error_message = Some(error);
                state.is_initializing = false;
            }

            AudioStoreAction::PlayPause => {
                if state.playback_state.is_playing {
                    state.playback_state.is_playing = false;
                    if let Some(ref mut coord) = *coordinator {
                        let _ = coord.pause();
                    }
                } else {
                    state.playback_state.is_playing = true;
                    if let Some(ref mut coord) = *coordinator {
                        let _ = coord.play();
                    }
                }
            }

            AudioStoreAction::Play => {
                state.playback_state.is_playing = true;
                if let Some(ref mut coord) = *coordinator {
                    let _ = coord.play();
                }
            }

            AudioStoreAction::Pause => {
                state.playback_state.is_playing = false;
                if let Some(ref mut coord) = *coordinator {
                    let _ = coord.pause();
                }
            }

            AudioStoreAction::Stop => {
                state.playback_state.is_playing = false;
                state.playback_state.current_time = 0.0;
                if let Some(ref mut coord) = *coordinator {
                    let _ = coord.stop();
                    let _ = coord.seek(0.0);
                }
            }

            AudioStoreAction::Seek(time) => {
                state.playback_state.current_time = time;
                if let Some(ref mut coord) = *coordinator {
                    let _ = coord.seek(time);
                }
            }

            AudioStoreAction::SetVolume(volume) => {
                state.master_volume = volume.clamp(0.0, 2.0);
                if let Some(ref mut coord) = *coordinator {
                    let _ = coord.set_master_volume(state.master_volume);
                }
            }

            AudioStoreAction::SetMuted(muted) => {
                state.is_muted = muted;
                if let Some(ref mut coord) = *coordinator {
                    let _ = coord.set_muted(muted);
                }
            }

            AudioStoreAction::LoadTimeline(timeline) => {
                state.current_timeline = Some(timeline.clone());
                // Extract audio elements from timeline
                let audio_elements: Vec<Uuid> = timeline.elements
                    .iter()
                    .filter(|element| matches!(element.element_type, crate::types::timeline::ElementType::Audio))
                    .map(|element| element.id)
                    .collect();
                state.loaded_audio_elements = audio_elements;

                // Load audio elements into coordinator
                if let Some(ref mut coord) = *coordinator {
                    let audio_timeline_elements: Vec<TimelineElement> = timeline.elements
                        .iter()
                        .filter(|element| matches!(element.element_type, crate::types::timeline::ElementType::Audio))
                        .cloned()
                        .collect();
                    let _ = coord.add_timeline_elements(&audio_timeline_elements);
                }
            }

            AudioStoreAction::LoadAudioElement(element_id) => {
                if !state.loaded_audio_elements.contains(&element_id) {
                    state.loaded_audio_elements.push(element_id);
                    // Note: Individual element loading would require the element data
                    // This would typically be implemented with a separate method
                }
            }

            AudioStoreAction::UnloadAudioElement(element_id) => {
                state.loaded_audio_elements.retain(|&id| id != element_id);
                // Note: Individual element unloading would require coordinator support
                // This would typically be implemented with a separate method
            }

            AudioStoreAction::UpdatePlaybackState(playback_state) => {
                state.playback_state = playback_state;
            }

            AudioStoreAction::UpdateSystemStatus(status) => {
                state.system_status = Some(status);
            }

            // Audio Effects Actions
            AudioStoreAction::SetElementVolume(element_id, volume) => {
                if let Some(effects) = state.audio_effects.get_mut(&element_id) {
                    effects.volume_control.volume = volume;
                    // Apply to coordinator if available
                    if let Some(ref mut coord) = *coordinator {
                        let _ = coord.set_element_volume(&element_id.to_string(), volume);
                    }
                }
            }

            AudioStoreAction::SetElementMuted(element_id, muted) => {
                if let Some(effects) = state.audio_effects.get_mut(&element_id) {
                    effects.volume_control.mute = muted;
                    // Apply to coordinator if available
                    if let Some(ref mut coord) = *coordinator {
                        let _ = coord.set_element_muted(&element_id.to_string(), muted);
                    }
                }
            }

            AudioStoreAction::SetElementPan(element_id, pan) => {
                if let Some(effects) = state.audio_effects.get_mut(&element_id) {
                    effects.volume_control.pan = pan;
                    // Apply to coordinator if available
                    if let Some(ref mut coord) = *coordinator {
                        let _ = coord.set_element_pan(&element_id.to_string(), pan);
                    }
                }
            }

            AudioStoreAction::ApplyElementFilter(element_id, filter_settings) => {
                if let Some(effects) = state.audio_effects.get_mut(&element_id) {
                    effects.filter_settings = filter_settings.clone();
                    // Apply to coordinator if available
                    if let Some(ref mut coord) = *coordinator {
                        let _ = coord.apply_element_filter(&element_id.to_string(), filter_settings);
                    }
                }
            }

            AudioStoreAction::UpdateElementEffects(element_id, element_effects) => {
                state.audio_effects.insert(element_id, element_effects.clone());
                // Apply to coordinator if available
                if let Some(ref mut coord) = *coordinator {
                    let _ = coord.update_element_effects(&element_id.to_string(), element_effects);
                }
            }

            // Sync Communication Actions
            AudioStoreAction::EnableRealtimePositionUpdates => {
                state.realtime_position_updates_enabled = true;
                if let Some(ref mut coord) = *coordinator {
                    let _ = coord.enable_realtime_position_updates();
                }
            }

            AudioStoreAction::DisableRealtimePositionUpdates => {
                state.realtime_position_updates_enabled = false;
                if let Some(ref mut coord) = *coordinator {
                    let _ = coord.disable_realtime_position_updates();
                }
            }

            AudioStoreAction::UpdateSyncStats(sync_stats) => {
                state.sync_stats = Some(sync_stats);
            }

            AudioStoreAction::UpdateRealtimeStats(realtime_stats) => {
                state.realtime_stats = Some(realtime_stats);
            }

            AudioStoreAction::UpdateTimelinePosition(position) => {
                state.playback_state.current_time = position;
                // Update coordinator with new position
                if let Some(ref mut coord) = *coordinator {
                    let _ = coord.update_timeline_position(position);
                }
            }

            AudioStoreAction::RequestSyncQualityUpdate => {
                if let Some(ref mut coord) = *coordinator {
                    if let Ok(quality_score) = coord.get_sync_quality_score() {
                        state.sync_quality_score = quality_score;
                    }
                }
            }

            AudioStoreAction::SeekToPosition(position, is_playing) => {
                state.playback_state.current_time = position;
                if let Some(ref mut coord) = *coordinator {
                    let _ = coord.seek_to_position(position, is_playing);
                }
            }
        }

        // Notify all listeners
        let current_state = state.clone();
        drop(state); // Release the borrow before calling listeners
        
        for listener in self.listeners.borrow().iter() {
            listener.emit(current_state.clone());
        }
    }
}

// Global audio store instance
thread_local! {
    pub static AUDIO_STORE: AudioStore = AudioStore::new();
}

pub fn use_audio_store() -> (AudioStoreState, Callback<AudioStoreAction>) {
    let state = AUDIO_STORE.with(|store| store.get_state());

    let dispatch = Callback::from(move |action: AudioStoreAction| {
        AUDIO_STORE.with(|store| {
            store.dispatch(action);
        });
    });

    (state, dispatch)
}

// Helper functions for common audio operations
pub fn initialize_audio_system() {
    AUDIO_STORE.with(|store| {
        store.dispatch(AudioStoreAction::InitializeAudioSystem);
    });
}

pub fn toggle_playback() {
    AUDIO_STORE.with(|store| {
        store.dispatch(AudioStoreAction::PlayPause);
    });
}

pub fn seek_to_time(time: f64) {
    AUDIO_STORE.with(|store| {
        store.dispatch(AudioStoreAction::Seek(time));
    });
}

pub fn set_master_volume(volume: f64) {
    AUDIO_STORE.with(|store| {
        store.dispatch(AudioStoreAction::SetVolume(volume));
    });
}

pub fn load_timeline_audio(timeline: Timeline) {
    AUDIO_STORE.with(|store| {
        store.dispatch(AudioStoreAction::LoadTimeline(timeline));
    });
}
