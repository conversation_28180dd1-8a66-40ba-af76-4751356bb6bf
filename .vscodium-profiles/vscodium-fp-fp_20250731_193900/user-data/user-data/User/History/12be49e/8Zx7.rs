use wasm_bindgen::prelude::*;
use web_sys::{HtmlCanvasElement, CanvasRenderingContext2d};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use uuid::Uuid;

use crate::utils::text_system::{TextElement, TextStyle, TextPosition, TextAnimation, TextAnimationType};
use crate::utils::font_management::{FontManager, FontFamilyManager};
use crate::types::timeline::{TimelineElement, ElementType};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextRenderingOptions {
    pub antialiasing: bool,
    pub subpixel_rendering: bool,
    pub text_baseline: String,
    pub text_align: String,
    pub max_width: Option<f64>,
    pub line_height: f64,
    pub letter_spacing: f64,
    pub word_spacing: f64,
}

impl Default for TextRenderingOptions {
    fn default() -> Self {
        Self {
            antialiasing: true,
            subpixel_rendering: true,
            text_baseline: "top".to_string(),
            text_align: "left".to_string(),
            max_width: None,
            line_height: 1.2,
            letter_spacing: 0.0,
            word_spacing: 0.0,
        }
    }
}

#[derive(Debug, Clone)]
pub struct RenderedText {
    pub element_id: Uuid,
    pub canvas: HtmlCanvasElement,
    pub width: f64,
    pub height: f64,
    pub baseline_offset: f64,
}

pub struct TextRenderer {
    font_manager: FontManager,
    font_family_manager: FontFamilyManager,
    rendered_cache: HashMap<Uuid, RenderedText>,
    rendering_options: TextRenderingOptions,
    canvas_pool: Vec<HtmlCanvasElement>,
}

impl TextRenderer {
    pub fn new() -> Self {
        Self {
            font_manager: FontManager::new(),
            font_family_manager: FontFamilyManager::new(),
            rendered_cache: HashMap::new(),
            rendering_options: TextRenderingOptions::default(),
            canvas_pool: Vec::new(),
        }
    }

    pub async fn initialize(&mut self) -> Result<(), JsValue> {
        self.font_manager.initialize()?;
        self.font_family_manager.load_popular_fonts().await?;
        Ok(())
    }

    pub async fn render_text_element(&mut self, element: &TextElement, current_time: f64) -> Result<RenderedText, JsValue> {
        // Check cache first
        if let Some(cached) = self.rendered_cache.get(&Uuid::parse_str(&element.id).unwrap()) {
            return Ok(cached.clone());
        }

        // Get or create canvas
        let canvas = self.get_canvas_from_pool();
        let context = canvas
            .get_context("2d")?
            .unwrap()
            .dyn_into::<CanvasRenderingContext2d>()?;

        // Apply font and style
        self.apply_text_style(&context, &element.style).await?;

        // Calculate text dimensions
        let (width, height, lines) = self.calculate_text_dimensions(&context, &element.text, &element.style)?;

        // Set canvas size
        canvas.set_width(width as u32);
        canvas.set_height(height as u32);

        // Clear canvas
        context.clear_rect(0.0, 0.0, width, height);

        // Apply style again after canvas resize
        self.apply_text_style(&context, &element.style).await?;

        // Apply animation if active
        if let Some(ref animation) = element.animation {
            self.apply_text_animation(&context, animation, current_time, element.start_time, element.duration)?;
        }

        // Render text lines
        self.render_text_lines(&context, &lines, &element.style, width, height)?;

        let rendered = RenderedText {
            element_id: Uuid::parse_str(&element.id).unwrap(),
            canvas,
            width,
            height,
            baseline_offset: self.calculate_baseline_offset(&element.style),
        };

        // Cache the result
        self.rendered_cache.insert(Uuid::parse_str(&element.id).unwrap(), rendered.clone());

        Ok(rendered)
    }

    async fn apply_text_style(&self, context: &CanvasRenderingContext2d, style: &TextStyle) -> Result<(), JsValue> {
        // Build font string
        let font_weight_str = match style.font_weight {
            crate::utils::text_system::FontWeight::Thin => "100",
            crate::utils::text_system::FontWeight::ExtraLight => "200",
            crate::utils::text_system::FontWeight::Light => "300",
            crate::utils::text_system::FontWeight::Normal => "normal",
            crate::utils::text_system::FontWeight::Medium => "500",
            crate::utils::text_system::FontWeight::SemiBold => "600",
            crate::utils::text_system::FontWeight::Bold => "bold",
            crate::utils::text_system::FontWeight::ExtraBold => "800",
            crate::utils::text_system::FontWeight::Black => "900",
            crate::utils::text_system::FontWeight::Custom(weight) => {
                // We need to handle this differently since we can't return a reference to a temporary
                context.set_font(&format!("{} {}px {}", weight, style.font_size, style.font_family));
                return Ok(());
            },
            crate::utils::text_system::FontWeight::Bolder => "bolder",
            crate::utils::text_system::FontWeight::Lighter => "lighter",
            crate::utils::text_system::FontWeight::W100 => "100",
            crate::utils::text_system::FontWeight::W200 => "200",
            crate::utils::text_system::FontWeight::W300 => "300",
            crate::utils::text_system::FontWeight::W400 => "400",
            crate::utils::text_system::FontWeight::W500 => "500",
            crate::utils::text_system::FontWeight::W600 => "600",
            crate::utils::text_system::FontWeight::W700 => "700",
            crate::utils::text_system::FontWeight::W800 => "800",
            crate::utils::text_system::FontWeight::W900 => "900",
        };
        let font_string = format!(
            "{} {}px {}",
            font_weight_str,
            style.font_size,
            style.font_family
        );
        context.set_font(&font_string);

        // Set text properties
        context.set_text_baseline(&self.rendering_options.text_baseline);
        context.set_text_align(&self.rendering_options.text_align);

        // Set fill style
        context.set_fill_style(&style.color.clone().into());

        Ok(())
    }

    fn calculate_text_dimensions(&self, context: &CanvasRenderingContext2d, text: &str, style: &TextStyle) -> Result<(f64, f64, Vec<String>), JsValue> {
        let lines: Vec<String> = if let Some(max_width) = self.rendering_options.max_width {
            self.wrap_text(context, text, max_width)?
        } else {
            text.lines().map(|s| s.to_string()).collect()
        };

        let line_height = style.font_size as f64 * self.rendering_options.line_height;
        let height = lines.len() as f64 * line_height;

        let mut max_width = 0.0_f64;
        for line in &lines {
            let metrics = context.measure_text(line)?;
            max_width = max_width.max(metrics.width());
        }

        Ok((max_width, height, lines))
    }

    fn wrap_text(&self, context: &CanvasRenderingContext2d, text: &str, max_width: f64) -> Result<Vec<String>, JsValue> {
        let words: Vec<&str> = text.split_whitespace().collect();
        let mut lines = Vec::new();
        let mut current_line = String::new();

        for word in words {
            let test_line = if current_line.is_empty() {
                word.to_string()
            } else {
                format!("{} {}", current_line, word)
            };

            let metrics = context.measure_text(&test_line)?;
            if metrics.width() <= max_width {
                current_line = test_line;
            } else {
                if !current_line.is_empty() {
                    lines.push(current_line);
                }
                current_line = word.to_string();
            }
        }

        if !current_line.is_empty() {
            lines.push(current_line);
        }

        Ok(lines)
    }

    fn render_text_lines(&self, context: &CanvasRenderingContext2d, lines: &[String], style: &TextStyle, canvas_width: f64, canvas_height: f64) -> Result<(), JsValue> {
        let line_height = style.font_size as f64 * self.rendering_options.line_height;
        
        for (i, line) in lines.iter().enumerate() {
            let y = i as f64 * line_height + style.font_size as f64;
            
            // Calculate x position based on alignment
            let x = match self.rendering_options.text_align.as_str() {
                "center" => canvas_width / 2.0,
                "right" => canvas_width,
                _ => 0.0, // left
            };

            // Render fill
            context.fill_text(line, x, y)?;
        }

        Ok(())
    }

    fn apply_text_animation(&self, context: &CanvasRenderingContext2d, animation: &TextAnimation, current_time: f64, start_time: f64, duration: f64) -> Result<(), JsValue> {
        let animation_time = current_time - start_time;
        let progress = (animation_time / animation.duration).clamp(0.0, 1.0);

        match &animation.animation_type {
            TextAnimationType::FadeIn => {
                let alpha = progress;
                context.set_global_alpha(alpha);
            },
            TextAnimationType::FadeOut => {
                let alpha = 1.0 - progress;
                context.set_global_alpha(alpha);
            },
            TextAnimationType::Scale { from, to } => {
                let scale = from + (to - from) * progress as f32;
                context.scale(scale as f64, scale as f64)?;
            },
            TextAnimationType::Rotate { from, to } => {
                let rotation = from + (to - from) * progress as f32;
                context.rotate(rotation as f64 * std::f64::consts::PI / 180.0)?;
            },
            _ => {
                // Other animation types would be implemented here
            }
        }

        Ok(())
    }

    fn calculate_baseline_offset(&self, style: &TextStyle) -> f64 {
        // Calculate baseline offset based on font metrics
        // This is a simplified calculation
        style.font_size as f64 * 0.8
    }

    fn get_canvas_from_pool(&mut self) -> HtmlCanvasElement {
        if let Some(canvas) = self.canvas_pool.pop() {
            canvas
        } else {
            web_sys::window()
                .unwrap()
                .document()
                .unwrap()
                .create_element("canvas")
                .unwrap()
                .dyn_into::<HtmlCanvasElement>()
                .unwrap()
        }
    }

    pub fn return_canvas_to_pool(&mut self, canvas: HtmlCanvasElement) {
        // Clear the canvas before returning to pool
        if let Ok(Some(context)) = canvas.get_context("2d") {
            if let Ok(context) = context.dyn_into::<CanvasRenderingContext2d>() {
                context.clear_rect(0.0, 0.0, canvas.width() as f64, canvas.height() as f64);
            }
        }
        self.canvas_pool.push(canvas);
    }

    pub fn clear_cache(&mut self) {
        // Collect canvases to return to pool
        let canvases: Vec<_> = self.rendered_cache.drain().map(|(_, rendered)| rendered.canvas).collect();

        // Return canvases to pool
        for canvas in canvases {
            self.return_canvas_to_pool(canvas);
        }
    }

    pub fn remove_from_cache(&mut self, element_id: &Uuid) {
        if let Some(rendered) = self.rendered_cache.remove(element_id) {
            self.return_canvas_to_pool(rendered.canvas);
        }
    }

    pub fn get_rendered_text(&self, element_id: &Uuid) -> Option<&RenderedText> {
        self.rendered_cache.get(element_id)
    }

    pub fn set_rendering_options(&mut self, options: TextRenderingOptions) {
        self.rendering_options = options;
        // Clear cache when rendering options change
        self.clear_cache();
    }

    pub fn get_rendering_options(&self) -> &TextRenderingOptions {
        &self.rendering_options
    }

    pub async fn preload_fonts(&mut self, font_families: &[String]) -> Result<(), JsValue> {
        for family in font_families {
            self.font_family_manager.load_font_family(family).await?;
        }
        Ok(())
    }

    pub async fn render_timeline_text_elements(&mut self, elements: &[TimelineElement], current_time: f64) -> Result<Vec<RenderedText>, JsValue> {
        let mut rendered_texts = Vec::new();

        for element in elements {
            if element.element_type == ElementType::Text {
                if let Some(text_element) = self.timeline_element_to_text_element(element) {
                    // Check if element should be visible at current time
                    if current_time >= element.start_time && current_time < element.start_time + element.duration {
                        match self.render_text_element(&text_element, current_time).await {
                            Ok(rendered) => rendered_texts.push(rendered),
                            Err(e) => {
                                web_sys::console::error_1(&format!("Failed to render text element {}: {:?}", element.id, e).into());
                            }
                        }
                    }
                }
            }
        }

        Ok(rendered_texts)
    }

    fn timeline_element_to_text_element(&self, timeline_element: &TimelineElement) -> Option<TextElement> {
        if timeline_element.element_type != ElementType::Text {
            return None;
        }

        let text_data = timeline_element.properties.text_data.as_ref()?;

        Some(TextElement {
            id: timeline_element.id.to_string(),
            text: text_data.text.clone(),
            position: text_data.position.clone(),
            style: text_data.style.clone(),
            animation: text_data.animation.clone(),
            start_time: timeline_element.start_time,
            duration: timeline_element.duration,
            layer: timeline_element.layer as u32,
            visible: !timeline_element.locked,
            locked: timeline_element.locked,
        })
    }
}

impl Default for TextRenderer {
    fn default() -> Self {
        Self::new()
    }
}
