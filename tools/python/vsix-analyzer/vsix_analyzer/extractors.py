"""
VSIX extraction and parsing functionality.
"""

import os
import json
import zipfile
import xml.etree.ElementTree as ET
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
import logging

from .utils import safe_json_loads, safe_decode, format_bytes


class VSIXExtractor:
    """Handles VSIX file extraction and parsing."""
    
    def __init__(self, vsix_path: str, extract_dir: Optional[str] = None):
        """Initialize VSIX extractor."""
        self.vsix_path = Path(vsix_path)
        self.extract_dir = extract_dir
        self.logger = logging.getLogger('vsix_analyzer.extractor')
        self._extracted_path: Optional[Path] = None
        self._manifest_data: Optional[Dict[str, Any]] = None
        self._package_json: Optional[Dict[str, Any]] = None
        
    def validate_vsix(self) -> bool:
        """Validate that the file is a valid VSIX package."""
        try:
            with zipfile.ZipFile(self.vsix_path, 'r') as zip_file:
                # Check for required files
                required_files = ['extension.vsixmanifest', '[Content_Types].xml']
                file_list = zip_file.namelist()
                
                for required_file in required_files:
                    if required_file not in file_list:
                        self.logger.error(f"Missing required file: {required_file}")
                        return False
                
                # Try to read manifest
                try:
                    manifest_content = zip_file.read('extension.vsixmanifest')
                    ET.fromstring(manifest_content)
                except ET.ParseError as e:
                    self.logger.error(f"Invalid manifest XML: {e}")
                    return False
                
                return True
                
        except zipfile.BadZipFile:
            self.logger.error("File is not a valid ZIP archive")
            return False
        except Exception as e:
            self.logger.error(f"Validation error: {e}")
            return False
    
    def extract(self) -> Path:
        """Extract VSIX contents to directory."""
        if self._extracted_path and self._extracted_path.exists():
            return self._extracted_path
        
        if self.extract_dir:
            extract_path = Path(self.extract_dir)
            extract_path.mkdir(parents=True, exist_ok=True)
        else:
            from .utils import create_temp_dir
            extract_path = Path(create_temp_dir())
        
        try:
            with zipfile.ZipFile(self.vsix_path, 'r') as zip_file:
                zip_file.extractall(extract_path)
                self.logger.info(f"Extracted VSIX to: {extract_path}")
                
            self._extracted_path = extract_path
            return extract_path
            
        except Exception as e:
            self.logger.error(f"Extraction failed: {e}")
            raise
    
    def get_file_info(self) -> Dict[str, Any]:
        """Get basic file information."""
        stat = self.vsix_path.stat()
        
        return {
            'path': str(self.vsix_path),
            'size': stat.st_size,
            'size_formatted': format_bytes(stat.st_size),
            'modified': stat.st_mtime,
            'is_valid': self.validate_vsix()
        }
    
    def parse_manifest(self) -> Dict[str, Any]:
        """Parse extension.vsixmanifest file."""
        if self._manifest_data:
            return self._manifest_data
        
        try:
            with zipfile.ZipFile(self.vsix_path, 'r') as zip_file:
                manifest_content = zip_file.read('extension.vsixmanifest')
                root = ET.fromstring(manifest_content)
                
                # Extract key information from manifest
                manifest_data = {
                    'identity': {},
                    'metadata': {},
                    'installation': {},
                    'dependencies': [],
                    'assets': []
                }
                
                # Find namespace
                ns = {'vs': 'http://schemas.microsoft.com/developer/vsx-schema/2011'}
                
                # Extract identity
                identity = root.find('.//vs:Identity', ns)
                if identity is not None:
                    manifest_data['identity'] = {
                        'id': identity.get('Id', ''),
                        'version': identity.get('Version', ''),
                        'language': identity.get('Language', ''),
                        'publisher': identity.get('Publisher', '')
                    }
                
                # Extract metadata
                metadata = root.find('.//vs:Metadata', ns)
                if metadata is not None:
                    display_name = metadata.find('.//vs:DisplayName', ns)
                    description = metadata.find('.//vs:Description', ns)
                    
                    manifest_data['metadata'] = {
                        'display_name': display_name.text if display_name is not None else '',
                        'description': description.text if description is not None else ''
                    }
                
                # Extract installation targets
                installation = root.find('.//vs:Installation', ns)
                if installation is not None:
                    targets = installation.findall('.//vs:InstallationTarget', ns)
                    manifest_data['installation'] = {
                        'targets': [target.get('Id', '') for target in targets]
                    }
                
                self._manifest_data = manifest_data
                return manifest_data
                
        except Exception as e:
            self.logger.error(f"Failed to parse manifest: {e}")
            return {}
    
    def parse_package_json(self) -> Dict[str, Any]:
        """Parse extension/package.json file."""
        if self._package_json:
            return self._package_json
        
        try:
            with zipfile.ZipFile(self.vsix_path, 'r') as zip_file:
                package_content = zip_file.read('extension/package.json')
                package_text = safe_decode(package_content)
                package_data = safe_json_loads(package_text)
                
                if package_data:
                    self._package_json = package_data
                    return package_data
                else:
                    self.logger.error("Failed to parse package.json")
                    return {}
                    
        except KeyError:
            self.logger.warning("package.json not found in extension/")
            return {}
        except Exception as e:
            self.logger.error(f"Failed to read package.json: {e}")
            return {}
    
    def get_extension_files(self) -> List[Dict[str, Any]]:
        """Get list of all files in the extension."""
        files = []
        
        try:
            with zipfile.ZipFile(self.vsix_path, 'r') as zip_file:
                for file_info in zip_file.filelist:
                    files.append({
                        'filename': file_info.filename,
                        'file_size': file_info.file_size,
                        'compress_size': file_info.compress_size,
                        'date_time': file_info.date_time,
                        'is_dir': file_info.is_dir()
                    })
                    
        except Exception as e:
            self.logger.error(f"Failed to list files: {e}")
        
        return files
    
    def read_file_content(self, file_path: str) -> Optional[str]:
        """Read content of a specific file from the VSIX."""
        try:
            with zipfile.ZipFile(self.vsix_path, 'r') as zip_file:
                content = zip_file.read(file_path)
                return safe_decode(content)
        except KeyError:
            self.logger.debug(f"File not found: {file_path}")
            return None
        except Exception as e:
            self.logger.error(f"Failed to read file {file_path}: {e}")
            return None
    
    def get_main_extension_file(self) -> Optional[str]:
        """Get the main extension JavaScript file content."""
        # Try common locations for the main extension file
        possible_paths = [
            'extension/out/extension.js',
            'extension/extension.js',
            'extension/dist/extension.js',
            'extension/lib/extension.js'
        ]
        
        for path in possible_paths:
            content = self.read_file_content(path)
            if content:
                self.logger.info(f"Found main extension file: {path}")
                return content
        
        # If not found, look for any .js file in extension directory
        try:
            with zipfile.ZipFile(self.vsix_path, 'r') as zip_file:
                for file_info in zip_file.filelist:
                    if (file_info.filename.startswith('extension/') and 
                        file_info.filename.endswith('.js') and
                        not file_info.is_dir()):
                        content = self.read_file_content(file_info.filename)
                        if content and len(content) > 1000:  # Assume main file is substantial
                            self.logger.info(f"Found extension file: {file_info.filename}")
                            return content
        except Exception as e:
            self.logger.error(f"Error searching for extension files: {e}")
        
        self.logger.warning("No main extension file found")
        return None
    
    def cleanup(self) -> None:
        """Clean up extracted files if using temporary directory."""
        if self._extracted_path and not self.extract_dir:
            from .utils import cleanup_temp_dir
            cleanup_temp_dir(str(self._extracted_path))
            self._extracted_path = None
