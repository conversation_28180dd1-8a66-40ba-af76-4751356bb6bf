#!/usr/bin/env python3
"""
Command-line interface for VSIX Analyzer.
"""

import argparse
import sys
import os
from pathlib import Path
from typing import List, Optional
import logging

from .analyzer import VSIXAnalyzer
from .reporters import ReportGenerator
from .utils import setup_logging, validate_file_path, load_config_file, merge_configs


def create_parser() -> argparse.ArgumentParser:
    """Create command-line argument parser."""
    parser = argparse.ArgumentParser(
        prog='vsix-analyzer',
        description='Analyze VS Code extension VSIX files for trial/subscription mechanisms',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic analysis
  vsix-analyzer extension.vsix
  
  # Specific analysis modules with markdown output
  vsix-analyzer extension.vsix --fingerprinting --auth --format markdown --output report.md
  
  # Batch analysis of directory
  vsix-analyzer --batch ./extensions/ --format json --output results.json
  
  # Custom pattern search
  vsix-analyzer extension.vsix --patterns "trial.*expired" --verbose
        """
    )
    
    # Required arguments
    parser.add_argument(
        'vsix_file',
        nargs='?',
        help='Path to the VSIX file to analyze'
    )
    
    # Analysis options
    analysis_group = parser.add_argument_group('Analysis Options')
    analysis_group.add_argument(
        '--all',
        action='store_true',
        default=True,
        help='Run all analysis modules (default)'
    )
    analysis_group.add_argument(
        '--fingerprinting',
        action='store_true',
        help='Analyze system fingerprinting mechanisms'
    )
    analysis_group.add_argument(
        '--auth',
        action='store_true',
        help='Analyze authentication and OAuth flows'
    )
    analysis_group.add_argument(
        '--storage',
        action='store_true',
        help='Analyze data storage and persistence'
    )
    analysis_group.add_argument(
        '--network',
        action='store_true',
        help='Analyze network calls and API endpoints'
    )
    analysis_group.add_argument(
        '--trial-detection',
        action='store_true',
        help='Analyze trial and subscription logic'
    )
    analysis_group.add_argument(
        '--patterns',
        metavar='PATTERN',
        help='Search for custom regex patterns'
    )
    
    # Output options
    output_group = parser.add_argument_group('Output Options')
    output_group.add_argument(
        '--format',
        choices=['console', 'markdown', 'json', 'html'],
        default='console',
        help='Output format (default: console)'
    )
    output_group.add_argument(
        '--output',
        metavar='FILE',
        help='Save results to file instead of stdout'
    )
    output_group.add_argument(
        '--verbose',
        action='store_true',
        help='Enable detailed logging and debug output'
    )
    output_group.add_argument(
        '--quiet',
        action='store_true',
        help='Suppress non-essential output'
    )
    
    # Advanced options
    advanced_group = parser.add_argument_group('Advanced Options')
    advanced_group.add_argument(
        '--extract-dir',
        metavar='DIR',
        help='Directory to extract VSIX contents (temp by default)'
    )
    advanced_group.add_argument(
        '--keep-extracted',
        action='store_true',
        help="Don't delete extracted files after analysis"
    )
    advanced_group.add_argument(
        '--config',
        metavar='FILE',
        help='Load configuration from file'
    )
    advanced_group.add_argument(
        '--batch',
        metavar='DIR',
        help='Analyze all VSIX files in directory'
    )
    
    return parser


def get_analysis_modules(args: argparse.Namespace) -> List[str]:
    """Determine which analysis modules to run based on arguments."""
    modules = []
    
    # Check if specific modules are requested
    if args.fingerprinting:
        modules.append('fingerprinting')
    if args.auth:
        modules.append('auth')
    if args.storage:
        modules.append('storage')
    if args.network:
        modules.append('network')
    if args.trial_detection:
        modules.append('trial-detection')
    if args.patterns:
        modules.append('patterns')
    
    # If no specific modules requested, run all
    if not modules:
        modules = ['all']
    
    return modules


def analyze_single_file(vsix_path: str, args: argparse.Namespace) -> dict:
    """Analyze a single VSIX file."""
    logger = logging.getLogger('vsix_analyzer.cli')
    
    try:
        # Validate file
        validate_file_path(vsix_path)
        
        # Create analyzer
        analyzer = VSIXAnalyzer(vsix_path, args.extract_dir)
        
        # Get analysis modules
        modules = get_analysis_modules(args)
        
        # Run analysis
        logger.info(f"Analyzing {vsix_path}")
        results = analyzer.analyze(modules)
        
        # Handle custom pattern search
        if args.patterns:
            logger.info(f"Searching for custom pattern: {args.patterns}")
            custom_matches = analyzer.search_custom_pattern(args.patterns)
            results['custom_pattern_matches'] = {
                'pattern': args.patterns,
                'matches': custom_matches,
                'count': len(custom_matches)
            }
        
        # Cleanup if not keeping extracted files
        if not args.keep_extracted:
            analyzer.cleanup()
        
        return results
        
    except Exception as e:
        logger.error(f"Analysis failed for {vsix_path}: {e}")
        return {'error': str(e), 'file': vsix_path}


def analyze_batch(directory: str, args: argparse.Namespace) -> dict:
    """Analyze all VSIX files in a directory."""
    logger = logging.getLogger('vsix_analyzer.cli')
    
    batch_results = {
        'batch_analysis': True,
        'directory': directory,
        'results': [],
        'summary': {
            'total_files': 0,
            'successful': 0,
            'failed': 0,
            'errors': []
        }
    }
    
    try:
        directory_path = Path(directory)
        if not directory_path.exists():
            raise FileNotFoundError(f"Directory not found: {directory}")
        
        # Find all VSIX files
        vsix_files = list(directory_path.glob('*.vsix'))
        if not vsix_files:
            logger.warning(f"No VSIX files found in {directory}")
            return batch_results
        
        batch_results['summary']['total_files'] = len(vsix_files)
        logger.info(f"Found {len(vsix_files)} VSIX files to analyze")
        
        # Analyze each file
        for vsix_file in vsix_files:
            logger.info(f"Processing {vsix_file.name}")
            
            try:
                result = analyze_single_file(str(vsix_file), args)
                if 'error' not in result:
                    batch_results['summary']['successful'] += 1
                else:
                    batch_results['summary']['failed'] += 1
                    batch_results['summary']['errors'].append({
                        'file': str(vsix_file),
                        'error': result['error']
                    })
                
                batch_results['results'].append(result)
                
            except Exception as e:
                logger.error(f"Failed to analyze {vsix_file}: {e}")
                batch_results['summary']['failed'] += 1
                batch_results['summary']['errors'].append({
                    'file': str(vsix_file),
                    'error': str(e)
                })
        
        logger.info(f"Batch analysis complete: {batch_results['summary']['successful']} successful, "
                   f"{batch_results['summary']['failed']} failed")
        
    except Exception as e:
        logger.error(f"Batch analysis failed: {e}")
        batch_results['error'] = str(e)
    
    return batch_results


def load_configuration(config_path: Optional[str]) -> dict:
    """Load configuration from file if provided."""
    default_config = {
        'analysis': {
            'max_matches_per_pattern': 50,
            'context_chars': 50
        },
        'output': {
            'truncate_length': 100
        }
    }
    
    if not config_path:
        return default_config
    
    try:
        user_config = load_config_file(config_path)
        return merge_configs(default_config, user_config)
    except Exception as e:
        logging.getLogger('vsix_analyzer.cli').warning(f"Failed to load config: {e}")
        return default_config


def main() -> int:
    """Main CLI entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging(args.verbose, args.quiet)
    
    try:
        # Load configuration
        config = load_configuration(args.config)
        
        # Validate arguments
        if not args.batch and not args.vsix_file:
            parser.error("Either VSIX_FILE or --batch DIR must be specified")
        
        # Run analysis
        if args.batch:
            results = analyze_batch(args.batch, args)
        else:
            results = analyze_single_file(args.vsix_file, args)
        
        # Generate report
        reporter = ReportGenerator()
        report = reporter.generate_report(results, args.format)
        
        # Output report
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"Report saved to {args.output}")
        else:
            print(report)
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Analysis interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())
