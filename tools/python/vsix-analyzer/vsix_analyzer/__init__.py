"""
VSIX Analyzer - A comprehensive tool for analyzing VS Code extension VSIX files.

This package provides functionality to extract, analyze, and report on VSIX files,
with a focus on detecting trial/subscription mechanisms and multi-trial prevention systems.
"""

__version__ = "1.0.0"
__author__ = "VSIX Security Research Team"
__email__ = "<EMAIL>"

from .analyzer import VSIXAnalyzer
from .extractors import VSIXExtractor
from .patterns import PatternMatcher
from .reporters import ReportGenerator

__all__ = [
    "VSIXAnalyzer",
    "VSIXExtractor", 
    "PatternMatcher",
    "ReportGenerator"
]
