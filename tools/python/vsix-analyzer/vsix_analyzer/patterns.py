"""
Pattern matching for detecting trial/subscription mechanisms and security features.
"""

import re
from typing import Dict, List, Any, Optional, Tuple
import logging


class PatternMatcher:
    """Handles pattern matching for various security and trial detection mechanisms."""
    
    def __init__(self):
        """Initialize pattern matcher with predefined patterns."""
        self.logger = logging.getLogger('vsix_analyzer.patterns')
        self._init_patterns()
    
    def _init_patterns(self) -> None:
        """Initialize all pattern definitions."""
        
        # System fingerprinting patterns
        self.fingerprinting_patterns = {
            'machine_id': [
                r'machineId[A-Za-z]*',
                r'env\.machineId[^;]*',
                r'vscode\.env\.machineId'
            ],
            'hardware_info': [
                r'os\.type\(\)[^;]*',
                r'os\.totalmem\(\)[^;]*',
                r'os\.hostname\(\)[^;]*',
                r'os\.arch\(\)[^;]*',
                r'os\.cpus\(\)[^;]*',
                r'os\.release\(\)[^;]*',
                r'os\.version\(\)[^;]*'
            ],
            'system_identifiers': [
                r'device[A-Za-z]*[Ii]d',
                r'hardware[A-Za-z]*[Ii]d',
                r'system[A-Za-z]*[Ii]d',
                r'unique[A-Za-z]*[Ii]d',
                r'installation[A-Za-z]*[Ii]d',
                r'client[A-Za-z]*[Ii]d'
            ],
            'fingerprint_creation': [
                r'fingerprint[A-Za-z]*',
                r'toVector\(\)[^}]*',
                r'canonicalize[A-Za-z]*',
                r'calculateChecksum[A-Za-z]*',
                r'hash[A-Za-z]*[Ff]unction'
            ]
        }
        
        # Authentication and OAuth patterns
        self.auth_patterns = {
            'oauth_flow': [
                r'oauth[A-Za-z]*',
                r'authorize[A-Za-z]*',
                r'authenticate[A-Za-z]*',
                r'signIn[A-Za-z]*',
                r'login[A-Za-z]*'
            ],
            'tokens': [
                r'accessToken[A-Za-z]*',
                r'refreshToken[A-Za-z]*',
                r'bearerToken[A-Za-z]*',
                r'authToken[A-Za-z]*',
                r'jwt[A-Za-z]*'
            ],
            'auth_headers': [
                r'Authorization[^;]*',
                r'Bearer[^;]*',
                r'X-[A-Za-z-]*[Aa]uth[A-Za-z-]*'
            ],
            'session_management': [
                r'session[A-Za-z]*[Ss]tate',
                r'auth[A-Za-z]*[Ss]tate',
                r'login[A-Za-z]*[Ss]tate'
            ]
        }
        
        # Storage mechanism patterns
        self.storage_patterns = {
            'vscode_storage': [
                r'globalState\.[a-zA-Z]+',
                r'workspaceState\.[a-zA-Z]+',
                r'secrets\.[a-zA-Z]+',
                r'memento\.[a-zA-Z]+',
                r'context\.[a-zA-Z]*[Ss]torage'
            ],
            'web_storage': [
                r'localStorage[A-Za-z]*',
                r'sessionStorage[A-Za-z]*',
                r'indexedDB[A-Za-z]*'
            ],
            'storage_keys': [
                r'["\'][a-zA-Z0-9_.-]*(?:auth|trial|subscription|license|user)[a-zA-Z0-9_.-]*["\']'
            ]
        }
        
        # Network and API patterns
        self.network_patterns = {
            'api_endpoints': [
                r'https?://[a-zA-Z0-9.-]+[a-zA-Z0-9/._-]*',
                r'api[A-Za-z]*[Uu]rl[^;]*',
                r'endpoint[^;]*',
                r'baseUrl[^;]*'
            ],
            'http_methods': [
                r'fetch\([^)]*\)',
                r'axios\.[a-zA-Z]+',
                r'request\([^)]*\)',
                r'POST[^"\'\\s]*',
                r'GET[^"\'\\s]*',
                r'PUT[^"\'\\s]*'
            ],
            'subscription_apis': [
                r'subscription[A-Za-z]*[Uu]rl',
                r'license[A-Za-z]*[Uu]rl',
                r'billing[A-Za-z]*[Uu]rl',
                r'payment[A-Za-z]*[Uu]rl'
            ]
        }
        
        # Trial and subscription patterns
        self.trial_patterns = {
            'trial_logic': [
                r'trial[A-Za-z]*[Ee]xpir',
                r'trial[A-Za-z]*[Rr]emaining',
                r'trial[A-Za-z]*[Dd]ays?',
                r'trial[A-Za-z]*[Pp]eriod',
                r'free[A-Za-z]*[Tt]rial'
            ],
            'subscription_status': [
                r'subscription[A-Za-z]*[Ss]tatus',
                r'subscription[A-Za-z]*[Ss]tate',
                r'subscription[A-Za-z]*[Aa]ctive',
                r'subscription[A-Za-z]*[Vv]alid'
            ],
            'license_validation': [
                r'license[A-Za-z]*[Vv]alid',
                r'license[A-Za-z]*[Cc]heck',
                r'license[A-Za-z]*[Vv]erif',
                r'has[A-Za-z]*[Ll]icense'
            ],
            'premium_features': [
                r'premium[A-Za-z]*',
                r'pro[A-Za-z]*[Ff]eature',
                r'paid[A-Za-z]*[Ff]eature',
                r'enterprise[A-Za-z]*'
            ]
        }
        
        # Telemetry and tracking patterns
        self.telemetry_patterns = {
            'analytics': [
                r'telemetry[A-Za-z]*',
                r'analytics[A-Za-z]*',
                r'tracking[A-Za-z]*',
                r'metrics[A-Za-z]*'
            ],
            'event_logging': [
                r'event[A-Za-z]*[Ll]og',
                r'log[A-Za-z]*[Ee]vent',
                r'track[A-Za-z]*[Ee]vent'
            ],
            'feature_reporting': [
                r'FeatureVector[A-Za-z]*',
                r'feature[A-Za-z]*[Rr]eport',
                r'usage[A-Za-z]*[Rr]eport'
            ]
        }
        
        # Suspicious/exploit patterns
        self.suspicious_patterns = {
            'exploit_keywords': [
                r'exploit[A-Za-z]*',
                r'bypass[A-Za-z]*',
                r'crack[A-Za-z]*',
                r'hack[A-Za-z]*',
                r'pirate[A-Za-z]*'
            ],
            'obfuscation': [
                r'eval\([^)]*\)',
                r'Function\([^)]*\)',
                r'atob\([^)]*\)',
                r'btoa\([^)]*\)'
            ]
        }
    
    def search_patterns(self, content: str, pattern_category: str, 
                       max_matches: int = 50) -> Dict[str, List[str]]:
        """Search for patterns in content by category."""
        if not hasattr(self, f'{pattern_category}_patterns'):
            raise ValueError(f"Unknown pattern category: {pattern_category}")
        
        patterns = getattr(self, f'{pattern_category}_patterns')
        results = {}
        
        for subcategory, pattern_list in patterns.items():
            matches = []
            for pattern in pattern_list:
                try:
                    found = re.findall(pattern, content, re.IGNORECASE)
                    matches.extend(found)
                except re.error as e:
                    self.logger.warning(f"Invalid regex pattern '{pattern}': {e}")
            
            # Remove duplicates and limit results
            unique_matches = list(set(matches))[:max_matches]
            if unique_matches:
                results[subcategory] = unique_matches
        
        return results
    
    def search_custom_pattern(self, content: str, pattern: str, 
                            case_sensitive: bool = False) -> List[str]:
        """Search for a custom regex pattern in content."""
        try:
            flags = 0 if case_sensitive else re.IGNORECASE
            matches = re.findall(pattern, content, flags)
            return list(set(matches))  # Remove duplicates
        except re.error as e:
            self.logger.error(f"Invalid regex pattern '{pattern}': {e}")
            return []
    
    def find_pattern_contexts(self, content: str, pattern: str, 
                            context_chars: int = 50) -> List[Dict[str, Any]]:
        """Find pattern matches with surrounding context."""
        contexts = []
        
        try:
            for match in re.finditer(pattern, content, re.IGNORECASE):
                start = max(0, match.start() - context_chars)
                end = min(len(content), match.end() + context_chars)
                
                contexts.append({
                    'match': match.group(),
                    'start_pos': match.start(),
                    'end_pos': match.end(),
                    'context': content[start:end],
                    'line_number': content[:match.start()].count('\n') + 1
                })
                
        except re.error as e:
            self.logger.error(f"Invalid regex pattern '{pattern}': {e}")
        
        return contexts
    
    def analyze_all_patterns(self, content: str) -> Dict[str, Any]:
        """Run all pattern analyses on content."""
        results = {
            'fingerprinting': self.search_patterns(content, 'fingerprinting'),
            'authentication': self.search_patterns(content, 'auth'),
            'storage': self.search_patterns(content, 'storage'),
            'network': self.search_patterns(content, 'network'),
            'trial_detection': self.search_patterns(content, 'trial'),
            'telemetry': self.search_patterns(content, 'telemetry'),
            'suspicious': self.search_patterns(content, 'suspicious')
        }
        
        # Calculate summary statistics
        total_matches = sum(
            len(matches) for category in results.values() 
            for matches in category.values()
        )
        
        results['summary'] = {
            'total_patterns_found': total_matches,
            'categories_with_matches': len([
                cat for cat in results.values() 
                if isinstance(cat, dict) and cat
            ]),
            'high_risk_indicators': self._assess_risk_level(results)
        }
        
        return results
    
    def _assess_risk_level(self, results: Dict[str, Any]) -> List[str]:
        """Assess risk level based on pattern matches."""
        indicators = []
        
        # Check for comprehensive fingerprinting
        fingerprinting = results.get('fingerprinting', {})
        if (len(fingerprinting.get('machine_id', [])) > 0 and
            len(fingerprinting.get('hardware_info', [])) > 2):
            indicators.append("Comprehensive system fingerprinting detected")
        
        # Check for trial prevention mechanisms
        trial = results.get('trial_detection', {})
        if (len(trial.get('trial_logic', [])) > 0 and
            len(trial.get('subscription_status', [])) > 0):
            indicators.append("Multi-trial prevention mechanisms detected")
        
        # Check for suspicious patterns
        suspicious = results.get('suspicious', {})
        if len(suspicious.get('exploit_keywords', [])) > 0:
            indicators.append("Potentially suspicious keywords found")
        
        # Check for extensive telemetry
        telemetry = results.get('telemetry', {})
        if len(telemetry.get('analytics', [])) > 3:
            indicators.append("Extensive telemetry and tracking detected")
        
        return indicators

    def get_pattern_statistics(self, results: Dict[str, Any]) -> Dict[str, int]:
        """Get statistics about pattern matches."""
        stats = {}

        for category, subcategories in results.items():
            if isinstance(subcategories, dict):
                for subcategory, matches in subcategories.items():
                    if isinstance(matches, list):
                        stats[f"{category}_{subcategory}"] = len(matches)

        return stats
