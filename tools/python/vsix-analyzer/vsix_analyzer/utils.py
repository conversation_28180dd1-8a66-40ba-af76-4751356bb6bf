"""
Utility functions for VSIX analysis.
"""

import os
import json
import logging
import tempfile
import shutil
from typing import Dict, Any, Optional, List
from pathlib import Path


def setup_logging(verbose: bool = False, quiet: bool = False) -> logging.Logger:
    """Set up logging configuration."""
    logger = logging.getLogger('vsix_analyzer')
    
    if quiet:
        level = logging.ERROR
    elif verbose:
        level = logging.DEBUG
    else:
        level = logging.INFO
    
    logger.setLevel(level)
    
    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create console handler
    handler = logging.StreamHandler()
    handler.setLevel(level)
    
    # Create formatter
    if verbose:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    else:
        formatter = logging.Formatter('%(levelname)s: %(message)s')
    
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    return logger


def create_temp_dir(prefix: str = "vsix_analyzer_") -> str:
    """Create a temporary directory for VSIX extraction."""
    return tempfile.mkdtemp(prefix=prefix)


def cleanup_temp_dir(temp_dir: str) -> None:
    """Clean up temporary directory."""
    try:
        shutil.rmtree(temp_dir)
    except Exception as e:
        logging.getLogger('vsix_analyzer').warning(f"Failed to cleanup temp dir: {e}")


def validate_file_path(file_path: str) -> Path:
    """Validate that a file path exists and is readable."""
    path = Path(file_path)
    
    if not path.exists():
        raise FileNotFoundError(f"File not found: {file_path}")
    
    if not path.is_file():
        raise ValueError(f"Path is not a file: {file_path}")
    
    if not os.access(path, os.R_OK):
        raise PermissionError(f"File is not readable: {file_path}")
    
    return path


def safe_json_loads(content: str) -> Optional[Dict[str, Any]]:
    """Safely parse JSON content, returning None on error."""
    try:
        return json.loads(content)
    except (json.JSONDecodeError, TypeError):
        return None


def safe_decode(content: bytes, encodings: List[str] = None) -> str:
    """Safely decode bytes to string, trying multiple encodings."""
    if encodings is None:
        encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
    
    for encoding in encodings:
        try:
            return content.decode(encoding)
        except UnicodeDecodeError:
            continue
    
    # Fallback: decode with errors='ignore'
    return content.decode('utf-8', errors='ignore')


def format_bytes(size: int) -> str:
    """Format byte size in human-readable format."""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size < 1024.0:
            return f"{size:.1f} {unit}"
        size /= 1024.0
    return f"{size:.1f} TB"


def truncate_string(text: str, max_length: int = 100) -> str:
    """Truncate string to maximum length with ellipsis."""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."


def extract_domain_from_url(url: str) -> Optional[str]:
    """Extract domain from URL."""
    try:
        from urllib.parse import urlparse
        parsed = urlparse(url)
        return parsed.netloc
    except Exception:
        return None


def is_suspicious_pattern(pattern: str, content: str) -> bool:
    """Check if a pattern appears suspicious in content."""
    import re
    
    suspicious_keywords = [
        'crack', 'bypass', 'exploit', 'hack', 'pirate',
        'keygen', 'serial', 'patch', 'nulled'
    ]
    
    try:
        matches = re.findall(pattern, content, re.IGNORECASE)
        for match in matches:
            if any(keyword in match.lower() for keyword in suspicious_keywords):
                return True
    except re.error:
        pass
    
    return False


def get_file_hash(file_path: str, algorithm: str = 'sha256') -> str:
    """Calculate file hash."""
    import hashlib
    
    hash_obj = hashlib.new(algorithm)
    
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_obj.update(chunk)
    
    return hash_obj.hexdigest()


class ProgressIndicator:
    """Simple progress indicator for long-running operations."""
    
    def __init__(self, total: int, description: str = "Processing"):
        self.total = total
        self.current = 0
        self.description = description
        self.logger = logging.getLogger('vsix_analyzer')
    
    def update(self, increment: int = 1) -> None:
        """Update progress."""
        self.current += increment
        if self.total > 0:
            percentage = (self.current / self.total) * 100
            self.logger.info(f"{self.description}: {percentage:.1f}% ({self.current}/{self.total})")
    
    def finish(self) -> None:
        """Mark progress as complete."""
        self.logger.info(f"{self.description}: Complete")


def load_config_file(config_path: str) -> Dict[str, Any]:
    """Load configuration from JSON file."""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        raise ValueError(f"Failed to load config file {config_path}: {e}")


def merge_configs(base_config: Dict[str, Any], user_config: Dict[str, Any]) -> Dict[str, Any]:
    """Merge user configuration with base configuration."""
    merged = base_config.copy()
    
    for key, value in user_config.items():
        if isinstance(value, dict) and key in merged and isinstance(merged[key], dict):
            merged[key] = merge_configs(merged[key], value)
        else:
            merged[key] = value
    
    return merged
