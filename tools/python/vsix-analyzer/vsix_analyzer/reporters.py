"""
Output formatting and reporting functionality.
"""

import json
import html
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging


class ReportGenerator:
    """Generates reports in various formats."""
    
    def __init__(self):
        """Initialize report generator."""
        self.logger = logging.getLogger('vsix_analyzer.reporter')
    
    def generate_report(self, results: Dict[str, Any], format_type: str = 'console') -> str:
        """Generate report in specified format."""
        if format_type == 'console':
            return self._generate_console_report(results)
        elif format_type == 'markdown':
            return self._generate_markdown_report(results)
        elif format_type == 'json':
            return self._generate_json_report(results)
        elif format_type == 'html':
            return self._generate_html_report(results)
        else:
            raise ValueError(f"Unsupported format: {format_type}")
    
    def _generate_console_report(self, results: Dict[str, Any]) -> str:
        """Generate console-formatted report with colors."""
        lines = []
        
        # Header
        lines.append("=" * 80)
        lines.append("VSIX ANALYZER REPORT")
        lines.append("=" * 80)
        lines.append("")
        
        # File information
        file_info = results.get('file_info', {})
        lines.append("📁 FILE INFORMATION")
        lines.append("-" * 40)
        lines.append(f"Path: {file_info.get('path', 'N/A')}")
        lines.append(f"Size: {file_info.get('size_formatted', 'N/A')}")
        lines.append(f"Valid VSIX: {'✅ Yes' if file_info.get('is_valid') else '❌ No'}")
        lines.append(f"SHA256: {file_info.get('hash_sha256', 'N/A')[:16]}...")
        lines.append("")
        
        # Extension information
        ext_info = results.get('extension_info', {})
        lines.append("🔌 EXTENSION INFORMATION")
        lines.append("-" * 40)
        lines.append(f"Name: {ext_info.get('display_name', ext_info.get('name', 'N/A'))}")
        lines.append(f"Publisher: {ext_info.get('publisher', 'N/A')}")
        lines.append(f"Version: {ext_info.get('version', 'N/A')}")
        lines.append(f"Categories: {', '.join(ext_info.get('categories', []))}")
        lines.append(f"Description: {self._truncate(ext_info.get('description', 'N/A'), 100)}")
        lines.append("")
        
        # Analysis results
        analysis = results.get('analysis', {})
        if analysis and 'error' not in analysis:
            lines.append("🔍 ANALYSIS RESULTS")
            lines.append("-" * 40)
            
            # Summary statistics
            summary = analysis.get('summary', {})
            if summary:
                lines.append(f"Total patterns found: {summary.get('total_patterns_found', 0)}")
                lines.append(f"Categories with matches: {summary.get('categories_with_matches', 0)}")
                lines.append("")
            
            # Pattern categories
            for category, patterns in analysis.items():
                if category == 'summary' or not isinstance(patterns, dict):
                    continue
                
                category_name = category.replace('_', ' ').title()
                total_matches = sum(len(matches) for matches in patterns.values())
                
                if total_matches > 0:
                    lines.append(f"📊 {category_name}: {total_matches} matches")
                    for subcategory, matches in patterns.items():
                        if matches:
                            lines.append(f"  • {subcategory}: {len(matches)} items")
                            # Show first few matches
                            for match in matches[:3]:
                                lines.append(f"    - {self._truncate(match, 60)}")
                            if len(matches) > 3:
                                lines.append(f"    ... and {len(matches) - 3} more")
                    lines.append("")
        
        # Security assessment
        security = results.get('security_assessment', {})
        if security:
            lines.append("🛡️ SECURITY ASSESSMENT")
            lines.append("-" * 40)
            
            risk_level = security.get('risk_level', 'unknown')
            risk_emoji = {'low': '🟢', 'medium': '🟡', 'high': '🔴'}.get(risk_level, '⚪')
            lines.append(f"Risk Level: {risk_emoji} {risk_level.upper()}")
            lines.append("")
            
            # Scores
            lines.append("Security Scores (0-10):")
            lines.append(f"  Fingerprinting: {security.get('fingerprinting_score', 0)}/10")
            lines.append(f"  Trial Prevention: {security.get('trial_prevention_score', 0)}/10")
            lines.append(f"  Network Activity: {security.get('network_activity_score', 0)}/10")
            lines.append("")
            
            # Concerns
            concerns = security.get('concerns', [])
            if concerns:
                lines.append("⚠️ Security Concerns:")
                for concern in concerns:
                    lines.append(f"  • {concern}")
                lines.append("")
            
            # Recommendations
            recommendations = security.get('recommendations', [])
            if recommendations:
                lines.append("💡 Recommendations:")
                for rec in recommendations:
                    lines.append(f"  • {rec}")
                lines.append("")
        
        # Footer
        lines.append("=" * 80)
        lines.append(f"Report generated at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("=" * 80)
        
        return "\n".join(lines)
    
    def _generate_markdown_report(self, results: Dict[str, Any]) -> str:
        """Generate markdown-formatted report."""
        lines = []
        
        # Header
        lines.append("# VSIX Analysis Report")
        lines.append("")
        lines.append(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")
        
        # File information
        file_info = results.get('file_info', {})
        lines.append("## File Information")
        lines.append("")
        lines.append("| Property | Value |")
        lines.append("|----------|-------|")
        lines.append(f"| Path | `{file_info.get('path', 'N/A')}` |")
        lines.append(f"| Size | {file_info.get('size_formatted', 'N/A')} |")
        lines.append(f"| Valid VSIX | {'✅ Yes' if file_info.get('is_valid') else '❌ No'} |")
        lines.append(f"| SHA256 | `{file_info.get('hash_sha256', 'N/A')}` |")
        lines.append("")
        
        # Extension information
        ext_info = results.get('extension_info', {})
        lines.append("## Extension Information")
        lines.append("")
        lines.append("| Property | Value |")
        lines.append("|----------|-------|")
        lines.append(f"| Name | {ext_info.get('display_name', ext_info.get('name', 'N/A'))} |")
        lines.append(f"| Publisher | {ext_info.get('publisher', 'N/A')} |")
        lines.append(f"| Version | {ext_info.get('version', 'N/A')} |")
        lines.append(f"| Categories | {', '.join(ext_info.get('categories', []))} |")
        lines.append(f"| License | {ext_info.get('license', 'N/A')} |")
        lines.append("")
        
        if ext_info.get('description'):
            lines.append("### Description")
            lines.append("")
            lines.append(ext_info['description'])
            lines.append("")
        
        # Analysis results
        analysis = results.get('analysis', {})
        if analysis and 'error' not in analysis:
            lines.append("## Analysis Results")
            lines.append("")
            
            # Summary
            summary = analysis.get('summary', {})
            if summary:
                lines.append("### Summary")
                lines.append("")
                lines.append(f"- **Total patterns found:** {summary.get('total_patterns_found', 0)}")
                lines.append(f"- **Categories with matches:** {summary.get('categories_with_matches', 0)}")
                
                high_risk = summary.get('high_risk_indicators', [])
                if high_risk:
                    lines.append("- **High-risk indicators:**")
                    for indicator in high_risk:
                        lines.append(f"  - {indicator}")
                lines.append("")
            
            # Detailed results
            for category, patterns in analysis.items():
                if category == 'summary' or not isinstance(patterns, dict):
                    continue
                
                category_name = category.replace('_', ' ').title()
                total_matches = sum(len(matches) for matches in patterns.values())
                
                if total_matches > 0:
                    lines.append(f"### {category_name}")
                    lines.append("")
                    
                    for subcategory, matches in patterns.items():
                        if matches:
                            lines.append(f"#### {subcategory.replace('_', ' ').title()}")
                            lines.append("")
                            lines.append("```")
                            for match in matches[:10]:  # Limit to first 10
                                lines.append(match)
                            if len(matches) > 10:
                                lines.append(f"... and {len(matches) - 10} more")
                            lines.append("```")
                            lines.append("")
        
        # Security assessment
        security = results.get('security_assessment', {})
        if security:
            lines.append("## Security Assessment")
            lines.append("")
            
            risk_level = security.get('risk_level', 'unknown')
            risk_emoji = {'low': '🟢', 'medium': '🟡', 'high': '🔴'}.get(risk_level, '⚪')
            lines.append(f"**Risk Level:** {risk_emoji} {risk_level.upper()}")
            lines.append("")
            
            # Scores
            lines.append("### Security Scores")
            lines.append("")
            lines.append("| Metric | Score |")
            lines.append("|--------|-------|")
            lines.append(f"| Fingerprinting | {security.get('fingerprinting_score', 0)}/10 |")
            lines.append(f"| Trial Prevention | {security.get('trial_prevention_score', 0)}/10 |")
            lines.append(f"| Network Activity | {security.get('network_activity_score', 0)}/10 |")
            lines.append("")
            
            # Concerns
            concerns = security.get('concerns', [])
            if concerns:
                lines.append("### Security Concerns")
                lines.append("")
                for concern in concerns:
                    lines.append(f"- ⚠️ {concern}")
                lines.append("")
            
            # Recommendations
            recommendations = security.get('recommendations', [])
            if recommendations:
                lines.append("### Recommendations")
                lines.append("")
                for rec in recommendations:
                    lines.append(f"- 💡 {rec}")
                lines.append("")
        
        return "\n".join(lines)
    
    def _generate_json_report(self, results: Dict[str, Any]) -> str:
        """Generate JSON-formatted report."""
        # Add metadata
        report_data = results.copy()
        report_data['report_metadata'] = {
            'generated_at': datetime.now().isoformat(),
            'format': 'json',
            'version': '1.0.0'
        }
        
        return json.dumps(report_data, indent=2, ensure_ascii=False)
    
    def _generate_html_report(self, results: Dict[str, Any]) -> str:
        """Generate HTML-formatted report."""
        # Convert markdown to HTML (simplified)
        markdown_content = self._generate_markdown_report(results)
        
        # Basic markdown to HTML conversion
        html_content = markdown_content
        html_content = html_content.replace('# ', '<h1>').replace('\n', '</h1>\n', 1)
        html_content = html_content.replace('## ', '<h2>').replace('\n', '</h2>\n')
        html_content = html_content.replace('### ', '<h3>').replace('\n', '</h3>\n')
        html_content = html_content.replace('#### ', '<h4>').replace('\n', '</h4>\n')
        
        # Convert tables
        lines = html_content.split('\n')
        in_table = False
        processed_lines = []
        
        for line in lines:
            if '|' in line and not line.startswith('```'):
                if not in_table:
                    processed_lines.append('<table class="table">')
                    in_table = True
                
                if line.startswith('|---'):
                    continue  # Skip separator line
                
                cells = [cell.strip() for cell in line.split('|')[1:-1]]
                if cells:
                    processed_lines.append('<tr>')
                    for cell in cells:
                        processed_lines.append(f'<td>{html.escape(cell)}</td>')
                    processed_lines.append('</tr>')
            else:
                if in_table:
                    processed_lines.append('</table>')
                    in_table = False
                processed_lines.append(line)
        
        if in_table:
            processed_lines.append('</table>')
        
        html_content = '\n'.join(processed_lines)
        
        # Wrap in HTML document
        html_template = f"""
<!DOCTYPE html>
<html>
<head>
    <title>VSIX Analysis Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        h1, h2, h3, h4 {{ color: #333; }}
        table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
        td, th {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        code {{ background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }}
        pre {{ background-color: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }}
        .risk-low {{ color: green; }}
        .risk-medium {{ color: orange; }}
        .risk-high {{ color: red; }}
    </style>
</head>
<body>
{html_content}
</body>
</html>
        """
        
        return html_template.strip()
    
    def _truncate(self, text: str, max_length: int) -> str:
        """Truncate text to maximum length."""
        if len(text) <= max_length:
            return text
        return text[:max_length-3] + "..."
