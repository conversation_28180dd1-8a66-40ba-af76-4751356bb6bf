"""
Core VSIX analysis engine.
"""

import os
from typing import Dict, Any, Optional, List
import logging
from pathlib import Path

from .extractors import VSIXExtractor
from .patterns import PatternMatcher
from .utils import get_file_hash, ProgressIndicator


class VSIXAnalyzer:
    """Main analysis engine for VSIX files."""
    
    def __init__(self, vsix_path: str, extract_dir: Optional[str] = None):
        """Initialize VSIX analyzer."""
        self.vsix_path = Path(vsix_path)
        self.extract_dir = extract_dir
        self.logger = logging.getLogger('vsix_analyzer.analyzer')
        
        # Initialize components
        self.extractor = VSIXExtractor(str(self.vsix_path), extract_dir)
        self.pattern_matcher = PatternMatcher()
        
        # Analysis results
        self.results: Dict[str, Any] = {}
        self._analyzed = False
    
    def analyze(self, modules: Optional[List[str]] = None) -> Dict[str, Any]:
        """Run comprehensive analysis on the VSIX file."""
        if modules is None:
            modules = ['all']
        
        self.logger.info(f"Starting analysis of {self.vsix_path}")
        
        # Initialize results structure
        self.results = {
            'file_info': {},
            'manifest': {},
            'package_json': {},
            'extension_files': [],
            'analysis': {},
            'security_assessment': {},
            'metadata': {
                'analyzer_version': '1.0.0',
                'analysis_modules': modules
            }
        }
        
        try:
            # Basic file validation and info
            self._analyze_file_info()
            
            # Extract and parse VSIX structure
            self._analyze_structure()
            
            # Run pattern analysis on extension code
            if 'all' in modules or any(mod in modules for mod in 
                ['fingerprinting', 'auth', 'storage', 'network', 'trial-detection', 'patterns']):
                self._analyze_patterns(modules)
            
            # Security assessment
            self._assess_security()
            
            self._analyzed = True
            self.logger.info("Analysis completed successfully")
            
        except Exception as e:
            self.logger.error(f"Analysis failed: {e}")
            self.results['error'] = str(e)
            raise
        
        return self.results
    
    def _analyze_file_info(self) -> None:
        """Analyze basic file information."""
        self.logger.debug("Analyzing file information")
        
        file_info = self.extractor.get_file_info()
        file_info['hash_sha256'] = get_file_hash(str(self.vsix_path))
        
        self.results['file_info'] = file_info
    
    def _analyze_structure(self) -> None:
        """Analyze VSIX structure and metadata."""
        self.logger.debug("Analyzing VSIX structure")
        
        # Parse manifest
        manifest = self.extractor.parse_manifest()
        self.results['manifest'] = manifest
        
        # Parse package.json
        package_json = self.extractor.parse_package_json()
        self.results['package_json'] = package_json
        
        # Get file list
        extension_files = self.extractor.get_extension_files()
        self.results['extension_files'] = extension_files
        
        # Extract basic extension info
        self.results['extension_info'] = self._extract_extension_info(manifest, package_json)
    
    def _extract_extension_info(self, manifest: Dict[str, Any], 
                               package_json: Dict[str, Any]) -> Dict[str, Any]:
        """Extract key extension information."""
        info = {}
        
        # From manifest
        if manifest.get('identity'):
            identity = manifest['identity']
            info.update({
                'id': identity.get('id', ''),
                'version': identity.get('version', ''),
                'publisher': identity.get('publisher', ''),
                'language': identity.get('language', '')
            })
        
        if manifest.get('metadata'):
            metadata = manifest['metadata']
            info.update({
                'display_name': metadata.get('display_name', ''),
                'description': metadata.get('description', '')
            })
        
        # From package.json
        if package_json:
            info.update({
                'name': package_json.get('name', ''),
                'main': package_json.get('main', ''),
                'engines': package_json.get('engines', {}),
                'categories': package_json.get('categories', []),
                'keywords': package_json.get('keywords', []),
                'repository': package_json.get('repository', {}),
                'license': package_json.get('license', ''),
                'homepage': package_json.get('homepage', '')
            })
            
            # Extract activation events
            activation_events = package_json.get('activationEvents', [])
            info['activation_events'] = activation_events
            
            # Extract contributes section
            contributes = package_json.get('contributes', {})
            info['contributes'] = {
                'commands': len(contributes.get('commands', [])),
                'keybindings': len(contributes.get('keybindings', [])),
                'configuration': len(contributes.get('configuration', [])),
                'languages': len(contributes.get('languages', [])),
                'grammars': len(contributes.get('grammars', [])),
                'themes': len(contributes.get('themes', []))
            }
        
        return info
    
    def _analyze_patterns(self, modules: List[str]) -> None:
        """Analyze code patterns in the extension."""
        self.logger.debug("Analyzing code patterns")
        
        # Get main extension file content
        extension_content = self.extractor.get_main_extension_file()
        if not extension_content:
            self.logger.warning("No main extension file found for pattern analysis")
            self.results['analysis'] = {'error': 'No extension code found'}
            return
        
        self.logger.info(f"Analyzing {len(extension_content)} characters of extension code")
        
        analysis_results = {}
        
        # Run specific analysis modules
        if 'all' in modules:
            analysis_results = self.pattern_matcher.analyze_all_patterns(extension_content)
        else:
            # Run specific modules
            if 'fingerprinting' in modules:
                analysis_results['fingerprinting'] = self.pattern_matcher.search_patterns(
                    extension_content, 'fingerprinting'
                )
            
            if 'auth' in modules:
                analysis_results['authentication'] = self.pattern_matcher.search_patterns(
                    extension_content, 'auth'
                )
            
            if 'storage' in modules:
                analysis_results['storage'] = self.pattern_matcher.search_patterns(
                    extension_content, 'storage'
                )
            
            if 'network' in modules:
                analysis_results['network'] = self.pattern_matcher.search_patterns(
                    extension_content, 'network'
                )
            
            if 'trial-detection' in modules:
                analysis_results['trial_detection'] = self.pattern_matcher.search_patterns(
                    extension_content, 'trial'
                )
        
        self.results['analysis'] = analysis_results
    
    def _assess_security(self) -> None:
        """Assess security implications of the extension."""
        self.logger.debug("Assessing security implications")
        
        assessment = {
            'risk_level': 'low',
            'concerns': [],
            'recommendations': [],
            'trial_prevention_score': 0,
            'fingerprinting_score': 0,
            'network_activity_score': 0
        }
        
        analysis = self.results.get('analysis', {})
        
        # Assess fingerprinting capabilities
        fingerprinting = analysis.get('fingerprinting', {})
        fingerprinting_indicators = sum(len(matches) for matches in fingerprinting.values())
        assessment['fingerprinting_score'] = min(fingerprinting_indicators, 10)
        
        if fingerprinting_indicators > 5:
            assessment['concerns'].append("Extensive system fingerprinting detected")
            assessment['risk_level'] = 'medium'
        
        # Assess trial prevention mechanisms
        trial = analysis.get('trial_detection', {})
        trial_indicators = sum(len(matches) for matches in trial.values())
        assessment['trial_prevention_score'] = min(trial_indicators, 10)
        
        if trial_indicators > 3:
            assessment['concerns'].append("Multi-trial prevention mechanisms present")
        
        # Assess network activity
        network = analysis.get('network', {})
        network_indicators = sum(len(matches) for matches in network.values())
        assessment['network_activity_score'] = min(network_indicators, 10)
        
        if network_indicators > 5:
            assessment['concerns'].append("Extensive network communication detected")
        
        # Check for suspicious patterns
        suspicious = analysis.get('suspicious', {})
        if suspicious:
            suspicious_count = sum(len(matches) for matches in suspicious.values())
            if suspicious_count > 0:
                assessment['concerns'].append("Potentially suspicious code patterns found")
                assessment['risk_level'] = 'high'
        
        # Generate recommendations
        if assessment['fingerprinting_score'] > 3:
            assessment['recommendations'].append(
                "Review system fingerprinting for privacy compliance"
            )
        
        if assessment['trial_prevention_score'] > 2:
            assessment['recommendations'].append(
                "Verify trial prevention mechanisms are legitimate"
            )
        
        if assessment['network_activity_score'] > 3:
            assessment['recommendations'].append(
                "Audit network communications for security"
            )
        
        self.results['security_assessment'] = assessment
    
    def search_custom_pattern(self, pattern: str, case_sensitive: bool = False) -> List[str]:
        """Search for custom pattern in extension code."""
        extension_content = self.extractor.get_main_extension_file()
        if not extension_content:
            return []
        
        return self.pattern_matcher.search_custom_pattern(
            extension_content, pattern, case_sensitive
        )
    
    def get_pattern_contexts(self, pattern: str, context_chars: int = 50) -> List[Dict[str, Any]]:
        """Get pattern matches with context."""
        extension_content = self.extractor.get_main_extension_file()
        if not extension_content:
            return []
        
        return self.pattern_matcher.find_pattern_contexts(
            extension_content, pattern, context_chars
        )
    
    def cleanup(self) -> None:
        """Clean up temporary files."""
        self.extractor.cleanup()
