#!/usr/bin/env python3
"""
Simple demo script for VSIX Analyzer.
"""

import sys
import os
from pathlib import Path

# Add the package to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from vsix_analyzer.analyzer import VSIXAnalyzer
from vsix_analyzer.reporters import ReportGenerator
from vsix_analyzer.utils import setup_logging


def main():
    """Run a simple demo of the VSIX analyzer."""
    print("🔍 VSIX Analyzer Demo")
    print("=" * 50)
    
    # Find the VSIX file
    parent_dir = Path(__file__).parent.parent
    vsix_file = parent_dir / "Microsoft.VisualStudio.Services.VSIX"
    
    if not vsix_file.exists():
        print(f"❌ VSIX file not found: {vsix_file}")
        print("Please ensure Microsoft.VisualStudio.Services.VSIX is in the parent directory")
        return 1
    
    print(f"✅ Found VSIX file: {vsix_file}")
    
    # Setup logging
    logger = setup_logging(verbose=False)
    
    try:
        # Create analyzer
        print("\n📁 Creating analyzer...")
        analyzer = VSIXAnalyzer(str(vsix_file))
        
        # Run analysis
        print("🔍 Running analysis...")
        results = analyzer.analyze(['fingerprinting', 'trial-detection'])
        
        # Generate report
        print("📝 Generating report...")
        reporter = ReportGenerator()
        report = reporter.generate_report(results, 'console')
        
        print("\n" + "="*60)
        print("ANALYSIS REPORT")
        print("="*60)
        print(report)
        
        # Cleanup
        analyzer.cleanup()
        
        print("\n✅ Demo completed successfully!")
        return 0
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
