{"analysis": {"max_matches_per_pattern": 50, "context_chars": 50, "enable_deep_scan": true, "skip_large_files": false, "max_file_size_mb": 100}, "output": {"truncate_length": 100, "show_line_numbers": true, "highlight_suspicious": true, "include_metadata": true}, "patterns": {"custom_fingerprinting": ["device[A-Za-z]*[Ii]d", "hardware[A-Za-z]*[Ii]nfo", "system[A-Za-z]*[Ff]ingerprint"], "custom_trial": ["trial[A-Za-z]*[Ee]xpir", "subscription[A-Za-z]*[Vv]alid", "license[A-Za-z]*[Cc]heck"]}, "security": {"risk_thresholds": {"fingerprinting_high": 7, "trial_prevention_high": 5, "network_activity_high": 8}, "suspicious_keywords": ["crack", "bypass", "exploit", "hack", "pirate"]}, "reporting": {"include_code_snippets": true, "max_snippet_length": 200, "show_file_paths": true, "group_by_category": true}}