"""
Setup script for VSIX Analyzer.
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "A comprehensive tool for analyzing VS Code extension VSIX files."

# Read requirements
def read_requirements():
    req_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    requirements = []
    if os.path.exists(req_path):
        with open(req_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    requirements.append(line)
    return requirements

setup(
    name='vsix-analyzer',
    version='1.0.0',
    description='A comprehensive tool for analyzing VS Code extension VSIX files',
    long_description=read_readme(),
    long_description_content_type='text/markdown',
    author='VSIX Security Research Team',
    author_email='<EMAIL>',
    url='https://github.com/example/vsix-analyzer',
    packages=find_packages(),
    classifiers=[
        'Development Status :: 4 - Beta',
        'Intended Audience :: Developers',
        'Intended Audience :: Information Technology',
        'License :: OSI Approved :: MIT License',
        'Operating System :: OS Independent',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.7',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Programming Language :: Python :: 3.11',
        'Topic :: Security',
        'Topic :: Software Development :: Testing',
        'Topic :: System :: Systems Administration',
        'Topic :: Utilities',
    ],
    keywords='vscode extension vsix security analysis trial subscription',
    python_requires='>=3.7',
    install_requires=read_requirements(),
    extras_require={
        'dev': [
            'pytest>=6.0.0',
            'pytest-cov>=2.0.0',
            'black>=21.0.0',
            'flake8>=3.8.0',
            'mypy>=0.800',
        ],
        'enhanced': [
            'colorama>=0.4.4',
            'rich>=10.0.0',
            'click>=8.0.0',
            'jinja2>=3.0.0',
        ]
    },
    entry_points={
        'console_scripts': [
            'vsix-analyzer=vsix_analyzer.cli:main',
        ],
    },
    include_package_data=True,
    zip_safe=False,
    project_urls={
        'Bug Reports': 'https://github.com/example/vsix-analyzer/issues',
        'Source': 'https://github.com/example/vsix-analyzer',
        'Documentation': 'https://github.com/example/vsix-analyzer/wiki',
    },
)
