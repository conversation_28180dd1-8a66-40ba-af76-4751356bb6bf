#!/usr/bin/env python3
"""
Test script for VSIX Analyzer.
"""

import sys
import os
from pathlib import Path

# Add the package to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from vsix_analyzer.cli import main


def test_basic_analysis():
    """Test basic analysis functionality."""
    print("Testing VSIX Analyzer...")
    print("=" * 50)
    
    # Find the VSIX file in parent directory
    parent_dir = Path(__file__).parent.parent
    vsix_file = parent_dir / "Microsoft.VisualStudio.Services.VSIX"
    
    if not vsix_file.exists():
        print(f"❌ VSIX file not found: {vsix_file}")
        print("Please ensure Microsoft.VisualStudio.Services.VSIX is in the parent directory")
        return False
    
    print(f"✅ Found VSIX file: {vsix_file}")
    
    # Test different analysis modes
    test_cases = [
        {
            'name': 'Basic Console Analysis',
            'args': [str(vsix_file)]
        },
        {
            'name': 'Fingerprinting Analysis',
            'args': [str(vsix_file), '--fingerprinting', '--verbose']
        },
        {
            'name': 'Trial Detection Analysis',
            'args': [str(vsix_file), '--trial-detection', '--format', 'markdown']
        },
        {
            'name': 'Custom Pattern Search',
            'args': [str(vsix_file), '--patterns', 'machineId|subscription', '--quiet']
        },
        {
            'name': 'JSON Output',
            'args': [str(vsix_file), '--auth', '--storage', '--format', 'json']
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['name']}")
        print("-" * 40)
        
        # Backup original sys.argv
        original_argv = sys.argv.copy()
        
        try:
            # Set up arguments for the test
            sys.argv = ['vsix-analyzer'] + test_case['args']
            
            # Run the analysis
            result = main()
            
            if result == 0:
                print(f"✅ Test {i} passed")
            else:
                print(f"❌ Test {i} failed with exit code {result}")
                return False
                
        except Exception as e:
            print(f"❌ Test {i} failed with exception: {e}")
            return False
        finally:
            # Restore original sys.argv
            sys.argv = original_argv
    
    print("\n🎉 All tests passed!")
    return True


def demo_features():
    """Demonstrate key features of the analyzer."""
    print("\n" + "=" * 60)
    print("VSIX ANALYZER FEATURE DEMONSTRATION")
    print("=" * 60)
    
    parent_dir = Path(__file__).parent.parent
    vsix_file = parent_dir / "Microsoft.VisualStudio.Services.VSIX"
    
    if not vsix_file.exists():
        print("❌ Demo requires Microsoft.VisualStudio.Services.VSIX file")
        return
    
    # Import analyzer components directly
    from vsix_analyzer.analyzer import VSIXAnalyzer
    from vsix_analyzer.reporters import ReportGenerator
    from vsix_analyzer.utils import setup_logging
    
    # Setup logging
    logger = setup_logging(verbose=True)
    
    print("\n📁 Creating analyzer instance...")
    analyzer = VSIXAnalyzer(str(vsix_file))
    
    print("\n🔍 Running comprehensive analysis...")
    results = analyzer.analyze(['all'])
    
    print("\n📊 Analysis Results Summary:")
    print("-" * 30)
    
    # Display key findings
    file_info = results.get('file_info', {})
    print(f"File size: {file_info.get('size_formatted', 'N/A')}")
    print(f"Valid VSIX: {'Yes' if file_info.get('is_valid') else 'No'}")
    
    ext_info = results.get('extension_info', {})
    print(f"Extension: {ext_info.get('display_name', 'N/A')}")
    print(f"Publisher: {ext_info.get('publisher', 'N/A')}")
    print(f"Version: {ext_info.get('version', 'N/A')}")
    
    analysis = results.get('analysis', {})
    if analysis:
        print(f"\nPattern Analysis Results:")
        for category, patterns in analysis.items():
            if isinstance(patterns, dict) and patterns:
                total_matches = sum(len(matches) for matches in patterns.values())
                if total_matches > 0:
                    print(f"  {category}: {total_matches} matches")
    
    security = results.get('security_assessment', {})
    if security:
        print(f"\nSecurity Assessment:")
        print(f"  Risk Level: {security.get('risk_level', 'unknown').upper()}")
        print(f"  Fingerprinting Score: {security.get('fingerprinting_score', 0)}/10")
        print(f"  Trial Prevention Score: {security.get('trial_prevention_score', 0)}/10")
    
    print("\n📝 Generating reports in different formats...")
    
    reporter = ReportGenerator()
    
    # Generate console report
    console_report = reporter.generate_report(results, 'console')
    print(f"Console report: {len(console_report)} characters")
    
    # Generate markdown report
    markdown_report = reporter.generate_report(results, 'markdown')
    print(f"Markdown report: {len(markdown_report)} characters")
    
    # Generate JSON report
    json_report = reporter.generate_report(results, 'json')
    print(f"JSON report: {len(json_report)} characters")
    
    print("\n🧪 Testing custom pattern search...")
    custom_matches = analyzer.search_custom_pattern('trial.*status')
    print(f"Custom pattern matches: {len(custom_matches)}")
    
    print("\n🔍 Testing pattern contexts...")
    contexts = analyzer.get_pattern_contexts('machineId', context_chars=30)
    print(f"Pattern contexts found: {len(contexts)}")
    
    # Cleanup
    analyzer.cleanup()
    
    print("\n✅ Feature demonstration complete!")


if __name__ == '__main__':
    print("VSIX Analyzer Test Suite")
    print("=" * 50)
    
    # Run basic tests
    if test_basic_analysis():
        # Run feature demonstration
        demo_features()
    else:
        print("❌ Basic tests failed, skipping feature demonstration")
        sys.exit(1)
    
    print("\n🎯 Test suite completed successfully!")
    print("\nTo use the analyzer:")
    print("  python -m vsix_analyzer.cli --help")
    print("  python -m vsix_analyzer.cli ../Microsoft.VisualStudio.Services.VSIX")
