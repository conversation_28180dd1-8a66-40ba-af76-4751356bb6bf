//! Privacy protection module
//!
//! This module implements comprehensive privacy protection mechanisms for fingerprinting,
//! including data anonymization, obfuscation, and differential privacy techniques.

use anyhow::Result;
use serde::{Deserialize, Serialize};
use sha2::{Sha256, Digest};
use ring::rand::{SecureRandom, SystemRandom};
use base64::{Engine as _, engine::general_purpose};
use std::collections::HashMap;

/// Privacy protection analysis result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrivacyAnalysis {
    pub overall_privacy_score: f64,
    pub anonymization_effectiveness: f64,
    pub tracking_resistance: f64,
    pub data_minimization_score: f64,
    pub privacy_risks: Vec<PrivacyRisk>,
    pub recommendations: Vec<String>,
    pub compliance_status: ComplianceStatus,
}

/// Privacy risk assessment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrivacyRisk {
    pub risk_type: String,
    pub severity: RiskSeverity,
    pub description: String,
    pub mitigation: String,
}

/// Risk severity levels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// Privacy compliance status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceStatus {
    pub gdpr_compliant: bool,
    pub ccpa_compliant: bool,
    pub data_retention_compliant: bool,
    pub anonymization_compliant: bool,
}

/// Privacy protector for fingerprint data
pub struct PrivacyProtector {
    rng: SystemRandom,
    salt: Vec<u8>,
}

impl PrivacyProtector {
    /// Create a new privacy protector
    pub fn new() -> Result<Self> {
        let rng = SystemRandom::new();
        let mut salt = vec![0u8; 32];
        rng.fill(&mut salt).map_err(|_| anyhow::anyhow!("Failed to generate salt"))?;
        
        Ok(Self { rng, salt })
    }

    /// Hash sensitive data with salt for anonymization
    pub fn hash_sensitive_data(&self, data: &str) -> Result<String> {
        let mut hasher = Sha256::new();
        hasher.update(&self.salt);
        hasher.update(data.as_bytes());
        let hash = hasher.finalize();
        Ok(general_purpose::URL_SAFE_NO_PAD.encode(&hash[..16]))
    }

    /// Obfuscate CPU information while maintaining useful characteristics
    pub fn obfuscate_cpu_info(&self, cpu_brand: &str) -> Result<String> {
        // Extract general CPU characteristics without revealing exact model
        let cpu_lower = cpu_brand.to_lowercase();
        
        let vendor = if cpu_lower.contains("intel") {
            "intel"
        } else if cpu_lower.contains("amd") {
            "amd"
        } else if cpu_lower.contains("apple") {
            "apple"
        } else {
            "unknown"
        };

        let generation = if cpu_lower.contains("i3") || cpu_lower.contains("ryzen 3") {
            "entry"
        } else if cpu_lower.contains("i5") || cpu_lower.contains("ryzen 5") {
            "mid"
        } else if cpu_lower.contains("i7") || cpu_lower.contains("ryzen 7") {
            "high"
        } else if cpu_lower.contains("i9") || cpu_lower.contains("ryzen 9") {
            "premium"
        } else if cpu_lower.contains("m1") || cpu_lower.contains("m2") {
            "apple-silicon"
        } else {
            "standard"
        };

        // Add some noise to prevent exact identification
        let mut noise_bytes = vec![0u8; 4];
        self.rng.fill(&mut noise_bytes).map_err(|_| anyhow::anyhow!("Failed to generate noise"))?;
        let noise = u32::from_le_bytes([noise_bytes[0], noise_bytes[1], noise_bytes[2], noise_bytes[3]]) % 1000;

        Ok(format!("{}-{}-{}", vendor, generation, noise))
    }

    /// Derive MAC address signature without revealing actual MAC
    pub fn derive_mac_signature(&self, mac_address: &str) -> Result<String> {
        // Use only the OUI (first 3 octets) to identify manufacturer
        let oui = mac_address.split(':').take(3).collect::<Vec<_>>().join(":");
        
        // Hash the full MAC with salt for uniqueness without revealing it
        let mac_hash = self.hash_sensitive_data(mac_address)?;
        
        // Combine OUI classification with hashed signature
        let manufacturer = self.classify_mac_oui(&oui);
        Ok(format!("{}-{}", manufacturer, &mac_hash[..8]))
    }

    /// Classify MAC OUI to manufacturer category
    fn classify_mac_oui(&self, oui: &str) -> &'static str {
        match oui.to_uppercase().as_str() {
            // Common manufacturers (simplified)
            "00:1B:63" | "00:26:BB" | "3C:07:54" => "apple",
            "00:15:5D" | "00:03:FF" => "microsoft",
            "08:00:27" => "virtualbox",
            "00:0C:29" | "00:50:56" => "vmware",
            "52:54:00" => "qemu",
            _ => "generic",
        }
    }

    /// Apply differential privacy to numerical data
    pub fn apply_differential_privacy(&self, value: f64, epsilon: f64) -> Result<f64> {
        // Laplace mechanism for differential privacy
        let mut random_bytes = vec![0u8; 8];
        self.rng.fill(&mut random_bytes).map_err(|_| anyhow::anyhow!("Failed to generate random bytes"))?;
        
        // Convert random bytes to uniform random value in [-1, 1]
        let uniform = (u64::from_le_bytes([
            random_bytes[0], random_bytes[1], random_bytes[2], random_bytes[3],
            random_bytes[4], random_bytes[5], random_bytes[6], random_bytes[7],
        ]) as f64 / u64::MAX as f64) * 2.0 - 1.0;
        
        // Apply Laplace noise
        let noise = if uniform >= 0.0 {
            -(1.0 / epsilon) * (1.0 - uniform).ln()
        } else {
            (1.0 / epsilon) * (1.0 + uniform).ln()
        };
        
        Ok(value + noise)
    }

    /// Anonymize personal identifiers
    pub fn anonymize_identifier(&self, identifier: &str, anonymization_level: u8) -> Result<String> {
        match anonymization_level {
            1 => {
                // Level 1: Simple hashing
                self.hash_sensitive_data(identifier)
            },
            2 => {
                // Level 2: Truncated hash
                let hash = self.hash_sensitive_data(identifier)?;
                Ok(hash[..8].to_string())
            },
            3 => {
                // Level 3: Generalized category
                Ok("anonymized".to_string())
            },
            _ => {
                // Default: Full anonymization
                Ok("redacted".to_string())
            }
        }
    }

    /// Analyze privacy protection effectiveness
    pub async fn analyze_privacy_protection(
        &self,
        fingerprint_path: &str,
        generate_report: bool,
    ) -> Result<PrivacyAnalysis> {
        // Load and analyze fingerprint
        let fingerprint_data = std::fs::read_to_string(fingerprint_path)?;
        let fingerprint: serde_json::Value = serde_json::from_str(&fingerprint_data)?;

        let mut privacy_risks = Vec::new();
        let mut recommendations = Vec::new();

        // Analyze different aspects of privacy protection
        let anonymization_score = self.assess_anonymization(&fingerprint)?;
        let tracking_resistance = self.assess_tracking_resistance(&fingerprint)?;
        let data_minimization = self.assess_data_minimization(&fingerprint)?;

        // Check for potential privacy risks
        if anonymization_score < 0.7 {
            privacy_risks.push(PrivacyRisk {
                risk_type: "Insufficient Anonymization".to_string(),
                severity: RiskSeverity::Medium,
                description: "Some personal identifiers may not be sufficiently anonymized".to_string(),
                mitigation: "Increase anonymization level or use stronger hashing".to_string(),
            });
            recommendations.push("Consider using higher privacy level settings".to_string());
        }

        if tracking_resistance < 0.6 {
            privacy_risks.push(PrivacyRisk {
                risk_type: "Cross-Session Tracking Risk".to_string(),
                severity: RiskSeverity::High,
                description: "Fingerprint may enable cross-session tracking".to_string(),
                mitigation: "Enable fingerprint rotation and reduce persistent identifiers".to_string(),
            });
            recommendations.push("Enable automatic fingerprint rotation".to_string());
        }

        // Calculate overall privacy score
        let overall_privacy_score = (anonymization_score + tracking_resistance + data_minimization) / 3.0;

        // Assess compliance status
        let compliance_status = ComplianceStatus {
            gdpr_compliant: overall_privacy_score >= 0.8,
            ccpa_compliant: overall_privacy_score >= 0.7,
            data_retention_compliant: fingerprint.get("rotation_enabled").and_then(|v| v.as_bool()).unwrap_or(false),
            anonymization_compliant: anonymization_score >= 0.8,
        };

        if generate_report {
            self.generate_privacy_report(&fingerprint, &privacy_risks, &recommendations)?;
        }

        Ok(PrivacyAnalysis {
            overall_privacy_score,
            anonymization_effectiveness: anonymization_score,
            tracking_resistance,
            data_minimization_score: data_minimization,
            privacy_risks,
            recommendations,
            compliance_status,
        })
    }

    /// Assess anonymization effectiveness
    fn assess_anonymization(&self, fingerprint: &serde_json::Value) -> Result<f64> {
        let mut score: f64 = 1.0;

        // Check if personal data is properly hashed
        if let Some(system_sig) = fingerprint.get("system_signature") {
            if let Some(hostname) = system_sig.get("hostname_hash") {
                if hostname.as_str().unwrap_or("").len() < 16 {
                    score -= 0.2; // Insufficient hash length
                }
            }
            
            if let Some(username) = system_sig.get("username_hash") {
                if username.as_str().unwrap_or("").len() < 16 {
                    score -= 0.2; // Insufficient hash length
                }
            }
        }

        // Check hardware obfuscation
        if let Some(hw_sig) = fingerprint.get("hardware_signature") {
            if let Some(cpu) = hw_sig.get("cpu_signature") {
                if cpu.as_str().unwrap_or("").contains("Intel Core i7-9750H") {
                    score -= 0.3; // Exact CPU model revealed
                }
            }
        }

        Ok(score.max(0.0))
    }

    /// Assess tracking resistance
    fn assess_tracking_resistance(&self, fingerprint: &serde_json::Value) -> Result<f64> {
        let mut score: f64 = 1.0;

        // Check if rotation is enabled
        if !fingerprint.get("rotation_enabled").and_then(|v| v.as_bool()).unwrap_or(false) {
            score -= 0.4;
        }

        // Check for persistent identifiers
        if let Some(device_id) = fingerprint.get("device_id") {
            if device_id.as_str().unwrap_or("").len() > 32 {
                score -= 0.2; // Very long persistent identifier
            }
        }

        // Check privacy level
        if let Some(privacy_level) = fingerprint.get("privacy_level") {
            match privacy_level.as_str().unwrap_or("") {
                "Low" => score -= 0.3,
                "Medium" => score -= 0.1,
                "High" => {}, // No penalty
                "Maximum" => score += 0.1,
                _ => score -= 0.2,
            }
        }

        Ok(score.max(0.0).min(1.0))
    }

    /// Assess data minimization
    fn assess_data_minimization(&self, fingerprint: &serde_json::Value) -> Result<f64> {
        let mut score = 1.0;
        let mut data_points = 0;
        let mut necessary_points = 0;

        // Count data points and assess necessity
        if let Some(hw_sig) = fingerprint.get("hardware_signature") {
            data_points += hw_sig.as_object().map(|o| o.len()).unwrap_or(0);
            necessary_points += 3; // CPU, memory, architecture are necessary
        }

        if let Some(sys_sig) = fingerprint.get("system_signature") {
            data_points += sys_sig.as_object().map(|o| o.len()).unwrap_or(0);
            necessary_points += 2; // OS family and version class are necessary
        }

        if let Some(net_sig) = fingerprint.get("network_signature") {
            data_points += net_sig.as_object().map(|o| o.len()).unwrap_or(0);
            necessary_points += 1; // Interface count is sufficient
        }

        // Calculate minimization score
        if data_points > 0 {
            let minimization_ratio = necessary_points as f64 / data_points as f64;
            score = minimization_ratio.min(1.0);
        }

        Ok(score)
    }

    /// Generate detailed privacy report
    fn generate_privacy_report(
        &self,
        _fingerprint: &serde_json::Value,
        risks: &[PrivacyRisk],
        recommendations: &[String],
    ) -> Result<()> {
        let report = format!(
            "# Privacy Protection Report\n\n\
            ## Risk Assessment\n\
            Found {} privacy risks:\n\
            {}\n\n\
            ## Recommendations\n\
            {}\n\n\
            Generated at: {}\n",
            risks.len(),
            risks.iter()
                .map(|r| format!("- **{}** ({}): {}", r.risk_type, 
                    match r.severity {
                        RiskSeverity::Low => "Low",
                        RiskSeverity::Medium => "Medium", 
                        RiskSeverity::High => "High",
                        RiskSeverity::Critical => "Critical",
                    }, r.description))
                .collect::<Vec<_>>()
                .join("\n"),
            recommendations.iter()
                .map(|r| format!("- {}", r))
                .collect::<Vec<_>>()
                .join("\n"),
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC")
        );

        std::fs::write("privacy_report.md", report)?;
        Ok(())
    }

    /// Create privacy-safe test data
    pub fn create_test_fingerprint(&self) -> Result<HashMap<String, serde_json::Value>> {
        let mut fingerprint = HashMap::new();
        
        fingerprint.insert("device_id".to_string(), 
            serde_json::Value::String(self.hash_sensitive_data("test-device")?));
        
        fingerprint.insert("privacy_level".to_string(), 
            serde_json::Value::String("High".to_string()));
        
        fingerprint.insert("rotation_enabled".to_string(), 
            serde_json::Value::Bool(true));

        // Add obfuscated hardware signature
        let mut hw_sig = HashMap::new();
        hw_sig.insert("cpu_signature".to_string(), 
            serde_json::Value::String(self.obfuscate_cpu_info("Intel Core i7-9750H")?));
        hw_sig.insert("memory_class".to_string(), 
            serde_json::Value::String("8-16GB".to_string()));
        
        fingerprint.insert("hardware_signature".to_string(), 
            serde_json::Value::Object(hw_sig.into_iter().collect()));

        // Add anonymized system signature
        let mut sys_sig = HashMap::new();
        sys_sig.insert("hostname_hash".to_string(), 
            serde_json::Value::String(self.hash_sensitive_data("test-hostname")?));
        sys_sig.insert("username_hash".to_string(), 
            serde_json::Value::String(self.hash_sensitive_data("test-user")?));
        
        fingerprint.insert("system_signature".to_string(), 
            serde_json::Value::Object(sys_sig.into_iter().collect()));

        Ok(fingerprint)
    }
}
