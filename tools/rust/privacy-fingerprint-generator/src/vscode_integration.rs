//! VS Code extension integration module
//!
//! This module provides seamless integration with VS Code extensions using real
//! extension APIs for storage, workspace state, and system information.

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

use crate::privacy::PrivacyProtector;

/// VS Code integration configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VSCodeIntegrationConfig {
    pub extension_path: PathBuf,
    pub enable_trial_prevention: bool,
    pub storage_location: StorageLocation,
    pub api_bindings: APIBindings,
    pub privacy_settings: IntegrationPrivacySettings,
}

/// Storage location options for fingerprint data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StorageLocation {
    /// VS Code global storage
    GlobalStorage,
    /// VS Code workspace storage
    WorkspaceStorage,
    /// Extension-specific storage
    ExtensionStorage,
    /// Secure system storage
    SecureStorage,
}

/// API bindings configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct APIBindings {
    pub use_vscode_storage_api: bool,
    pub use_workspace_state_api: bool,
    pub use_system_info_api: bool,
    pub use_lifecycle_events: bool,
}

/// Privacy settings for integration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegrationPrivacySettings {
    pub encrypt_stored_data: bool,
    pub use_secure_storage: bool,
    pub enable_data_expiration: bool,
    pub anonymize_telemetry: bool,
}

/// Integration result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegrationResult {
    pub success: bool,
    pub extension_path: String,
    pub fingerprint_location: String,
    pub api_bindings_created: Vec<String>,
    pub trial_prevention_enabled: bool,
    pub privacy_features_enabled: Vec<String>,
    pub integration_metadata: IntegrationMetadata,
}

/// Integration metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegrationMetadata {
    pub integration_timestamp: chrono::DateTime<chrono::Utc>,
    pub vscode_version_detected: Option<String>,
    pub extension_manifest_found: bool,
    pub storage_permissions: Vec<String>,
    pub privacy_compliance_level: String,
}

/// Trial prevention configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrialPreventionConfig {
    pub enable_fingerprint_tracking: bool,
    pub enable_bypass_detection: bool,
    pub enable_persistence_monitoring: bool,
    pub trial_duration_days: u32,
    pub max_trial_resets: u32,
}

/// VS Code extension integration handler
pub struct VSCodeIntegration {
    config: VSCodeIntegrationConfig,
    privacy_protector: PrivacyProtector,
}

impl VSCodeIntegration {
    /// Create a new VS Code integration
    pub fn new(extension_path: &str) -> Result<Self> {
        let config = VSCodeIntegrationConfig {
            extension_path: PathBuf::from(extension_path),
            enable_trial_prevention: false,
            storage_location: StorageLocation::ExtensionStorage,
            api_bindings: APIBindings::default(),
            privacy_settings: IntegrationPrivacySettings::default(),
        };

        let privacy_protector = PrivacyProtector::new()?;

        Ok(Self {
            config,
            privacy_protector,
        })
    }

    /// Create integration with custom configuration
    pub fn with_config(config: VSCodeIntegrationConfig) -> Result<Self> {
        let privacy_protector = PrivacyProtector::new()?;
        Ok(Self { config, privacy_protector })
    }

    /// Integrate fingerprinting with VS Code extension
    pub async fn integrate_fingerprinting(&self, enable_trial_prevention: bool) -> Result<IntegrationResult> {
        let mut api_bindings_created = Vec::new();
        let mut privacy_features_enabled = Vec::new();

        // Validate extension path and structure
        self.validate_extension_structure()?;

        // Create TypeScript/JavaScript bindings
        if self.config.api_bindings.use_vscode_storage_api {
            self.create_storage_api_bindings().await?;
            api_bindings_created.push("storage_api".to_string());
        }

        if self.config.api_bindings.use_workspace_state_api {
            self.create_workspace_state_bindings().await?;
            api_bindings_created.push("workspace_state_api".to_string());
        }

        if self.config.api_bindings.use_system_info_api {
            self.create_system_info_bindings().await?;
            api_bindings_created.push("system_info_api".to_string());
        }

        if self.config.api_bindings.use_lifecycle_events {
            self.create_lifecycle_event_bindings().await?;
            api_bindings_created.push("lifecycle_events".to_string());
        }

        // Set up fingerprint storage
        let fingerprint_location = self.setup_fingerprint_storage().await?;

        // Configure trial prevention if enabled
        if enable_trial_prevention {
            self.setup_trial_prevention().await?;
        }

        // Enable privacy features
        if self.config.privacy_settings.encrypt_stored_data {
            self.enable_data_encryption().await?;
            privacy_features_enabled.push("data_encryption".to_string());
        }

        if self.config.privacy_settings.use_secure_storage {
            self.enable_secure_storage().await?;
            privacy_features_enabled.push("secure_storage".to_string());
        }

        if self.config.privacy_settings.enable_data_expiration {
            self.enable_data_expiration().await?;
            privacy_features_enabled.push("data_expiration".to_string());
        }

        // Create integration metadata
        let integration_metadata = self.create_integration_metadata().await?;

        Ok(IntegrationResult {
            success: true,
            extension_path: self.config.extension_path.to_string_lossy().to_string(),
            fingerprint_location,
            api_bindings_created,
            trial_prevention_enabled: enable_trial_prevention,
            privacy_features_enabled,
            integration_metadata,
        })
    }

    /// Validate extension structure and permissions
    fn validate_extension_structure(&self) -> Result<()> {
        let extension_path = &self.config.extension_path;

        // Check if extension directory exists
        if !extension_path.exists() {
            return Err(anyhow::anyhow!("Extension path does not exist: {:?}", extension_path));
        }

        // Check for package.json
        let package_json_path = extension_path.join("package.json");
        if !package_json_path.exists() {
            return Err(anyhow::anyhow!("package.json not found in extension directory"));
        }

        // Validate package.json structure
        let package_json_content = std::fs::read_to_string(&package_json_path)?;
        let package_json: serde_json::Value = serde_json::from_str(&package_json_content)?;

        // Check for required VS Code extension fields
        if package_json.get("engines").and_then(|e| e.get("vscode")).is_none() {
            return Err(anyhow::anyhow!("Invalid VS Code extension: missing vscode engine requirement"));
        }

        // Check for main entry point
        if package_json.get("main").is_none() {
            return Err(anyhow::anyhow!("Invalid VS Code extension: missing main entry point"));
        }

        Ok(())
    }

    /// Create storage API bindings
    async fn create_storage_api_bindings(&self) -> Result<()> {
        let bindings_content = self.generate_storage_api_bindings()?;
        let bindings_path = self.config.extension_path.join("src").join("fingerprint_storage.ts");

        // Ensure src directory exists
        if let Some(parent) = bindings_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        std::fs::write(&bindings_path, bindings_content)?;
        Ok(())
    }

    /// Create workspace state bindings
    async fn create_workspace_state_bindings(&self) -> Result<()> {
        let bindings_content = self.generate_workspace_state_bindings()?;
        let bindings_path = self.config.extension_path.join("src").join("workspace_state.ts");

        if let Some(parent) = bindings_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        std::fs::write(&bindings_path, bindings_content)?;
        Ok(())
    }

    /// Create system info bindings
    async fn create_system_info_bindings(&self) -> Result<()> {
        let bindings_content = self.generate_system_info_bindings()?;
        let bindings_path = self.config.extension_path.join("src").join("system_info.ts");

        if let Some(parent) = bindings_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        std::fs::write(&bindings_path, bindings_content)?;
        Ok(())
    }

    /// Create lifecycle event bindings
    async fn create_lifecycle_event_bindings(&self) -> Result<()> {
        let bindings_content = self.generate_lifecycle_bindings()?;
        let bindings_path = self.config.extension_path.join("src").join("lifecycle_events.ts");

        if let Some(parent) = bindings_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        std::fs::write(&bindings_path, bindings_content)?;
        Ok(())
    }

    /// Setup fingerprint storage location
    async fn setup_fingerprint_storage(&self) -> Result<String> {
        let storage_path = match self.config.storage_location {
            StorageLocation::GlobalStorage => {
                self.config.extension_path.join("storage").join("global")
            },
            StorageLocation::WorkspaceStorage => {
                self.config.extension_path.join("storage").join("workspace")
            },
            StorageLocation::ExtensionStorage => {
                self.config.extension_path.join("storage").join("extension")
            },
            StorageLocation::SecureStorage => {
                self.config.extension_path.join("storage").join("secure")
            },
        };

        std::fs::create_dir_all(&storage_path)?;
        Ok(storage_path.to_string_lossy().to_string())
    }

    /// Setup trial prevention system
    async fn setup_trial_prevention(&self) -> Result<()> {
        let trial_config = TrialPreventionConfig::default();
        let config_content = serde_json::to_string_pretty(&trial_config)?;
        let config_path = self.config.extension_path.join("src").join("trial_prevention_config.json");

        std::fs::write(&config_path, config_content)?;

        // Create trial prevention TypeScript module
        let trial_prevention_content = self.generate_trial_prevention_bindings()?;
        let trial_prevention_path = self.config.extension_path.join("src").join("trial_prevention.ts");

        std::fs::write(&trial_prevention_path, trial_prevention_content)?;
        Ok(())
    }

    /// Enable data encryption
    async fn enable_data_encryption(&self) -> Result<()> {
        let encryption_content = self.generate_encryption_bindings()?;
        let encryption_path = self.config.extension_path.join("src").join("data_encryption.ts");

        std::fs::write(&encryption_path, encryption_content)?;
        Ok(())
    }

    /// Enable secure storage
    async fn enable_secure_storage(&self) -> Result<()> {
        // Secure storage is handled in the main storage bindings
        Ok(())
    }

    /// Enable data expiration
    async fn enable_data_expiration(&self) -> Result<()> {
        let expiration_content = self.generate_expiration_bindings()?;
        let expiration_path = self.config.extension_path.join("src").join("data_expiration.ts");

        std::fs::write(&expiration_path, expiration_content)?;
        Ok(())
    }

    /// Create integration metadata
    async fn create_integration_metadata(&self) -> Result<IntegrationMetadata> {
        let package_json_path = self.config.extension_path.join("package.json");
        let vscode_version_detected = if package_json_path.exists() {
            let content = std::fs::read_to_string(&package_json_path)?;
            let package_json: serde_json::Value = serde_json::from_str(&content)?;
            package_json.get("engines")
                .and_then(|e| e.get("vscode"))
                .and_then(|v| v.as_str())
                .map(|s| s.to_string())
        } else {
            None
        };

        Ok(IntegrationMetadata {
            integration_timestamp: chrono::Utc::now(),
            vscode_version_detected,
            extension_manifest_found: package_json_path.exists(),
            storage_permissions: vec![
                "globalState".to_string(),
                "secrets".to_string(),
                "workspaceState".to_string(),
            ],
            privacy_compliance_level: "High".to_string(),
        })
    }

    /// Generate TypeScript bindings for VS Code storage API
    fn generate_storage_api_bindings(&self) -> Result<String> {
        let bindings = r#"
/**
 * Privacy-focused fingerprint storage using VS Code APIs
 *
 * This module provides secure storage and retrieval of device fingerprints
 * using VS Code's built-in storage APIs with privacy protection.
 */

import * as vscode from 'vscode';
import * as crypto from 'crypto';

export interface PrivacyFingerprint {
    id: string;
    version: string;
    created_at: string;
    privacy_level: 'Low' | 'Medium' | 'High' | 'Maximum';
    rotation_enabled: boolean;
    next_rotation?: string;
    device_id: string;
    hardware_signature: HardwareSignature;
    system_signature: SystemSignature;
    vscode_signature: VSCodeSignature;
    network_signature: NetworkSignature;
    privacy_metadata: PrivacyMetadata;
    fingerprint_hash: string;
}

export interface HardwareSignature {
    cpu_signature: string;
    memory_class: string;
    architecture: string;
    hardware_uuid_hash: string;
    performance_class: string;
}

export interface SystemSignature {
    os_family: string;
    os_version_class: string;
    locale_region: string;
    timezone_offset: number;
    hostname_hash: string;
    username_hash: string;
}

export interface VSCodeSignature {
    version_class: string;
    machine_id_hash: string;
    session_signature: string;
    workspace_signature?: string;
    extensions_signature: string;
}

export interface NetworkSignature {
    interface_count: number;
    mac_signature: string;
    network_class: string;
    connectivity_signature: string;
}

export interface PrivacyMetadata {
    anonymization_applied: string[];
    obfuscation_methods: string[];
    data_retention_policy: string;
    privacy_score: number;
    tracking_resistance: number;
}

export class FingerprintStorage {
    private context: vscode.ExtensionContext;
    private encryptionKey: string;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.encryptionKey = this.generateEncryptionKey();
    }

    /**
     * Store fingerprint using VS Code's secure storage
     */
    async storeFingerprint(fingerprint: PrivacyFingerprint): Promise<void> {
        try {
            const encryptedData = this.encryptData(JSON.stringify(fingerprint));

            // Store in global storage for persistence across workspaces
            await this.context.globalState.update('privacy_fingerprint', encryptedData);

            // Also store in secure storage if available
            if (this.context.secrets) {
                await this.context.secrets.store('fingerprint_data', encryptedData);
            }

            // Update last access timestamp
            await this.context.globalState.update('fingerprint_last_access', Date.now());

        } catch (error) {
            throw new Error(`Failed to store fingerprint: ${error}`);
        }
    }

    /**
     * Retrieve fingerprint from storage
     */
    async retrieveFingerprint(): Promise<PrivacyFingerprint | null> {
        try {
            // Try secure storage first
            let encryptedData: string | undefined;

            if (this.context.secrets) {
                encryptedData = await this.context.secrets.get('fingerprint_data');
            }

            // Fallback to global storage
            if (!encryptedData) {
                encryptedData = this.context.globalState.get<string>('privacy_fingerprint');
            }

            if (!encryptedData) {
                return null;
            }

            const decryptedData = this.decryptData(encryptedData);
            const fingerprint: PrivacyFingerprint = JSON.parse(decryptedData);

            // Update last access
            await this.context.globalState.update('fingerprint_last_access', Date.now());

            return fingerprint;

        } catch (error) {
            console.error('Failed to retrieve fingerprint:', error);
            return null;
        }
    }

    /**
     * Check if fingerprint rotation is due
     */
    async isRotationDue(): Promise<boolean> {
        const fingerprint = await this.retrieveFingerprint();
        if (!fingerprint || !fingerprint.rotation_enabled) {
            return false;
        }

        if (fingerprint.next_rotation) {
            const nextRotation = new Date(fingerprint.next_rotation);
            return Date.now() >= nextRotation.getTime();
        }

        return false;
    }

    /**
     * Clear stored fingerprint data
     */
    async clearFingerprint(): Promise<void> {
        await this.context.globalState.update('privacy_fingerprint', undefined);
        if (this.context.secrets) {
            await this.context.secrets.delete('fingerprint_data');
        }
        await this.context.globalState.update('fingerprint_last_access', undefined);
    }

    /**
     * Get storage statistics
     */
    async getStorageStats(): Promise<{lastAccess?: number, hasFingerprint: boolean}> {
        const lastAccess = this.context.globalState.get<number>('fingerprint_last_access');
        const hasFingerprint = (await this.retrieveFingerprint()) !== null;

        return { lastAccess, hasFingerprint };
    }

    private generateEncryptionKey(): string {
        // Generate a deterministic key based on VS Code machine ID
        const machineId = vscode.env.machineId;
        return crypto.createHash('sha256').update(machineId + 'fingerprint_key').digest('hex');
    }

    private encryptData(data: string): string {
        const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return encrypted;
    }

    private decryptData(encryptedData: string): string {
        const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
        let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }
}

/**
 * Utility functions for VS Code integration
 */
export class VSCodeUtils {
    /**
     * Get VS Code machine ID (hashed for privacy)
     */
    static getMachineIdHash(): string {
        return crypto.createHash('sha256').update(vscode.env.machineId).digest('hex').substring(0, 16);
    }

    /**
     * Get VS Code session ID
     */
    static getSessionId(): string {
        return vscode.env.sessionId;
    }

    /**
     * Get workspace signature
     */
    static getWorkspaceSignature(): string | undefined {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (workspaceFolder) {
            return crypto.createHash('sha256').update(workspaceFolder.uri.fsPath).digest('hex').substring(0, 16);
        }
        return undefined;
    }

    /**
     * Get installed extensions signature
     */
    static getExtensionsSignature(): string {
        const extensions = vscode.extensions.all
            .filter(ext => !ext.packageJSON.isBuiltin)
            .map(ext => ext.id)
            .sort()
            .join(',');

        return crypto.createHash('sha256').update(extensions).digest('hex').substring(0, 16);
    }

    /**
     * Get VS Code version class
     */
    static getVersionClass(): string {
        const version = vscode.version;
        const majorMinor = version.split('.').slice(0, 2).join('.');
        return `${majorMinor}.x`;
    }
}
"#;
        Ok(bindings.to_string())
    }

    /// Generate workspace state bindings
    fn generate_workspace_state_bindings(&self) -> Result<String> {
        let bindings = r#"
/**
 * Workspace state management for fingerprint data
 */

import * as vscode from 'vscode';

export class WorkspaceStateManager {
    private context: vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
    }

    async storeWorkspaceFingerprint(workspaceId: string, signature: string): Promise<void> {
        await this.context.workspaceState.update(`workspace_fingerprint_${workspaceId}`, signature);
    }

    async getWorkspaceFingerprint(workspaceId: string): Promise<string | undefined> {
        return this.context.workspaceState.get<string>(`workspace_fingerprint_${workspaceId}`);
    }

    async clearWorkspaceData(): Promise<void> {
        const keys = this.context.workspaceState.keys();
        for (const key of keys) {
            if (key.startsWith('workspace_fingerprint_')) {
                await this.context.workspaceState.update(key, undefined);
            }
        }
    }
}
"#;
        Ok(bindings.to_string())
    }

    /// Generate system info bindings
    fn generate_system_info_bindings(&self) -> Result<String> {
        let bindings = r#"
/**
 * System information collection with privacy protection
 */

import * as vscode from 'vscode';
import * as os from 'os';
import * as crypto from 'crypto';

export class SystemInfoCollector {
    /**
     * Collect privacy-protected system information
     */
    static collectSystemInfo(): any {
        return {
            platform: os.platform(),
            arch: os.arch(),
            release: this.getOSVersionClass(),
            hostname_hash: this.hashSensitiveData(os.hostname()),
            username_hash: this.hashSensitiveData(os.userInfo().username),
            memory_class: this.classifyMemory(os.totalmem()),
            cpu_signature: this.getCPUSignature(),
            timezone_offset: new Date().getTimezoneOffset(),
            locale: vscode.env.language,
        };
    }

    private static getOSVersionClass(): string {
        const release = os.release();
        const platform = os.platform();

        if (platform === 'darwin') {
            return 'macos-recent';
        } else if (platform === 'win32') {
            return 'windows-10-11';
        } else {
            return 'linux-modern';
        }
    }

    private static classifyMemory(totalMemory: number): string {
        const gb = totalMemory / (1024 * 1024 * 1024);
        if (gb <= 4) return '0-4GB';
        if (gb <= 8) return '4-8GB';
        if (gb <= 16) return '8-16GB';
        if (gb <= 32) return '16-32GB';
        return '32GB+';
    }

    private static getCPUSignature(): string {
        const cpus = os.cpus();
        if (cpus.length === 0) return 'unknown';

        const model = cpus[0].model.toLowerCase();
        let vendor = 'unknown';
        let tier = 'standard';

        if (model.includes('intel')) vendor = 'intel';
        else if (model.includes('amd')) vendor = 'amd';
        else if (model.includes('apple')) vendor = 'apple';

        if (model.includes('i3') || model.includes('ryzen 3')) tier = 'entry';
        else if (model.includes('i5') || model.includes('ryzen 5')) tier = 'mid';
        else if (model.includes('i7') || model.includes('ryzen 7')) tier = 'high';
        else if (model.includes('i9') || model.includes('ryzen 9')) tier = 'premium';
        else if (model.includes('m1') || model.includes('m2')) tier = 'apple-silicon';

        const noise = Math.floor(Math.random() * 1000);
        return `${vendor}-${tier}-${noise}`;
    }

    private static hashSensitiveData(data: string): string {
        return crypto.createHash('sha256').update(data + 'privacy_salt').digest('hex').substring(0, 16);
    }
}
"#;
        Ok(bindings.to_string())
    }

    /// Generate lifecycle event bindings
    fn generate_lifecycle_bindings(&self) -> Result<String> {
        let bindings = r#"
/**
 * Extension lifecycle event handlers for fingerprint management
 */

import * as vscode from 'vscode';
import { FingerprintStorage } from './fingerprint_storage';

export class LifecycleManager {
    private storage: FingerprintStorage;
    private rotationTimer?: NodeJS.Timeout;

    constructor(context: vscode.ExtensionContext) {
        this.storage = new FingerprintStorage(context);
        this.setupLifecycleHandlers(context);
    }

    private setupLifecycleHandlers(context: vscode.ExtensionContext): void {
        // Handle extension activation
        this.onActivation();

        // Handle workspace changes
        vscode.workspace.onDidChangeWorkspaceFolders(() => {
            this.onWorkspaceChange();
        });

        // Handle extension deactivation
        context.subscriptions.push({
            dispose: () => this.onDeactivation()
        });

        // Setup automatic rotation check
        this.setupRotationTimer();
    }

    private async onActivation(): Promise<void> {
        console.log('Privacy fingerprint system activated');

        // Check if rotation is due
        if (await this.storage.isRotationDue()) {
            console.log('Fingerprint rotation is due');
            // Trigger rotation process
        }
    }

    private async onWorkspaceChange(): Promise<void> {
        console.log('Workspace changed - updating fingerprint');
        // Update workspace-specific components of fingerprint
    }

    private onDeactivation(): void {
        console.log('Privacy fingerprint system deactivated');
        if (this.rotationTimer) {
            clearInterval(this.rotationTimer);
        }
    }

    private setupRotationTimer(): void {
        // Check for rotation every hour
        this.rotationTimer = setInterval(async () => {
            if (await this.storage.isRotationDue()) {
                console.log('Automatic fingerprint rotation triggered');
                // Trigger rotation
            }
        }, 60 * 60 * 1000); // 1 hour
    }
}
"#;
        Ok(bindings.to_string())
    }

    /// Generate encryption bindings
    fn generate_encryption_bindings(&self) -> Result<String> {
        let bindings = r#"
/**
 * Data encryption utilities for fingerprint storage
 */

import * as crypto from 'crypto';

export class DataEncryption {
    private static readonly ALGORITHM = 'aes-256-gcm';
    private static readonly KEY_LENGTH = 32;
    private static readonly IV_LENGTH = 16;
    private static readonly TAG_LENGTH = 16;

    static encrypt(data: string, key: string): string {
        const keyBuffer = crypto.scryptSync(key, 'salt', DataEncryption.KEY_LENGTH);
        const iv = crypto.randomBytes(DataEncryption.IV_LENGTH);
        const cipher = crypto.createCipherGCM(DataEncryption.ALGORITHM, keyBuffer, iv);

        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');

        const tag = cipher.getAuthTag();

        return iv.toString('hex') + ':' + tag.toString('hex') + ':' + encrypted;
    }

    static decrypt(encryptedData: string, key: string): string {
        const parts = encryptedData.split(':');
        if (parts.length !== 3) {
            throw new Error('Invalid encrypted data format');
        }

        const iv = Buffer.from(parts[0], 'hex');
        const tag = Buffer.from(parts[1], 'hex');
        const encrypted = parts[2];

        const keyBuffer = crypto.scryptSync(key, 'salt', DataEncryption.KEY_LENGTH);
        const decipher = crypto.createDecipherGCM(DataEncryption.ALGORITHM, keyBuffer, iv);
        decipher.setAuthTag(tag);

        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');

        return decrypted;
    }

    static generateKey(): string {
        return crypto.randomBytes(DataEncryption.KEY_LENGTH).toString('hex');
    }
}
"#;
        Ok(bindings.to_string())
    }

    /// Generate data expiration bindings
    fn generate_expiration_bindings(&self) -> Result<String> {
        let bindings = r#"
/**
 * Data expiration management for privacy compliance
 */

import * as vscode from 'vscode';

export interface ExpirationPolicy {
    maxAge: number; // milliseconds
    checkInterval: number; // milliseconds
    autoCleanup: boolean;
}

export class DataExpirationManager {
    private context: vscode.ExtensionContext;
    private policy: ExpirationPolicy;
    private cleanupTimer?: NodeJS.Timeout;

    constructor(context: vscode.ExtensionContext, policy?: ExpirationPolicy) {
        this.context = context;
        this.policy = policy || {
            maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
            checkInterval: 24 * 60 * 60 * 1000, // 24 hours
            autoCleanup: true,
        };

        if (this.policy.autoCleanup) {
            this.startCleanupTimer();
        }
    }

    async markDataCreated(key: string): Promise<void> {
        const timestamp = Date.now();
        await this.context.globalState.update(`${key}_created`, timestamp);
    }

    async isDataExpired(key: string): Promise<boolean> {
        const created = this.context.globalState.get<number>(`${key}_created`);
        if (!created) return true;

        const age = Date.now() - created;
        return age > this.policy.maxAge;
    }

    async cleanupExpiredData(): Promise<string[]> {
        const cleanedKeys: string[] = [];
        const allKeys = this.context.globalState.keys();

        for (const key of allKeys) {
            if (key.endsWith('_created')) continue;

            if (await this.isDataExpired(key)) {
                await this.context.globalState.update(key, undefined);
                await this.context.globalState.update(`${key}_created`, undefined);
                cleanedKeys.push(key);
            }
        }

        return cleanedKeys;
    }

    private startCleanupTimer(): void {
        this.cleanupTimer = setInterval(async () => {
            const cleaned = await this.cleanupExpiredData();
            if (cleaned.length > 0) {
                console.log(`Cleaned up ${cleaned.length} expired data entries`);
            }
        }, this.policy.checkInterval);
    }

    dispose(): void {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }
    }
}
"#;
        Ok(bindings.to_string())
    }

    /// Generate trial prevention bindings
    fn generate_trial_prevention_bindings(&self) -> Result<String> {
        let bindings = r#"
/**
 * Trial prevention system with privacy-protected fingerprinting
 */

import * as vscode from 'vscode';
import { FingerprintStorage, PrivacyFingerprint } from './fingerprint_storage';

export interface TrialState {
    isTrialActive: boolean;
    trialStartDate: string;
    trialEndDate: string;
    usageCount: number;
    fingerprintId: string;
    bypassAttempts: number;
}

export class TrialPreventionSystem {
    private storage: FingerprintStorage;
    private context: vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.storage = new FingerprintStorage(context);
    }

    async initializeTrial(): Promise<TrialState> {
        let fingerprint = await this.storage.retrieveFingerprint();

        if (!fingerprint) {
            // Generate new fingerprint for new trial
            fingerprint = await this.generateNewFingerprint();
            await this.storage.storeFingerprint(fingerprint);
        }

        const existingTrial = await this.getTrialState(fingerprint.id);
        if (existingTrial) {
            return existingTrial;
        }

        // Create new trial state
        const trialState: TrialState = {
            isTrialActive: true,
            trialStartDate: new Date().toISOString(),
            trialEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
            usageCount: 1,
            fingerprintId: fingerprint.id,
            bypassAttempts: 0,
        };

        await this.storeTrialState(trialState);
        return trialState;
    }

    async checkTrialStatus(): Promise<{ valid: boolean; daysRemaining: number }> {
        const fingerprint = await this.storage.retrieveFingerprint();
        if (!fingerprint) {
            return { valid: false, daysRemaining: 0 };
        }

        const trialState = await this.getTrialState(fingerprint.id);
        if (!trialState) {
            return { valid: false, daysRemaining: 0 };
        }

        const now = new Date();
        const endDate = new Date(trialState.trialEndDate);
        const daysRemaining = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000)));

        return {
            valid: trialState.isTrialActive && now < endDate,
            daysRemaining,
        };
    }

    async detectBypassAttempt(): Promise<boolean> {
        const fingerprint = await this.storage.retrieveFingerprint();
        if (!fingerprint) return false;

        // Check for signs of bypass attempts
        const stats = await this.storage.getStorageStats();
        const trialState = await this.getTrialState(fingerprint.id);

        if (!trialState) return false;

        // Detect if fingerprint was tampered with
        const currentFingerprint = await this.generateCurrentFingerprint();
        const similarity = this.calculateSimilarity(fingerprint, currentFingerprint);

        if (similarity < 0.7) {
            // Significant change detected - possible bypass attempt
            trialState.bypassAttempts++;
            await this.storeTrialState(trialState);
            return true;
        }

        return false;
    }

    private async generateNewFingerprint(): Promise<PrivacyFingerprint> {
        // This would call the Rust fingerprint generator
        // For now, return a mock fingerprint
        return {
            id: this.generateId(),
            version: '1.0.0',
            created_at: new Date().toISOString(),
            privacy_level: 'High',
            rotation_enabled: true,
            next_rotation: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
            device_id: this.generateId(),
            hardware_signature: {
                cpu_signature: 'mock-cpu',
                memory_class: '8-16GB',
                architecture: 'x64',
                hardware_uuid_hash: this.generateId(),
                performance_class: 'high',
            },
            system_signature: {
                os_family: process.platform,
                os_version_class: 'modern',
                locale_region: 'US',
                timezone_offset: new Date().getTimezoneOffset(),
                hostname_hash: this.generateId(),
                username_hash: this.generateId(),
            },
            vscode_signature: {
                version_class: '1.80-1.90',
                machine_id_hash: this.generateId(),
                session_signature: vscode.env.sessionId,
                workspace_signature: this.generateId(),
                extensions_signature: this.generateId(),
            },
            network_signature: {
                interface_count: 1,
                mac_signature: this.generateId(),
                network_class: 'ethernet',
                connectivity_signature: this.generateId(),
            },
            privacy_metadata: {
                anonymization_applied: ['hashing', 'classification'],
                obfuscation_methods: ['sha256'],
                data_retention_policy: '24h-rotation',
                privacy_score: 0.8,
                tracking_resistance: 0.75,
            },
            fingerprint_hash: this.generateId(),
        };
    }

    private async generateCurrentFingerprint(): Promise<PrivacyFingerprint> {
        // Generate fingerprint based on current system state
        return this.generateNewFingerprint();
    }

    private calculateSimilarity(fp1: PrivacyFingerprint, fp2: PrivacyFingerprint): number {
        // Simple similarity calculation
        let matches = 0;
        let total = 0;

        if (fp1.hardware_signature.cpu_signature === fp2.hardware_signature.cpu_signature) matches++;
        total++;

        if (fp1.hardware_signature.memory_class === fp2.hardware_signature.memory_class) matches++;
        total++;

        if (fp1.system_signature.os_family === fp2.system_signature.os_family) matches++;
        total++;

        return matches / total;
    }

    private async getTrialState(fingerprintId: string): Promise<TrialState | null> {
        const stateData = this.context.globalState.get<string>(`trial_state_${fingerprintId}`);
        return stateData ? JSON.parse(stateData) : null;
    }

    private async storeTrialState(state: TrialState): Promise<void> {
        await this.context.globalState.update(`trial_state_${state.fingerprintId}`, JSON.stringify(state));
    }

    private generateId(): string {
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }
}
"#;
        Ok(bindings.to_string())
    }
}

impl Default for APIBindings {
    fn default() -> Self {
        Self {
            use_vscode_storage_api: true,
            use_workspace_state_api: true,
            use_system_info_api: true,
            use_lifecycle_events: true,
        }
    }
}

impl Default for IntegrationPrivacySettings {
    fn default() -> Self {
        Self {
            encrypt_stored_data: true,
            use_secure_storage: true,
            enable_data_expiration: true,
            anonymize_telemetry: true,
        }
    }
}

impl Default for TrialPreventionConfig {
    fn default() -> Self {
        Self {
            enable_fingerprint_tracking: true,
            enable_bypass_detection: true,
            enable_persistence_monitoring: true,
            trial_duration_days: 30,
            max_trial_resets: 3,
        }
    }
}