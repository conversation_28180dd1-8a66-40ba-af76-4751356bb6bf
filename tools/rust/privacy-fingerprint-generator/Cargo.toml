[package]
name = "privacy-fingerprint-generator"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
homepage.workspace = true
documentation.workspace = true
keywords.workspace = true
categories.workspace = true
description = "Privacy-focused fingerprint generation tool for VS Code extension security research"

[dependencies]
# Workspace dependencies
shared = { path = "../shared" }
serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
clap = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
sha2 = { workspace = true }
uuid = { workspace = true }
hex = { workspace = true }
dirs = { workspace = true }
chrono = { workspace = true }
colored = { workspace = true }

# Additional dependencies for system fingerprinting
sysinfo = "0.30"
machine-uid = "0.5"
mac_address = "1.1"
local-ip-address = "0.6"
hostname = "0.4"
whoami = "1.4"

# Crypto and privacy
ring = "0.17"
rand = "0.8"
base64 = "0.22"

# File operations
tempfile = { workspace = true }

[[bin]]
name = "privacy-fingerprint-generator"
path = "src/main.rs"
