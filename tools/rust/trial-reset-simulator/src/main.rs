//! Trial Reset Simulator
//!
//! An educational tool that simulates common trial bypass attempts to demonstrate
//! why they fail with modern VS Code extensions. This tool helps security researchers
//! and developers understand trial protection mechanisms.

use anyhow::Result;
use clap::{Parser, Subcommand};
use colored::Colorize;
use shared::{
    init, <PERSON><PERSON><PERSON><PERSON>, MockStorage, FingerprintCollector, MockExtensionData,
    ToolConfig, OutputFormat, display_results, show_info, show_warning, show_success,
    display_educational_info,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Parser)]
#[command(name = "trial-reset-simulator")]
#[command(about = "Educational tool for simulating trial reset attempts")]
#[command(long_about = "
This tool simulates common trial bypass attempts to demonstrate why they fail 
with modern VS Code extensions. It's designed for educational and security 
research purposes only.

⚠️  EDUCATIONAL USE ONLY ⚠️
This tool does NOT actually bypass any software. It simulates bypass attempts
using mock data to help understand protection mechanisms.
")]
struct Cli {
    #[command(subcommand)]
    command: Commands,

    /// Output format
    #[arg(short, long, default_value = "console")]
    format: String,

    /// Verbose output
    #[arg(short, long)]
    verbose: bool,

    /// Save results to file
    #[arg(short, long)]
    output: Option<String>,
}

#[derive(Subcommand)]
enum Commands {
    /// Simulate storage clearing attempts
    ClearStorage {
        /// Simulate clearing all storage types
        #[arg(long)]
        all: bool,
        /// Specific storage types to clear
        #[arg(long)]
        storage_types: Vec<String>,
    },
    /// Simulate system identifier modification
    ModifyIdentifiers {
        /// Simulate machine ID changes
        #[arg(long)]
        machine_id: bool,
        /// Simulate hardware changes
        #[arg(long)]
        hardware: bool,
        /// Simulate network changes
        #[arg(long)]
        network: bool,
    },
    /// Simulate reinstallation attempts
    Reinstall {
        /// Simulate clean reinstall
        #[arg(long)]
        clean: bool,
        /// Simulate different installation path
        #[arg(long)]
        different_path: bool,
    },
    /// Run all simulation scenarios
    SimulateAll,
    /// Show educational information about trial protection
    Explain {
        /// Topic to explain
        topic: String,
    },
}

#[derive(Debug, Serialize, Deserialize)]
struct SimulationResult {
    scenario: String,
    description: String,
    attempted_methods: Vec<String>,
    success_rate: f64,
    remaining_protections: Vec<String>,
    educational_notes: Vec<String>,
    why_it_fails: String,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize shared library with ethics check
    init()?;

    let cli = Cli::parse();

    // Parse output format
    let output_format = match cli.format.as_str() {
        "json" => OutputFormat::Json,
        "markdown" => OutputFormat::Markdown,
        "html" => OutputFormat::Html,
        _ => OutputFormat::Console,
    };

    let config = ToolConfig {
        tool_name: "trial-reset-simulator".to_string(),
        version: shared::VERSION.to_string(),
        output_format,
        verbose: cli.verbose,
        mock_mode: true,
        educational_mode: true,
    };

    // Log usage for educational analysis
    EthicsChecker::log_usage("trial-reset-simulator", "simulation");

    let result = match cli.command {
        Commands::ClearStorage { all, storage_types } => {
            simulate_storage_clearing(all, storage_types).await?
        }
        Commands::ModifyIdentifiers { machine_id, hardware, network } => {
            simulate_identifier_modification(machine_id, hardware, network).await?
        }
        Commands::Reinstall { clean, different_path } => {
            simulate_reinstallation(clean, different_path).await?
        }
        Commands::SimulateAll => {
            let results = simulate_all_scenarios().await?;
            // For display purposes, create a summary result
            SimulationResult {
                scenario: "All Scenarios".to_string(),
                description: "Comprehensive simulation of all bypass attempts".to_string(),
                attempted_methods: vec!["Storage clearing".to_string(), "Identifier modification".to_string(), "Reinstallation".to_string()],
                success_rate: results.iter().map(|r| r.success_rate).sum::<f64>() / results.len() as f64,
                remaining_protections: vec!["All protections active".to_string()],
                educational_notes: vec![format!("Tested {} scenarios", results.len())],
                why_it_fails: "Multiple protection layers prevent bypass attempts".to_string(),
            }
        }
        Commands::Explain { topic } => {
            explain_protection_mechanism(&topic);
            return Ok(());
        }
    };

    // Display results
    display_results(&result, &config)?;

    // Save to file if requested
    if let Some(output_path) = cli.output {
        shared::save_results(&result, &std::path::Path::new(&output_path), &config.output_format)?;
    }

    Ok(())
}

async fn simulate_storage_clearing(all: bool, storage_types: Vec<String>) -> Result<SimulationResult> {
    show_info("🧹 Simulating storage clearing attempts...");
    
    EthicsChecker::verify_educational_operation("simulate storage clearing")?;

    let mut storage = MockStorage::new(true);
    let clearing_result = storage.simulate_storage_clearing();

    let attempted_methods = if all {
        vec![
            "Clear VS Code global state".to_string(),
            "Clear workspace state".to_string(),
            "Clear extension secrets".to_string(),
            "Clear file system storage".to_string(),
            "Clear browser localStorage".to_string(),
        ]
    } else {
        storage_types.iter().map(|t| format!("Clear {}", t)).collect()
    };

    let remaining_protections = vec![
        "Server-side trial tracking".to_string(),
        "Hardware fingerprint validation".to_string(),
        "Encrypted secrets in OS keychain".to_string(),
        "Machine ID persistence".to_string(),
        "Network-based validation".to_string(),
    ];

    let educational_notes = vec![
        "Modern extensions use multiple storage layers for redundancy".to_string(),
        "Server-side validation prevents local bypass attempts".to_string(),
        "Hardware fingerprinting survives software reinstallation".to_string(),
        "OS-level encrypted storage is difficult to clear".to_string(),
    ];

    show_warning(&format!(
        "Storage clearing effectiveness: {:.1}%", 
        clearing_result.effectiveness * 100.0
    ));

    Ok(SimulationResult {
        scenario: "Storage Clearing Simulation".to_string(),
        description: "Simulates attempts to reset trial by clearing local storage".to_string(),
        attempted_methods,
        success_rate: clearing_result.effectiveness,
        remaining_protections,
        educational_notes,
        why_it_fails: "Modern extensions store trial data in multiple persistent locations and validate against server-side records. Even if local storage is cleared, the server remembers the device fingerprint.".to_string(),
    })
}

async fn simulate_identifier_modification(machine_id: bool, hardware: bool, network: bool) -> Result<SimulationResult> {
    show_info("🔧 Simulating system identifier modification...");
    
    EthicsChecker::verify_educational_operation("simulate identifier modification")?;

    let collector = FingerprintCollector::new(true);
    let original_fingerprint = collector.collect_fingerprint()?;
    let modified_fingerprint = collector.collect_fingerprint()?; // Would be different in real scenario

    let mut attempted_methods = Vec::new();
    if machine_id {
        attempted_methods.push("Modify VS Code machine ID".to_string());
    }
    if hardware {
        attempted_methods.push("Spoof hardware information".to_string());
    }
    if network {
        attempted_methods.push("Change MAC addresses".to_string());
    }

    let comparison = collector.compare_fingerprints(&original_fingerprint, &modified_fingerprint);

    let remaining_protections = vec![
        "Multi-point fingerprint validation".to_string(),
        "Hardware attestation".to_string(),
        "Behavioral analysis".to_string(),
        "Server-side device tracking".to_string(),
    ];

    let educational_notes = vec![
        "Extensions use 12+ data points for device identification".to_string(),
        "Changing one identifier doesn't reset the fingerprint".to_string(),
        "Hardware information is difficult to spoof convincingly".to_string(),
        "Server-side validation detects fingerprint inconsistencies".to_string(),
    ];

    show_warning(&format!(
        "Fingerprint similarity after modification: {:.1}%", 
        comparison.similarity_score * 100.0
    ));

    Ok(SimulationResult {
        scenario: "Identifier Modification Simulation".to_string(),
        description: "Simulates attempts to reset trial by modifying system identifiers".to_string(),
        attempted_methods,
        success_rate: 1.0 - comparison.similarity_score, // Lower similarity = higher "success"
        remaining_protections,
        educational_notes,
        why_it_fails: "Extensions create composite fingerprints from multiple system identifiers. Changing individual components doesn't significantly alter the overall fingerprint, and servers can detect partial modifications.".to_string(),
    })
}

async fn simulate_reinstallation(clean: bool, different_path: bool) -> Result<SimulationResult> {
    show_info("📦 Simulating reinstallation attempts...");
    
    EthicsChecker::verify_educational_operation("simulate reinstallation")?;

    let mut attempted_methods = vec!["Uninstall and reinstall extension".to_string()];
    
    if clean {
        attempted_methods.push("Clean VS Code installation".to_string());
    }
    if different_path {
        attempted_methods.push("Install in different directory".to_string());
    }

    // Simulate that some data persists even after reinstallation
    let persistence_factors = vec![
        "VS Code machine ID persists across installations".to_string(),
        "Hardware fingerprint remains unchanged".to_string(),
        "Server-side trial records persist".to_string(),
        "OS-level encrypted storage survives reinstallation".to_string(),
    ];

    let educational_notes = vec![
        "VS Code generates a persistent machine ID stored in user profile".to_string(),
        "Hardware fingerprints are independent of software installation".to_string(),
        "Server-side validation prevents trial reset through reinstallation".to_string(),
        "Some storage locations survive complete software removal".to_string(),
    ];

    // Reinstallation typically has very low success rate against modern protections
    let success_rate = 0.1;

    show_warning(&format!("Reinstallation bypass success rate: {:.1}%", success_rate * 100.0));

    Ok(SimulationResult {
        scenario: "Reinstallation Simulation".to_string(),
        description: "Simulates attempts to reset trial through software reinstallation".to_string(),
        attempted_methods,
        success_rate,
        remaining_protections: persistence_factors,
        educational_notes,
        why_it_fails: "Modern extensions use persistent identifiers that survive software reinstallation. The VS Code machine ID, hardware fingerprint, and server-side records all persist across reinstalls.".to_string(),
    })
}

async fn simulate_all_scenarios() -> Result<Vec<SimulationResult>> {
    show_info("🎯 Running comprehensive trial bypass simulation...");
    
    let mut results = Vec::new();

    // Run all simulation types
    results.push(simulate_storage_clearing(true, vec![]).await?);
    results.push(simulate_identifier_modification(true, true, true).await?);
    results.push(simulate_reinstallation(true, true).await?);

    // Calculate overall effectiveness
    let average_success: f64 = results.iter().map(|r| r.success_rate).sum::<f64>() / results.len() as f64;
    
    show_warning(&format!(
        "Overall bypass success rate across all methods: {:.1}%", 
        average_success * 100.0
    ));

    show_success("✅ Educational simulation complete!");
    
    display_educational_info(
        "Why Trial Bypasses Fail",
        "Modern VS Code extensions use sophisticated multi-layered protection mechanisms that make trial bypasses extremely difficult and unreliable.",
        &[
            "Server-side validation prevents local manipulation",
            "Hardware fingerprinting survives software changes", 
            "Multiple persistent storage locations provide redundancy",
            "Behavioral analysis detects suspicious patterns",
            "Encrypted storage protects sensitive trial data"
        ]
    );

    Ok(results)
}

fn explain_protection_mechanism(topic: &str) {
    match topic.to_lowercase().as_str() {
        "fingerprinting" => {
            display_educational_info(
                "System Fingerprinting",
                "Extensions collect multiple system identifiers to create a unique device fingerprint that persists across software changes.",
                &[
                    "VS Code machine ID (persistent across installations)",
                    "Hardware specifications (CPU, memory, architecture)",
                    "Network interface MAC addresses",
                    "Operating system details and version",
                    "Hostname and user information"
                ]
            );
        },
        "storage" => {
            display_educational_info(
                "Multi-Layer Storage",
                "Extensions use multiple storage mechanisms to ensure trial data persists across various bypass attempts.",
                &[
                    "VS Code global state (survives workspace changes)",
                    "Encrypted secrets storage (OS keychain protection)",
                    "File system storage (extension directory)",
                    "Server-side records (immune to local changes)",
                    "Workspace-specific state (project-level tracking)"
                ]
            );
        },
        "validation" => {
            display_educational_info(
                "Server-Side Validation",
                "Extensions validate trial status against remote servers, making local bypass attempts ineffective.",
                &[
                    "Periodic trial status checks",
                    "Device fingerprint validation",
                    "Usage pattern analysis",
                    "Anomaly detection for suspicious behavior",
                    "Real-time subscription verification"
                ]
            );
        },
        _ => {
            show_info("Available topics: fingerprinting, storage, validation");
        }
    }
}
