[package]
name = "trial-reset-simulator"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
homepage.workspace = true
documentation.workspace = true
keywords.workspace = true
categories.workspace = true
description = "Educational tool for simulating trial reset attempts and understanding why they fail"

[[bin]]
name = "trial-reset-simulator"
path = "src/main.rs"

[dependencies]
shared = { path = "../shared" }
clap.workspace = true
anyhow.workspace = true
serde.workspace = true
serde_json.workspace = true
tokio.workspace = true
tracing.workspace = true
colored.workspace = true
uuid.workspace = true
chrono.workspace = true

[dev-dependencies]
tempfile.workspace = true
proptest.workspace = true
