[package]
name = "persistence-monitor"
version = "0.1.0"
edition = "2021"
description = "Educational tool for long-term analysis of VS Code extension trial persistence mechanisms"
authors = ["Security Research Team"]
license = "MIT"

[[bin]]
name = "persistence-monitor"
path = "src/main.rs"

[dependencies]
shared = { path = "../shared" }
clap = { version = "4.0", features = ["derive"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4"] }
colored = "2.0"
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"
plotters = "0.3"
csv = "1.2"
dirs = "5.0"
notify = "6.0"
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }
reqwest = { version = "0.11", features = ["json"] }

[dev-dependencies]
tempfile = "3.0"
tokio-test = "0.4"
