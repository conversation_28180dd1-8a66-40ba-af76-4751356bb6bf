use anyhow::Result;
use chrono::{DateTime, Utc};
use serde_json;
use sqlx::{SqlitePool, Row};
use std::path::Path;

use crate::MonitoringSnapshot;

#[derive(Clone)]
pub struct MonitoringDatabase {
    pool: SqlitePool,
}

impl MonitoringDatabase {
    pub async fn new(db_path: &Path) -> Result<Self> {
        let database_url = format!("sqlite:{}", db_path.display());
        let pool = SqlitePool::connect(&database_url).await?;
        
        Ok(Self { pool })
    }
    
    pub async fn initialize_schema(&self) -> Result<()> {
        // Create snapshots table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS snapshots (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                session_id TEXT NOT NULL,
                fingerprint_data TEXT NOT NULL,
                storage_entries TEXT NOT NULL,
                trial_data TEXT,
                system_state TEXT NOT NULL,
                validation_events TEXT NOT NULL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(&self.pool)
        .await?;
        
        // Create fingerprint_changes table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS fingerprint_changes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                session_id TEXT NOT NULL,
                component TEXT NOT NULL,
                old_value TEXT,
                new_value TEXT NOT NULL,
                change_reason TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(&self.pool)
        .await?;
        
        // Create storage_events table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS storage_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                session_id TEXT NOT NULL,
                event_type TEXT NOT NULL,
                storage_location TEXT NOT NULL,
                key_name TEXT NOT NULL,
                old_value TEXT,
                new_value TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(&self.pool)
        .await?;
        
        // Create validation_events table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS validation_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                session_id TEXT NOT NULL,
                event_type TEXT NOT NULL,
                endpoint TEXT,
                success BOOLEAN NOT NULL,
                response_time_ms INTEGER,
                data_transmitted BOOLEAN NOT NULL,
                fingerprint_sent BOOLEAN NOT NULL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(&self.pool)
        .await?;
        
        // Create system_events table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS system_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                session_id TEXT NOT NULL,
                event_type TEXT NOT NULL,
                description TEXT NOT NULL,
                impact_score REAL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(&self.pool)
        .await?;
        
        // Create analysis_results table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS analysis_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                analysis_timestamp TEXT NOT NULL,
                analysis_type TEXT NOT NULL,
                period_start TEXT NOT NULL,
                period_end TEXT NOT NULL,
                results_data TEXT NOT NULL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(&self.pool)
        .await?;
        
        // Create indexes for better query performance
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_snapshots_timestamp ON snapshots(timestamp)")
            .execute(&self.pool)
            .await?;
        
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_snapshots_session ON snapshots(session_id)")
            .execute(&self.pool)
            .await?;
        
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_validation_timestamp ON validation_events(timestamp)")
            .execute(&self.pool)
            .await?;
        
        Ok(())
    }
    
    pub async fn store_snapshot(&self, snapshot: &MonitoringSnapshot) -> Result<i64> {
        let fingerprint_json = serde_json::to_string(&snapshot.fingerprint)?;
        let storage_entries_json = serde_json::to_string(&snapshot.storage_entries)?;
        let trial_data_json = snapshot.trial_data.as_ref()
            .map(|td| serde_json::to_string(td))
            .transpose()?;
        let system_state_json = serde_json::to_string(&snapshot.system_state)?;
        let validation_events_json = serde_json::to_string(&snapshot.validation_events)?;
        
        let result = sqlx::query(
            r#"
            INSERT INTO snapshots (
                timestamp, session_id, fingerprint_data, storage_entries,
                trial_data, system_state, validation_events
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            "#,
        )
        .bind(snapshot.timestamp.to_rfc3339())
        .bind(&snapshot.session_id)
        .bind(fingerprint_json)
        .bind(storage_entries_json)
        .bind(trial_data_json)
        .bind(system_state_json)
        .bind(validation_events_json)
        .execute(&self.pool)
        .await?;
        
        // Store individual validation events
        for event in &snapshot.validation_events {
            sqlx::query(
                r#"
                INSERT INTO validation_events (
                    timestamp, session_id, event_type, endpoint, success,
                    data_transmitted, fingerprint_sent
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
                "#,
            )
            .bind(event.timestamp.to_rfc3339())
            .bind(&snapshot.session_id)
            .bind(&event.event_type)
            .bind(&event.endpoint)
            .bind(event.success)
            .bind(event.data_transmitted)
            .bind(event.fingerprint_sent)
            .execute(&self.pool)
            .await?;
        }
        
        Ok(result.last_insert_rowid())
    }
    
    pub async fn get_all_snapshots(&self) -> Result<Vec<MonitoringSnapshot>> {
        let rows = sqlx::query(
            "SELECT timestamp, session_id, fingerprint_data, storage_entries, trial_data, system_state, validation_events FROM snapshots ORDER BY timestamp"
        )
        .fetch_all(&self.pool)
        .await?;
        
        let mut snapshots = Vec::new();
        
        for row in rows {
            let timestamp_str: String = row.get("timestamp");
            let timestamp = DateTime::parse_from_rfc3339(&timestamp_str)?
                .with_timezone(&Utc);
            
            let session_id: String = row.get("session_id");
            
            let fingerprint_json: String = row.get("fingerprint_data");
            let fingerprint = serde_json::from_str(&fingerprint_json)?;
            
            let storage_entries_json: String = row.get("storage_entries");
            let storage_entries = serde_json::from_str(&storage_entries_json)?;
            
            let trial_data_json: Option<String> = row.get("trial_data");
            let trial_data = trial_data_json
                .map(|json| serde_json::from_str(&json))
                .transpose()?;
            
            let system_state_json: String = row.get("system_state");
            let system_state = serde_json::from_str(&system_state_json)?;
            
            let validation_events_json: String = row.get("validation_events");
            let validation_events = serde_json::from_str(&validation_events_json)?;
            
            snapshots.push(MonitoringSnapshot {
                timestamp,
                session_id,
                fingerprint,
                storage_entries,
                trial_data,
                system_state,
                validation_events,
            });
        }
        
        Ok(snapshots)
    }
    
    pub async fn get_snapshots_in_range(
        &self,
        start: DateTime<Utc>,
        end: DateTime<Utc>,
    ) -> Result<Vec<MonitoringSnapshot>> {
        let rows = sqlx::query(
            "SELECT timestamp, session_id, fingerprint_data, storage_entries, trial_data, system_state, validation_events FROM snapshots WHERE timestamp BETWEEN ? AND ? ORDER BY timestamp"
        )
        .bind(start.to_rfc3339())
        .bind(end.to_rfc3339())
        .fetch_all(&self.pool)
        .await?;
        
        let mut snapshots = Vec::new();
        
        for row in rows {
            let timestamp_str: String = row.get("timestamp");
            let timestamp = DateTime::parse_from_rfc3339(&timestamp_str)?
                .with_timezone(&Utc);
            
            let session_id: String = row.get("session_id");
            
            let fingerprint_json: String = row.get("fingerprint_data");
            let fingerprint = serde_json::from_str(&fingerprint_json)?;
            
            let storage_entries_json: String = row.get("storage_entries");
            let storage_entries = serde_json::from_str(&storage_entries_json)?;
            
            let trial_data_json: Option<String> = row.get("trial_data");
            let trial_data = trial_data_json
                .map(|json| serde_json::from_str(&json))
                .transpose()?;
            
            let system_state_json: String = row.get("system_state");
            let system_state = serde_json::from_str(&system_state_json)?;
            
            let validation_events_json: String = row.get("validation_events");
            let validation_events = serde_json::from_str(&validation_events_json)?;
            
            snapshots.push(MonitoringSnapshot {
                timestamp,
                session_id,
                fingerprint,
                storage_entries,
                trial_data,
                system_state,
                validation_events,
            });
        }
        
        Ok(snapshots)
    }
    
    pub async fn get_latest_snapshot_before(
        &self,
        timestamp: &DateTime<Utc>,
    ) -> Result<Option<MonitoringSnapshot>> {
        let row = sqlx::query(
            "SELECT timestamp, session_id, fingerprint_data, storage_entries, trial_data, system_state, validation_events FROM snapshots WHERE timestamp < ? ORDER BY timestamp DESC LIMIT 1"
        )
        .bind(timestamp.to_rfc3339())
        .fetch_optional(&self.pool)
        .await?;
        
        if let Some(row) = row {
            let timestamp_str: String = row.get("timestamp");
            let timestamp = DateTime::parse_from_rfc3339(&timestamp_str)?
                .with_timezone(&Utc);
            
            let session_id: String = row.get("session_id");
            
            let fingerprint_json: String = row.get("fingerprint_data");
            let fingerprint = serde_json::from_str(&fingerprint_json)?;
            
            let storage_entries_json: String = row.get("storage_entries");
            let storage_entries = serde_json::from_str(&storage_entries_json)?;
            
            let trial_data_json: Option<String> = row.get("trial_data");
            let trial_data = trial_data_json
                .map(|json| serde_json::from_str(&json))
                .transpose()?;
            
            let system_state_json: String = row.get("system_state");
            let system_state = serde_json::from_str(&system_state_json)?;
            
            let validation_events_json: String = row.get("validation_events");
            let validation_events = serde_json::from_str(&validation_events_json)?;
            
            Ok(Some(MonitoringSnapshot {
                timestamp,
                session_id,
                fingerprint,
                storage_entries,
                trial_data,
                system_state,
                validation_events,
            }))
        } else {
            Ok(None)
        }
    }
    
    pub async fn get_snapshot_count(&self) -> Result<i64> {
        let row = sqlx::query("SELECT COUNT(*) as count FROM snapshots")
            .fetch_one(&self.pool)
            .await?;
        
        Ok(row.get("count"))
    }
    
    pub async fn get_session_count(&self) -> Result<i64> {
        let row = sqlx::query("SELECT COUNT(DISTINCT session_id) as count FROM snapshots")
            .fetch_one(&self.pool)
            .await?;
        
        Ok(row.get("count"))
    }
    
    pub async fn cleanup_old_data(&self, days_to_keep: i32) -> Result<u64> {
        let cutoff_date = Utc::now() - chrono::Duration::days(days_to_keep as i64);
        
        let result = sqlx::query("DELETE FROM snapshots WHERE timestamp < ?")
            .bind(cutoff_date.to_rfc3339())
            .execute(&self.pool)
            .await?;
        
        // Also cleanup related tables
        sqlx::query("DELETE FROM validation_events WHERE timestamp < ?")
            .bind(cutoff_date.to_rfc3339())
            .execute(&self.pool)
            .await?;
        
        sqlx::query("DELETE FROM storage_events WHERE timestamp < ?")
            .bind(cutoff_date.to_rfc3339())
            .execute(&self.pool)
            .await?;
        
        sqlx::query("DELETE FROM system_events WHERE timestamp < ?")
            .bind(cutoff_date.to_rfc3339())
            .execute(&self.pool)
            .await?;
        
        Ok(result.rows_affected())
    }
}
