use anyhow::Result;
use chrono::{DateTime, Utc};
use colored::Colorize;
use log::{info, warn, error};
use notify::{Watcher, RecursiveMode, Event, EventKind};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::Mutex;
use tokio::time::{interval, sleep};
use uuid::Uuid;

use shared::{FingerprintCollector, SystemFingerprint, StorageLocation, StorageEntry};
use crate::database::MonitoringDatabase;
use crate::{MonitoringSnapshot, TrialData, SystemState, ValidationEvent};

pub struct ContinuousMonitor {
    extension_id: String,
    interval: Duration,
    database: MonitoringDatabase,
    session_id: String,
    fingerprint_collector: FingerprintCollector,
    storage_watcher: Option<Arc<Mutex<notify::RecommendedWatcher>>>,
    monitoring_active: Arc<Mutex<bool>>,
}

impl ContinuousMonitor {
    pub fn new(extension_id: String, interval: Duration, database: MonitoringDatabase) -> Self {
        Self {
            extension_id,
            interval,
            database,
            session_id: Uuid::new_v4().to_string(),
            fingerprint_collector: FingerprintCollector::new(),
            storage_watcher: None,
            monitoring_active: Arc::new(Mutex::new(false)),
        }
    }
    
    pub fn extension_id(&self) -> &str {
        &self.extension_id
    }
    
    pub async fn run_indefinitely(&self) -> Result<()> {
        info!("Starting indefinite monitoring for extension: {}", self.extension_id);
        
        *self.monitoring_active.lock().await = true;
        
        // Setup file system monitoring
        self.setup_storage_monitoring().await?;
        
        // Start periodic data collection
        let mut interval_timer = interval(self.interval);
        let mut snapshot_count = 0u64;
        
        println!("{}", "🔄 Monitoring started - Press Ctrl+C to stop".green());
        println!();
        
        loop {
            interval_timer.tick().await;
            
            if !*self.monitoring_active.lock().await {
                break;
            }
            
            snapshot_count += 1;
            
            match self.collect_snapshot().await {
                Ok(snapshot) => {
                    self.database.store_snapshot(&snapshot).await?;
                    
                    println!("{} Snapshot #{} collected at {}", 
                        "✅".green(), 
                        snapshot_count.to_string().cyan(),
                        snapshot.timestamp.format("%Y-%m-%d %H:%M:%S UTC").to_string().yellow()
                    );
                    
                    self.display_snapshot_summary(&snapshot);
                    
                    // Analyze persistence changes
                    if snapshot_count > 1 {
                        self.analyze_changes_since_last(&snapshot).await?;
                    }
                }
                Err(e) => {
                    error!("Failed to collect snapshot: {}", e);
                    println!("{} Failed to collect snapshot: {}", "❌".red(), e);
                }
            }
            
            println!("{}", "─".repeat(60).dimmed());
        }
        
        Ok(())
    }
    
    pub async fn run_for_duration(&self, duration: Duration) -> Result<()> {
        info!("Starting monitoring for {} seconds", duration.as_secs());
        
        *self.monitoring_active.lock().await = true;
        
        // Setup file system monitoring
        self.setup_storage_monitoring().await?;
        
        let start_time = std::time::Instant::now();
        let mut interval_timer = interval(self.interval);
        let mut snapshot_count = 0u64;
        
        println!("{} Monitoring for {} minutes", 
            "🔄".green(), 
            (duration.as_secs() / 60).to_string().cyan()
        );
        println!();
        
        while start_time.elapsed() < duration {
            interval_timer.tick().await;
            
            if !*self.monitoring_active.lock().await {
                break;
            }
            
            snapshot_count += 1;
            let remaining = duration - start_time.elapsed();
            
            match self.collect_snapshot().await {
                Ok(snapshot) => {
                    self.database.store_snapshot(&snapshot).await?;
                    
                    println!("{} Snapshot #{} | Remaining: {}m {}s", 
                        "✅".green(), 
                        snapshot_count.to_string().cyan(),
                        (remaining.as_secs() / 60).to_string().yellow(),
                        (remaining.as_secs() % 60).to_string().yellow()
                    );
                    
                    self.display_snapshot_summary(&snapshot);
                    
                    if snapshot_count > 1 {
                        self.analyze_changes_since_last(&snapshot).await?;
                    }
                }
                Err(e) => {
                    error!("Failed to collect snapshot: {}", e);
                    println!("{} Failed to collect snapshot: {}", "❌".red(), e);
                }
            }
            
            println!("{}", "─".repeat(60).dimmed());
        }
        
        println!("{} Monitoring completed after {} snapshots", 
            "🏁".green(), 
            snapshot_count.to_string().cyan()
        );
        
        Ok(())
    }
    
    async fn collect_snapshot(&self) -> Result<MonitoringSnapshot> {
        let timestamp = Utc::now();
        
        // Collect system fingerprint
        let fingerprint = self.fingerprint_collector.collect_fingerprint().await?;
        
        // Collect storage entries
        let storage_entries = self.collect_storage_entries().await?;
        
        // Extract trial data
        let trial_data = self.extract_trial_data(&storage_entries);
        
        // Collect system state
        let system_state = self.collect_system_state().await?;
        
        // Collect validation events (simulated for educational purposes)
        let validation_events = self.collect_validation_events().await?;
        
        Ok(MonitoringSnapshot {
            timestamp,
            session_id: self.session_id.clone(),
            fingerprint,
            storage_entries,
            trial_data,
            system_state,
            validation_events,
        })
    }
    
    async fn collect_storage_entries(&self) -> Result<Vec<StorageEntry>> {
        // In a real implementation, this would scan actual VS Code storage locations
        // For educational purposes, we'll simulate realistic storage entries
        
        let mut entries = Vec::new();
        
        // Simulate global state entries
        entries.push(StorageEntry {
            key: "trial_info".to_string(),
            value: serde_json::json!({
                "trial_id": "fb8b30f3-6883-445e-b2be-9572c8217719",
                "days_remaining": 12,
                "expires_at": "2025-06-18T22:08:51.877625Z",
                "usage_count": 45,
                "features_used": ["completion", "chat", "analysis"]
            }),
            storage_type: "GlobalState".to_string(),
            encrypted: false,
            persistent: true,
            created_at: Utc::now(),
            last_modified: Utc::now(),
        });
        
        // Simulate subscription info
        entries.push(StorageEntry {
            key: "subscription_info".to_string(),
            value: serde_json::json!({
                "status": "Trial",
                "plan": "premium",
                "trial_end": "2025-06-19T22:08:51.877617Z",
                "features": ["ai_completion", "advanced_analysis"]
            }),
            storage_type: "GlobalState".to_string(),
            encrypted: false,
            persistent: true,
            created_at: Utc::now(),
            last_modified: Utc::now(),
        });
        
        // Simulate encrypted secrets
        entries.push(StorageEntry {
            key: "machine_fingerprint".to_string(),
            value: serde_json::json!("encrypted_fingerprint_hash_data"),
            storage_type: "Secrets".to_string(),
            encrypted: true,
            persistent: true,
            created_at: Utc::now(),
            last_modified: Utc::now(),
        });
        
        Ok(entries)
    }
    
    fn extract_trial_data(&self, storage_entries: &[StorageEntry]) -> Option<TrialData> {
        // Extract trial information from storage entries
        let trial_entry = storage_entries.iter()
            .find(|entry| entry.key == "trial_info")?;
        
        let trial_value = trial_entry.value.as_object()?;
        
        Some(TrialData {
            trial_id: trial_value.get("trial_id")
                .and_then(|v| v.as_str())
                .map(|s| s.to_string()),
            status: "Active".to_string(),
            days_remaining: trial_value.get("days_remaining")
                .and_then(|v| v.as_i64())
                .map(|n| n as i32),
            expires_at: trial_value.get("expires_at")
                .and_then(|v| v.as_str())
                .and_then(|s| DateTime::parse_from_rfc3339(s).ok())
                .map(|dt| dt.with_timezone(&Utc)),
            usage_count: trial_value.get("usage_count")
                .and_then(|v| v.as_i64())
                .map(|n| n as i32),
            features_used: trial_value.get("features_used")
                .and_then(|v| v.as_array())
                .map(|arr| arr.iter()
                    .filter_map(|v| v.as_str())
                    .map(|s| s.to_string())
                    .collect())
                .unwrap_or_default(),
            last_validation: Some(Utc::now()),
        })
    }
    
    async fn collect_system_state(&self) -> Result<SystemState> {
        // Collect current system state information
        Ok(SystemState {
            vscode_running: self.is_vscode_running().await,
            extension_active: self.is_extension_active().await,
            network_connected: self.check_network_connectivity().await,
            system_uptime: self.get_system_uptime().await,
            last_reboot: self.get_last_reboot_time().await,
        })
    }
    
    async fn collect_validation_events(&self) -> Result<Vec<ValidationEvent>> {
        // Simulate validation events for educational purposes
        let mut events = Vec::new();
        
        // Simulate periodic server validation
        events.push(ValidationEvent {
            timestamp: Utc::now(),
            event_type: "subscription_check".to_string(),
            endpoint: Some("https://api.augmentcode.com/subscription".to_string()),
            success: true,
            data_transmitted: true,
            fingerprint_sent: true,
        });
        
        Ok(events)
    }
    
    async fn setup_storage_monitoring(&self) -> Result<()> {
        // Setup file system monitoring for storage changes
        // This would monitor actual VS Code storage directories in a real implementation
        info!("Setting up storage monitoring for educational analysis");
        Ok(())
    }
    
    fn display_snapshot_summary(&self, snapshot: &MonitoringSnapshot) {
        println!("   📊 Trial Status: {}", 
            snapshot.trial_data.as_ref()
                .map(|t| format!("{} days remaining", t.days_remaining.unwrap_or(0)))
                .unwrap_or_else(|| "Unknown".to_string())
                .yellow()
        );
        
        println!("   💾 Storage Entries: {}", 
            snapshot.storage_entries.len().to_string().cyan()
        );
        
        println!("   🔍 Fingerprint Hash: {}", 
            snapshot.fingerprint.fingerprint_hash[..8].dimmed()
        );
        
        println!("   🌐 VS Code Running: {}", 
            if snapshot.system_state.vscode_running { "Yes".green() } else { "No".red() }
        );
    }
    
    async fn analyze_changes_since_last(&self, current: &MonitoringSnapshot) -> Result<()> {
        // Compare with previous snapshot to detect changes
        if let Some(previous) = self.database.get_latest_snapshot_before(&current.timestamp).await? {
            let changes = self.detect_changes(&previous, current);
            if !changes.is_empty() {
                println!("   🔄 Changes detected: {}", changes.join(", ").yellow());
            }
        }
        Ok(())
    }
    
    fn detect_changes(&self, previous: &MonitoringSnapshot, current: &MonitoringSnapshot) -> Vec<String> {
        let mut changes = Vec::new();
        
        // Check trial data changes
        if let (Some(prev_trial), Some(curr_trial)) = (&previous.trial_data, &current.trial_data) {
            if prev_trial.days_remaining != curr_trial.days_remaining {
                changes.push("trial_days".to_string());
            }
            if prev_trial.usage_count != curr_trial.usage_count {
                changes.push("usage_count".to_string());
            }
        }
        
        // Check storage entry changes
        if previous.storage_entries.len() != current.storage_entries.len() {
            changes.push("storage_entries".to_string());
        }
        
        // Check system state changes
        if previous.system_state.vscode_running != current.system_state.vscode_running {
            changes.push("vscode_state".to_string());
        }
        
        changes
    }
    
    // Helper methods for system state collection
    async fn is_vscode_running(&self) -> bool {
        // Check if VS Code processes are running
        true // Simulated for educational purposes
    }
    
    async fn is_extension_active(&self) -> bool {
        // Check if the specific extension is active
        true // Simulated for educational purposes
    }
    
    async fn check_network_connectivity(&self) -> bool {
        // Check network connectivity
        true // Simulated for educational purposes
    }
    
    async fn get_system_uptime(&self) -> Duration {
        // Get system uptime
        Duration::from_secs(3600) // Simulated
    }
    
    async fn get_last_reboot_time(&self) -> Option<DateTime<Utc>> {
        // Get last system reboot time
        Some(Utc::now() - chrono::Duration::hours(1)) // Simulated
    }
}

impl Drop for ContinuousMonitor {
    fn drop(&mut self) {
        // Cleanup monitoring resources
        info!("Cleaning up continuous monitor for extension: {}", self.extension_id);
    }
}
