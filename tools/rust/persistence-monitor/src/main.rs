use anyhow::Result;
use chrono::{DateTime, Utc};
use clap::{Parser, Subcommand};
use colored::Colorize;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::time::Duration;
use tokio::time::interval;

use shared::{
    init, <PERSON><PERSON><PERSON><PERSON>, FingerprintCollector, SystemFingerprint, 
    StorageLocation, StorageEntry, MockStorage
};

mod monitor;
mod persistence;
mod analysis;
mod reporting;
mod database;

use monitor::ContinuousMonitor;
use persistence::PersistenceAnalyzer;
use analysis::TrendAnalyzer;
use reporting::ReportGenerator;
use database::MonitoringDatabase;

#[derive(Parser)]
#[command(name = "persistence-monitor")]
#[command(about = "Educational tool for long-term VS Code extension trial persistence analysis")]
#[command(version = "1.0.0")]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Start continuous monitoring of trial persistence mechanisms
    Monitor {
        /// Extension to monitor
        #[arg(long)]
        extension: Option<String>,
        
        /// Monitoring interval in minutes
        #[arg(long, default_value = "60")]
        interval: u64,
        
        /// Duration to monitor in hours (0 = indefinite)
        #[arg(long, default_value = "0")]
        duration: u64,
        
        /// Output directory for monitoring data
        #[arg(long)]
        output_dir: Option<PathBuf>,
    },
    
    /// Analyze persistence patterns from collected data
    Analyze {
        /// Data directory to analyze
        #[arg(long)]
        data_dir: PathBuf,
        
        /// Analysis type
        #[arg(long, value_enum, default_value = "comprehensive")]
        analysis_type: AnalysisType,
        
        /// Generate visualization charts
        #[arg(long)]
        visualize: bool,
    },
    
    /// Generate educational research reports
    Report {
        /// Data directory
        #[arg(long)]
        data_dir: PathBuf,
        
        /// Report format
        #[arg(long, value_enum, default_value = "markdown")]
        format: ReportFormat,
        
        /// Output file
        #[arg(long)]
        output: Option<PathBuf>,
    },
    
    /// Explain persistence mechanisms for educational purposes
    Explain {
        /// Topic to explain
        #[arg(value_enum)]
        topic: ExplainTopic,
    },
    
    /// Initialize monitoring database and configuration
    Init {
        /// Database location
        #[arg(long)]
        db_path: Option<PathBuf>,
    },
}

#[derive(clap::ValueEnum, Clone, Debug)]
enum AnalysisType {
    Comprehensive,
    Persistence,
    Trends,
    Validation,
    Storage,
}

#[derive(clap::ValueEnum, Clone, Debug)]
enum ReportFormat {
    Markdown,
    Json,
    Csv,
    Html,
}

#[derive(clap::ValueEnum, Clone, Debug)]
enum ExplainTopic {
    Persistence,
    Monitoring,
    Storage,
    Validation,
    Trends,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MonitoringSnapshot {
    pub timestamp: DateTime<Utc>,
    pub session_id: String,
    pub fingerprint: SystemFingerprint,
    pub storage_entries: Vec<StorageEntry>,
    pub trial_data: Option<TrialData>,
    pub system_state: SystemState,
    pub validation_events: Vec<ValidationEvent>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TrialData {
    pub trial_id: Option<String>,
    pub status: String,
    pub days_remaining: Option<i32>,
    pub expires_at: Option<DateTime<Utc>>,
    pub usage_count: Option<i32>,
    pub features_used: Vec<String>,
    pub last_validation: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SystemState {
    pub vscode_running: bool,
    pub extension_active: bool,
    pub network_connected: bool,
    pub system_uptime: Duration,
    pub last_reboot: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ValidationEvent {
    pub timestamp: DateTime<Utc>,
    pub event_type: String,
    pub endpoint: Option<String>,
    pub success: bool,
    pub data_transmitted: bool,
    pub fingerprint_sent: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PersistenceMetrics {
    pub storage_stability: f64,
    pub fingerprint_consistency: f64,
    pub trial_data_persistence: f64,
    pub validation_frequency: f64,
    pub recovery_effectiveness: f64,
}

#[tokio::main]
async fn main() -> Result<()> {
    env_logger::init();
    init();
    
    let cli = Cli::parse();
    
    // Ethics verification
    let ethics_checker = EthicsChecker::new();
    if !ethics_checker.verify_educational_use("persistence-monitor", "long-term-analysis").await? {
        return Ok(());
    }
    
    match cli.command {
        Commands::Monitor { extension, interval, duration, output_dir } => {
            monitor_persistence(extension, interval, duration, output_dir).await
        }
        Commands::Analyze { data_dir, analysis_type, visualize } => {
            analyze_persistence(data_dir, analysis_type, visualize).await
        }
        Commands::Report { data_dir, format, output } => {
            generate_report(data_dir, format, output).await
        }
        Commands::Explain { topic } => {
            explain_topic(topic).await
        }
        Commands::Init { db_path } => {
            initialize_monitoring(db_path).await
        }
    }
}

async fn monitor_persistence(
    extension: Option<String>,
    interval_minutes: u64,
    duration_hours: u64,
    output_dir: Option<PathBuf>,
) -> Result<()> {
    println!("{}", "🔍 Starting Long-Term Persistence Monitoring".blue().bold());
    println!("{}", "═══════════════════════════════════════════════".blue());
    
    let output_dir = output_dir.unwrap_or_else(|| {
        dirs::home_dir()
            .unwrap_or_else(|| PathBuf::from("."))
            .join(".persistence-monitor")
    });
    
    std::fs::create_dir_all(&output_dir)?;
    
    let db_path = output_dir.join("monitoring.db");
    let db = MonitoringDatabase::new(&db_path).await?;
    
    let monitor = ContinuousMonitor::new(
        extension.unwrap_or_else(|| "augment.vscode-augment".to_string()),
        Duration::from_secs(interval_minutes * 60),
        db,
    );
    
    println!("📊 Monitoring Configuration:");
    println!("   Extension: {}", monitor.extension_id().cyan());
    println!("   Interval: {} minutes", interval_minutes.to_string().yellow());
    println!("   Duration: {}", if duration_hours == 0 { 
        "Indefinite".green() 
    } else { 
        format!("{} hours", duration_hours).yellow() 
    });
    println!("   Output: {}", output_dir.display().to_string().cyan());
    println!();
    
    if duration_hours > 0 {
        let total_duration = Duration::from_secs(duration_hours * 3600);
        monitor.run_for_duration(total_duration).await?;
    } else {
        monitor.run_indefinitely().await?;
    }
    
    Ok(())
}

async fn analyze_persistence(
    data_dir: PathBuf,
    analysis_type: AnalysisType,
    visualize: bool,
) -> Result<()> {
    println!("{}", "📈 Analyzing Persistence Patterns".green().bold());
    println!("{}", "═══════════════════════════════════════".green());
    
    let db_path = data_dir.join("monitoring.db");
    let db = MonitoringDatabase::new(&db_path).await?;
    
    let analyzer = match analysis_type {
        AnalysisType::Comprehensive => {
            println!("🔍 Running comprehensive persistence analysis...");
            PersistenceAnalyzer::new(db)
        }
        AnalysisType::Persistence => {
            println!("🛡️ Analyzing storage persistence mechanisms...");
            PersistenceAnalyzer::new(db)
        }
        AnalysisType::Trends => {
            println!("📊 Analyzing long-term trends...");
            PersistenceAnalyzer::new(db)
        }
        AnalysisType::Validation => {
            println!("🌐 Analyzing server validation patterns...");
            PersistenceAnalyzer::new(db)
        }
        AnalysisType::Storage => {
            println!("💾 Analyzing storage mechanisms...");
            PersistenceAnalyzer::new(db)
        }
    };
    
    let results = analyzer.analyze_persistence_patterns().await?;
    
    println!("\n{}", "📊 Analysis Results".cyan().bold());
    println!("{}", "─────────────────────".cyan());
    
    display_persistence_metrics(&results);
    
    if visualize {
        println!("\n{}", "📈 Generating visualizations...".yellow());
        let trend_analyzer = TrendAnalyzer::new(analyzer.database().clone());
        trend_analyzer.generate_charts(&data_dir.join("charts")).await?;
        println!("✅ Charts saved to: {}", data_dir.join("charts").display());
    }
    
    Ok(())
}

async fn generate_report(
    data_dir: PathBuf,
    format: ReportFormat,
    output: Option<PathBuf>,
) -> Result<()> {
    println!("{}", "📝 Generating Educational Research Report".magenta().bold());
    println!("{}", "═══════════════════════════════════════════".magenta());
    
    let db_path = data_dir.join("monitoring.db");
    let db = MonitoringDatabase::new(&db_path).await?;
    
    let report_generator = ReportGenerator::new(db);
    
    let output_path = output.unwrap_or_else(|| {
        let extension = match format {
            ReportFormat::Markdown => "md",
            ReportFormat::Json => "json",
            ReportFormat::Csv => "csv",
            ReportFormat::Html => "html",
        };
        data_dir.join(format!("persistence_report.{}", extension))
    });
    
    match format {
        ReportFormat::Markdown => {
            report_generator.generate_markdown_report(&output_path).await?;
        }
        ReportFormat::Json => {
            report_generator.generate_json_report(&output_path).await?;
        }
        ReportFormat::Csv => {
            report_generator.generate_csv_report(&output_path).await?;
        }
        ReportFormat::Html => {
            report_generator.generate_html_report(&output_path).await?;
        }
    }
    
    println!("✅ Report generated: {}", output_path.display().to_string().cyan());
    
    Ok(())
}

async fn explain_topic(topic: ExplainTopic) -> Result<()> {
    match topic {
        ExplainTopic::Persistence => explain_persistence_mechanisms().await,
        ExplainTopic::Monitoring => explain_monitoring_approach().await,
        ExplainTopic::Storage => explain_storage_analysis().await,
        ExplainTopic::Validation => explain_validation_tracking().await,
        ExplainTopic::Trends => explain_trend_analysis().await,
    }
}

async fn explain_persistence_mechanisms() -> Result<()> {
    println!("{}", "🛡️ Extension Trial Persistence Mechanisms".blue().bold());
    println!("{}", "═══════════════════════════════════════════".blue());
    println!();
    
    println!("{}", "📚 Educational Overview:".cyan().bold());
    println!("Modern VS Code extensions use sophisticated persistence mechanisms to");
    println!("maintain trial state across various system changes and time periods.");
    println!();
    
    println!("{}", "🔍 Key Persistence Strategies:".green().bold());
    println!();
    println!("1. {} - Multiple storage locations ensure redundancy", "Multi-Storage Redundancy".yellow().bold());
    println!("   • Global state (survives workspace changes)");
    println!("   • Encrypted secrets (OS-level protection)");
    println!("   • File system storage (backup persistence)");
    println!("   • Server-side records (remote backup)");
    println!();
    
    println!("2. {} - Device-specific identification", "Hardware Fingerprinting".yellow().bold());
    println!("   • CPU model and specifications");
    println!("   • Memory configuration");
    println!("   • Network interface identifiers");
    println!("   • VS Code machine ID");
    println!();
    
    println!("3. {} - Continuous verification", "Server-Side Validation".yellow().bold());
    println!("   • Regular trial status checks");
    println!("   • Hardware fingerprint validation");
    println!("   • Usage pattern monitoring");
    println!("   • Real-time license verification");
    println!();
    
    println!("{}", "⏱️ Long-Term Effectiveness:".red().bold());
    println!("These mechanisms work together to maintain trial control over");
    println!("extended periods, surviving system reboots, software updates,");
    println!("and various user attempts to reset trial periods.");
    
    Ok(())
}

async fn initialize_monitoring(db_path: Option<PathBuf>) -> Result<()> {
    println!("{}", "🚀 Initializing Persistence Monitoring System".green().bold());
    println!("{}", "═══════════════════════════════════════════════".green());
    
    let db_path = db_path.unwrap_or_else(|| {
        dirs::home_dir()
            .unwrap_or_else(|| PathBuf::from("."))
            .join(".persistence-monitor")
            .join("monitoring.db")
    });
    
    if let Some(parent) = db_path.parent() {
        std::fs::create_dir_all(parent)?;
    }
    
    let db = MonitoringDatabase::new(&db_path).await?;
    db.initialize_schema().await?;
    
    println!("✅ Database initialized: {}", db_path.display().to_string().cyan());
    println!("✅ Monitoring system ready for educational research");
    
    Ok(())
}

fn display_persistence_metrics(metrics: &PersistenceMetrics) {
    println!("📊 {} {:.1}%", "Storage Stability:".cyan(), metrics.storage_stability * 100.0);
    println!("🔍 {} {:.1}%", "Fingerprint Consistency:".cyan(), metrics.fingerprint_consistency * 100.0);
    println!("🛡️ {} {:.1}%", "Trial Data Persistence:".cyan(), metrics.trial_data_persistence * 100.0);
    println!("🌐 {} {:.1}%", "Validation Frequency:".cyan(), metrics.validation_frequency * 100.0);
    println!("🔄 {} {:.1}%", "Recovery Effectiveness:".cyan(), metrics.recovery_effectiveness * 100.0);
}

// Additional explanation functions
async fn explain_monitoring_approach() -> Result<()> {
    println!("{}", "🔍 Long-Term Monitoring Approach".blue().bold());
    println!("{}", "═══════════════════════════════════════".blue());
    println!();

    println!("{}", "📚 Educational Methodology:".cyan().bold());
    println!("This tool employs continuous, non-invasive monitoring to understand");
    println!("how VS Code extensions maintain trial state over extended periods.");
    println!();

    println!("{}", "🕐 Monitoring Intervals:".green().bold());
    println!("• {} - Periodic snapshots of trial state", "Scheduled Collection".yellow());
    println!("• {} - Real-time detection of storage changes", "File System Monitoring".yellow());
    println!("• {} - Tracking of network validation events", "Network Activity".yellow());
    println!("• {} - System state and process monitoring", "System Monitoring".yellow());
    println!();

    println!("{}", "📊 Data Collection:".red().bold());
    println!("• Trial status and remaining time");
    println!("• Storage entry persistence across sessions");
    println!("• Hardware fingerprint stability");
    println!("• Server validation frequency and patterns");
    println!("• System events (reboots, updates, etc.)");

    Ok(())
}

async fn explain_storage_analysis() -> Result<()> {
    println!("{}", "💾 Storage Persistence Analysis".blue().bold());
    println!("{}", "═══════════════════════════════════════".blue());
    println!();

    println!("{}", "🔍 Storage Locations Monitored:".cyan().bold());
    println!("1. {} - Cross-workspace persistence", "VS Code Global State".yellow());
    println!("2. {} - OS-level encrypted storage", "System Keychain/Secrets".yellow());
    println!("3. {} - Local workspace data", "Workspace State".yellow());
    println!("4. {} - Extension directory files", "File System Storage".yellow());
    println!();

    println!("{}", "📈 Persistence Metrics:".green().bold());
    println!("• {} - How long data survives system changes", "Survival Rate".cyan());
    println!("• {} - Consistency across storage locations", "Redundancy Level".cyan());
    println!("• {} - Speed of data restoration", "Recovery Time".cyan());
    println!("• {} - Resistance to clearing attempts", "Protection Strength".cyan());

    Ok(())
}

async fn explain_validation_tracking() -> Result<()> {
    println!("{}", "🌐 Server Validation Tracking".blue().bold());
    println!("{}", "═══════════════════════════════════════".blue());
    println!();

    println!("{}", "📡 Network Monitoring:".cyan().bold());
    println!("This tool tracks server communication patterns to understand");
    println!("how extensions validate trial status remotely.");
    println!();

    println!("{}", "🔍 Validation Events:".green().bold());
    println!("• {} - Regular trial status checks", "Periodic Validation".yellow());
    println!("• {} - Device fingerprint verification", "Hardware Validation".yellow());
    println!("• {} - Feature usage reporting", "Usage Telemetry".yellow());
    println!("• {} - License compliance checks", "Compliance Monitoring".yellow());

    Ok(())
}

async fn explain_trend_analysis() -> Result<()> {
    println!("{}", "📈 Long-Term Trend Analysis".blue().bold());
    println!("{}", "═══════════════════════════════════════".blue());
    println!();

    println!("{}", "📊 Trend Metrics:".cyan().bold());
    println!("• {} - Changes in trial data over time", "Temporal Patterns".yellow());
    println!("• {} - Frequency of server validations", "Validation Trends".yellow());
    println!("• {} - Storage stability across sessions", "Persistence Trends".yellow());
    println!("• {} - System event correlations", "Event Patterns".yellow());
    println!();

    println!("{}", "🎯 Educational Value:".green().bold());
    println!("Long-term analysis reveals how modern extensions maintain");
    println!("persistent control and adapt to various system changes,");
    println!("providing insights into sophisticated protection mechanisms.");

    Ok(())
}
