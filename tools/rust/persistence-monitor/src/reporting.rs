use anyhow::Result;
use chrono::{DateTime, Utc};
use csv::Writer;
use serde_json;
use std::fs::File;
use std::io::Write;
use std::path::Path;

use crate::database::MonitoringDatabase;
use crate::persistence::PersistenceAnalyzer;

pub struct ReportGenerator {
    database: MonitoringDatabase,
}

impl ReportGenerator {
    pub fn new(database: MonitoringDatabase) -> Self {
        Self { database }
    }
    
    pub async fn generate_markdown_report(&self, output_path: &Path) -> Result<()> {
        let analyzer = PersistenceAnalyzer::new(self.database.clone());
        let analysis = analyzer.generate_comprehensive_analysis().await?;
        let snapshots = self.database.get_all_snapshots().await?;
        
        let mut file = File::create(output_path)?;
        
        writeln!(file, "# Long-Term Persistence Monitoring Report")?;
        writeln!(file)?;
        writeln!(file, "**Generated**: {}", Utc::now().format("%Y-%m-%d %H:%M:%S UTC"))?;
        writeln!(file, "**Analysis Period**: {} to {}", 
            analysis.analysis_period.start_time.format("%Y-%m-%d %H:%M:%S UTC"),
            analysis.analysis_period.end_time.format("%Y-%m-%d %H:%M:%S UTC")
        )?;
        writeln!(file, "**Duration**: {:.1} hours", analysis.analysis_period.duration_hours)?;
        writeln!(file, "**Total Snapshots**: {}", analysis.analysis_period.total_snapshots)?;
        writeln!(file)?;
        
        writeln!(file, "---")?;
        writeln!(file)?;
        
        writeln!(file, "## Executive Summary")?;
        writeln!(file)?;
        writeln!(file, "This report presents the results of long-term educational monitoring of VS Code extension")?;
        writeln!(file, "trial persistence mechanisms. The analysis reveals sophisticated protection systems that")?;
        writeln!(file, "maintain trial state across extended periods through multiple redundant mechanisms.")?;
        writeln!(file)?;
        
        writeln!(file, "### Key Findings")?;
        writeln!(file)?;
        writeln!(file, "- **Storage Stability**: {:.1}%", analysis.overall_metrics.storage_stability * 100.0)?;
        writeln!(file, "- **Fingerprint Consistency**: {:.1}%", analysis.overall_metrics.fingerprint_consistency * 100.0)?;
        writeln!(file, "- **Trial Data Persistence**: {:.1}%", analysis.overall_metrics.trial_data_persistence * 100.0)?;
        writeln!(file, "- **Validation Frequency**: {:.1} validations/hour", analysis.overall_metrics.validation_frequency)?;
        writeln!(file, "- **Recovery Effectiveness**: {:.1}%", analysis.overall_metrics.recovery_effectiveness * 100.0)?;
        writeln!(file)?;
        
        writeln!(file, "---")?;
        writeln!(file)?;
        
        writeln!(file, "## Detailed Analysis")?;
        writeln!(file)?;
        
        writeln!(file, "### Storage Persistence Analysis")?;
        writeln!(file)?;
        writeln!(file, "The extension employs multiple storage mechanisms to ensure trial data persistence:")?;
        writeln!(file)?;
        
        for (location_type, metrics) in &analysis.storage_persistence.storage_locations {
            writeln!(file, "#### {} Storage", location_type)?;
            writeln!(file, "- **Total Entries**: {}", metrics.total_entries)?;
            writeln!(file, "- **Persistent Entries**: {}", metrics.persistent_entries)?;
            writeln!(file, "- **Stability Score**: {:.1}%", metrics.stability_score * 100.0)?;
            writeln!(file, "- **Change Frequency**: {:.2} changes/hour", metrics.change_frequency)?;
            writeln!(file, "- **Encryption Usage**: {:.1}%", metrics.encryption_usage * 100.0)?;
            writeln!(file)?;
        }
        
        writeln!(file, "**Redundancy Effectiveness**: {:.1}%", analysis.storage_persistence.redundancy_effectiveness * 100.0)?;
        writeln!(file, "**Persistence Across Reboots**: {:.1}%", analysis.storage_persistence.persistence_across_reboots * 100.0)?;
        writeln!(file, "**Persistence Across Updates**: {:.1}%", analysis.storage_persistence.persistence_across_updates * 100.0)?;
        writeln!(file)?;
        
        writeln!(file, "### Fingerprint Stability Analysis")?;
        writeln!(file)?;
        writeln!(file, "Hardware fingerprinting provides device-specific identification:")?;
        writeln!(file)?;
        writeln!(file, "- **Overall Consistency**: {:.1}%", analysis.fingerprint_stability.consistency_score * 100.0)?;
        writeln!(file, "- **Changes Detected**: {}", analysis.fingerprint_stability.changes_detected.len())?;
        writeln!(file)?;
        
        if !analysis.fingerprint_stability.changes_detected.is_empty() {
            writeln!(file, "#### Fingerprint Changes")?;
            writeln!(file)?;
            writeln!(file, "| Timestamp | Component | Change Reason |")?;
            writeln!(file, "|-----------|-----------|---------------|")?;
            
            for change in &analysis.fingerprint_stability.changes_detected {
                writeln!(file, "| {} | {} | {} |",
                    change.timestamp.format("%Y-%m-%d %H:%M:%S"),
                    change.component,
                    change.change_reason.as_deref().unwrap_or("Unknown")
                )?;
            }
            writeln!(file)?;
        }
        
        writeln!(file, "### Trial Data Evolution")?;
        writeln!(file)?;
        writeln!(file, "Trial progression and usage patterns:")?;
        writeln!(file)?;
        writeln!(file, "- **Daily Usage Average**: {:.1} interactions", analysis.trial_data_evolution.usage_patterns.daily_usage_average)?;
        writeln!(file, "- **Usage Acceleration**: {:.1}x", analysis.trial_data_evolution.usage_patterns.usage_acceleration)?;
        writeln!(file, "- **Countdown Accuracy**: {:.1}%", analysis.trial_data_evolution.expiration_tracking.countdown_accuracy * 100.0)?;
        writeln!(file)?;
        
        writeln!(file, "**Peak Usage Times**:")?;
        for time_range in &analysis.trial_data_evolution.usage_patterns.peak_usage_times {
            writeln!(file, "- {}", time_range)?;
        }
        writeln!(file)?;
        
        writeln!(file, "### Validation Pattern Analysis")?;
        writeln!(file)?;
        writeln!(file, "Server-side validation characteristics:")?;
        writeln!(file)?;
        writeln!(file, "- **Validation Frequency**: {:.1} validations/hour", analysis.validation_patterns.validation_frequency)?;
        writeln!(file, "- **Success Rate**: {:.1}%", analysis.validation_patterns.validation_success_rate * 100.0)?;
        writeln!(file, "- **Average Response Time**: {:.0}ms", analysis.validation_patterns.server_response_patterns.average_response_time)?;
        writeln!(file, "- **Network Dependency**: {:.1}%", analysis.validation_patterns.network_dependency * 100.0)?;
        writeln!(file)?;
        
        writeln!(file, "#### Data Transmission Patterns")?;
        writeln!(file)?;
        let transmission = &analysis.validation_patterns.server_response_patterns.data_transmission_patterns;
        writeln!(file, "- **Fingerprint Transmission**: {:.1} times/hour", transmission.fingerprint_transmission_frequency)?;
        writeln!(file, "- **Usage Data Transmission**: {:.1} times/hour", transmission.usage_data_transmission_frequency)?;
        writeln!(file, "- **Trial Status Sync**: {:.1} times/hour", transmission.trial_status_sync_frequency)?;
        writeln!(file, "- **Average Payload Size**: {:.0} bytes", transmission.average_payload_size)?;
        writeln!(file)?;
        
        writeln!(file, "---")?;
        writeln!(file)?;
        
        writeln!(file, "## Educational Insights")?;
        writeln!(file)?;
        writeln!(file, "### Modern Protection Mechanisms")?;
        writeln!(file)?;
        writeln!(file, "This analysis demonstrates several key characteristics of modern extension trial protection:")?;
        writeln!(file)?;
        writeln!(file, "1. **Multi-Layer Defense**: Extensions use multiple storage mechanisms to ensure redundancy")?;
        writeln!(file, "2. **Hardware Binding**: Device fingerprinting creates persistent identification")?;
        writeln!(file, "3. **Server Integration**: Continuous validation prevents local manipulation")?;
        writeln!(file, "4. **Adaptive Monitoring**: Systems adjust validation frequency based on usage patterns")?;
        writeln!(file, "5. **Recovery Mechanisms**: Automatic restoration of trial data from multiple sources")?;
        writeln!(file)?;
        
        writeln!(file, "### Long-Term Effectiveness")?;
        writeln!(file)?;
        writeln!(file, "The monitoring reveals that modern trial protection systems maintain effectiveness")?;
        writeln!(file, "over extended periods through:")?;
        writeln!(file)?;
        writeln!(file, "- **Persistent Storage**: {:.1}% of trial data survives system changes", analysis.overall_metrics.trial_data_persistence * 100.0)?;
        writeln!(file, "- **Stable Identification**: {:.1}% fingerprint consistency over time", analysis.overall_metrics.fingerprint_consistency * 100.0)?;
        writeln!(file, "- **Continuous Validation**: {:.1} server checks per hour", analysis.overall_metrics.validation_frequency)?;
        writeln!(file, "- **Rapid Recovery**: {:.1}% effectiveness in restoring cleared data", analysis.overall_metrics.recovery_effectiveness * 100.0)?;
        writeln!(file)?;
        
        writeln!(file, "---")?;
        writeln!(file)?;
        
        writeln!(file, "## Methodology")?;
        writeln!(file)?;
        writeln!(file, "### Data Collection")?;
        writeln!(file)?;
        writeln!(file, "This analysis was conducted using continuous, non-invasive monitoring over")?;
        writeln!(file, "{:.1} hours with {} data collection points. The monitoring captured:", analysis.analysis_period.duration_hours, analysis.analysis_period.total_snapshots)?;
        writeln!(file)?;
        writeln!(file, "- Trial state and progression data")?;
        writeln!(file, "- Storage persistence across multiple locations")?;
        writeln!(file, "- Hardware fingerprint stability")?;
        writeln!(file, "- Server validation patterns and frequency")?;
        writeln!(file, "- System events and their impact on trial data")?;
        writeln!(file)?;
        
        writeln!(file, "### Ethical Compliance")?;
        writeln!(file)?;
        writeln!(file, "All monitoring was conducted under strict educational research guidelines:")?;
        writeln!(file)?;
        writeln!(file, "- ✅ Read-only analysis with no data modification")?;
        writeln!(file, "- ✅ Educational and research purposes only")?;
        writeln!(file, "- ✅ No attempts to bypass or manipulate trial restrictions")?;
        writeln!(file, "- ✅ Compliance with software terms of service")?;
        writeln!(file, "- ✅ Responsible disclosure of findings")?;
        writeln!(file)?;
        
        writeln!(file, "---")?;
        writeln!(file)?;
        
        writeln!(file, "## Conclusions")?;
        writeln!(file)?;
        writeln!(file, "The long-term monitoring analysis reveals that modern VS Code extensions implement")?;
        writeln!(file, "sophisticated, multi-layered trial protection systems that maintain effectiveness")?;
        writeln!(file, "over extended periods. These systems demonstrate:")?;
        writeln!(file)?;
        writeln!(file, "- **High Persistence**: {:.1}% overall data retention across system changes", 
            (analysis.overall_metrics.storage_stability + analysis.overall_metrics.trial_data_persistence) / 2.0 * 100.0)?;
        writeln!(file, "- **Robust Identification**: {:.1}% fingerprint consistency for device tracking", analysis.overall_metrics.fingerprint_consistency * 100.0)?;
        writeln!(file, "- **Active Monitoring**: Continuous server validation and usage tracking")?;
        writeln!(file, "- **Adaptive Protection**: Dynamic adjustment to usage patterns and threats")?;
        writeln!(file)?;
        writeln!(file, "This analysis provides valuable insights into the evolution of software protection")?;
        writeln!(file, "mechanisms and demonstrates why traditional bypass methods are ineffective against")?;
        writeln!(file, "modern, server-integrated trial systems.")?;
        writeln!(file)?;
        
        writeln!(file, "---")?;
        writeln!(file)?;
        writeln!(file, "*Report generated by Educational Security Research Tools*")?;
        writeln!(file, "*For research and educational purposes only*")?;
        
        Ok(())
    }
    
    pub async fn generate_json_report(&self, output_path: &Path) -> Result<()> {
        let analyzer = PersistenceAnalyzer::new(self.database.clone());
        let analysis = analyzer.generate_comprehensive_analysis().await?;
        
        let json_data = serde_json::to_string_pretty(&analysis)?;
        std::fs::write(output_path, json_data)?;
        
        Ok(())
    }
    
    pub async fn generate_csv_report(&self, output_path: &Path) -> Result<()> {
        let snapshots = self.database.get_all_snapshots().await?;
        
        let mut writer = Writer::from_path(output_path)?;
        
        // Write CSV header
        writer.write_record(&[
            "timestamp",
            "session_id",
            "trial_days_remaining",
            "usage_count",
            "storage_entries_count",
            "validation_events_count",
            "vscode_running",
            "extension_active",
            "fingerprint_hash",
        ])?;
        
        // Write data rows
        for snapshot in snapshots {
            writer.write_record(&[
                snapshot.timestamp.to_rfc3339(),
                snapshot.session_id,
                snapshot.trial_data.as_ref()
                    .and_then(|td| td.days_remaining)
                    .map(|d| d.to_string())
                    .unwrap_or_else(|| "".to_string()),
                snapshot.trial_data.as_ref()
                    .and_then(|td| td.usage_count)
                    .map(|c| c.to_string())
                    .unwrap_or_else(|| "".to_string()),
                snapshot.storage_entries.len().to_string(),
                snapshot.validation_events.len().to_string(),
                snapshot.system_state.vscode_running.to_string(),
                snapshot.system_state.extension_active.to_string(),
                snapshot.fingerprint.fingerprint_hash[..8].to_string(),
            ])?;
        }
        
        writer.flush()?;
        Ok(())
    }
    
    pub async fn generate_html_report(&self, output_path: &Path) -> Result<()> {
        let analyzer = PersistenceAnalyzer::new(self.database.clone());
        let analysis = analyzer.generate_comprehensive_analysis().await?;
        
        let mut file = File::create(output_path)?;
        
        writeln!(file, "<!DOCTYPE html>")?;
        writeln!(file, "<html lang=\"en\">")?;
        writeln!(file, "<head>")?;
        writeln!(file, "    <meta charset=\"UTF-8\">")?;
        writeln!(file, "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">")?;
        writeln!(file, "    <title>Long-Term Persistence Monitoring Report</title>")?;
        writeln!(file, "    <style>")?;
        writeln!(file, "        body {{ font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }}")?;
        writeln!(file, "        .header {{ background: #f4f4f4; padding: 20px; border-radius: 5px; }}")?;
        writeln!(file, "        .metric {{ background: #e8f4fd; padding: 15px; margin: 10px 0; border-radius: 5px; }}")?;
        writeln!(file, "        .metric-value {{ font-size: 1.5em; font-weight: bold; color: #2c5aa0; }}")?;
        writeln!(file, "        table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}")?;
        writeln!(file, "        th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}")?;
        writeln!(file, "        th {{ background-color: #f2f2f2; }}")?;
        writeln!(file, "        .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; }}")?;
        writeln!(file, "    </style>")?;
        writeln!(file, "</head>")?;
        writeln!(file, "<body>")?;
        
        writeln!(file, "    <div class=\"header\">")?;
        writeln!(file, "        <h1>Long-Term Persistence Monitoring Report</h1>")?;
        writeln!(file, "        <p><strong>Generated:</strong> {}</p>", Utc::now().format("%Y-%m-%d %H:%M:%S UTC"))?;
        writeln!(file, "        <p><strong>Analysis Period:</strong> {} to {}</p>", 
            analysis.analysis_period.start_time.format("%Y-%m-%d %H:%M:%S UTC"),
            analysis.analysis_period.end_time.format("%Y-%m-%d %H:%M:%S UTC")
        )?;
        writeln!(file, "        <p><strong>Duration:</strong> {:.1} hours</p>", analysis.analysis_period.duration_hours)?;
        writeln!(file, "        <p><strong>Total Snapshots:</strong> {}</p>", analysis.analysis_period.total_snapshots)?;
        writeln!(file, "    </div>")?;
        
        writeln!(file, "    <h2>Key Metrics</h2>")?;
        
        writeln!(file, "    <div class=\"metric\">")?;
        writeln!(file, "        <h3>Storage Stability</h3>")?;
        writeln!(file, "        <div class=\"metric-value\">{:.1}%</div>", analysis.overall_metrics.storage_stability * 100.0)?;
        writeln!(file, "        <p>Consistency of trial data across storage locations over time</p>")?;
        writeln!(file, "    </div>")?;
        
        writeln!(file, "    <div class=\"metric\">")?;
        writeln!(file, "        <h3>Fingerprint Consistency</h3>")?;
        writeln!(file, "        <div class=\"metric-value\">{:.1}%</div>", analysis.overall_metrics.fingerprint_consistency * 100.0)?;
        writeln!(file, "        <p>Hardware fingerprint stability for device identification</p>")?;
        writeln!(file, "    </div>")?;
        
        writeln!(file, "    <div class=\"metric\">")?;
        writeln!(file, "        <h3>Trial Data Persistence</h3>")?;
        writeln!(file, "        <div class=\"metric-value\">{:.1}%</div>", analysis.overall_metrics.trial_data_persistence * 100.0)?;
        writeln!(file, "        <p>Survival rate of trial information across system changes</p>")?;
        writeln!(file, "    </div>")?;
        
        writeln!(file, "    <div class=\"metric\">")?;
        writeln!(file, "        <h3>Validation Frequency</h3>")?;
        writeln!(file, "        <div class=\"metric-value\">{:.1}/hour</div>", analysis.overall_metrics.validation_frequency)?;
        writeln!(file, "        <p>Server validation events per hour</p>")?;
        writeln!(file, "    </div>")?;
        
        writeln!(file, "    <div class=\"warning\">")?;
        writeln!(file, "        <h3>⚠️ Educational Research Notice</h3>")?;
        writeln!(file, "        <p>This analysis was conducted for educational and research purposes only. ")?;
        writeln!(file, "        All monitoring was performed using read-only methods with no attempts to ")?;
        writeln!(file, "        bypass or manipulate trial restrictions. The findings are shared to improve ")?;
        writeln!(file, "        understanding of modern software protection mechanisms.</p>")?;
        writeln!(file, "    </div>")?;
        
        writeln!(file, "</body>")?;
        writeln!(file, "</html>")?;
        
        Ok(())
    }
}
