//! Storage Inspector
//!
//! An educational tool that safely examines VS Code extension storage patterns
//! to understand how trial and subscription data is persisted. This tool operates
//! in read-only mode to avoid modifying any actual data.

use anyhow::Result;
use clap::{Parser, Subcommand};
use colored::Colorize;
use shared::{
    init, <PERSON><PERSON><PERSON><PERSON>, MockStorage, StorageType, StorageEntry, StorageLocation,
    ToolConfig, OutputFormat, display_results, show_info, show_warning, show_success,
    display_educational_info, create_table, get_vscode_paths,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;

#[derive(Parser)]
#[command(name = "storage-inspector")]
#[command(about = "Educational tool for examining VS Code extension storage patterns")]
#[command(long_about = "
This tool safely examines VS Code extension storage patterns to understand how
trial and subscription data is persisted. It operates in read-only mode and
uses mock data for educational purposes.

⚠️  EDUCATIONAL USE ONLY ⚠️
This tool does NOT modify any actual extension data. It uses mock data and
read-only analysis to demonstrate storage mechanisms.
")]
struct Cli {
    #[command(subcommand)]
    command: Commands,

    /// Output format
    #[arg(short, long, default_value = "console")]
    format: String,

    /// Verbose output
    #[arg(short, long)]
    verbose: bool,

    /// Save results to file
    #[arg(short, long)]
    output: Option<String>,

    /// Use mock data instead of real VS Code storage
    #[arg(long, default_value = "true")]
    mock_mode: bool,
}

#[derive(Subcommand)]
enum Commands {
    /// Inspect storage locations and contents
    Inspect {
        /// VS Code installation path
        #[arg(long)]
        vscode_path: Option<String>,
        /// Specific extension to inspect
        #[arg(long)]
        extension: Option<String>,
    },
    /// Analyze trial tracking storage patterns
    AnalyzeTrialTracking {
        /// Show detailed storage analysis
        #[arg(long)]
        detailed: bool,
    },
    /// Map storage locations across the system
    MapLocations {
        /// Include system-wide locations
        #[arg(long)]
        system_wide: bool,
    },
    /// Simulate storage clearing to show what persists
    SimulateClear {
        /// Show what would remain after clearing
        #[arg(long)]
        show_persistent: bool,
    },
    /// Show educational information about storage mechanisms
    Explain {
        /// Topic to explain
        topic: String,
    },
}

#[derive(Debug, Serialize, Deserialize)]
struct StorageInspectionResult {
    storage_locations: Vec<StorageLocationInfo>,
    storage_entries: Vec<StorageEntry>,
    trial_tracking_analysis: TrialTrackingAnalysis,
    persistence_analysis: PersistenceAnalysis,
    educational_notes: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct StorageLocationInfo {
    location: StorageLocation,
    exists: bool,
    accessible: bool,
    entry_count: usize,
    size_bytes: u64,
    last_modified: Option<chrono::DateTime<chrono::Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
struct TrialTrackingAnalysis {
    trial_related_entries: Vec<StorageEntry>,
    auth_related_entries: Vec<StorageEntry>,
    subscription_related_entries: Vec<StorageEntry>,
    tracking_mechanisms: Vec<String>,
    bypass_resistance: f64,
}

#[derive(Debug, Serialize, Deserialize)]
struct PersistenceAnalysis {
    persistent_entries: Vec<StorageEntry>,
    temporary_entries: Vec<StorageEntry>,
    encrypted_entries: Vec<StorageEntry>,
    clearing_effectiveness: f64,
    survival_strategies: Vec<String>,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize shared library with ethics check
    init()?;

    let cli = Cli::parse();

    // Parse output format
    let output_format = match cli.format.as_str() {
        "json" => OutputFormat::Json,
        "markdown" => OutputFormat::Markdown,
        "html" => OutputFormat::Html,
        _ => OutputFormat::Console,
    };

    let config = ToolConfig {
        tool_name: "storage-inspector".to_string(),
        version: shared::VERSION.to_string(),
        output_format,
        verbose: cli.verbose,
        mock_mode: cli.mock_mode,
        educational_mode: true,
    };

    // Log usage for educational analysis
    EthicsChecker::log_usage("storage-inspector", "inspection");

    match cli.command {
        Commands::Inspect { vscode_path, extension } => {
            let result = inspect_storage(vscode_path, extension, cli.mock_mode).await?;
            display_results(&result, &config)?;
            
            if let Some(output_path) = cli.output {
                shared::save_results(&result, &std::path::Path::new(&output_path), &config.output_format)?;
            }
        }
        Commands::AnalyzeTrialTracking { detailed } => {
            let analysis = analyze_trial_tracking_storage(cli.mock_mode, detailed).await?;
            display_results(&analysis, &config)?;
        }
        Commands::MapLocations { system_wide } => {
            let locations = map_storage_locations(system_wide).await?;
            display_results(&locations, &config)?;
        }
        Commands::SimulateClear { show_persistent } => {
            simulate_storage_clearing(show_persistent).await?;
        }
        Commands::Explain { topic } => {
            explain_storage_concept(&topic);
        }
    }

    Ok(())
}

async fn inspect_storage(
    _vscode_path: Option<String>,
    _extension: Option<String>,
    mock_mode: bool,
) -> Result<StorageInspectionResult> {
    show_info("🔍 Inspecting VS Code extension storage...");
    
    EthicsChecker::verify_educational_operation("inspect storage")?;

    if !mock_mode {
        show_warning("Real storage inspection requested - using mock data for safety");
    }

    // Use mock storage for educational purposes
    let storage = MockStorage::new(true);
    let entries = storage.get_all_entries();
    let locations = storage.get_trial_storage_locations();

    // Create storage location info
    let storage_locations: Vec<StorageLocationInfo> = locations
        .into_iter()
        .map(|loc| StorageLocationInfo {
            location: loc,
            exists: true, // Mock data always "exists"
            accessible: true,
            entry_count: 5, // Mock count
            size_bytes: 1024, // Mock size
            last_modified: Some(chrono::Utc::now()),
        })
        .collect();

    // Analyze trial tracking
    let trial_tracking_analysis = analyze_trial_tracking_entries(&entries);
    
    // Analyze persistence
    let persistence_analysis = analyze_persistence_patterns(&entries);

    let educational_notes = vec![
        "VS Code extensions use multiple storage mechanisms for redundancy".to_string(),
        "Global state persists across workspaces and sessions".to_string(),
        "Secrets storage is encrypted and protected by the OS".to_string(),
        "File system storage can survive extension reinstallation".to_string(),
        "Server-side validation provides additional persistence".to_string(),
    ];

    show_success(&format!(
        "✅ Storage inspection complete: {} locations, {} entries", 
        storage_locations.len(),
        entries.len()
    ));

    Ok(StorageInspectionResult {
        storage_locations,
        storage_entries: entries,
        trial_tracking_analysis,
        persistence_analysis,
        educational_notes,
    })
}

fn analyze_trial_tracking_entries(entries: &[StorageEntry]) -> TrialTrackingAnalysis {
    let trial_related: Vec<_> = entries
        .iter()
        .filter(|e| {
            e.key.to_lowercase().contains("trial") ||
            e.key.to_lowercase().contains("subscription") ||
            e.key.to_lowercase().contains("license")
        })
        .cloned()
        .collect();

    let auth_related: Vec<_> = entries
        .iter()
        .filter(|e| {
            e.key.to_lowercase().contains("auth") ||
            e.key.to_lowercase().contains("token") ||
            e.key.to_lowercase().contains("login")
        })
        .cloned()
        .collect();

    let subscription_related: Vec<_> = entries
        .iter()
        .filter(|e| {
            e.key.to_lowercase().contains("subscription") ||
            e.key.to_lowercase().contains("plan") ||
            e.key.to_lowercase().contains("billing")
        })
        .cloned()
        .collect();

    let tracking_mechanisms = vec![
        "Global state persistence".to_string(),
        "Encrypted secrets storage".to_string(),
        "Server-side validation".to_string(),
        "Hardware fingerprint tracking".to_string(),
        "Usage pattern analysis".to_string(),
    ];

    // Calculate bypass resistance based on storage diversity
    let storage_types: std::collections::HashSet<_> = entries
        .iter()
        .map(|e| format!("{:?}", e.storage_type))
        .collect();
    let bypass_resistance = (storage_types.len() as f64 / 5.0).min(1.0);

    TrialTrackingAnalysis {
        trial_related_entries: trial_related,
        auth_related_entries: auth_related,
        subscription_related_entries: subscription_related,
        tracking_mechanisms,
        bypass_resistance,
    }
}

fn analyze_persistence_patterns(entries: &[StorageEntry]) -> PersistenceAnalysis {
    let persistent_entries: Vec<_> = entries
        .iter()
        .filter(|e| e.persistent)
        .cloned()
        .collect();

    let temporary_entries: Vec<_> = entries
        .iter()
        .filter(|e| !e.persistent)
        .cloned()
        .collect();

    let encrypted_entries: Vec<_> = entries
        .iter()
        .filter(|e| e.encrypted)
        .cloned()
        .collect();

    // Calculate clearing effectiveness (how much would survive clearing)
    let clearing_effectiveness = if entries.is_empty() {
        0.0
    } else {
        1.0 - (persistent_entries.len() as f64 / entries.len() as f64)
    };

    let survival_strategies = vec![
        "Multiple storage location redundancy".to_string(),
        "OS-level encryption protection".to_string(),
        "Server-side backup validation".to_string(),
        "Hardware fingerprint correlation".to_string(),
        "Cross-session data persistence".to_string(),
    ];

    PersistenceAnalysis {
        persistent_entries,
        temporary_entries,
        encrypted_entries,
        clearing_effectiveness,
        survival_strategies,
    }
}

async fn analyze_trial_tracking_storage(mock_mode: bool, detailed: bool) -> Result<TrialTrackingAnalysis> {
    show_info("📊 Analyzing trial tracking storage patterns...");
    
    let storage = MockStorage::new(mock_mode);
    let entries = storage.get_all_entries();
    let findings = storage.analyze_trial_tracking();

    let analysis = analyze_trial_tracking_entries(&entries);

    if detailed {
        display_detailed_trial_analysis(&analysis, &findings);
    }

    show_info(&format!(
        "Trial tracking resistance: {:.1}%", 
        analysis.bypass_resistance * 100.0
    ));

    Ok(analysis)
}

fn display_detailed_trial_analysis(
    analysis: &TrialTrackingAnalysis,
    findings: &[shared::Finding],
) {
    println!("\n{}", "📊 Detailed Trial Tracking Analysis".bold().blue());
    println!("{}", "═".repeat(50).blue());

    // Display storage distribution
    let headers = &["Storage Type", "Entry Count", "Persistence", "Encryption"];
    let mut storage_counts: HashMap<String, (usize, usize, usize)> = HashMap::new();

    for entry in &analysis.trial_related_entries {
        let key = format!("{:?}", entry.storage_type);
        let (count, persistent, encrypted) = storage_counts.entry(key).or_insert((0, 0, 0));
        *count += 1;
        if entry.persistent { *persistent += 1; }
        if entry.encrypted { *encrypted += 1; }
    }

    let rows: Vec<Vec<String>> = storage_counts
        .iter()
        .map(|(storage_type, (count, persistent, encrypted))| {
            vec![
                storage_type.clone(),
                count.to_string(),
                format!("{}/{}", persistent, count),
                format!("{}/{}", encrypted, count),
            ]
        })
        .collect();

    println!("{}", create_table(headers, &rows));

    // Display findings
    println!("\n{}", "🔍 Security Findings:".bold().yellow());
    for finding in findings {
        println!("  • {} ({})", finding.title, finding.severity);
    }
}

async fn map_storage_locations(system_wide: bool) -> Result<Vec<StorageLocationInfo>> {
    show_info("🗺️  Mapping VS Code storage locations...");
    
    let mut locations = Vec::new();
    
    // Get VS Code paths
    let vscode_paths = get_vscode_paths();
    
    for path in vscode_paths {
        if path.exists() {
            locations.push(StorageLocationInfo {
                location: StorageLocation {
                    path: path.to_string_lossy().to_string(),
                    storage_type: StorageType::GlobalState,
                    description: "VS Code user data directory".to_string(),
                    clearable: true,
                    protected: false,
                },
                exists: true,
                accessible: true,
                entry_count: 10, // Mock count
                size_bytes: 1024 * 1024, // Mock size
                last_modified: Some(chrono::Utc::now()),
            });
        }
    }

    if system_wide {
        // Add system-wide locations (mock)
        locations.push(StorageLocationInfo {
            location: StorageLocation {
                path: "/System/Library/Keychains".to_string(),
                storage_type: StorageType::Secrets,
                description: "System keychain (macOS)".to_string(),
                clearable: false,
                protected: true,
            },
            exists: true,
            accessible: false, // Protected
            entry_count: 0,
            size_bytes: 0,
            last_modified: None,
        });
    }

    show_success(&format!("✅ Found {} storage locations", locations.len()));

    Ok(locations)
}

async fn simulate_storage_clearing(show_persistent: bool) -> Result<()> {
    show_info("🧹 Simulating storage clearing operation...");
    
    EthicsChecker::verify_educational_operation("simulate storage clearing")?;

    let mut storage = MockStorage::new(true);
    let initial_entries = storage.get_all_entries();
    let clearing_result = storage.simulate_storage_clearing();

    println!("\n{}", "📊 Storage Clearing Simulation Results".bold().blue());
    println!("{}", "═".repeat(50).blue());

    println!("Initial entries: {}", clearing_result.initial_entries);
    println!("Cleared entries: {}", clearing_result.cleared_entries.len());
    println!("Persistent entries: {}", clearing_result.persistent_entries.len());
    println!("Effectiveness: {:.1}%", clearing_result.effectiveness * 100.0);

    if show_persistent {
        println!("\n{}", "🔒 Entries that would persist:".yellow());
        for entry in &clearing_result.persistent_entries {
            println!("  • {}", entry);
        }
    }

    display_educational_info(
        "Why Storage Clearing Often Fails",
        "Modern extensions use multiple storage mechanisms that survive clearing attempts.",
        &[
            "OS-level encrypted storage (keychain/credential manager)",
            "Server-side validation and backup",
            "Hardware fingerprint correlation",
            "Multiple redundant storage locations",
            "Cross-application data sharing"
        ]
    );

    Ok(())
}

fn explain_storage_concept(topic: &str) {
    match topic.to_lowercase().as_str() {
        "types" => {
            display_educational_info(
                "VS Code Storage Types",
                "VS Code extensions can use several different storage mechanisms, each with different characteristics.",
                &[
                    "Global State: Persists across workspaces and sessions",
                    "Workspace State: Specific to individual projects",
                    "Secrets: Encrypted storage for sensitive data",
                    "File System: Direct file storage in extension directory",
                    "Extension Context: Runtime storage tied to extension lifecycle"
                ]
            );
        },
        "persistence" => {
            display_educational_info(
                "Storage Persistence",
                "Different storage types have varying levels of persistence and resistance to clearing.",
                &[
                    "Global State: Survives workspace changes and VS Code restarts",
                    "Secrets: Protected by OS encryption, very persistent",
                    "File Storage: Can survive extension reinstallation",
                    "Server-side: Immune to local storage clearing",
                    "Hardware-tied: Persists across software changes"
                ]
            );
        },
        "security" => {
            display_educational_info(
                "Storage Security",
                "Extensions implement various security measures to protect stored data.",
                &[
                    "Encryption for sensitive authentication data",
                    "OS-level protection through keychain/credential manager",
                    "Server-side validation and backup",
                    "Integrity checking and tamper detection",
                    "Access control and permission management"
                ]
            );
        },
        _ => {
            show_info("Available topics: types, persistence, security");
        }
    }
}
