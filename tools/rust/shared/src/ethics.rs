//! Ethical usage enforcement and guidelines.
//!
//! This module provides mechanisms to ensure these tools are used only for
//! legitimate educational and security research purposes.

use anyhow::{bail, Result};
use colored::Colorize;
use std::io::{self, Write};

/// Ethical usage checker that enforces responsible use of security research tools
pub struct EthicsChecker;

impl EthicsChecker {
    /// Verify that the user understands and agrees to ethical usage guidelines
    pub fn verify_usage() -> Result<()> {
        // Check for environment variable that bypasses interactive check
        // (useful for automated testing and CI)
        if std::env::var("SECURITY_TOOLS_ETHICS_ACCEPTED").is_ok() {
            return Ok(());
        }

        // Display ethical usage prompt
        Self::display_ethics_prompt()?;
        
        // Get user confirmation with retry limit
        if !Self::get_user_confirmation_with_retries(3)? {
            bail!("Ethical usage agreement required to proceed");
        }

        Ok(())
    }

    /// Display the ethical usage prompt to the user
    fn display_ethics_prompt() -> Result<()> {
        println!("\n{}", "🔒 ETHICAL USAGE VERIFICATION".bold().red());
        println!("{}", "═".repeat(50).red());
        
        println!("\n{}", "These security research tools are designed for:".yellow());
        println!("  ✓ Educational purposes");
        println!("  ✓ Legitimate security research");
        println!("  ✓ Understanding protection mechanisms");
        println!("  ✓ Improving extension security");
        
        println!("\n{}", "These tools must NOT be used for:".red());
        println!("  ✗ Bypassing commercial software licenses");
        println!("  ✗ Extending or resetting trial periods");
        println!("  ✗ Piracy or unauthorized software use");
        println!("  ✗ Any illegal or unethical activities");
        
        println!("\n{}", "By proceeding, you confirm that you will:".cyan());
        println!("  • Use these tools only for legitimate research");
        println!("  • Comply with all applicable laws and ToS");
        println!("  • Practice responsible disclosure");
        println!("  • Respect intellectual property rights");
        
        println!("\n{}", "For complete guidelines, see: ETHICS.md".bright_blue());
        
        Ok(())
    }

    /// Get user confirmation of ethical usage agreement with retry limit
    fn get_user_confirmation_with_retries(max_retries: usize) -> Result<bool> {
        for attempt in 1..=max_retries {
            print!("\n{} ", "Do you agree to use these tools ethically? (yes/no):".bold());
            io::stdout().flush()?;
            
            let mut input = String::new();
            match io::stdin().read_line(&mut input) {
                Ok(0) => {
                    // EOF reached - likely non-interactive environment
                    bail!("Cannot get user input in non-interactive environment. Set SECURITY_TOOLS_ETHICS_ACCEPTED=true to bypass.");
                }
                Ok(_) => {
                    let response = input.trim().to_lowercase();
                    match response.as_str() {
                        "yes" | "y" | "agree" | "accept" => {
                            println!("{}", "✓ Ethical usage agreement accepted".green());
                            return Ok(true);
                        }
                        "no" | "n" | "decline" | "reject" => {
                            println!("{}", "✗ Ethical usage agreement declined".red());
                            return Ok(false);
                        }
                        _ => {
                            if attempt < max_retries {
                                println!("{}", "Please respond with 'yes' or 'no'".yellow());
                            } else {
                                println!("{}", "Too many invalid responses. Exiting.".red());
                                return Ok(false);
                            }
                        }
                    }
                }
                Err(e) => {
                    bail!("Failed to read user input: {}", e);
                }
            }
        }
        Ok(false)
    }

    /// Get user confirmation of ethical usage agreement (deprecated - use get_user_confirmation_with_retries)
    fn get_user_confirmation() -> Result<bool> {
        Self::get_user_confirmation_with_retries(3)
    }

    /// Log usage for educational analysis (anonymized)
    pub fn log_usage(tool_name: &str, operation: &str) {
        tracing::info!(
            tool = tool_name,
            operation = operation,
            "Educational tool usage logged"
        );
    }

    /// Verify that an operation is educational/research-only
    pub fn verify_educational_operation(operation: &str) -> Result<()> {
        // Check for potentially harmful operations
        let harmful_patterns = [
            "bypass",
            "crack",
            "pirate",
            "steal",
            "hack",
            "exploit",
        ];

        for pattern in &harmful_patterns {
            if operation.to_lowercase().contains(pattern) {
                bail!(
                    "Operation '{}' appears to be harmful and is not allowed. \
                     These tools are for educational purposes only.",
                    operation
                );
            }
        }

        Ok(())
    }

    /// Display a warning about responsible usage
    pub fn display_warning(message: &str) {
        eprintln!("{} {}", "⚠️  WARNING:".yellow().bold(), message.yellow());
    }

    /// Display information about ethical research practices
    pub fn display_research_info() {
        println!("\n{}", "📚 EDUCATIONAL RESEARCH GUIDELINES".bold().blue());
        println!("{}", "═".repeat(50).blue());
        
        println!("\n{}", "Best Practices for Security Research:".cyan());
        println!("  • Always obtain proper authorization");
        println!("  • Use isolated test environments");
        println!("  • Document your research methodology");
        println!("  • Share findings responsibly");
        println!("  • Respect vendor disclosure timelines");
        
        println!("\n{}", "Learning Objectives:".green());
        println!("  • Understand how protection mechanisms work");
        println!("  • Identify common security patterns");
        println!("  • Learn why certain attacks fail");
        println!("  • Develop better security practices");
        
        println!("\n{}", "Remember: The goal is education, not exploitation!".bold());
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_verify_educational_operation() {
        // Valid educational operations
        assert!(EthicsChecker::verify_educational_operation("analyze").is_ok());
        assert!(EthicsChecker::verify_educational_operation("research").is_ok());
        assert!(EthicsChecker::verify_educational_operation("study").is_ok());
        
        // Invalid harmful operations
        assert!(EthicsChecker::verify_educational_operation("bypass license").is_err());
        assert!(EthicsChecker::verify_educational_operation("crack software").is_err());
        assert!(EthicsChecker::verify_educational_operation("exploit vulnerability").is_err());
    }

    #[test]
    fn test_ethics_bypass_with_env_var() {
        std::env::set_var("SECURITY_TOOLS_ETHICS_ACCEPTED", "true");
        assert!(EthicsChecker::verify_usage().is_ok());
        std::env::remove_var("SECURITY_TOOLS_ETHICS_ACCEPTED");
    }
}
