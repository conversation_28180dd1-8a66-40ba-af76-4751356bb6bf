//! VS Code storage mechanism simulation and analysis.
//!
//! This module provides tools to understand how VS Code extensions store
//! trial and subscription data across different storage mechanisms.

use crate::{Finding, FindingCategory, Severity, AuthInfo, SubscriptionInfo, TrialInfo};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;

/// Types of storage used by VS Code extensions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StorageType {
    GlobalState,
    WorkspaceState,
    Secrets,
    FileSystem,
    ExtensionContext,
}

/// Storage entry with metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageEntry {
    pub key: String,
    pub value: serde_json::Value,
    pub storage_type: StorageType,
    pub encrypted: bool,
    pub persistent: bool,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_modified: chrono::DateTime<chrono::Utc>,
}

/// Mock storage implementation for educational analysis
pub struct MockStorage {
    global_state: HashMap<String, serde_json::Value>,
    workspace_state: HashMap<String, serde_json::Value>,
    secrets: HashMap<String, String>,
    file_storage: HashMap<PathBuf, Vec<u8>>,
    mock_mode: bool,
}

impl MockStorage {
    /// Create a new mock storage instance
    pub fn new(mock_mode: bool) -> Self {
        let mut storage = Self {
            global_state: HashMap::new(),
            workspace_state: HashMap::new(),
            secrets: HashMap::new(),
            file_storage: HashMap::new(),
            mock_mode,
        };

        if mock_mode {
            storage.populate_mock_data();
        }

        storage
    }

    /// Populate storage with realistic mock data for educational purposes
    fn populate_mock_data(&mut self) {
        // Mock authentication data
        let auth_info = AuthInfo {
            access_token: Some("mock_access_token_12345".to_string()),
            refresh_token: Some("mock_refresh_token_67890".to_string()),
            tenant_url: Some("https://api.example.com".to_string()),
            scopes: vec!["read".to_string(), "write".to_string()],
            expires_at: Some(chrono::Utc::now() + chrono::Duration::days(30)),
            user_id: Some("user_12345".to_string()),
        };

        // Mock subscription data
        let subscription_info = SubscriptionInfo {
            subscription_id: Some("sub_12345".to_string()),
            status: crate::types::SubscriptionStatus::Trial,
            plan: "premium".to_string(),
            trial_end: Some(chrono::Utc::now() + chrono::Duration::days(14)),
            subscription_end: None,
            features: vec!["ai_completion".to_string(), "advanced_analysis".to_string()],
        };

        // Mock trial tracking
        let trial_info = TrialInfo {
            trial_id: uuid::Uuid::new_v4(),
            started_at: chrono::Utc::now() - chrono::Duration::days(1),
            expires_at: chrono::Utc::now() + chrono::Duration::days(13),
            days_remaining: 13,
            usage_count: 42,
            features_used: vec!["completion".to_string(), "chat".to_string()],
        };

        // Store in different storage types
        self.global_state.insert(
            "auth_info".to_string(),
            serde_json::to_value(&auth_info).unwrap(),
        );

        self.global_state.insert(
            "subscription_info".to_string(),
            serde_json::to_value(&subscription_info).unwrap(),
        );

        self.workspace_state.insert(
            "trial_info".to_string(),
            serde_json::to_value(&trial_info).unwrap(),
        );

        // Mock secrets (encrypted storage)
        self.secrets.insert(
            "access_token".to_string(),
            "encrypted_access_token_data".to_string(),
        );

        self.secrets.insert(
            "machine_fingerprint".to_string(),
            "encrypted_fingerprint_hash".to_string(),
        );

        // Mock extension-specific storage keys (based on real analysis)
        self.global_state.insert(
            "ClientAuth".to_string(),
            serde_json::json!({
                "authenticated": true,
                "user_id": "user_12345",
                "last_auth": chrono::Utc::now().to_rfc3339()
            }),
        );

        self.global_state.insert(
            "get-subscription-info-response".to_string(),
            serde_json::json!({
                "status": "trial",
                "expires": (chrono::Utc::now() + chrono::Duration::days(13)).to_rfc3339(),
                "features": ["premium_features"]
            }),
        );

        self.workspace_state.insert(
            "UserConfigRequired".to_string(),
            serde_json::json!(false),
        );
    }

    /// Get all storage entries for analysis
    pub fn get_all_entries(&self) -> Vec<StorageEntry> {
        let mut entries = Vec::new();
        let now = chrono::Utc::now();

        // Global state entries
        for (key, value) in &self.global_state {
            entries.push(StorageEntry {
                key: key.clone(),
                value: value.clone(),
                storage_type: StorageType::GlobalState,
                encrypted: false,
                persistent: true,
                created_at: now,
                last_modified: now,
            });
        }

        // Workspace state entries
        for (key, value) in &self.workspace_state {
            entries.push(StorageEntry {
                key: key.clone(),
                value: value.clone(),
                storage_type: StorageType::WorkspaceState,
                encrypted: false,
                persistent: false,
                created_at: now,
                last_modified: now,
            });
        }

        // Secrets (encrypted)
        for (key, value) in &self.secrets {
            entries.push(StorageEntry {
                key: key.clone(),
                value: serde_json::Value::String(value.clone()),
                storage_type: StorageType::Secrets,
                encrypted: true,
                persistent: true,
                created_at: now,
                last_modified: now,
            });
        }

        entries
    }

    /// Analyze storage patterns for trial tracking mechanisms
    pub fn analyze_trial_tracking(&self) -> Vec<Finding> {
        let mut findings = Vec::new();
        let entries = self.get_all_entries();

        // Look for authentication-related storage
        let auth_entries: Vec<_> = entries
            .iter()
            .filter(|e| {
                e.key.to_lowercase().contains("auth") ||
                e.key.to_lowercase().contains("token") ||
                e.key.to_lowercase().contains("login")
            })
            .collect();

        if !auth_entries.is_empty() {
            findings.push(Finding {
                category: FindingCategory::Authentication,
                severity: Severity::High,
                title: "Authentication Data Storage Detected".to_string(),
                description: "Extension stores authentication tokens and user credentials in persistent storage.".to_string(),
                evidence: auth_entries.iter().map(|e| format!("Key: {} ({})", e.key, format!("{:?}", e.storage_type))).collect(),
                mitigation: Some("Authentication data persists across extension reinstalls. Clearing requires manual removal from VS Code storage.".to_string()),
            });
        }

        // Look for subscription-related storage
        let subscription_entries: Vec<_> = entries
            .iter()
            .filter(|e| {
                e.key.to_lowercase().contains("subscription") ||
                e.key.to_lowercase().contains("trial") ||
                e.key.to_lowercase().contains("license")
            })
            .collect();

        if !subscription_entries.is_empty() {
            findings.push(Finding {
                category: FindingCategory::TrialTracking,
                severity: Severity::High,
                title: "Trial/Subscription Tracking Storage".to_string(),
                description: "Extension stores trial and subscription status in multiple storage locations.".to_string(),
                evidence: subscription_entries.iter().map(|e| format!("Key: {} ({})", e.key, format!("{:?}", e.storage_type))).collect(),
                mitigation: Some("Trial data is stored in multiple locations and may be validated against server-side records.".to_string()),
            });
        }

        // Look for encrypted storage usage
        let encrypted_entries: Vec<_> = entries
            .iter()
            .filter(|e| e.encrypted)
            .collect();

        if !encrypted_entries.is_empty() {
            findings.push(Finding {
                category: FindingCategory::Security,
                severity: Severity::Medium,
                title: "Encrypted Storage Usage".to_string(),
                description: "Extension uses VS Code's encrypted secrets storage for sensitive data.".to_string(),
                evidence: encrypted_entries.iter().map(|e| format!("Encrypted key: {}", e.key)).collect(),
                mitigation: Some("Encrypted storage is protected by the OS keychain and is difficult to access or modify.".to_string()),
            });
        }

        // Analyze storage persistence
        let persistent_entries: Vec<_> = entries
            .iter()
            .filter(|e| e.persistent)
            .collect();

        if persistent_entries.len() > 5 {
            findings.push(Finding {
                category: FindingCategory::Storage,
                severity: Severity::Medium,
                title: "Extensive Persistent Storage Usage".to_string(),
                description: format!("Extension stores {} persistent data entries that survive reinstallation.", persistent_entries.len()),
                evidence: vec![format!("{} persistent storage entries detected", persistent_entries.len())],
                mitigation: Some("Persistent storage survives extension uninstall/reinstall cycles. Manual cleanup required.".to_string()),
            });
        }

        findings
    }

    /// Simulate storage clearing attempts (educational)
    pub fn simulate_storage_clearing(&mut self) -> StorageClearingResult {
        let initial_count = self.get_all_entries().len();
        let mut cleared_entries = Vec::new();
        let mut persistent_entries = Vec::new();

        // Simulate clearing workspace state (would be cleared)
        for key in self.workspace_state.keys() {
            cleared_entries.push(format!("WorkspaceState: {}", key));
        }
        self.workspace_state.clear();

        // Simulate attempting to clear global state (some would persist)
        for (key, _) in &self.global_state {
            if key.contains("auth") || key.contains("subscription") {
                persistent_entries.push(format!("GlobalState: {} (protected)", key));
            } else {
                cleared_entries.push(format!("GlobalState: {}", key));
            }
        }

        // Secrets would typically persist (encrypted by OS)
        for key in self.secrets.keys() {
            persistent_entries.push(format!("Secrets: {} (encrypted, protected)", key));
        }

        let final_count = persistent_entries.len();

        StorageClearingResult {
            initial_entries: initial_count,
            cleared_entries,
            persistent_entries,
            final_entries: final_count,
            effectiveness: if final_count == 0 { 1.0 } else { 1.0 - (final_count as f64 / initial_count as f64) },
        }
    }

    /// Get storage locations that would need to be cleared for trial reset
    pub fn get_trial_storage_locations(&self) -> Vec<StorageLocation> {
        vec![
            StorageLocation {
                path: "~/.vscode/User/globalStorage/publisher.extension".to_string(),
                storage_type: StorageType::GlobalState,
                description: "Global extension state (persists across workspaces)".to_string(),
                clearable: true,
                protected: false,
            },
            StorageLocation {
                path: "~/Library/Application Support/Code/User/secrets".to_string(),
                storage_type: StorageType::Secrets,
                description: "Encrypted secrets storage (OS keychain)".to_string(),
                clearable: false,
                protected: true,
            },
            StorageLocation {
                path: ".vscode/settings.json".to_string(),
                storage_type: StorageType::WorkspaceState,
                description: "Workspace-specific settings".to_string(),
                clearable: true,
                protected: false,
            },
            StorageLocation {
                path: "~/.vscode/extensions/publisher.extension/storage".to_string(),
                storage_type: StorageType::FileSystem,
                description: "Extension file-based storage".to_string(),
                clearable: true,
                protected: false,
            },
        ]
    }
}

/// Result of simulated storage clearing attempt
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageClearingResult {
    pub initial_entries: usize,
    pub cleared_entries: Vec<String>,
    pub persistent_entries: Vec<String>,
    pub final_entries: usize,
    pub effectiveness: f64, // 0.0 = no effect, 1.0 = completely cleared
}

/// Storage location information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageLocation {
    pub path: String,
    pub storage_type: StorageType,
    pub description: String,
    pub clearable: bool,
    pub protected: bool,
}

impl std::fmt::Display for StorageType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            StorageType::GlobalState => write!(f, "Global State"),
            StorageType::WorkspaceState => write!(f, "Workspace State"),
            StorageType::Secrets => write!(f, "Secrets"),
            StorageType::FileSystem => write!(f, "File System"),
            StorageType::ExtensionContext => write!(f, "Extension Context"),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_mock_storage_creation() {
        let storage = MockStorage::new(true);
        let entries = storage.get_all_entries();
        assert!(!entries.is_empty());
    }

    #[test]
    fn test_trial_tracking_analysis() {
        let storage = MockStorage::new(true);
        let findings = storage.analyze_trial_tracking();
        assert!(!findings.is_empty());
    }

    #[test]
    fn test_storage_clearing_simulation() {
        let mut storage = MockStorage::new(true);
        let result = storage.simulate_storage_clearing();
        assert!(result.effectiveness >= 0.0 && result.effectiveness <= 1.0);
        assert!(!result.persistent_entries.is_empty());
    }
}
