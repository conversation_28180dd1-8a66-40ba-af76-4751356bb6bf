//! Shared utilities and types for VS Code extension security research tools.
//!
//! This crate provides common functionality used across all security research tools,
//! including mock data structures, ethical usage enforcement, and utility functions.
//!
//! # Ethical Usage
//!
//! All tools in this workspace are designed for educational and legitimate security
//! research purposes only. See the ETHICS.md file for detailed guidelines.

pub mod ethics;
pub mod fingerprint;
pub mod mock_data;
pub mod profile;
pub mod storage;
pub mod types;
pub mod utils;

pub use ethics::EthicsChecker;
pub use fingerprint::{SystemFingerprint, FingerprintCollector};
pub use mock_data::MockExtensionData;
pub use profile::{ProfileDetector, profile_utils};
pub use storage::{MockStorage, StorageType, StorageEntry, StorageLocation};
pub use types::*;
pub use utils::*;

/// Result type used throughout the security research tools
pub type Result<T> = anyhow::Result<T>;

/// Version information for the security research tools
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// Educational disclaimer that must be shown to users
pub const EDUCATIONAL_DISCLAIMER: &str = r#"
⚠️  EDUCATIONAL AND RESEARCH USE ONLY ⚠️

These tools are designed for legitimate security research and educational purposes.
DO NOT use these tools to bypass commercial software licenses or trial restrictions.

By using these tools, you agree to:
- Use them only for educational and research purposes
- Not bypass any commercial software protections
- Comply with all applicable laws and terms of service
- Report any vulnerabilities through responsible disclosure

For full ethical guidelines, see: ETHICS.md
"#;

/// Initialize the shared library with ethical checks
pub fn init() -> Result<()> {
    // Display educational disclaimer
    eprintln!("{}", EDUCATIONAL_DISCLAIMER);

    // Initialize logging
    if std::env::var("RUST_LOG").is_err() {
        std::env::set_var("RUST_LOG", "info");
    }
    tracing_subscriber::fmt::init();

    // Perform ethics check
    EthicsChecker::verify_usage()?;

    tracing::info!("Security research tools initialized");
    Ok(())
}
