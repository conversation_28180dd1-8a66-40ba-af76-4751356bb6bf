//! Common types and data structures used across security research tools.

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};

/// Represents a VS Code extension for analysis purposes
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Extension {
    pub id: String,
    pub name: String,
    pub version: String,
    pub publisher: String,
    pub description: String,
    pub categories: Vec<String>,
    pub activation_events: Vec<String>,
    pub contributes: ExtensionContributes,
    pub engines: ExtensionEngines,
}

/// Extension contribution points (commands, configuration, etc.)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtensionContributes {
    pub commands: Vec<Command>,
    pub configuration: Vec<ConfigurationProperty>,
    pub keybindings: Vec<Keybinding>,
    pub views: HashMap<String, Vec<View>>,
}

/// VS Code engine requirements
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtensionEngines {
    pub vscode: String,
    pub node: Option<String>,
}

/// Extension command definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Command {
    pub command: String,
    pub title: String,
    pub category: Option<String>,
    pub when: Option<String>,
}

/// Configuration property
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigurationProperty {
    pub key: String,
    pub title: String,
    pub description: String,
    pub property_type: String,
    pub default: Option<serde_json::Value>,
}

/// Keybinding definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Keybinding {
    pub command: String,
    pub key: String,
    pub when: Option<String>,
}

/// View definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct View {
    pub id: String,
    pub name: String,
    pub view_type: String,
    pub when: Option<String>,
}

/// Authentication information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthInfo {
    pub access_token: Option<String>,
    pub refresh_token: Option<String>,
    pub tenant_url: Option<String>,
    pub scopes: Vec<String>,
    pub expires_at: Option<DateTime<Utc>>,
    pub user_id: Option<String>,
}

/// Subscription information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubscriptionInfo {
    pub subscription_id: Option<String>,
    pub status: SubscriptionStatus,
    pub plan: String,
    pub trial_end: Option<DateTime<Utc>>,
    pub subscription_end: Option<DateTime<Utc>>,
    pub features: Vec<String>,
}

/// Subscription status enumeration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SubscriptionStatus {
    Trial,
    Active,
    Expired,
    Cancelled,
    Unknown,
}

/// Trial tracking information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrialInfo {
    pub trial_id: Uuid,
    pub started_at: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
    pub days_remaining: i64,
    pub usage_count: u32,
    pub features_used: Vec<String>,
}

/// Network endpoint information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkEndpoint {
    pub url: String,
    pub method: String,
    pub purpose: String,
    pub auth_required: bool,
    pub parameters: Vec<String>,
}

/// VS Code profile configuration information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VSCodeProfile {
    pub profile_name: String,
    pub profile_path: String,
    pub is_default: bool,
    pub is_temporary: bool,
    pub user_data_dir: String,
    pub extensions_dir: String,
    pub global_storage_dir: String,
    pub workspace_storage_dir: String,
    pub created_at: Option<DateTime<Utc>>,
    pub last_used: Option<DateTime<Utc>>,
}

/// Profile persistence analysis result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProfilePersistenceAnalysis {
    pub current_profile: VSCodeProfile,
    pub persistence_level: PersistenceLevel,
    pub trial_data_locations: Vec<TrialDataLocation>,
    pub cross_session_persistence: bool,
    pub cross_profile_persistence: bool,
    pub server_side_tracking: bool,
    pub bypass_resistance_score: f64,
    pub recommendations: Vec<String>,
}

/// Level of data persistence
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PersistenceLevel {
    Temporary,    // Data cleared on restart
    Session,      // Data persists during session
    Profile,      // Data persists in profile
    System,       // Data persists system-wide
    Server,       // Data persists server-side
}

/// Location where trial data is stored
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrialDataLocation {
    pub location_type: String,
    pub path: String,
    pub persistence_level: PersistenceLevel,
    pub encrypted: bool,
    pub size_bytes: u64,
    pub contains_trial_data: bool,
    pub contains_auth_data: bool,
    pub last_modified: Option<DateTime<Utc>>,
}

/// Analysis result for security research
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisResult {
    pub tool_name: String,
    pub extension_id: String,
    pub timestamp: DateTime<Utc>,
    pub findings: Vec<Finding>,
    pub risk_score: u8,
    pub recommendations: Vec<String>,
}

/// Individual security finding
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Finding {
    pub category: FindingCategory,
    pub severity: Severity,
    pub title: String,
    pub description: String,
    pub evidence: Vec<String>,
    pub mitigation: Option<String>,
}

/// Category of security finding
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FindingCategory {
    Fingerprinting,
    TrialTracking,
    Authentication,
    Storage,
    Network,
    Privacy,
    Security,
}

/// Severity level of finding
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Severity {
    Low,
    Medium,
    High,
    Critical,
}

/// Configuration for security research tools
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolConfig {
    pub tool_name: String,
    pub version: String,
    pub output_format: OutputFormat,
    pub verbose: bool,
    pub mock_mode: bool,
    pub educational_mode: bool,
}

/// Output format options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OutputFormat {
    Console,
    Json,
    Markdown,
    Html,
}

impl Default for ToolConfig {
    fn default() -> Self {
        Self {
            tool_name: "security-research-tool".to_string(),
            version: crate::VERSION.to_string(),
            output_format: OutputFormat::Console,
            verbose: false,
            mock_mode: true,
            educational_mode: true,
        }
    }
}

impl std::fmt::Display for SubscriptionStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SubscriptionStatus::Trial => write!(f, "Trial"),
            SubscriptionStatus::Active => write!(f, "Active"),
            SubscriptionStatus::Expired => write!(f, "Expired"),
            SubscriptionStatus::Cancelled => write!(f, "Cancelled"),
            SubscriptionStatus::Unknown => write!(f, "Unknown"),
        }
    }
}

impl std::fmt::Display for FindingCategory {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            FindingCategory::Fingerprinting => write!(f, "Fingerprinting"),
            FindingCategory::TrialTracking => write!(f, "Trial Tracking"),
            FindingCategory::Authentication => write!(f, "Authentication"),
            FindingCategory::Storage => write!(f, "Storage"),
            FindingCategory::Network => write!(f, "Network"),
            FindingCategory::Privacy => write!(f, "Privacy"),
            FindingCategory::Security => write!(f, "Security"),
        }
    }
}

impl std::fmt::Display for Severity {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Severity::Low => write!(f, "Low"),
            Severity::Medium => write!(f, "Medium"),
            Severity::High => write!(f, "High"),
            Severity::Critical => write!(f, "Critical"),
        }
    }
}
