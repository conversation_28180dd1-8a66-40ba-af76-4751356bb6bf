//! System fingerprinting utilities for educational analysis.
//!
//! This module provides tools to understand how VS Code extensions collect
//! system information for trial tracking and user identification.

use crate::{Result, Finding, FindingCategory, Severity};
use serde::{Deserialize, Serialize};
use sha2::{Sha256, Digest};
use uuid::Uuid;

/// System fingerprint data structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemFingerprint {
    pub machine_id: String,
    pub hardware_info: HardwareInfo,
    pub system_info: SystemInfo,
    pub vscode_info: VSCodeInfo,
    pub network_info: NetworkInfo,
    pub fingerprint_hash: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// Hardware information collected for fingerprinting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HardwareInfo {
    pub cpu_model: String,
    pub cpu_cores: u32,
    pub total_memory: u64,
    pub architecture: String,
    pub hardware_uuid: Option<String>,
}

/// System information for fingerprinting
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SystemInfo {
    pub os_type: String,
    pub os_version: String,
    pub hostname: String,
    pub username: String,
    pub timezone: String,
    pub locale: String,
}

/// VS Code specific information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VSCodeInfo {
    pub version: String,
    pub machine_id: String,
    pub session_id: String,
    pub workspace_id: Option<String>,
    pub extensions_dir: String,
}

/// Network interface information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkInfo {
    pub mac_addresses: Vec<String>,
    pub ip_addresses: Vec<String>,
    pub network_interfaces: Vec<String>,
}

/// Fingerprint collector for educational analysis
pub struct FingerprintCollector {
    mock_mode: bool,
}

impl FingerprintCollector {
    /// Create a new fingerprint collector
    pub fn new(mock_mode: bool) -> Self {
        Self { mock_mode }
    }

    /// Collect system fingerprint (educational simulation)
    pub fn collect_fingerprint(&self) -> Result<SystemFingerprint> {
        if self.mock_mode {
            self.create_mock_fingerprint()
        } else {
            self.collect_real_fingerprint()
        }
    }

    /// Create a mock fingerprint for educational purposes
    fn create_mock_fingerprint(&self) -> Result<SystemFingerprint> {
        let hardware_info = HardwareInfo {
            cpu_model: "Intel Core i7-9750H @ 2.60GHz (Mock)".to_string(),
            cpu_cores: 8,
            total_memory: 16_777_216_000, // 16GB
            architecture: "x86_64".to_string(),
            hardware_uuid: Some(Uuid::new_v4().to_string()),
        };

        let system_info = SystemInfo {
            os_type: "Darwin".to_string(),
            os_version: "23.1.0".to_string(),
            hostname: "mock-research-machine".to_string(),
            username: "researcher".to_string(),
            timezone: "UTC".to_string(),
            locale: "en-US".to_string(),
        };

        let vscode_info = VSCodeInfo {
            version: "1.85.0".to_string(),
            machine_id: Uuid::new_v4().to_string(),
            session_id: Uuid::new_v4().to_string(),
            workspace_id: Some(Uuid::new_v4().to_string()),
            extensions_dir: "/mock/path/to/extensions".to_string(),
        };

        let network_info = NetworkInfo {
            mac_addresses: vec![
                "00:11:22:33:44:55".to_string(),
                "66:77:88:99:AA:BB".to_string(),
            ],
            ip_addresses: vec![
                "*************".to_string(),
                "::1".to_string(),
            ],
            network_interfaces: vec![
                "en0".to_string(),
                "lo0".to_string(),
            ],
        };

        let fingerprint = SystemFingerprint {
            machine_id: vscode_info.machine_id.clone(),
            hardware_info,
            system_info,
            vscode_info,
            network_info,
            fingerprint_hash: String::new(),
            created_at: chrono::Utc::now(),
        };

        let mut fingerprint_with_hash = fingerprint;
        fingerprint_with_hash.fingerprint_hash = self.calculate_fingerprint_hash(&fingerprint_with_hash)?;

        Ok(fingerprint_with_hash)
    }

    /// Collect real system fingerprint (for legitimate research only)
    fn collect_real_fingerprint(&self) -> Result<SystemFingerprint> {
        // This would collect real system information
        // For educational purposes, we'll use mock data with a warning
        tracing::warn!("Real fingerprint collection requested - using mock data for safety");
        self.create_mock_fingerprint()
    }

    /// Calculate a hash of the fingerprint data
    fn calculate_fingerprint_hash(&self, fingerprint: &SystemFingerprint) -> Result<String> {
        let mut hasher = Sha256::new();
        
        // Create a deterministic string representation
        let fingerprint_string = format!(
            "{}|{}|{}|{}|{}|{}|{}|{}",
            fingerprint.machine_id,
            fingerprint.hardware_info.cpu_model,
            fingerprint.hardware_info.cpu_cores,
            fingerprint.hardware_info.total_memory,
            fingerprint.system_info.os_type,
            fingerprint.system_info.hostname,
            fingerprint.vscode_info.version,
            fingerprint.network_info.mac_addresses.join(",")
        );

        hasher.update(fingerprint_string.as_bytes());
        let result = hasher.finalize();
        Ok(hex::encode(result))
    }

    /// Analyze fingerprint uniqueness and persistence
    pub fn analyze_fingerprint(&self, fingerprint: &SystemFingerprint) -> Vec<Finding> {
        let mut findings = Vec::new();

        // Analyze machine ID persistence
        findings.push(Finding {
            category: FindingCategory::Fingerprinting,
            severity: Severity::High,
            title: "VS Code Machine ID Detected".to_string(),
            description: "Extension uses VS Code's built-in machine ID for user tracking. This ID persists across sessions and is difficult to change.".to_string(),
            evidence: vec![format!("Machine ID: {}", fingerprint.machine_id)],
            mitigation: Some("Machine ID is generated by VS Code and stored in user settings. Changing it requires modifying VS Code's internal storage.".to_string()),
        });

        // Analyze hardware fingerprinting
        if fingerprint.hardware_info.cpu_cores > 0 {
            findings.push(Finding {
                category: FindingCategory::Fingerprinting,
                severity: Severity::Medium,
                title: "Hardware Fingerprinting Detected".to_string(),
                description: "Extension collects detailed hardware information that can uniquely identify the system.".to_string(),
                evidence: vec![
                    format!("CPU: {}", fingerprint.hardware_info.cpu_model),
                    format!("Cores: {}", fingerprint.hardware_info.cpu_cores),
                    format!("Memory: {} bytes", fingerprint.hardware_info.total_memory),
                ],
                mitigation: Some("Hardware information is difficult to spoof and provides a stable identifier across software reinstalls.".to_string()),
            });
        }

        // Analyze network fingerprinting
        if !fingerprint.network_info.mac_addresses.is_empty() {
            findings.push(Finding {
                category: FindingCategory::Fingerprinting,
                severity: Severity::High,
                title: "Network Interface Fingerprinting".to_string(),
                description: "Extension collects MAC addresses and network interface information for device identification.".to_string(),
                evidence: fingerprint.network_info.mac_addresses.iter().map(|mac| format!("MAC: {}", mac)).collect(),
                mitigation: Some("MAC addresses are hardware-specific and persist across OS reinstalls. Changing them requires specialized tools.".to_string()),
            });
        }

        // Analyze system information collection
        findings.push(Finding {
            category: FindingCategory::Privacy,
            severity: Severity::Medium,
            title: "System Information Collection".to_string(),
            description: "Extension collects detailed system information including hostname and username.".to_string(),
            evidence: vec![
                format!("OS: {} {}", fingerprint.system_info.os_type, fingerprint.system_info.os_version),
                format!("Hostname: {}", fingerprint.system_info.hostname),
                format!("Username: {}", fingerprint.system_info.username),
            ],
            mitigation: Some("System information can be changed but may affect system functionality.".to_string()),
        });

        findings
    }

    /// Compare two fingerprints to assess similarity
    pub fn compare_fingerprints(&self, fp1: &SystemFingerprint, fp2: &SystemFingerprint) -> FingerprintComparison {
        let mut similarity_score = 0.0;
        let mut differences = Vec::new();

        // Compare machine IDs
        if fp1.machine_id == fp2.machine_id {
            similarity_score += 0.3;
        } else {
            differences.push("Machine ID differs".to_string());
        }

        // Compare hardware info
        if fp1.hardware_info.cpu_model == fp2.hardware_info.cpu_model {
            similarity_score += 0.2;
        } else {
            differences.push("CPU model differs".to_string());
        }

        if fp1.hardware_info.cpu_cores == fp2.hardware_info.cpu_cores {
            similarity_score += 0.1;
        } else {
            differences.push("CPU core count differs".to_string());
        }

        if fp1.hardware_info.total_memory == fp2.hardware_info.total_memory {
            similarity_score += 0.1;
        } else {
            differences.push("Memory amount differs".to_string());
        }

        // Compare system info
        if fp1.system_info.hostname == fp2.system_info.hostname {
            similarity_score += 0.1;
        } else {
            differences.push("Hostname differs".to_string());
        }

        // Compare network info
        let mac_intersection: Vec<_> = fp1.network_info.mac_addresses
            .iter()
            .filter(|mac| fp2.network_info.mac_addresses.contains(mac))
            .collect();
        
        if !mac_intersection.is_empty() {
            similarity_score += 0.2;
        } else {
            differences.push("No common MAC addresses".to_string());
        }

        FingerprintComparison {
            similarity_score,
            differences,
            likely_same_device: similarity_score > 0.7,
        }
    }
}

/// Result of comparing two fingerprints
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FingerprintComparison {
    pub similarity_score: f64,
    pub differences: Vec<String>,
    pub likely_same_device: bool,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_mock_fingerprint_collection() {
        let collector = FingerprintCollector::new(true);
        let fingerprint = collector.collect_fingerprint().unwrap();
        
        assert!(!fingerprint.machine_id.is_empty());
        assert!(!fingerprint.fingerprint_hash.is_empty());
        assert_eq!(fingerprint.hardware_info.cpu_cores, 8);
    }

    #[test]
    fn test_fingerprint_analysis() {
        let collector = FingerprintCollector::new(true);
        let fingerprint = collector.collect_fingerprint().unwrap();
        let findings = collector.analyze_fingerprint(&fingerprint);
        
        assert!(!findings.is_empty());
        assert!(findings.iter().any(|f| matches!(f.category, FindingCategory::Fingerprinting)));
    }

    #[test]
    fn test_fingerprint_comparison() {
        let collector = FingerprintCollector::new(true);
        let fp1 = collector.collect_fingerprint().unwrap();
        let fp2 = collector.collect_fingerprint().unwrap();
        
        let comparison = collector.compare_fingerprints(&fp1, &fp2);
        assert!(comparison.similarity_score >= 0.0 && comparison.similarity_score <= 1.0);
    }
}
