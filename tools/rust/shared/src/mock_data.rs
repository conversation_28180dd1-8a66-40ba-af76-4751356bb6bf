//! Mock data generation for educational security research.
//!
//! This module provides realistic mock data that simulates real VS Code extension
//! behavior without using actual commercial software data.

use crate::types::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{Utc, Duration};

/// Mock extension data generator for educational purposes
pub struct MockExtensionData;

impl MockExtensionData {
    /// Generate a mock VS Code extension for analysis
    pub fn create_mock_extension() -> Extension {
        Extension {
            id: "mock-publisher.mock-extension".to_string(),
            name: "Mock AI Assistant".to_string(),
            version: "1.0.0".to_string(),
            publisher: "MockPublisher".to_string(),
            description: "A mock AI-powered coding assistant for educational analysis".to_string(),
            categories: vec![
                "AI".to_string(),
                "Programming Languages".to_string(),
                "Snippets".to_string(),
            ],
            activation_events: vec!["onStartupFinished".to_string()],
            contributes: ExtensionContributes {
                commands: Self::create_mock_commands(),
                configuration: Self::create_mock_configuration(),
                keybindings: Self::create_mock_keybindings(),
                views: Self::create_mock_views(),
            },
            engines: ExtensionEngines {
                vscode: "^1.82.0".to_string(),
                node: Some(">= 18.15.0".to_string()),
            },
        }
    }

    /// Generate mock commands
    fn create_mock_commands() -> Vec<Command> {
        vec![
            Command {
                command: "mock-extension.authenticate".to_string(),
                title: "Authenticate with Mock Service".to_string(),
                category: Some("Mock Extension".to_string()),
                when: None,
            },
            Command {
                command: "mock-extension.startTrial".to_string(),
                title: "Start Free Trial".to_string(),
                category: Some("Mock Extension".to_string()),
                when: Some("!mock-extension.authenticated".to_string()),
            },
            Command {
                command: "mock-extension.checkSubscription".to_string(),
                title: "Check Subscription Status".to_string(),
                category: Some("Mock Extension".to_string()),
                when: Some("mock-extension.authenticated".to_string()),
            },
        ]
    }

    /// Generate mock configuration properties
    fn create_mock_configuration() -> Vec<ConfigurationProperty> {
        vec![
            ConfigurationProperty {
                key: "mock-extension.enableTelemetry".to_string(),
                title: "Enable Telemetry".to_string(),
                description: "Enable usage telemetry collection".to_string(),
                property_type: "boolean".to_string(),
                default: Some(serde_json::Value::Bool(true)),
            },
            ConfigurationProperty {
                key: "mock-extension.apiEndpoint".to_string(),
                title: "API Endpoint".to_string(),
                description: "API endpoint for subscription validation".to_string(),
                property_type: "string".to_string(),
                default: Some(serde_json::Value::String("https://api.mock-service.com".to_string())),
            },
        ]
    }

    /// Generate mock keybindings
    fn create_mock_keybindings() -> Vec<Keybinding> {
        vec![
            Keybinding {
                command: "mock-extension.quickAction".to_string(),
                key: "ctrl+shift+m".to_string(),
                when: Some("editorTextFocus".to_string()),
            },
        ]
    }

    /// Generate mock views
    fn create_mock_views() -> std::collections::HashMap<String, Vec<View>> {
        let mut views = std::collections::HashMap::new();
        views.insert(
            "mock-panel".to_string(),
            vec![View {
                id: "mock-extension.panel".to_string(),
                name: "Mock Assistant".to_string(),
                view_type: "webview".to_string(),
                when: Some("mock-extension.authenticated".to_string()),
            }],
        );
        views
    }

    /// Generate mock authentication information
    pub fn create_mock_auth_info() -> AuthInfo {
        AuthInfo {
            access_token: Some("mock_access_token_abcdef123456".to_string()),
            refresh_token: Some("mock_refresh_token_789012ghijkl".to_string()),
            tenant_url: Some("https://api.mock-service.com".to_string()),
            scopes: vec!["read".to_string(), "write".to_string(), "premium".to_string()],
            expires_at: Some(Utc::now() + Duration::hours(24)),
            user_id: Some("mock_user_12345".to_string()),
        }
    }

    /// Generate mock subscription information
    pub fn create_mock_subscription_info(status: SubscriptionStatus) -> SubscriptionInfo {
        let (trial_end, subscription_end) = match status {
            SubscriptionStatus::Trial => (Some(Utc::now() + Duration::days(14)), None),
            SubscriptionStatus::Active => (None, Some(Utc::now() + Duration::days(365))),
            SubscriptionStatus::Expired => (Some(Utc::now() - Duration::days(1)), None),
            SubscriptionStatus::Cancelled => (None, Some(Utc::now() - Duration::days(30))),
            SubscriptionStatus::Unknown => (None, None),
        };

        SubscriptionInfo {
            subscription_id: Some(format!("sub_{}", Uuid::new_v4())),
            status,
            plan: "premium".to_string(),
            trial_end,
            subscription_end,
            features: vec![
                "ai_completion".to_string(),
                "advanced_analysis".to_string(),
                "priority_support".to_string(),
            ],
        }
    }

    /// Generate mock trial information
    pub fn create_mock_trial_info(days_used: i64) -> TrialInfo {
        let started_at = Utc::now() - Duration::days(days_used);
        let expires_at = started_at + Duration::days(14);
        let days_remaining = (expires_at - Utc::now()).num_days().max(0);

        TrialInfo {
            trial_id: Uuid::new_v4(),
            started_at,
            expires_at,
            days_remaining,
            usage_count: (days_used * 10) as u32, // Simulate usage
            features_used: vec![
                "completion".to_string(),
                "chat".to_string(),
                "analysis".to_string(),
            ],
        }
    }

    /// Generate mock network endpoints
    pub fn create_mock_network_endpoints() -> Vec<NetworkEndpoint> {
        vec![
            NetworkEndpoint {
                url: "https://api.mock-service.com/auth/login".to_string(),
                method: "POST".to_string(),
                purpose: "User authentication".to_string(),
                auth_required: false,
                parameters: vec!["username".to_string(), "password".to_string()],
            },
            NetworkEndpoint {
                url: "https://api.mock-service.com/subscription/status".to_string(),
                method: "GET".to_string(),
                purpose: "Check subscription status".to_string(),
                auth_required: true,
                parameters: vec!["user_id".to_string()],
            },
            NetworkEndpoint {
                url: "https://api.mock-service.com/trial/start".to_string(),
                method: "POST".to_string(),
                purpose: "Start free trial".to_string(),
                auth_required: true,
                parameters: vec!["machine_id".to_string(), "fingerprint".to_string()],
            },
            NetworkEndpoint {
                url: "https://api.mock-service.com/telemetry".to_string(),
                method: "POST".to_string(),
                purpose: "Send usage telemetry".to_string(),
                auth_required: true,
                parameters: vec!["events".to_string(), "timestamp".to_string()],
            },
        ]
    }

    /// Generate a complete mock analysis result
    pub fn create_mock_analysis_result(tool_name: &str, extension_id: &str) -> AnalysisResult {
        AnalysisResult {
            tool_name: tool_name.to_string(),
            extension_id: extension_id.to_string(),
            timestamp: Utc::now(),
            findings: Self::create_mock_findings(),
            risk_score: 7, // Medium-high risk
            recommendations: Self::create_mock_recommendations(),
        }
    }

    /// Generate mock security findings
    fn create_mock_findings() -> Vec<Finding> {
        vec![
            Finding {
                category: FindingCategory::Fingerprinting,
                severity: Severity::High,
                title: "Comprehensive System Fingerprinting".to_string(),
                description: "Extension collects detailed system information for device identification".to_string(),
                evidence: vec![
                    "Machine ID collection detected".to_string(),
                    "Hardware specifications gathered".to_string(),
                    "Network interface enumeration".to_string(),
                ],
                mitigation: Some("System fingerprinting makes device identification persistent across reinstalls".to_string()),
            },
            Finding {
                category: FindingCategory::TrialTracking,
                severity: Severity::Medium,
                title: "Multi-Layer Trial Tracking".to_string(),
                description: "Trial status stored in multiple persistent locations".to_string(),
                evidence: vec![
                    "Global state storage usage".to_string(),
                    "Encrypted secrets storage".to_string(),
                    "Server-side validation".to_string(),
                ],
                mitigation: Some("Trial data persists across local storage clearing attempts".to_string()),
            },
            Finding {
                category: FindingCategory::Network,
                severity: Severity::Medium,
                title: "Server-Side Validation".to_string(),
                description: "Extension validates trial status against remote servers".to_string(),
                evidence: vec![
                    "API endpoint: /subscription/status".to_string(),
                    "Periodic validation requests".to_string(),
                    "Fingerprint transmission".to_string(),
                ],
                mitigation: Some("Server-side validation prevents local bypass attempts".to_string()),
            },
        ]
    }

    /// Generate mock recommendations
    fn create_mock_recommendations() -> Vec<String> {
        vec![
            "Implement additional server-side validation for trial tracking".to_string(),
            "Consider using hardware-based attestation for device identification".to_string(),
            "Add rate limiting to prevent automated trial creation attempts".to_string(),
            "Implement anomaly detection for suspicious usage patterns".to_string(),
            "Use encrypted communication for all subscription-related API calls".to_string(),
        ]
    }

    /// Create mock data for different trial scenarios
    pub fn create_trial_scenarios() -> Vec<TrialScenario> {
        vec![
            TrialScenario {
                name: "Fresh Installation".to_string(),
                description: "New user starting trial for the first time".to_string(),
                trial_info: Self::create_mock_trial_info(0),
                auth_info: Self::create_mock_auth_info(),
                subscription_info: Self::create_mock_subscription_info(SubscriptionStatus::Trial),
                expected_behavior: "Full trial access granted".to_string(),
            },
            TrialScenario {
                name: "Mid-Trial".to_string(),
                description: "User in the middle of trial period".to_string(),
                trial_info: Self::create_mock_trial_info(7),
                auth_info: Self::create_mock_auth_info(),
                subscription_info: Self::create_mock_subscription_info(SubscriptionStatus::Trial),
                expected_behavior: "Continued trial access with reminder".to_string(),
            },
            TrialScenario {
                name: "Expired Trial".to_string(),
                description: "Trial period has ended".to_string(),
                trial_info: Self::create_mock_trial_info(15),
                auth_info: Self::create_mock_auth_info(),
                subscription_info: Self::create_mock_subscription_info(SubscriptionStatus::Expired),
                expected_behavior: "Trial access blocked, upgrade prompt shown".to_string(),
            },
            TrialScenario {
                name: "Active Subscription".to_string(),
                description: "User with active paid subscription".to_string(),
                trial_info: Self::create_mock_trial_info(14),
                auth_info: Self::create_mock_auth_info(),
                subscription_info: Self::create_mock_subscription_info(SubscriptionStatus::Active),
                expected_behavior: "Full premium access granted".to_string(),
            },
        ]
    }
}

/// Trial scenario for testing and education
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrialScenario {
    pub name: String,
    pub description: String,
    pub trial_info: TrialInfo,
    pub auth_info: AuthInfo,
    pub subscription_info: SubscriptionInfo,
    pub expected_behavior: String,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_mock_extension_creation() {
        let extension = MockExtensionData::create_mock_extension();
        assert_eq!(extension.id, "mock-publisher.mock-extension");
        assert!(!extension.contributes.commands.is_empty());
    }

    #[test]
    fn test_mock_trial_scenarios() {
        let scenarios = MockExtensionData::create_trial_scenarios();
        assert_eq!(scenarios.len(), 4);
        
        let fresh_trial = &scenarios[0];
        assert_eq!(fresh_trial.trial_info.days_remaining, 14);
        
        let expired_trial = &scenarios[2];
        assert!(matches!(expired_trial.subscription_info.status, SubscriptionStatus::Expired));
    }

    #[test]
    fn test_mock_analysis_result() {
        let result = MockExtensionData::create_mock_analysis_result("test-tool", "test-extension");
        assert_eq!(result.tool_name, "test-tool");
        assert!(!result.findings.is_empty());
        assert!(result.risk_score <= 10);
    }
}
