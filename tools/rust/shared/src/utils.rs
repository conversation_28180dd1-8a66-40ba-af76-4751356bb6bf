//! Utility functions for security research tools.

use crate::{Result, ToolConfig, OutputFormat};
use colored::Colorize;
use serde::Serialize;
use std::fs;
use std::path::Path;

/// Format and display analysis results
pub fn display_results<T: Serialize>(results: &T, config: &ToolConfig) -> Result<()> {
    match config.output_format {
        OutputFormat::Console => display_console_results(results, config.verbose),
        OutputFormat::Json => display_json_results(results),
        OutputFormat::Markdown => display_markdown_results(results),
        OutputFormat::Html => display_html_results(results),
    }
}

/// Display results in console format
fn display_console_results<T: Serialize>(results: &T, verbose: bool) -> Result<()> {
    if verbose {
        println!("{}", serde_json::to_string_pretty(results)?);
    } else {
        // For console output, we'd typically format this nicely
        // For now, just show J<PERSON><PERSON> for simplicity
        println!("{}", serde_json::to_string_pretty(results)?);
    }
    Ok(())
}

/// Display results in JSON format
fn display_json_results<T: Serialize>(results: &T) -> Result<()> {
    println!("{}", serde_json::to_string_pretty(results)?);
    Ok(())
}

/// Display results in Markdown format
fn display_markdown_results<T: Serialize>(_results: &T) -> Result<()> {
    // TODO: Implement markdown formatting
    println!("# Security Analysis Results\n");
    println!("*Markdown output not yet implemented*");
    Ok(())
}

/// Display results in HTML format
fn display_html_results<T: Serialize>(_results: &T) -> Result<()> {
    // TODO: Implement HTML formatting
    println!("<!DOCTYPE html><html><head><title>Security Analysis Results</title></head>");
    println!("<body><h1>Security Analysis Results</h1>");
    println!("<p><em>HTML output not yet implemented</em></p>");
    println!("</body></html>");
    Ok(())
}

/// Save results to a file
pub fn save_results<T: Serialize>(results: &T, output_path: &Path, format: &OutputFormat) -> Result<()> {
    let content = match format {
        OutputFormat::Json => serde_json::to_string_pretty(results)?,
        OutputFormat::Markdown => {
            // TODO: Implement proper markdown formatting
            format!("# Security Analysis Results\n\n```json\n{}\n```", serde_json::to_string_pretty(results)?)
        },
        OutputFormat::Html => {
            // TODO: Implement proper HTML formatting
            format!(
                "<!DOCTYPE html><html><head><title>Security Analysis Results</title></head><body><h1>Results</h1><pre>{}</pre></body></html>",
                serde_json::to_string_pretty(results)?
            )
        },
        OutputFormat::Console => serde_json::to_string_pretty(results)?,
    };

    fs::write(output_path, content)?;
    println!("Results saved to: {}", output_path.display());
    Ok(())
}

/// Display a progress indicator
pub fn show_progress(message: &str) {
    println!("{} {}", "🔍".cyan(), message);
}

/// Display a warning message
pub fn show_warning(message: &str) {
    eprintln!("{} {}", "⚠️".yellow(), message.yellow());
}

/// Display an error message
pub fn show_error(message: &str) {
    eprintln!("{} {}", "❌".red(), message.red());
}

/// Display a success message
pub fn show_success(message: &str) {
    println!("{} {}", "✅".green(), message.green());
}

/// Display an info message
pub fn show_info(message: &str) {
    println!("{} {}", "ℹ️".blue(), message);
}

/// Create a formatted table for console output
pub fn create_table(headers: &[&str], rows: &[Vec<String>]) -> String {
    let mut table = String::new();
    
    // Calculate column widths
    let mut widths = headers.iter().map(|h| h.len()).collect::<Vec<_>>();
    for row in rows {
        for (i, cell) in row.iter().enumerate() {
            if i < widths.len() {
                widths[i] = widths[i].max(cell.len());
            }
        }
    }

    // Create header
    table.push_str("┌");
    for (i, width) in widths.iter().enumerate() {
        table.push_str(&"─".repeat(width + 2));
        if i < widths.len() - 1 {
            table.push_str("┬");
        }
    }
    table.push_str("┐\n");

    // Header row
    table.push_str("│");
    for (header, width) in headers.iter().zip(&widths) {
        table.push_str(&format!(" {:<width$} ", header, width = width));
        table.push_str("│");
    }
    table.push_str("\n");

    // Header separator
    table.push_str("├");
    for (i, width) in widths.iter().enumerate() {
        table.push_str(&"─".repeat(width + 2));
        if i < widths.len() - 1 {
            table.push_str("┼");
        }
    }
    table.push_str("┤\n");

    // Data rows
    for row in rows {
        table.push_str("│");
        for (i, width) in widths.iter().enumerate() {
            let cell = row.get(i).map(|s| s.as_str()).unwrap_or("");
            table.push_str(&format!(" {:<width$} ", cell, width = width));
            table.push_str("│");
        }
        table.push_str("\n");
    }

    // Bottom border
    table.push_str("└");
    for (i, width) in widths.iter().enumerate() {
        table.push_str(&"─".repeat(width + 2));
        if i < widths.len() - 1 {
            table.push_str("┴");
        }
    }
    table.push_str("┘");

    table
}

/// Validate that a path exists and is accessible
pub fn validate_path(path: &Path) -> Result<()> {
    if !path.exists() {
        anyhow::bail!("Path does not exist: {}", path.display());
    }
    
    if path.is_file() && !path.metadata()?.permissions().readonly() {
        // File is writable, which is fine
    } else if path.is_dir() {
        // Directory exists, which is fine
    }
    
    Ok(())
}

/// Get VS Code installation paths for different platforms
pub fn get_vscode_paths() -> Vec<std::path::PathBuf> {
    let mut paths = Vec::new();
    
    #[cfg(target_os = "macos")]
    {
        paths.push(dirs::home_dir().unwrap_or_default().join("Library/Application Support/Code"));
        paths.push(dirs::home_dir().unwrap_or_default().join(".vscode"));
    }
    
    #[cfg(target_os = "windows")]
    {
        if let Some(appdata) = dirs::config_dir() {
            paths.push(appdata.join("Code"));
        }
        paths.push(dirs::home_dir().unwrap_or_default().join(".vscode"));
    }
    
    #[cfg(target_os = "linux")]
    {
        if let Some(config) = dirs::config_dir() {
            paths.push(config.join("Code"));
        }
        paths.push(dirs::home_dir().unwrap_or_default().join(".vscode"));
    }
    
    paths
}

/// Check if running in a safe educational environment
pub fn is_educational_environment() -> bool {
    // Check for educational environment indicators
    std::env::var("EDUCATIONAL_MODE").is_ok() ||
    std::env::var("SECURITY_RESEARCH_MODE").is_ok() ||
    std::env::var("CI").is_ok() // CI environments are considered safe
}

/// Generate a timestamp string for file naming
pub fn generate_timestamp() -> String {
    chrono::Utc::now().format("%Y%m%d_%H%M%S").to_string()
}

/// Sanitize a string for use in filenames
pub fn sanitize_filename(input: &str) -> String {
    input
        .chars()
        .map(|c| match c {
            'a'..='z' | 'A'..='Z' | '0'..='9' | '-' | '_' | '.' => c,
            _ => '_',
        })
        .collect()
}

/// Display educational information about a security concept
pub fn display_educational_info(concept: &str, description: &str, examples: &[&str]) {
    println!("\n{}", format!("📚 Educational Info: {}", concept).bold().blue());
    println!("{}", "═".repeat(50).blue());
    println!("\n{}", description);
    
    if !examples.is_empty() {
        println!("\n{}:", "Examples".bold());
        for (i, example) in examples.iter().enumerate() {
            println!("  {}. {}", i + 1, example);
        }
    }
    println!();
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[test]
    fn test_create_table() {
        let headers = &["Name", "Value", "Type"];
        let rows = &[
            vec!["test".to_string(), "123".to_string(), "number".to_string()],
            vec!["example".to_string(), "abc".to_string(), "string".to_string()],
        ];
        
        let table = create_table(headers, rows);
        assert!(table.contains("Name"));
        assert!(table.contains("test"));
        assert!(table.contains("123"));
    }

    #[test]
    fn test_sanitize_filename() {
        assert_eq!(sanitize_filename("test file.txt"), "test_file.txt");
        assert_eq!(sanitize_filename("valid-name_123.json"), "valid-name_123.json");
        assert_eq!(sanitize_filename("invalid/path\\name"), "invalid_path_name");
    }

    #[test]
    fn test_generate_timestamp() {
        let timestamp = generate_timestamp();
        assert!(timestamp.len() >= 15); // YYYYMMDD_HHMMSS format
        assert!(timestamp.contains("_"));
    }

    #[test]
    fn test_save_results() {
        let temp_dir = tempdir().unwrap();
        let output_path = temp_dir.path().join("test_results.json");
        
        let test_data = serde_json::json!({
            "test": "data",
            "number": 42
        });
        
        save_results(&test_data, &output_path, &OutputFormat::Json).unwrap();
        assert!(output_path.exists());
        
        let content = fs::read_to_string(&output_path).unwrap();
        assert!(content.contains("test"));
        assert!(content.contains("42"));
    }
}
