//! Fingerprint Analyzer
//!
//! An educational tool that demonstrates how VS Code extensions collect system
//! fingerprints for trial tracking and user identification. This tool helps
//! understand what data points are gathered and how they're used.

use anyhow::Result;
use clap::{Parser, Subcommand};
use colored::Colorize;
use shared::{
    init, <PERSON><PERSON><PERSON><PERSON>, FingerprintCollector, SystemFingerprint, MockExtensionData,
    ToolConfig, OutputFormat, display_results, show_info, show_warning, show_success,
    display_educational_info, create_table,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Parser)]
#[command(name = "fingerprint-analyzer")]
#[command(about = "Educational tool for analyzing system fingerprinting techniques")]
#[command(long_about = "
This tool demonstrates how VS Code extensions collect system information for
device identification and trial tracking. It's designed for educational and
security research purposes only.

⚠️  EDUCATIONAL USE ONLY ⚠️
This tool uses mock data and does not collect real system information for
malicious purposes. It's designed to help understand fingerprinting techniques.
")]
struct Cli {
    #[command(subcommand)]
    command: Commands,

    /// Output format
    #[arg(short, long, default_value = "console")]
    format: String,

    /// Verbose output
    #[arg(short, long)]
    verbose: bool,

    /// Save results to file
    #[arg(short, long)]
    output: Option<String>,

    /// Use mock data instead of real system info
    #[arg(long, default_value = "true")]
    mock_mode: bool,
}

#[derive(Subcommand)]
enum Commands {
    /// Collect and analyze system fingerprint
    Collect {
        /// Show detailed breakdown of fingerprint components
        #[arg(long)]
        detailed: bool,
    },
    /// Compare two fingerprints for similarity
    Compare {
        /// Path to first fingerprint file
        #[arg(long)]
        fingerprint1: Option<String>,
        /// Path to second fingerprint file
        #[arg(long)]
        fingerprint2: Option<String>,
    },
    /// Analyze fingerprint uniqueness and persistence
    Analyze {
        /// Analyze specific fingerprint file
        #[arg(long)]
        fingerprint_file: Option<String>,
    },
    /// Demonstrate fingerprint evasion techniques and their limitations
    Evasion {
        /// Show why evasion techniques fail
        #[arg(long)]
        show_failures: bool,
    },
    /// Show educational information about fingerprinting
    Explain {
        /// Topic to explain
        topic: String,
    },
}

#[derive(Debug, Serialize, Deserialize)]
struct FingerprintAnalysis {
    fingerprint: SystemFingerprint,
    uniqueness_score: f64,
    persistence_score: f64,
    collectible_data_points: Vec<DataPoint>,
    privacy_implications: Vec<String>,
    evasion_difficulty: EvasionDifficulty,
    educational_notes: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct DataPoint {
    name: String,
    value: String,
    category: String,
    persistence: String,
    spoofability: String,
    privacy_impact: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct EvasionDifficulty {
    overall_score: f64, // 0.0 = easy to evade, 1.0 = impossible to evade
    component_scores: HashMap<String, f64>,
    mitigation_strategies: Vec<String>,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize shared library with ethics check
    init()?;

    let cli = Cli::parse();

    // Parse output format
    let output_format = match cli.format.as_str() {
        "json" => OutputFormat::Json,
        "markdown" => OutputFormat::Markdown,
        "html" => OutputFormat::Html,
        _ => OutputFormat::Console,
    };

    let config = ToolConfig {
        tool_name: "fingerprint-analyzer".to_string(),
        version: shared::VERSION.to_string(),
        output_format,
        verbose: cli.verbose,
        mock_mode: cli.mock_mode,
        educational_mode: true,
    };

    // Log usage for educational analysis
    EthicsChecker::log_usage("fingerprint-analyzer", "analysis");

    match cli.command {
        Commands::Collect { detailed } => {
            let analysis = collect_and_analyze_fingerprint(cli.mock_mode, detailed).await?;
            display_results(&analysis, &config)?;
            
            if let Some(output_path) = cli.output {
                shared::save_results(&analysis, &std::path::Path::new(&output_path), &config.output_format)?;
            }
        }
        Commands::Compare { fingerprint1, fingerprint2 } => {
            let comparison = compare_fingerprints(fingerprint1, fingerprint2, cli.mock_mode).await?;
            display_results(&comparison, &config)?;
        }
        Commands::Analyze { fingerprint_file } => {
            let analysis = analyze_fingerprint_file(fingerprint_file, cli.mock_mode).await?;
            display_results(&analysis, &config)?;
        }
        Commands::Evasion { show_failures } => {
            demonstrate_evasion_techniques(show_failures).await?;
        }
        Commands::Explain { topic } => {
            explain_fingerprinting_concept(&topic);
        }
    }

    Ok(())
}

async fn collect_and_analyze_fingerprint(mock_mode: bool, detailed: bool) -> Result<FingerprintAnalysis> {
    show_info("🔍 Collecting system fingerprint for educational analysis...");
    
    EthicsChecker::verify_educational_operation("collect system fingerprint")?;

    if !mock_mode {
        show_warning("Real system fingerprinting requested - using mock data for safety");
    }

    let collector = FingerprintCollector::new(true); // Always use mock mode for safety
    let fingerprint = collector.collect_fingerprint()?;
    
    // Analyze the fingerprint
    let findings = collector.analyze_fingerprint(&fingerprint);
    
    // Calculate uniqueness score based on data points
    let uniqueness_score = calculate_uniqueness_score(&fingerprint);
    
    // Calculate persistence score
    let persistence_score = calculate_persistence_score(&fingerprint);
    
    // Extract data points for analysis
    let collectible_data_points = extract_data_points(&fingerprint);
    
    // Analyze privacy implications
    let privacy_implications = analyze_privacy_implications(&fingerprint);
    
    // Assess evasion difficulty
    let evasion_difficulty = assess_evasion_difficulty(&fingerprint);
    
    let educational_notes = vec![
        "This fingerprint demonstrates the extensive data collection possible".to_string(),
        "Multiple data points create a unique device signature".to_string(),
        "Hardware information persists across software reinstallation".to_string(),
        "Network interfaces provide additional identification vectors".to_string(),
        "Combining multiple identifiers increases uniqueness and persistence".to_string(),
    ];

    if detailed {
        display_detailed_fingerprint_breakdown(&fingerprint);
    }

    show_success(&format!(
        "✅ Fingerprint collected: {} unique data points", 
        collectible_data_points.len()
    ));

    Ok(FingerprintAnalysis {
        fingerprint,
        uniqueness_score,
        persistence_score,
        collectible_data_points,
        privacy_implications,
        evasion_difficulty,
        educational_notes,
    })
}

fn calculate_uniqueness_score(fingerprint: &SystemFingerprint) -> f64 {
    let mut score: f64 = 0.0;
    
    // Machine ID contributes heavily to uniqueness
    if !fingerprint.machine_id.is_empty() {
        score += 0.3;
    }
    
    // Hardware info
    if fingerprint.hardware_info.cpu_cores > 0 {
        score += 0.2;
    }
    if fingerprint.hardware_info.total_memory > 0 {
        score += 0.1;
    }
    
    // Network info
    if !fingerprint.network_info.mac_addresses.is_empty() {
        score += 0.2;
    }
    
    // System info
    if !fingerprint.system_info.hostname.is_empty() {
        score += 0.1;
    }
    if !fingerprint.system_info.username.is_empty() {
        score += 0.1;
    }
    
    score.min(1.0)
}

fn calculate_persistence_score(fingerprint: &SystemFingerprint) -> f64 {
    let mut score: f64 = 0.0;
    
    // Hardware info is highly persistent
    score += 0.4;
    
    // Machine ID is persistent
    if !fingerprint.machine_id.is_empty() {
        score += 0.3;
    }
    
    // MAC addresses are persistent
    if !fingerprint.network_info.mac_addresses.is_empty() {
        score += 0.2;
    }
    
    // Some system info is moderately persistent
    score += 0.1;
    
    score.min(1.0)
}

fn extract_data_points(fingerprint: &SystemFingerprint) -> Vec<DataPoint> {
    vec![
        DataPoint {
            name: "Machine ID".to_string(),
            value: fingerprint.machine_id.clone(),
            category: "VS Code".to_string(),
            persistence: "High".to_string(),
            spoofability: "Difficult".to_string(),
            privacy_impact: "High".to_string(),
        },
        DataPoint {
            name: "CPU Model".to_string(),
            value: fingerprint.hardware_info.cpu_model.clone(),
            category: "Hardware".to_string(),
            persistence: "Very High".to_string(),
            spoofability: "Very Difficult".to_string(),
            privacy_impact: "Medium".to_string(),
        },
        DataPoint {
            name: "CPU Cores".to_string(),
            value: fingerprint.hardware_info.cpu_cores.to_string(),
            category: "Hardware".to_string(),
            persistence: "Very High".to_string(),
            spoofability: "Very Difficult".to_string(),
            privacy_impact: "Low".to_string(),
        },
        DataPoint {
            name: "Total Memory".to_string(),
            value: format!("{} bytes", fingerprint.hardware_info.total_memory),
            category: "Hardware".to_string(),
            persistence: "Very High".to_string(),
            spoofability: "Very Difficult".to_string(),
            privacy_impact: "Medium".to_string(),
        },
        DataPoint {
            name: "Operating System".to_string(),
            value: format!("{} {}", fingerprint.system_info.os_type, fingerprint.system_info.os_version),
            category: "System".to_string(),
            persistence: "High".to_string(),
            spoofability: "Moderate".to_string(),
            privacy_impact: "Low".to_string(),
        },
        DataPoint {
            name: "Hostname".to_string(),
            value: fingerprint.system_info.hostname.clone(),
            category: "System".to_string(),
            persistence: "Medium".to_string(),
            spoofability: "Easy".to_string(),
            privacy_impact: "High".to_string(),
        },
        DataPoint {
            name: "Username".to_string(),
            value: fingerprint.system_info.username.clone(),
            category: "System".to_string(),
            persistence: "Medium".to_string(),
            spoofability: "Easy".to_string(),
            privacy_impact: "Very High".to_string(),
        },
        DataPoint {
            name: "MAC Addresses".to_string(),
            value: fingerprint.network_info.mac_addresses.join(", "),
            category: "Network".to_string(),
            persistence: "Very High".to_string(),
            spoofability: "Difficult".to_string(),
            privacy_impact: "High".to_string(),
        },
    ]
}

fn analyze_privacy_implications(fingerprint: &SystemFingerprint) -> Vec<String> {
    vec![
        "Personal username and hostname are collected".to_string(),
        "Hardware specifications can reveal device value and capabilities".to_string(),
        "Network interfaces can be used for cross-application tracking".to_string(),
        "Combined data creates a unique device signature".to_string(),
        "Fingerprint persists across software reinstallation".to_string(),
        "Data may be transmitted to remote servers for validation".to_string(),
    ]
}

fn assess_evasion_difficulty(fingerprint: &SystemFingerprint) -> EvasionDifficulty {
    let mut component_scores = HashMap::new();
    
    component_scores.insert("machine_id".to_string(), 0.8); // Hard to change
    component_scores.insert("hardware_info".to_string(), 0.95); // Very hard to spoof
    component_scores.insert("system_info".to_string(), 0.4); // Moderately easy to change
    component_scores.insert("network_info".to_string(), 0.7); // Difficult to change
    
    let overall_score = component_scores.values().sum::<f64>() / component_scores.len() as f64;
    
    let mitigation_strategies = vec![
        "Use virtual machines with randomized hardware profiles".to_string(),
        "Employ network interface spoofing tools".to_string(),
        "Modify VS Code machine ID (requires deep system access)".to_string(),
        "Use containerized environments".to_string(),
        "Note: These techniques may violate software terms of service".to_string(),
    ];
    
    EvasionDifficulty {
        overall_score,
        component_scores,
        mitigation_strategies,
    }
}

fn display_detailed_fingerprint_breakdown(fingerprint: &SystemFingerprint) {
    println!("\n{}", "📊 Detailed Fingerprint Breakdown".bold().blue());
    println!("{}", "═".repeat(50).blue());
    
    // Create a table of fingerprint components
    let headers = &["Component", "Value", "Persistence", "Privacy Impact"];
    let rows = &[
        vec![
            "Machine ID".to_string(),
            fingerprint.machine_id.chars().take(20).collect::<String>() + "...",
            "High".to_string(),
            "High".to_string(),
        ],
        vec![
            "CPU Model".to_string(),
            fingerprint.hardware_info.cpu_model.clone(),
            "Very High".to_string(),
            "Medium".to_string(),
        ],
        vec![
            "CPU Cores".to_string(),
            fingerprint.hardware_info.cpu_cores.to_string(),
            "Very High".to_string(),
            "Low".to_string(),
        ],
        vec![
            "Memory".to_string(),
            format!("{} GB", fingerprint.hardware_info.total_memory / 1_000_000_000),
            "Very High".to_string(),
            "Medium".to_string(),
        ],
        vec![
            "OS Type".to_string(),
            fingerprint.system_info.os_type.clone(),
            "High".to_string(),
            "Low".to_string(),
        ],
        vec![
            "Hostname".to_string(),
            fingerprint.system_info.hostname.clone(),
            "Medium".to_string(),
            "High".to_string(),
        ],
    ];
    
    println!("{}", create_table(headers, rows));
}

async fn compare_fingerprints(
    _fingerprint1: Option<String>,
    _fingerprint2: Option<String>,
    mock_mode: bool,
) -> Result<serde_json::Value> {
    show_info("🔍 Comparing fingerprints for similarity analysis...");
    
    // For educational purposes, create two mock fingerprints
    let collector = FingerprintCollector::new(mock_mode);
    let fp1 = collector.collect_fingerprint()?;
    let fp2 = collector.collect_fingerprint()?;
    
    let comparison = collector.compare_fingerprints(&fp1, &fp2);
    
    show_info(&format!(
        "Fingerprint similarity: {:.1}%", 
        comparison.similarity_score * 100.0
    ));
    
    Ok(serde_json::json!({
        "comparison": comparison,
        "educational_note": "This comparison shows how similar two fingerprints from the same system would be"
    }))
}

async fn analyze_fingerprint_file(
    _fingerprint_file: Option<String>,
    mock_mode: bool,
) -> Result<FingerprintAnalysis> {
    show_info("📁 Analyzing fingerprint file...");
    
    // For educational purposes, analyze a mock fingerprint
    collect_and_analyze_fingerprint(mock_mode, true).await
}

async fn demonstrate_evasion_techniques(show_failures: bool) -> Result<()> {
    show_info("🎭 Demonstrating fingerprint evasion techniques...");
    
    EthicsChecker::verify_educational_operation("demonstrate evasion techniques")?;
    
    display_educational_info(
        "Fingerprint Evasion Techniques",
        "Various techniques exist to attempt fingerprint evasion, but modern systems have countermeasures.",
        &[
            "Virtual machines with randomized hardware",
            "Network interface MAC address spoofing",
            "System information modification",
            "Browser fingerprint randomization",
            "Container-based isolation"
        ]
    );
    
    if show_failures {
        display_educational_info(
            "Why Evasion Techniques Often Fail",
            "Modern fingerprinting systems use multiple detection layers and validation mechanisms.",
            &[
                "Hardware attestation detects virtualization",
                "Behavioral analysis identifies suspicious patterns",
                "Server-side validation cross-references multiple data points",
                "Inconsistent fingerprint components trigger alerts",
                "Deep system integration makes spoofing difficult"
            ]
        );
    }
    
    show_warning("Remember: Attempting to evade commercial software protections may violate terms of service");
    
    Ok(())
}

fn explain_fingerprinting_concept(topic: &str) {
    match topic.to_lowercase().as_str() {
        "basics" => {
            display_educational_info(
                "Fingerprinting Basics",
                "Device fingerprinting collects multiple system identifiers to create a unique signature for each device.",
                &[
                    "Combines hardware, software, and network information",
                    "Creates a probabilistically unique identifier",
                    "Persists across software changes and reinstallation",
                    "Used for trial tracking and user identification",
                    "Balances uniqueness with privacy considerations"
                ]
            );
        },
        "privacy" => {
            display_educational_info(
                "Privacy Implications",
                "Fingerprinting raises important privacy concerns that users and developers should understand.",
                &[
                    "Can track users across different applications",
                    "Reveals personal information (username, hostname)",
                    "Hardware details may indicate device value",
                    "Difficult for users to control or modify",
                    "May persist longer than users expect"
                ]
            );
        },
        "countermeasures" => {
            display_educational_info(
                "Fingerprinting Countermeasures",
                "Various techniques can be used to limit fingerprinting effectiveness.",
                &[
                    "Use virtual machines or containers",
                    "Employ privacy-focused browsers",
                    "Randomize system identifiers where possible",
                    "Use VPNs to mask network information",
                    "Understand software privacy policies"
                ]
            );
        },
        _ => {
            show_info("Available topics: basics, privacy, countermeasures");
        }
    }
}
