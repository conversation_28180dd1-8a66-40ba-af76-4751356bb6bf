[package]
name = "fingerprint-analyzer"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
homepage.workspace = true
documentation.workspace = true
keywords.workspace = true
categories.workspace = true
description = "Educational tool for analyzing system fingerprinting techniques used by VS Code extensions"

[[bin]]
name = "fingerprint-analyzer"
path = "src/main.rs"

[dependencies]
shared = { path = "../shared" }
clap.workspace = true
anyhow.workspace = true
serde.workspace = true
serde_json.workspace = true
tokio.workspace = true
tracing.workspace = true
colored.workspace = true
uuid.workspace = true
chrono.workspace = true
sha2.workspace = true
hex.workspace = true

[dev-dependencies]
tempfile.workspace = true
proptest.workspace = true
