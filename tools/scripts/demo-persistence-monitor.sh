#!/bin/bash

# Educational Security Research Tool Demonstration
# Persistence Monitor - Long-Term Trial Tracking Analysis
# 
# This script demonstrates the capabilities of the persistence monitor
# for educational analysis of VS Code extension trial mechanisms.

set -e

echo "🔬 Educational Security Research Tool Demonstration"
echo "=================================================="
echo "Persistence Monitor - Long-Term Trial Tracking Analysis"
echo ""

# Set environment variable to bypass ethics prompt for demo
export SECURITY_TOOLS_ETHICS_ACCEPTED=true

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}📚 Educational Purpose:${NC}"
echo "This demonstration shows how to use the persistence monitor to analyze"
echo "long-term trial tracking mechanisms in VS Code extensions for educational"
echo "and research purposes only."
echo ""

echo -e "${YELLOW}⚠️  Ethical Guidelines:${NC}"
echo "• This tool is for educational and research purposes ONLY"
echo "• No attempts to bypass commercial software licenses"
echo "• Read-only analysis with no data modification"
echo "• Compliance with software terms of service"
echo "• Responsible disclosure of findings"
echo ""

# Check if VS Code is running
echo -e "${CYAN}🔍 Checking System Prerequisites...${NC}"

if pgrep -f "Visual Studio Code" > /dev/null; then
    echo "✅ VS Code is running"
else
    echo "❌ VS Code is not running"
    echo "   Please start VS Code with the Augment extension before running this demo"
    exit 1
fi

# Check for Augment extension
if [ -d "$HOME/.vscode/extensions/augment.vscode-augment"* ]; then
    echo "✅ Augment extension found"
else
    echo "❌ Augment extension not found"
    echo "   Please install the Augment extension before running this demo"
    exit 1
fi

echo ""

# Build the persistence monitor
echo -e "${CYAN}🔨 Building Persistence Monitor...${NC}"
cargo build --release --bin persistence-monitor
echo "✅ Build completed successfully"
echo ""

# Initialize monitoring database
echo -e "${CYAN}🗄️  Initializing Monitoring Database...${NC}"
DEMO_DIR="./demo-persistence-data"
mkdir -p "$DEMO_DIR"

cargo run --bin persistence-monitor -- init --db-path "$DEMO_DIR/monitoring.db"
echo "✅ Database initialized"
echo ""

# Demonstrate educational explanations
echo -e "${BLUE}📖 Educational Explanations${NC}"
echo "============================================"
echo ""

echo -e "${YELLOW}1. Understanding Persistence Mechanisms:${NC}"
cargo run --bin persistence-monitor -- explain persistence
echo ""

echo -e "${YELLOW}2. Monitoring Methodology:${NC}"
cargo run --bin persistence-monitor -- explain monitoring
echo ""

# Start short-term monitoring demonstration
echo -e "${BLUE}🔄 Starting Demonstration Monitoring${NC}"
echo "============================================"
echo ""

echo -e "${CYAN}Starting 5-minute monitoring demonstration...${NC}"
echo "This will collect 5 snapshots at 1-minute intervals to demonstrate"
echo "the monitoring capabilities."
echo ""

# Run monitoring for 5 minutes with 1-minute intervals
timeout 300 cargo run --bin persistence-monitor -- monitor \
    --extension augment.vscode-augment \
    --interval 1 \
    --duration 0.083 \
    --output-dir "$DEMO_DIR" || true

echo ""
echo "✅ Monitoring demonstration completed"
echo ""

# Analyze the collected data
echo -e "${BLUE}📊 Analyzing Collected Data${NC}"
echo "============================================"
echo ""

echo -e "${CYAN}Running comprehensive analysis...${NC}"
cargo run --bin persistence-monitor -- analyze \
    --data-dir "$DEMO_DIR" \
    --analysis-type comprehensive \
    --visualize

echo ""
echo "✅ Analysis completed"
echo ""

# Generate reports
echo -e "${BLUE}📝 Generating Educational Reports${NC}"
echo "============================================"
echo ""

REPORTS_DIR="$DEMO_DIR/reports"
mkdir -p "$REPORTS_DIR"

echo -e "${CYAN}Generating Markdown report...${NC}"
cargo run --bin persistence-monitor -- report \
    --data-dir "$DEMO_DIR" \
    --format markdown \
    --output "$REPORTS_DIR/persistence_analysis.md"

echo -e "${CYAN}Generating JSON data export...${NC}"
cargo run --bin persistence-monitor -- report \
    --data-dir "$DEMO_DIR" \
    --format json \
    --output "$REPORTS_DIR/analysis_data.json"

echo -e "${CYAN}Generating CSV data export...${NC}"
cargo run --bin persistence-monitor -- report \
    --data-dir "$DEMO_DIR" \
    --format csv \
    --output "$REPORTS_DIR/monitoring_data.csv"

echo -e "${CYAN}Generating HTML report...${NC}"
cargo run --bin persistence-monitor -- report \
    --data-dir "$DEMO_DIR" \
    --format html \
    --output "$REPORTS_DIR/interactive_report.html"

echo ""
echo "✅ All reports generated successfully"
echo ""

# Display results summary
echo -e "${GREEN}🎉 Demonstration Completed Successfully!${NC}"
echo "============================================"
echo ""

echo -e "${YELLOW}📁 Generated Files:${NC}"
echo "• Database: $DEMO_DIR/monitoring.db"
echo "• Markdown Report: $REPORTS_DIR/persistence_analysis.md"
echo "• JSON Data: $REPORTS_DIR/analysis_data.json"
echo "• CSV Data: $REPORTS_DIR/monitoring_data.csv"
echo "• HTML Report: $REPORTS_DIR/interactive_report.html"

if [ -d "$DEMO_DIR/charts" ]; then
    echo "• Visualization Charts: $DEMO_DIR/charts/"
fi

echo ""

echo -e "${YELLOW}📊 Quick Results Summary:${NC}"
if [ -f "$REPORTS_DIR/analysis_data.json" ]; then
    echo "Analysis data has been collected and is available in JSON format."
    echo "The monitoring captured trial state, storage persistence, and"
    echo "system information over the demonstration period."
fi

echo ""

echo -e "${BLUE}🔍 Next Steps for Extended Research:${NC}"
echo ""
echo "For comprehensive long-term analysis, consider:"
echo ""
echo "1. ${CYAN}Extended Monitoring:${NC}"
echo "   cargo run --bin persistence-monitor -- monitor \\"
echo "       --extension augment.vscode-augment \\"
echo "       --interval 60 \\"
echo "       --duration 168 \\"  # 1 week
echo "       --output-dir ./long-term-study"
echo ""
echo "2. ${CYAN}Continuous Monitoring:${NC}"
echo "   cargo run --bin persistence-monitor -- monitor \\"
echo "       --extension augment.vscode-augment \\"
echo "       --interval 120 \\"  # 2 hours
echo "       --output-dir ./continuous-study"
echo ""
echo "3. ${CYAN}Specific Analysis Types:${NC}"
echo "   • --analysis-type persistence  (storage mechanisms)"
echo "   • --analysis-type trends       (long-term patterns)"
echo "   • --analysis-type validation   (server communication)"
echo "   • --analysis-type storage      (multi-location analysis)"
echo ""

echo -e "${YELLOW}📚 Educational Value:${NC}"
echo ""
echo "This demonstration shows how modern VS Code extensions implement"
echo "sophisticated trial protection mechanisms that:"
echo ""
echo "• Use multiple storage locations for redundancy"
echo "• Employ hardware fingerprinting for device identification"
echo "• Maintain server-side validation for remote verification"
echo "• Implement recovery mechanisms for data restoration"
echo "• Adapt protection strategies based on usage patterns"
echo ""

echo -e "${RED}⚠️  Important Reminders:${NC}"
echo ""
echo "• This tool is for EDUCATIONAL PURPOSES ONLY"
echo "• Do NOT use to bypass commercial software licenses"
echo "• Respect intellectual property rights and terms of service"
echo "• Use findings to improve understanding of protection mechanisms"
echo "• Practice responsible disclosure for any security insights"
echo ""

echo -e "${GREEN}🎓 Educational Research Complete!${NC}"
echo ""
echo "The persistence monitor has successfully demonstrated long-term"
echo "analysis capabilities for understanding modern extension trial"
echo "tracking mechanisms. Use these insights responsibly for educational"
echo "and research purposes."
echo ""

# Optional: Open the HTML report if available
if command -v open &> /dev/null && [ -f "$REPORTS_DIR/interactive_report.html" ]; then
    echo -e "${CYAN}Opening interactive HTML report...${NC}"
    open "$REPORTS_DIR/interactive_report.html"
elif command -v xdg-open &> /dev/null && [ -f "$REPORTS_DIR/interactive_report.html" ]; then
    echo -e "${CYAN}Opening interactive HTML report...${NC}"
    xdg-open "$REPORTS_DIR/interactive_report.html"
fi

echo "Demo completed at $(date)"
echo ""
echo "For more information, see: persistence-monitor/README.md"
