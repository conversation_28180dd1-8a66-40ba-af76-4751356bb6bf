#!/bin/bash

# Test script for VS Code Extension Security Research Tools
# This script tests all tools to ensure they work correctly

set -e

echo "🧪 Testing VS Code Extension Security Research Tools"
echo "=================================================="

# Set environment variable to bypass ethics prompt for testing
export SECURITY_TOOLS_ETHICS_ACCEPTED=true

echo ""
echo "🔧 Building all tools..."
cargo build --release

echo ""
echo "✅ Build successful! Testing individual tools..."

echo ""
echo "1️⃣ Testing Fingerprint Analyzer..."
echo "-----------------------------------"
cargo run --bin fingerprint-analyzer -- explain basics
echo ""
cargo run --bin fingerprint-analyzer -- collect --format json > /tmp/fingerprint-test.json
echo "✅ Fingerprint analyzer working correctly"

echo ""
echo "2️⃣ Testing Trial Reset Simulator..."
echo "------------------------------------"
cargo run --bin trial-reset-simulator -- explain fingerprinting
echo ""
cargo run --bin trial-reset-simulator -- clear-storage --all > /tmp/trial-test.json
echo "✅ Trial reset simulator working correctly"

echo ""
echo "3️⃣ Testing Storage Inspector..."
echo "--------------------------------"
cargo run --bin storage-inspector -- explain types
echo ""
cargo run --bin storage-inspector -- inspect > /tmp/storage-test.json
echo "✅ Storage inspector working correctly"

echo ""
echo "4️⃣ Testing Network Traffic Simulator..."
echo "-----------------------------------------"
cargo run --bin network-traffic-simulator -- explain validation
echo ""
cargo run --bin network-traffic-simulator -- simulate-auth > /tmp/network-test.json
echo "✅ Network traffic simulator working correctly"

echo ""
echo "🧪 Running unit tests..."
echo "------------------------"
cargo test

echo ""
echo "🎉 All tests passed successfully!"
echo ""
echo "📁 Test output files created:"
echo "  - /tmp/fingerprint-test.json"
echo "  - /tmp/trial-test.json"
echo "  - /tmp/storage-test.json"
echo "  - /tmp/network-test.json"
echo ""
echo "🚀 Tools are ready for educational use!"
echo ""
echo "⚠️  Remember: These tools are for EDUCATIONAL purposes only!"
echo "   - Do NOT use to bypass commercial software"
echo "   - Follow ethical guidelines in ETHICS.md"
echo "   - Use only for legitimate security research"
