{"id": "aec3d410-e5d7-4842-b9de-9a13ef1a23f9", "version": "1.0.0", "created_at": "2025-06-06T01:16:48.657469Z", "privacy_level": "Maximum", "rotation_enabled": true, "next_rotation": "2025-06-07T01:16:48.657469Z", "device_id": "HbJ7haK2vePkHN8uCHT-bg", "hardware_signature": {"cpu_signature": "apple-apple-silicon-8", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "LWdEdCkWGpxEQQ6pp_rmoA", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "_RQL-qOw7rNiLEAF076jSg", "username_hash": "bT5pJqqxjB22IDnyyLECIQ"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "eZGJIJOJuivOdfblrjTc2w", "session_signature": "pXRQxvnT9VI", "workspace_signature": "GDBD7Eoh_jI", "extensions_signature": "GFA4Mqz51vs"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-IL7-vSxQ", "network_class": "ethernet", "connectivity_signature": "Mc-CGtXqkfA"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.95, "tracking_resistance": 0.855}, "fingerprint_hash": "ff431d172291b5e52b811f38a730d35044f53dd39c55cb782dab04687268a99a"}