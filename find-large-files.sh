#!/usr/bin/env bash

# Find Large Files Script - Identify what's taking up the 13GB "Other" space

echo "🔍 Finding large files and directories..."
echo "========================================"

echo ""
echo "📊 Disk Usage Summary:"
df -h | grep -E "(Filesystem|/dev/disk)"

echo ""
echo "🗂️ Top 20 largest directories in your home folder:"
du -sh ~/Library/* 2>/dev/null | sort -hr | head -20

echo ""
echo "📁 Top 10 largest directories in /private/var:"
sudo du -sh /private/var/* 2>/dev/null | sort -hr | head -10

echo ""
echo "🗃️ Large files (>100MB) in common locations:"
find ~/Library -size +100M -type f 2>/dev/null | head -10
find /private/var -size +100M -type f 2>/dev/null | head -10

echo ""
echo "🧹 Common cleanup targets:"
echo "Browser caches:"
du -sh ~/Library/Caches/com.apple.Safari* 2>/dev/null || echo "  Safari: Not found"
du -sh ~/Library/Caches/Google/Chrome* 2>/dev/null || echo "  Chrome: Not found"
du -sh ~/Library/Caches/Firefox* 2>/dev/null || echo "  Firefox: Not found"

echo ""
echo "System caches:"
du -sh /Library/Caches 2>/dev/null || echo "  System caches: Cannot access"
du -sh ~/Library/Caches 2>/dev/null || echo "  User caches: Not found"

echo ""
echo "Development tools:"
du -sh ~/Library/Developer/Xcode/DerivedData 2>/dev/null || echo "  Xcode DerivedData: Not found"
du -sh ~/.npm 2>/dev/null || echo "  npm cache: Not found"
du -sh ~/.cargo 2>/dev/null || echo "  Cargo cache: Not found"

echo ""
echo "Docker (if installed):"
du -sh ~/Library/Containers/com.docker.docker 2>/dev/null || echo "  Docker: Not found"

echo ""
echo "iOS backups:"
du -sh ~/Library/Application\ Support/MobileSync/Backup 2>/dev/null || echo "  iOS backups: Not found"

echo ""
echo "Logs:"
sudo du -sh /private/var/log 2>/dev/null || echo "  System logs: Cannot access"
du -sh ~/Library/Logs 2>/dev/null || echo "  User logs: Not found"

echo ""
echo "✅ Analysis complete!"
echo ""
echo "💡 To clean up space, you can:"
echo "1. Clear browser caches"
echo "2. Remove old iOS backups"
echo "3. Clean Xcode DerivedData"
echo "4. Clear system logs (with sudo)"
echo "5. Remove unused Docker images"
