#!/usr/bin/env bash

# Test script for Nix Firefox runner
# This script verifies that the Firefox profile is created correctly

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "🧪 Testing Nix Firefox Runner"
echo "=============================="

# Test 1: Check if script exists and is executable
echo "1️⃣ Testing script availability..."
if [[ -x "$SCRIPT_DIR/nix-firefox-runner.sh" ]]; then
    echo "✅ Script is executable"
else
    echo "❌ Script not found or not executable"
    exit 1
fi

# Test 2: Check help functionality
echo "2️⃣ Testing help functionality..."
if "$SCRIPT_DIR/nix-firefox-runner.sh" --help > /tmp/firefox-help.txt 2>&1; then
    echo "✅ Help command works"
    if grep -q "Nix Firefox Fingerprint Runner" /tmp/firefox-help.txt; then
        echo "✅ Help content is correct"
    else
        echo "❌ Help content missing"
    fi
else
    echo "❌ Help command failed"
fi

# Test 3: Check Nix availability
echo "3️⃣ Testing Nix availability..."
if command -v nix-shell &> /dev/null; then
    echo "✅ Nix is available"
else
    echo "⚠️  Nix not available - skipping Nix-specific tests"
    exit 0
fi

# Test 4: Check Firefox availability
echo "4️⃣ Testing Firefox availability..."
if [[ -f "/Applications/Firefox.app/Contents/MacOS/firefox" ]]; then
    echo "✅ Firefox found at /Applications/Firefox.app"
elif command -v firefox &> /dev/null; then
    echo "✅ Firefox found in PATH"
else
    echo "⚠️  Firefox not found - install from https://firefox.com"
fi

# Test 5: Test fingerprint generation (dry run)
echo "5️⃣ Testing fingerprint generation..."
if "$SCRIPT_DIR/nix-firefox-runner.sh" --list > /tmp/firefox-list.txt 2>&1; then
    echo "✅ Fingerprint listing works"
else
    echo "❌ Fingerprint listing failed"
fi

echo ""
echo "🎉 Basic tests completed!"
echo ""
echo "To run a full test with Firefox launch:"
echo "  $SCRIPT_DIR/nix-firefox-runner.sh --new-fingerprint"
echo ""
echo "To run with a specific URL:"
echo "  $SCRIPT_DIR/nix-firefox-runner.sh --new-fingerprint https://browserleaks.com/canvas"
