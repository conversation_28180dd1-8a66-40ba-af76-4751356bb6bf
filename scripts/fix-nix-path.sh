#!/usr/bin/env bash

# Quick Nix PATH Fix Script
# Fixes Nix environment without reinstalling

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Fix Nix environment for current session
fix_current_session() {
    log_info "Fixing Nix environment for current session..."
    
    # Try to source Nix daemon script
    if [[ -f '/nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' ]]; then
        source '/nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh'
        log_success "Sourced Nix daemon script"
    fi
    
    # Add Nix to PATH
    if [[ ":$PATH:" != *":/nix/var/nix/profiles/default/bin:"* ]]; then
        export PATH="/nix/var/nix/profiles/default/bin:$PATH"
        log_success "Added Nix to PATH"
    fi
    
    # Test if nix command works
    if command -v nix &> /dev/null; then
        log_success "Nix is now available in current session"
        nix --version
        return 0
    else
        log_error "Nix is still not available"
        return 1
    fi
}

# Create minimal Nix configuration
create_nix_config() {
    log_info "Creating minimal Nix configuration..."
    
    mkdir -p ~/.config/nix
    
    cat > ~/.config/nix/nix.conf << 'EOF'
# Minimal Nix configuration with 5GB limit
experimental-features = nix-command flakes
max-free = 1073741824
min-free = 268435456
max-store-size = 5368709120
auto-optimise-store = true
keep-outputs = false
keep-derivations = false
EOF
    
    log_success "Nix configuration created"
}

# Fix shell configuration
fix_shell_config() {
    log_info "Fixing shell configuration..."
    
    local shell_rc=""
    case "$SHELL" in
        */zsh)
            shell_rc="$HOME/.zshrc"
            ;;
        */bash)
            shell_rc="$HOME/.bash_profile"
            ;;
        *)
            shell_rc="$HOME/.profile"
            ;;
    esac
    
    # Check if Nix is already configured
    if [[ -f "$shell_rc" ]] && grep -q "nix" "$shell_rc"; then
        log_info "Nix already configured in $shell_rc"
        return 0
    fi
    
    # Add Nix configuration
    cat >> "$shell_rc" << 'EOF'

# Nix environment setup
if [ -e '/nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' ]; then
  . '/nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh'
fi

# Add Nix to PATH
if [[ ":$PATH:" != *":/nix/var/nix/profiles/default/bin:"* ]]; then
    export PATH="/nix/var/nix/profiles/default/bin:$PATH"
fi
EOF
    
    log_success "Shell configuration updated: $shell_rc"
}

# Main execution
main() {
    log_info "Quick Nix PATH Fix"
    echo
    
    # Check if Nix directory exists
    if [[ ! -d "/nix" ]]; then
        log_error "Nix is not installed. Please run: ./scripts/cleanup-and-install-nix.sh"
        exit 1
    fi
    
    # Create configuration
    create_nix_config
    
    # Fix shell configuration
    fix_shell_config
    
    # Fix current session
    if fix_current_session; then
        echo
        log_success "Nix environment fixed!"
        log_info "For new terminals, restart or run: source ~/.zshrc"
        log_info "Current session is ready to use Nix"
    else
        echo
        log_warning "Could not fix Nix in current session"
        log_info "Please restart your terminal or run: source ~/.zshrc"
    fi
}

main "$@"
