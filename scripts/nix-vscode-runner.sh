#!/usr/bin/env bash

# Nix-based VS Code Runner with Dynamic Fingerprint Generation
# This script creates isolated VS Code instances with unique fingerprints on each run

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FINGERPRINTS_DIR="$PROJECT_ROOT/.fingerprints"
PROFILES_DIR="/tmp/vscode-fingerprint-profiles"
DEFAULT_WORKSPACE="$PROJECT_ROOT"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🔧 $1${NC}"
}

# Help function
show_help() {
    cat << EOF
${CYAN}Nix-based VS Code Runner with Dynamic Fingerprint Generation${NC}

USAGE:
    $0 [OPTIONS] [WORKSPACE_PATH]

OPTIONS:
    --new-fingerprint, -n    Generate a new fingerprint for this session
    --fingerprint-id ID      Use specific fingerprint by ID
    --privacy-level LEVEL    Set privacy level (low, medium, high, maximum) [default: high]
    --profile-name NAME      Use custom profile name
    --list-fingerprints, -l  List available fingerprints
    --clean, -c              Clean up old profiles and fingerprints
    --help, -h               Show this help message
    --verbose, -v            Enable verbose output
    --dry-run                Show what would be done without executing

EXAMPLES:
    # Run VS Code with a new fingerprint
    $0 --new-fingerprint

    # Run with specific fingerprint
    $0 --fingerprint-id fp_20241201_123456

    # Run with maximum privacy
    $0 --new-fingerprint --privacy-level maximum

    # Open specific workspace with new fingerprint
    $0 --new-fingerprint /path/to/workspace

    # List available fingerprints
    $0 --list-fingerprints

    # Clean up old data
    $0 --clean

FINGERPRINT ISOLATION:
    Each run creates a completely isolated VS Code environment with:
    - Unique device fingerprint
    - Isolated user data directory
    - Separate extension storage
    - Privacy-protected system signatures
    - Rotatable fingerprint components

EOF
}

# Parse command line arguments
GENERATE_NEW_FP=false
FINGERPRINT_ID=""
PRIVACY_LEVEL="high"
PROFILE_NAME=""
LIST_FINGERPRINTS=false
CLEAN_MODE=false
VERBOSE=false
DRY_RUN=false
WORKSPACE_PATH="$DEFAULT_WORKSPACE"

while [[ $# -gt 0 ]]; do
    case $1 in
        --new-fingerprint|-n)
            GENERATE_NEW_FP=true
            shift
            ;;
        --fingerprint-id)
            FINGERPRINT_ID="$2"
            shift 2
            ;;
        --privacy-level)
            PRIVACY_LEVEL="$2"
            shift 2
            ;;
        --profile-name)
            PROFILE_NAME="$2"
            shift 2
            ;;
        --list-fingerprints|-l)
            LIST_FINGERPRINTS=true
            shift
            ;;
        --clean|-c)
            CLEAN_MODE=true
            shift
            ;;
        --verbose|-v)
            VERBOSE=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        -*)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
        *)
            WORKSPACE_PATH="$1"
            shift
            ;;
    esac
done

# Verbose logging
verbose_log() {
    if [[ "$VERBOSE" == "true" ]]; then
        log_info "$1"
    fi
}

# Check if we're in a Nix environment
check_nix_environment() {
    if [[ -z "${IN_NIX_SHELL:-}" ]] && ! command -v nix &> /dev/null; then
        log_error "This script requires Nix. Please install Nix or run within a Nix shell."
        echo ""
        echo "To enter the Nix environment:"
        echo "  nix develop  # (if you have flakes enabled)"
        echo "  nix-shell    # (legacy)"
        exit 1
    fi
}

# Ensure we're in the Nix development environment
ensure_nix_dev_environment() {
    if [[ -z "${IN_NIX_SHELL:-}" ]]; then
        log_step "Entering Nix development environment..."
        if [[ "$DRY_RUN" == "false" ]]; then
            cd "$PROJECT_ROOT"
            exec nix develop --command "$0" "$@"
        else
            log_info "Would execute: nix develop --command $0 $*"
        fi
    fi
}

# List available fingerprints
list_fingerprints() {
    log_step "Available fingerprints:"
    
    if [[ ! -d "$FINGERPRINTS_DIR" ]]; then
        log_warning "No fingerprints directory found. Generate one with --new-fingerprint"
        return
    fi
    
    local count=0
    for fp_file in "$FINGERPRINTS_DIR"/*.json; do
        if [[ -f "$fp_file" ]]; then
            local fp_name=$(basename "$fp_file" .json)
            local created=$(stat -c %y "$fp_file" 2>/dev/null || stat -f %Sm "$fp_file" 2>/dev/null || echo "unknown")
            echo "  📋 $fp_name (created: $created)"
            ((count++))
        fi
    done
    
    if [[ $count -eq 0 ]]; then
        log_warning "No fingerprints found. Generate one with --new-fingerprint"
    else
        log_success "Found $count fingerprint(s)"
    fi
}

# Clean up old profiles and fingerprints
cleanup_old_data() {
    log_step "Cleaning up old data..."
    
    local cleaned=0
    
    # Clean old profiles (older than 7 days)
    if [[ -d "$PROFILES_DIR" ]]; then
        verbose_log "Cleaning profiles older than 7 days..."
        if [[ "$DRY_RUN" == "false" ]]; then
            find "$PROFILES_DIR" -type d -name "vscode-fp-*" -mtime +7 -exec rm -rf {} + 2>/dev/null || true
        else
            find "$PROFILES_DIR" -type d -name "vscode-fp-*" -mtime +7 -print | while read -r dir; do
                log_info "Would remove: $dir"
                ((cleaned++))
            done
        fi
    fi
    
    # Clean old fingerprints (older than 30 days)
    if [[ -d "$FINGERPRINTS_DIR" ]]; then
        verbose_log "Cleaning fingerprints older than 30 days..."
        if [[ "$DRY_RUN" == "false" ]]; then
            find "$FINGERPRINTS_DIR" -name "*.json" -mtime +30 -delete 2>/dev/null || true
        else
            find "$FINGERPRINTS_DIR" -name "*.json" -mtime +30 -print | while read -r file; do
                log_info "Would remove: $file"
                ((cleaned++))
            done
        fi
    fi
    
    log_success "Cleanup completed"
}

# Generate a new fingerprint
generate_new_fingerprint() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local fp_id="fp_${timestamp}"
    local fp_path="$FINGERPRINTS_DIR/${fp_id}.json"

    log_step "Generating new fingerprint: $fp_id"

    mkdir -p "$FINGERPRINTS_DIR"

    if [[ "$DRY_RUN" == "false" ]]; then
        verbose_log "Building fingerprint generator..."
        cd "$PROJECT_ROOT"
        cargo build --release -p privacy-fingerprint-generator

        verbose_log "Generating fingerprint with privacy level: $PRIVACY_LEVEL"
        cargo run --release -p privacy-fingerprint-generator -- \
            --privacy-level "$PRIVACY_LEVEL" \
            --enable-rotation \
            --format json \
            generate \
            --real-system \
            --save-path "$fp_path"

        if [[ -f "$fp_path" ]]; then
            log_success "Fingerprint generated: $fp_id"
            echo "$fp_id"
        else
            log_error "Failed to generate fingerprint"
            exit 1
        fi
    else
        log_info "Would generate fingerprint: $fp_id at $fp_path"
        echo "$fp_id"
    fi
}

# Create isolated VS Code profile
create_isolated_profile() {
    local fp_id="$1"
    local fp_path="$FINGERPRINTS_DIR/${fp_id}.json"

    if [[ -z "$PROFILE_NAME" ]]; then
        PROFILE_NAME="vscode-fp-${fp_id}"
    fi

    local profile_dir="$PROFILES_DIR/$PROFILE_NAME"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"

    log_step "Creating isolated VS Code profile: $PROFILE_NAME"

    if [[ "$DRY_RUN" == "false" ]]; then
        mkdir -p "$user_data_dir"
        mkdir -p "$extensions_dir"
        mkdir -p "$user_data_dir/User/globalStorage"
        mkdir -p "$user_data_dir/User/workspaceStorage"

        # Copy fingerprint to profile
        if [[ -f "$fp_path" ]]; then
            cp "$fp_path" "$user_data_dir/User/globalStorage/fingerprint.json"
            verbose_log "Fingerprint installed to profile"
        fi

        # Create VS Code settings with fingerprint integration
        cat > "$user_data_dir/User/settings.json" << EOF
{
  "security.workspace.trust.enabled": false,
  "telemetry.telemetryLevel": "off",
  "update.mode": "none",
  "extensions.autoUpdate": false,
  "workbench.enableExperiments": false,
  "workbench.settings.enableNaturalLanguageSearch": false,
  "workbench.startupEditor": "none",
  "window.restoreWindows": "none",
  "privacy.fingerprintIsolation": true,
  "privacy.profileName": "$PROFILE_NAME",
  "privacy.fingerprintId": "$fp_id",
  "privacy.fingerprintPath": "$user_data_dir/User/globalStorage/fingerprint.json",
  "privacy.privacyLevel": "$PRIVACY_LEVEL",
  "augment.fingerprintOverride": "$user_data_dir/User/globalStorage/fingerprint.json"
}
EOF

        # Create keybindings for fingerprint management
        cat > "$user_data_dir/User/keybindings.json" << EOF
[
  {
    "key": "ctrl+shift+f1",
    "command": "workbench.action.showCommands",
    "args": "Privacy: Show Fingerprint Info"
  },
  {
    "key": "ctrl+shift+f2",
    "command": "workbench.action.showCommands",
    "args": "Privacy: Rotate Fingerprint"
  }
]
EOF

        log_success "Profile created: $profile_dir"
    else
        log_info "Would create profile: $profile_dir"
    fi

    echo "$profile_dir"
}

# Launch VS Code with isolated profile
launch_vscode() {
    local profile_dir="$1"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"

    log_step "Launching VS Code with isolated fingerprint..."

    # Prepare VS Code command
    local vscode_cmd=(
        "code"
        "--user-data-dir=$user_data_dir"
        "--extensions-dir=$extensions_dir"
        "--disable-telemetry"
        "--disable-updates"
        "--disable-crash-reporter"
        "--no-sandbox"
    )

    # Add workspace if specified
    if [[ -n "$WORKSPACE_PATH" ]] && [[ -d "$WORKSPACE_PATH" ]]; then
        vscode_cmd+=("$WORKSPACE_PATH")
    fi

    verbose_log "VS Code command: ${vscode_cmd[*]}"

    if [[ "$DRY_RUN" == "false" ]]; then
        log_info "Starting VS Code with fingerprint isolation..."
        log_info "Profile: $PROFILE_NAME"
        log_info "Workspace: $WORKSPACE_PATH"
        log_info "Privacy Level: $PRIVACY_LEVEL"
        echo ""
        log_warning "VS Code will start with a completely isolated environment."
        log_warning "Extensions and settings are separate from your main VS Code."
        echo ""

        # Launch VS Code
        "${vscode_cmd[@]}" &
        local vscode_pid=$!

        log_success "VS Code launched (PID: $vscode_pid)"
        log_info "To view fingerprint details, check: $user_data_dir/User/globalStorage/fingerprint.json"

        # Wait a moment and check if VS Code started successfully
        sleep 2
        if kill -0 $vscode_pid 2>/dev/null; then
            log_success "VS Code is running successfully with isolated fingerprint"
        else
            log_error "VS Code failed to start properly"
            exit 1
        fi
    else
        log_info "Would execute: ${vscode_cmd[*]}"
    fi
}

# Main execution function
main() {
    log_info "Nix-based VS Code Runner with Dynamic Fingerprint Generation"
    echo ""

    # Handle special modes first
    if [[ "$LIST_FINGERPRINTS" == "true" ]]; then
        list_fingerprints
        exit 0
    fi

    if [[ "$CLEAN_MODE" == "true" ]]; then
        cleanup_old_data
        exit 0
    fi

    # Check environment
    check_nix_environment
    ensure_nix_dev_environment "$@"

    # Determine fingerprint to use
    local fp_id=""
    if [[ "$GENERATE_NEW_FP" == "true" ]]; then
        fp_id=$(generate_new_fingerprint)
    elif [[ -n "$FINGERPRINT_ID" ]]; then
        fp_id="$FINGERPRINT_ID"
        local fp_path="$FINGERPRINTS_DIR/${fp_id}.json"
        if [[ ! -f "$fp_path" ]]; then
            log_error "Fingerprint not found: $fp_id"
            log_info "Available fingerprints:"
            list_fingerprints
            exit 1
        fi
    else
        log_error "No fingerprint specified. Use --new-fingerprint or --fingerprint-id"
        show_help
        exit 1
    fi

    # Create isolated profile and launch VS Code
    local profile_dir=$(create_isolated_profile "$fp_id")
    launch_vscode "$profile_dir"
}

# Run main function
main "$@"
