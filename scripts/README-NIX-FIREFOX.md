# Nix Firefox Fingerprint Runner

A privacy-focused Firefox launcher that uses <PERSON> for reproducible environments and dynamic fingerprint protection.

## Features

### 🔒 Privacy Protection
- **Dynamic User Agent Rotation**: Automatically rotates browser identification
- **Canvas Fingerprinting Protection**: Blocks canvas-based tracking
- **WebGL Fingerprinting Protection**: Disables WebGL fingerprinting vectors
- **Audio Fingerprinting Protection**: Blocks audio context fingerprinting
- **Screen Resolution Spoofing**: Masks real screen dimensions
- **Timezone Spoofing**: Randomizes timezone information
- **Language Spoofing**: Rotates accept-language headers
- **Cookie and Tracking Protection**: Enhanced privacy settings

### 🏗️ Nix Integration
- **Reproducible Environment**: Uses Nix for consistent Firefox setups
- **Automatic Extension Management**: Pre-configured privacy extensions
- **Isolated Profiles**: Each session uses a temporary, isolated profile
- **Cross-Platform Support**: Works on any system with Nix installed

### 🛡️ Security Features
- **DNS over HTTPS**: Encrypted DNS queries
- **WebRTC Disabled**: Prevents IP leaks
- **Telemetry Disabled**: No data sent to Mozilla
- **Studies Disabled**: No Firefox experiments
- **Fingerprinting Resistance**: Built-in Firefox privacy features

## Installation

### Prerequisites
1. **Install Nix**: https://nixos.org/download.html
2. **Clone this repository** or download the script

### Quick Start
```bash
# Make script executable
chmod +x scripts/nix-firefox-runner.sh

# Run with new fingerprint
./scripts/nix-firefox-runner.sh --new-fingerprint

# Run with maximum privacy
./scripts/nix-firefox-runner.sh --new-fingerprint --private --tor-mode
```

## Usage

### Basic Usage
```bash
# Generate new fingerprint and open blank page
./scripts/nix-firefox-runner.sh --new-fingerprint

# Open specific URL with new fingerprint
./scripts/nix-firefox-runner.sh --new-fingerprint https://example.com

# Use existing fingerprint
./scripts/nix-firefox-runner.sh --fingerprint-id firefox_fp_20241219_143022
```

### Privacy Modes
```bash
# Private browsing mode
./scripts/nix-firefox-runner.sh --new-fingerprint --private

# Maximum privacy (Tor-like settings)
./scripts/nix-firefox-runner.sh --new-fingerprint --tor-mode

# Custom privacy level
./scripts/nix-firefox-runner.sh --new-fingerprint --privacy-level maximum
```

### Management
```bash
# List available fingerprints
./scripts/nix-firefox-runner.sh --list

# Clean old profiles and fingerprints
./scripts/nix-firefox-runner.sh --clean
```

## Privacy Levels

### Low Privacy
- Standard user agents (Chrome, Firefox variants)
- Basic fingerprinting protection
- Cookies enabled
- Standard tracking protection

### Medium Privacy
- Mixed OS user agents (Windows, macOS, Linux)
- Enhanced fingerprinting protection
- Session cookies only
- Enhanced tracking protection

### High Privacy
- Firefox-focused user agents
- Strong fingerprinting protection
- Cookies disabled
- Maximum tracking protection

### Maximum Privacy
- Tor Browser-like user agents
- All fingerprinting protections enabled
- No cookies, no JavaScript fingerprinting
- DNS over HTTPS enabled
- WebRTC disabled

## Pre-installed Extensions

The Nix environment automatically configures Firefox with these privacy extensions:

- **uBlock Origin**: Advanced ad and tracker blocking
- **Privacy Badger**: Automatic tracker blocking
- **Decentraleyes**: CDN emulation for privacy
- **ClearURLs**: Removes tracking parameters from URLs
- **Canvas Blocker**: Prevents canvas fingerprinting

## Fingerprint Storage

Fingerprints are stored in JSON format in `.fingerprints/` directory:

```json
{
  "id": "firefox_fp_20241219_143022",
  "timestamp": "2024-12-19T14:30:22Z",
  "privacy_level": "maximum",
  "browser_type": "firefox",
  "user_agent": "Mozilla/5.0 (X11; Linux x86_64; rv:78.0) Gecko/20100101 Firefox/78.0",
  "languages": "en-US,en;q=0.9",
  "timezone": "UTC",
  "screen_resolution": "1280x720",
  "canvas_fingerprint_blocked": true,
  "webgl_fingerprint_blocked": true,
  "audio_fingerprint_blocked": true,
  "webrtc_disabled": true,
  "private_browsing": false,
  "tor_mode": false,
  "dns_over_https": true,
  "fingerprinting_resistance": true
}
```

## Technical Details

### Nix Shell Configuration
The script automatically creates a `shell.nix` file with:
- Firefox browser
- Tor tools (for advanced privacy modes)
- Proxychains (for network routing)

### Firefox Configuration
Each profile includes a comprehensive `user.js` with:
- Disabled telemetry and studies
- Enhanced tracking protection
- Fingerprinting resistance
- Network privacy settings
- Security hardening

### Profile Isolation
- Each session uses a temporary profile directory
- Profiles are automatically cleaned up on exit
- No persistent data between sessions (unless explicitly saved)

## Security Considerations

### What This Protects Against
- ✅ Browser fingerprinting
- ✅ Tracking cookies
- ✅ Canvas fingerprinting
- ✅ WebGL fingerprinting
- ✅ Audio fingerprinting
- ✅ WebRTC IP leaks
- ✅ DNS tracking
- ✅ User agent tracking

### What This Doesn't Protect Against
- ❌ Network-level monitoring (use Tor Browser for this)
- ❌ JavaScript-based attacks (use NoScript for this)
- ❌ Deep packet inspection
- ❌ Behavioral analysis
- ❌ Government-level surveillance

### Best Practices
1. **Use with VPN**: Combine with a VPN for network-level privacy
2. **Regular Updates**: Keep Nix and Firefox updated
3. **Avoid Login**: Don't log into accounts when using privacy mode
4. **Use Tor Browser**: For maximum anonymity, use Tor Browser instead
5. **Verify Settings**: Check about:config to verify privacy settings

## Troubleshooting

### Nix Not Found
```bash
# Install Nix (single-user installation)
sh <(curl -L https://nixos.org/nix/install) --no-daemon

# Or multi-user installation (macOS/Linux)
sh <(curl -L https://nixos.org/nix/install) --daemon
```

### Profile Creation Fails
```bash
# Check permissions
ls -la /tmp/firefox-fingerprint-profiles/

# Clean old profiles
./scripts/nix-firefox-runner.sh --clean
```

### Firefox Won't Start
```bash
# Check if Firefox is available in Nix
nix-shell -p firefox --run "firefox --version"

# Try with verbose output
NIX_BUILD_SHELL=/bin/bash ./scripts/nix-firefox-runner.sh --new-fingerprint
```

### Extensions Not Loading
- Extensions are configured via user.js preferences
- Some extensions may need manual installation on first run
- Check Firefox's about:addons page

## Related Scripts

- `simple-vscode-runner.sh`: VS Code fingerprint runner
- `simple-vscodium-runner.sh`: VSCodium fingerprint runner
- `nix-vscode-runner.sh`: Nix-based VS Code runner

## License

This script is part of the exploit-extension project and follows the same ethical usage guidelines. See the main project README and ETHICS.md for details.

## Contributing

1. Test changes with different privacy levels
2. Ensure Nix compatibility across platforms
3. Follow the project's ethical guidelines
4. Update documentation for new features
