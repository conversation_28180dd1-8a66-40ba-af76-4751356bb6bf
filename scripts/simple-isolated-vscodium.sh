#!/usr/bin/env bash

# Simple Isolated VSCodium - Single Window Mode
# No Nix dependencies, just pure isolation

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROFILES_DIR="/tmp/vscodium-single-window"
VSCODIUM_CMD=""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🔧 $1${NC}"; }

show_help() {
    cat << EOF
Simple Isolated VSCodium - Single Window Mode

USAGE:
    $0 [OPTIONS] [WORKSPACE_PATH]

OPTIONS:
    --session-name NAME      Custom session name [default: auto-generated]
    --kill-existing          Kill any existing VSCodium instances
    --workspace PATH         Workspace to open [default: current directory]
    --help, -h               Show this help

FEATURES:
    🪟 Single window mode - prevents multiple windows
    🔒 Complete isolation from other VSCodium instances
    🧹 Automatic cleanup on exit
    🚫 No interference with system VSCodium
    ⚡ No Nix dependencies - works immediately

EXAMPLES:
    $0                                    # Quick isolated session
    $0 --session-name "my-work"           # Named session
    $0 --kill-existing /path/to/project   # Force new session with project
    $0 --workspace /path/to/project       # Open specific project

EOF
}

# Parse arguments
SESSION_NAME=""
KILL_EXISTING=false
WORKSPACE_PATH="$(pwd)"

while [[ $# -gt 0 ]]; do
    case $1 in
        --session-name)
            SESSION_NAME="$2"
            shift 2
            ;;
        --kill-existing)
            KILL_EXISTING=true
            shift
            ;;
        --workspace)
            WORKSPACE_PATH="$2"
            shift 2
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        -*)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
        *)
            WORKSPACE_PATH="$1"
            shift
            ;;
    esac
done

# Find VSCodium executable
find_vscodium() {
    local vscodium_cmd=""

    # Check common VSCodium locations on macOS
    if [[ -f "/Applications/VSCodium.app/Contents/Resources/app/bin/codium" ]]; then
        vscodium_cmd="/Applications/VSCodium.app/Contents/Resources/app/bin/codium"
    elif [[ -f "/usr/local/bin/codium" ]]; then
        vscodium_cmd="/usr/local/bin/codium"
    elif command -v codium &> /dev/null; then
        vscodium_cmd="codium"
    elif [[ -f "/opt/homebrew/bin/codium" ]]; then
        vscodium_cmd="/opt/homebrew/bin/codium"
    elif command -v vscodium &> /dev/null; then
        vscodium_cmd="vscodium"
    elif [[ -f "/snap/bin/codium" ]]; then
        vscodium_cmd="/snap/bin/codium"
    elif command -v flatpak &> /dev/null && flatpak list | grep -q "com.vscodium.codium"; then
        vscodium_cmd="flatpak run com.vscodium.codium"
    fi

    if [[ -z "$vscodium_cmd" ]]; then
        log_error "VSCodium not found!"
        log_info "Please install VSCodium:"
        log_info "  macOS (Homebrew): brew install --cask vscodium"
        log_info "  macOS (Manual):   Download from https://vscodium.com/"
        exit 1
    fi

    echo "$vscodium_cmd"
}

# Kill existing VSCodium instances
kill_existing_instances() {
    log_step "Checking for existing VSCodium instances..."
    
    local killed=false
    
    # Kill VSCodium processes
    if pgrep -f "codium" > /dev/null 2>&1; then
        log_warning "Killing existing VSCodium instances..."
        pkill -f "codium" || true
        killed=true
    fi
    
    if [[ "$killed" == "true" ]]; then
        log_info "Waiting for processes to terminate..."
        sleep 2
        log_success "Existing instances terminated"
    else
        log_success "No existing instances found"
    fi
}

# Create isolated profile
create_isolated_profile() {
    if [[ -z "$SESSION_NAME" ]]; then
        SESSION_NAME="session-$(date +%H%M%S)"
    fi

    local profile_name="isolated-${SESSION_NAME}"
    local profile_dir="$PROFILES_DIR/$profile_name"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"

    log_step "Creating isolated profile: $profile_name"

    # Clean up any existing profile with same name
    if [[ -d "$profile_dir" ]]; then
        rm -rf "$profile_dir"
    fi

    # Create directories
    mkdir -p "$user_data_dir/User"
    mkdir -p "$extensions_dir"

    # Create single-window isolation settings
    cat > "$user_data_dir/User/settings.json" << EOF
{
  "security.workspace.trust.enabled": false,
  "telemetry.telemetryLevel": "off",
  "update.mode": "none",
  "extensions.autoUpdate": false,
  "workbench.enableExperiments": false,
  "workbench.startupEditor": "none",
  "window.restoreWindows": "none",
  "window.newWindowDimensions": "inherit",
  "window.openFilesInNewWindow": "off",
  "window.openFoldersInNewWindow": "off",
  "window.openWithoutArgumentsInNewWindow": "off",
  "window.restoreFullscreen": false,
  "window.titleBarStyle": "custom",
  "window.menuBarVisibility": "toggle",
  "workbench.editor.enablePreview": false,
  "workbench.editor.enablePreviewFromQuickOpen": false,
  "workbench.editor.showTabs": true,
  "workbench.editor.tabCloseButton": "right",
  "workbench.editor.tabSizing": "fit",
  "workbench.editor.wrapTabs": false,
  "workbench.activityBar.visible": true,
  "workbench.statusBar.visible": true,
  "workbench.sideBar.location": "left",
  "workbench.panel.defaultLocation": "bottom",
  "workbench.panel.opensMaximized": "never",
  "privacy.isolationMode": "single_window",
  "privacy.sessionName": "$SESSION_NAME",
  "extensions.gallery.serviceUrl": "https://open-vsx.org/vscode/gallery",
  "extensions.gallery.itemUrl": "https://open-vsx.org/vscode/item"
}
EOF

    # Create simple keybindings
    cat > "$user_data_dir/User/keybindings.json" << EOF
[
  {
    "key": "ctrl+shift+alt+q",
    "command": "workbench.action.quit"
  }
]
EOF

    log_success "Isolated profile created: $profile_dir"
    echo "$profile_dir"
}

# Launch isolated VSCodium
launch_isolated_vscodium() {
    local profile_dir="$1"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"
    local profile_name=$(basename "$profile_dir")

    log_step "Launching isolated VSCodium..."

    # Create lock file to prevent multiple instances
    local lock_file="$user_data_dir/.isolation-lock"
    echo $$ > "$lock_file"
    
    # Set up cleanup on exit
    cleanup_on_exit() {
        log_info "Cleaning up isolated session..."
        rm -f "$lock_file"
        # Optionally remove the entire profile on exit
        # rm -rf "$profile_dir"
    }
    trap cleanup_on_exit EXIT INT TERM

    # Build command with isolation flags
    local cmd=(
        "$VSCODIUM_CMD"
        "--user-data-dir=$user_data_dir"
        "--extensions-dir=$extensions_dir"
        "--disable-telemetry"
        "--disable-updates"
        "--disable-crash-reporter"
        "--new-window"
        "--disable-gpu-sandbox"
        "--no-sandbox"
    )

    if [[ -d "$WORKSPACE_PATH" ]]; then
        cmd+=("$WORKSPACE_PATH")
    fi

    log_info "Starting isolated VSCodium session..."
    log_info "Session: $SESSION_NAME"
    log_info "Profile: $profile_name"
    log_info "Workspace: $WORKSPACE_PATH"
    log_info "Lock file: $lock_file"
    echo ""
    log_warning "This VSCodium instance is completely isolated:"
    log_warning "• Separate user data and extensions"
    log_warning "• Single window mode enforced"
    log_warning "• No interference with other instances"
    echo ""

    # Launch VSCodium and wait for it
    "${cmd[@]}" &
    local vscodium_pid=$!
    
    # Wait a moment for VSCodium to start
    sleep 3
    
    if kill -0 "$vscodium_pid" 2>/dev/null; then
        log_success "Isolated VSCodium launched successfully (PID: $vscodium_pid)"
        echo ""
        log_info "Press Ctrl+C to close this isolated session"
        
        # Wait for VSCodium to exit
        wait "$vscodium_pid"
        log_info "Isolated VSCodium session ended"
    else
        log_error "Failed to start isolated VSCodium"
        exit 1
    fi
}

# Main execution
main() {
    log_info "Simple Isolated VSCodium - Single Window Mode"
    echo ""
    
    # Find VSCodium
    VSCODIUM_CMD=$(find_vscodium)
    log_success "Found VSCodium: $VSCODIUM_CMD"
    
    # Kill existing instances if requested
    if [[ "$KILL_EXISTING" == "true" ]]; then
        kill_existing_instances
    fi

    # Create isolated profile and launch
    local profile_dir=$(create_isolated_profile)
    launch_isolated_vscodium "$profile_dir"
}

# Run main function
main "$@"
