#!/usr/bin/env bash

# Minimal Nix Installation and Configuration Script
# Limits Nix store to 5GB and fixes PATH configuration

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    log_error "This script is designed for macOS. For Linux, please adapt accordingly."
    exit 1
fi

# Create Nix configuration directory
setup_nix_config() {
    log_info "Setting up Nix configuration..."

    mkdir -p ~/.config/nix

    # Create nix.conf with space limitations
    cat > ~/.config/nix/nix.conf << 'EOF'
# Nix configuration with 5GB limit
experimental-features = nix-command flakes
max-free = 1073741824
min-free = 268435456
max-store-size = 5368709120
auto-optimise-store = true
keep-outputs = false
keep-derivations = false
EOF

    log_success "Nix configuration created"
}

# Fix Nix environment in shell
fix_shell_environment() {
    log_info "Fixing shell environment..."

    # Detect shell
    local shell_rc=""
    case "$SHELL" in
        */zsh)
            shell_rc="$HOME/.zshrc"
            ;;
        */bash)
            shell_rc="$HOME/.bash_profile"
            ;;
        *)
            log_warning "Unknown shell: $SHELL, using .profile"
            shell_rc="$HOME/.profile"
            ;;
    esac

    # Remove existing Nix entries
    if [[ -f "$shell_rc" ]]; then
        grep -v "nix" "$shell_rc" > "${shell_rc}.tmp" || true
        mv "${shell_rc}.tmp" "$shell_rc"
    fi

    # Add Nix environment setup
    cat >> "$shell_rc" << 'EOF'

# Nix environment setup
if [ -e '/nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' ]; then
  . '/nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh'
fi

# Add Nix to PATH if not already there
if [[ ":$PATH:" != *":/nix/var/nix/profiles/default/bin:"* ]]; then
    export PATH="/nix/var/nix/profiles/default/bin:$PATH"
fi
EOF

    log_success "Shell environment configured for $SHELL"
}

# Install minimal Nix if not present
install_nix() {
    if command -v nix &> /dev/null; then
        log_success "Nix is already installed"
        return 0
    fi

    log_info "Installing minimal Nix..."

    # Download and install Nix with minimal options
    curl -L https://nixos.org/nix/install | sh -s -- --daemon --yes

    log_success "Nix installation completed"
}

# Clean up existing Nix store to save space
cleanup_nix_store() {
    log_info "Cleaning up Nix store to save space..."

    # Source Nix environment first
    if [[ -f '/nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' ]]; then
        source '/nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh'
    fi

    # Clean up if nix command is available
    if command -v nix &> /dev/null; then
        nix-collect-garbage -d || true
        nix store optimise || true
    fi

    log_success "Nix store cleanup completed"
}

# Test Nix installation
test_nix() {
    log_info "Testing Nix installation..."

    # Source the environment
    if [[ -f '/nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' ]]; then
        source '/nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh'
    fi

    if command -v nix &> /dev/null; then
        log_success "Nix is working correctly"
        nix --version
        return 0
    else
        log_error "Nix installation failed"
        return 1
    fi
}

# Main execution
main() {
    log_info "Minimal Nix Setup (5GB limit)"
    echo

    # Setup configuration first
    setup_nix_config

    # Install Nix if needed
    install_nix

    # Fix shell environment
    fix_shell_environment

    # Clean up to save space
    cleanup_nix_store

    # Test installation
    if test_nix; then
        echo
        log_success "Nix setup completed successfully!"
        log_info "Please restart your terminal or run: source ~/.zshrc"
        log_info "Then test with: nix --version"
    else
        echo
        log_error "Nix setup failed. Please check the installation manually."
        exit 1
    fi
}

main "$@"