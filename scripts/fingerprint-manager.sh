#!/usr/bin/env bash

# Fingerprint Management Utility
# Companion script for managing fingerprints used by nix-vscode-runner.sh

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FINGERPRINTS_DIR="$PROJECT_ROOT/.fingerprints"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🔧 $1${NC}"; }

show_help() {
    cat << EOF
${CYAN}Fingerprint Management Utility${NC}

USAGE:
    $0 COMMAND [OPTIONS]

COMMANDS:
    list                     List all available fingerprints
    show ID                  Show detailed fingerprint information
    generate [OPTIONS]       Generate new fingerprint
    rotate ID                Rotate existing fingerprint
    delete ID                Delete fingerprint
    export ID PATH           Export fingerprint to file
    import PATH [ID]         Import fingerprint from file
    validate ID              Validate fingerprint integrity
    compare ID1 ID2          Compare two fingerprints
    stats                    Show fingerprint statistics

GENERATE OPTIONS:
    --privacy-level LEVEL    Privacy level (low, medium, high, maximum)
    --real-system           Use real system data
    --mock-system           Use mock system data
    --enable-rotation       Enable automatic rotation
    --save-as ID            Save with specific ID

EXAMPLES:
    $0 list
    $0 generate --privacy-level maximum --real-system
    $0 show fp_20241201_123456
    $0 rotate fp_20241201_123456
    $0 compare fp_001 fp_002

EOF
}

# List all fingerprints with details
list_fingerprints() {
    log_step "Available fingerprints:"
    
    if [[ ! -d "$FINGERPRINTS_DIR" ]]; then
        log_warning "No fingerprints directory found"
        return
    fi
    
    local count=0
    printf "%-20s %-12s %-15s %-10s %s\n" "ID" "Privacy" "Created" "Rotation" "Status"
    printf "%-20s %-12s %-15s %-10s %s\n" "----" "-------" "-------" "--------" "------"
    
    for fp_file in "$FINGERPRINTS_DIR"/*.json; do
        if [[ -f "$fp_file" ]]; then
            local fp_id=$(basename "$fp_file" .json)
            local privacy_level=$(jq -r '.privacy_level // "unknown"' "$fp_file" 2>/dev/null || echo "unknown")
            local created=$(jq -r '.created_at // "unknown"' "$fp_file" 2>/dev/null || echo "unknown")
            local rotation=$(jq -r '.rotation_enabled // false' "$fp_file" 2>/dev/null || echo "false")
            local status="✅ Valid"
            
            # Validate JSON
            if ! jq empty "$fp_file" 2>/dev/null; then
                status="❌ Invalid"
            fi
            
            # Format created date
            if [[ "$created" != "unknown" ]]; then
                created=$(date -d "$created" "+%Y-%m-%d" 2>/dev/null || echo "$created")
            fi
            
            printf "%-20s %-12s %-15s %-10s %s\n" "$fp_id" "$privacy_level" "$created" "$rotation" "$status"
            ((count++))
        fi
    done
    
    if [[ $count -eq 0 ]]; then
        log_warning "No fingerprints found"
    else
        echo ""
        log_success "Found $count fingerprint(s)"
    fi
}

# Show detailed fingerprint information
show_fingerprint() {
    local fp_id="$1"
    local fp_path="$FINGERPRINTS_DIR/${fp_id}.json"
    
    if [[ ! -f "$fp_path" ]]; then
        log_error "Fingerprint not found: $fp_id"
        return 1
    fi
    
    log_step "Fingerprint Details: $fp_id"
    echo ""
    
    # Basic information
    echo "📋 Basic Information:"
    echo "  ID: $(jq -r '.id // "unknown"' "$fp_path")"
    echo "  Version: $(jq -r '.version // "unknown"' "$fp_path")"
    echo "  Created: $(jq -r '.created_at // "unknown"' "$fp_path")"
    echo "  Privacy Level: $(jq -r '.privacy_level // "unknown"' "$fp_path")"
    echo "  Rotation Enabled: $(jq -r '.rotation_enabled // false' "$fp_path")"
    
    local next_rotation=$(jq -r '.next_rotation // null' "$fp_path")
    if [[ "$next_rotation" != "null" ]]; then
        echo "  Next Rotation: $next_rotation"
    fi
    
    echo ""
    echo "🖥️  Hardware Signature:"
    echo "  CPU: $(jq -r '.hardware_signature.cpu_signature // "unknown"' "$fp_path")"
    echo "  Memory: $(jq -r '.hardware_signature.memory_class // "unknown"' "$fp_path")"
    echo "  Architecture: $(jq -r '.hardware_signature.architecture // "unknown"' "$fp_path")"
    
    echo ""
    echo "💻 System Signature:"
    echo "  OS Family: $(jq -r '.system_signature.os_family // "unknown"' "$fp_path")"
    echo "  OS Version: $(jq -r '.system_signature.os_version_class // "unknown"' "$fp_path")"
    echo "  Timezone: $(jq -r '.system_signature.timezone_class // "unknown"' "$fp_path")"
    
    echo ""
    echo "🔧 VS Code Signature:"
    echo "  Version Class: $(jq -r '.vscode_signature.version_class // "unknown"' "$fp_path")"
    echo "  Machine ID Hash: $(jq -r '.vscode_signature.machine_id_hash // "unknown"' "$fp_path")"
    
    echo ""
    echo "🔐 Privacy Metadata:"
    echo "  Anonymization Level: $(jq -r '.privacy_metadata.anonymization_level // "unknown"' "$fp_path")"
    echo "  Data Minimization: $(jq -r '.privacy_metadata.data_minimization_applied // false' "$fp_path")"
    echo "  Tracking Protection: $(jq -r '.privacy_metadata.tracking_protection_enabled // false' "$fp_path")"
    
    echo ""
    echo "🔍 Fingerprint Hash:"
    echo "  $(jq -r '.fingerprint_hash // "unknown"' "$fp_path")"
}

# Generate new fingerprint
generate_fingerprint() {
    local privacy_level="high"
    local real_system=false
    local enable_rotation=false
    local save_as=""
    
    # Parse options
    while [[ $# -gt 0 ]]; do
        case $1 in
            --privacy-level)
                privacy_level="$2"
                shift 2
                ;;
            --real-system)
                real_system=true
                shift
                ;;
            --mock-system)
                real_system=false
                shift
                ;;
            --enable-rotation)
                enable_rotation=true
                shift
                ;;
            --save-as)
                save_as="$2"
                shift 2
                ;;
            *)
                log_error "Unknown option: $1"
                return 1
                ;;
        esac
    done
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local fp_id="${save_as:-fp_${timestamp}}"
    local fp_path="$FINGERPRINTS_DIR/${fp_id}.json"
    
    log_step "Generating fingerprint: $fp_id"
    
    mkdir -p "$FINGERPRINTS_DIR"
    
    cd "$PROJECT_ROOT"
    
    local cmd=(
        "cargo" "run" "--release" "-p" "privacy-fingerprint-generator" "--"
        "--privacy-level" "$privacy_level"
        "--format" "json"
    )

    if [[ "$enable_rotation" == "true" ]]; then
        cmd+=("--enable-rotation")
    fi

    cmd+=("generate")
    cmd+=("--save-path" "$fp_path")

    if [[ "$real_system" == "true" ]]; then
        cmd+=("--real-system")
    fi
    
    log_info "Executing: ${cmd[*]}"
    "${cmd[@]}"
    
    if [[ -f "$fp_path" ]]; then
        log_success "Fingerprint generated: $fp_id"
        show_fingerprint "$fp_id"
    else
        log_error "Failed to generate fingerprint"
        return 1
    fi
}

# Main function
main() {
    if [[ $# -eq 0 ]]; then
        show_help
        exit 1
    fi
    
    local command="$1"
    shift
    
    case "$command" in
        list)
            list_fingerprints
            ;;
        show)
            if [[ $# -eq 0 ]]; then
                log_error "Fingerprint ID required"
                exit 1
            fi
            show_fingerprint "$1"
            ;;
        generate)
            generate_fingerprint "$@"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
