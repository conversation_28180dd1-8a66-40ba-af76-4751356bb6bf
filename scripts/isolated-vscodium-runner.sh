#!/usr/bin/env bash

# Isolated VSCodium Runner - Single Window Mode
# Ensures only one VSCodium window opens with complete isolation from other instances

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FINGERPRINTS_DIR="$PROJECT_ROOT/.fingerprints"
PROFILES_DIR="/tmp/vscodium-isolated-profiles"
VSCODIUM_CMD=""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🔧 $1${NC}"; }

show_help() {
    cat << EOF
Isolated VSCodium Runner - Single Window Mode

USAGE:
    $0 [OPTIONS] [WORKSPACE_PATH]

OPTIONS:
    --new-fingerprint, -n    Generate new fingerprint and run VSCodium
    --fingerprint-id ID      Use specific fingerprint by ID
    --privacy-level LEVEL    Privacy level (low, medium, high, maximum) [default: high]
    --session-name NAME      Custom session name for this instance
    --kill-existing          Kill any existing VSCodium instances before starting
    --list, -l               List available fingerprints
    --clean, -c              Clean up old profiles and fingerprints
    --help, -h               Show this help

FEATURES:
    🔒 Complete isolation from other VSCodium/VS Code instances
    🪟 Single window mode - prevents multiple windows
    🔐 Unique fingerprint per session
    🧹 Automatic cleanup on exit
    🚫 No interference with system VSCodium installation

EXAMPLES:
    $0 --new-fingerprint                    # New isolated session
    $0 --new-fingerprint /path/to/project   # Open specific project
    $0 --fingerprint-id fp_123 --session-name "dev-work"
    $0 --kill-existing --new-fingerprint    # Force new session

EOF
}

# Parse arguments
GENERATE_NEW=false
FINGERPRINT_ID=""
PRIVACY_LEVEL="high"
SESSION_NAME=""
KILL_EXISTING=false
LIST_MODE=false
CLEAN_MODE=false
WORKSPACE_PATH="$(pwd)"

while [[ $# -gt 0 ]]; do
    case $1 in
        --new-fingerprint|-n)
            GENERATE_NEW=true
            shift
            ;;
        --fingerprint-id)
            FINGERPRINT_ID="$2"
            shift 2
            ;;
        --privacy-level)
            PRIVACY_LEVEL="$2"
            shift 2
            ;;
        --session-name)
            SESSION_NAME="$2"
            shift 2
            ;;
        --kill-existing)
            KILL_EXISTING=true
            shift
            ;;
        --list|-l)
            LIST_MODE=true
            shift
            ;;
        --clean|-c)
            CLEAN_MODE=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        -*)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
        *)
            WORKSPACE_PATH="$1"
            shift
            ;;
    esac
done

# Find VSCodium executable
find_vscodium() {
    local vscodium_cmd=""

    if [[ -f "/Applications/VSCodium.app/Contents/Resources/app/bin/codium" ]]; then
        vscodium_cmd="/Applications/VSCodium.app/Contents/Resources/app/bin/codium"
    elif [[ -f "/usr/local/bin/codium" ]]; then
        vscodium_cmd="/usr/local/bin/codium"
    elif command -v codium &> /dev/null; then
        vscodium_cmd="codium"
    elif [[ -f "/opt/homebrew/bin/codium" ]]; then
        vscodium_cmd="/opt/homebrew/bin/codium"
    elif command -v vscodium &> /dev/null; then
        vscodium_cmd="vscodium"
    elif [[ -f "/snap/bin/codium" ]]; then
        vscodium_cmd="/snap/bin/codium"
    elif command -v flatpak &> /dev/null && flatpak list | grep -q "com.vscodium.codium"; then
        vscodium_cmd="flatpak run com.vscodium.codium"
    fi

    if [[ -z "$vscodium_cmd" ]]; then
        log_error "VSCodium not found!"
        log_info "Please install VSCodium:"
        log_info "  macOS (Homebrew): brew install --cask vscodium"
        log_info "  macOS (Manual):   Download from https://vscodium.com/"
        exit 1
    fi

    echo "$vscodium_cmd"
}

# Kill existing VSCodium instances
kill_existing_instances() {
    log_step "Checking for existing VSCodium instances..."
    
    local killed=false
    
    # Kill VSCodium processes
    if pgrep -f "codium" > /dev/null 2>&1; then
        log_warning "Killing existing VSCodium instances..."
        pkill -f "codium" || true
        killed=true
    fi
    
    # Kill VS Code processes if they might interfere
    if pgrep -f "Visual Studio Code" > /dev/null 2>&1; then
        log_warning "Found VS Code instances (might interfere)"
        if [[ "$KILL_EXISTING" == "true" ]]; then
            pkill -f "Visual Studio Code" || true
            killed=true
        else
            log_info "Use --kill-existing to terminate VS Code instances too"
        fi
    fi
    
    if [[ "$killed" == "true" ]]; then
        log_info "Waiting for processes to terminate..."
        sleep 2
    else
        log_success "No existing instances found"
    fi
}

# List fingerprints
list_fingerprints() {
    log_step "Available fingerprints:"
    
    if [[ ! -d "$FINGERPRINTS_DIR" ]]; then
        log_warning "No fingerprints found. Generate one with --new-fingerprint"
        return
    fi
    
    local count=0
    for fp_file in "$FINGERPRINTS_DIR"/*.json; do
        if [[ -f "$fp_file" ]]; then
            local fp_id=$(basename "$fp_file" .json)
            local created=$(stat -f %Sm "$fp_file" 2>/dev/null || stat -c %y "$fp_file" 2>/dev/null || echo "unknown")
            echo "  📋 $fp_id (created: $created)"
            ((count++))
        fi
    done
    
    if [[ $count -eq 0 ]]; then
        log_warning "No fingerprints found"
    else
        log_success "Found $count fingerprint(s)"
    fi
}

# Clean old data
cleanup() {
    log_step "Cleaning up old data..."
    
    # Clean old profiles (older than 7 days)
    if [[ -d "$PROFILES_DIR" ]]; then
        find "$PROFILES_DIR" -type d -name "vscodium-isolated-*" -mtime +7 -exec rm -rf {} + 2>/dev/null || true
    fi
    
    # Clean old fingerprints (older than 30 days)
    if [[ -d "$FINGERPRINTS_DIR" ]]; then
        find "$FINGERPRINTS_DIR" -name "*.json" -mtime +30 -delete 2>/dev/null || true
    fi
    
    log_success "Cleanup completed"
}

# Generate fingerprint using the fixed Rust tool
generate_fingerprint() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local fp_id="isolated_fp_${timestamp}"
    local fp_path="$FINGERPRINTS_DIR/${fp_id}.json"

    # Try Rust tool first
    if [[ -f "$PROJECT_ROOT/Cargo.toml" ]] && command -v cargo &> /dev/null; then
        log_step "Generating privacy-protected fingerprint..."

        cd "$PROJECT_ROOT"
        export SECURITY_TOOLS_ETHICS_ACCEPTED=true
        
        if cargo run --release -p privacy-fingerprint-generator -- \
            --privacy-level "$PRIVACY_LEVEL" \
            --enable-rotation \
            --format json \
            generate \
            --real-system \
            --save-path "$fp_path" >/dev/null 2>&1; then
            log_success "Fingerprint generated: $fp_id"
            echo "$fp_id"
            return
        else
            log_warning "Rust tool failed, using mock fingerprint"
        fi
    fi

    # Fallback to mock fingerprint
    log_step "Generating mock fingerprint: $fp_id"
    mkdir -p "$(dirname "$fp_path")"
    
    cat > "$fp_path" << EOF
{
  "id": "$fp_id",
  "version": "1.0.0",
  "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "privacy_level": "$PRIVACY_LEVEL",
  "rotation_enabled": true,
  "editor_type": "vscodium_isolated",
  "session_name": "${SESSION_NAME:-isolated}",
  "device_id": "$(openssl rand -hex 16)",
  "isolation_mode": "single_window"
}
EOF
    
    log_success "Mock fingerprint generated: $fp_id"
    echo "$fp_id"
}

# Create isolated profile
create_isolated_profile() {
    local fp_id="$1"
    local fp_path="$FINGERPRINTS_DIR/${fp_id}.json"

    if [[ -z "$SESSION_NAME" ]]; then
        SESSION_NAME="isolated-$(date +%H%M%S)"
    fi

    local profile_name="vscodium-isolated-${SESSION_NAME}-${fp_id}"
    local profile_dir="$PROFILES_DIR/$profile_name"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"

    log_step "Creating isolated profile: $profile_name"

    # Create directories
    mkdir -p "$user_data_dir/User/globalStorage"
    mkdir -p "$extensions_dir"

    # Copy fingerprint to profile
    if [[ -f "$fp_path" ]]; then
        cp "$fp_path" "$user_data_dir/User/globalStorage/fingerprint.json"
    fi

    # Create single-window isolation settings
    cat > "$user_data_dir/User/settings.json" << EOF
{
  "security.workspace.trust.enabled": false,
  "telemetry.telemetryLevel": "off",
  "update.mode": "none",
  "extensions.autoUpdate": false,
  "workbench.enableExperiments": false,
  "workbench.startupEditor": "none",
  "window.restoreWindows": "none",
  "window.newWindowDimensions": "inherit",
  "window.openFilesInNewWindow": "off",
  "window.openFoldersInNewWindow": "off",
  "window.openWithoutArgumentsInNewWindow": "off",
  "window.restoreFullscreen": false,
  "window.titleBarStyle": "custom",
  "window.menuBarVisibility": "toggle",
  "workbench.editor.enablePreview": false,
  "workbench.editor.enablePreviewFromQuickOpen": false,
  "workbench.editor.showTabs": true,
  "workbench.editor.tabCloseButton": "right",
  "workbench.editor.tabSizing": "fit",
  "workbench.editor.wrapTabs": false,
  "workbench.editor.scrollToSwitchTabs": false,
  "workbench.editor.focusRecentEditorAfterClose": true,
  "workbench.editor.limit.enabled": false,
  "workbench.editor.restoreViewState": false,
  "workbench.activityBar.visible": true,
  "workbench.statusBar.visible": true,
  "workbench.sideBar.location": "left",
  "workbench.panel.defaultLocation": "bottom",
  "workbench.panel.opensMaximized": "never",
  "privacy.fingerprintIsolation": true,
  "privacy.profileName": "$profile_name",
  "privacy.fingerprintId": "$fp_id",
  "privacy.sessionName": "$SESSION_NAME",
  "privacy.isolationMode": "single_window",
  "extensions.gallery.serviceUrl": "https://open-vsx.org/vscode/gallery",
  "extensions.gallery.itemUrl": "https://open-vsx.org/vscode/item"
}
EOF

    # Create keybindings for isolation management
    cat > "$user_data_dir/User/keybindings.json" << EOF
[
  {
    "key": "ctrl+shift+alt+i",
    "command": "workbench.action.showCommands",
    "args": "Privacy: Show Isolation Info"
  },
  {
    "key": "ctrl+shift+alt+q",
    "command": "workbench.action.quit"
  }
]
EOF

    log_success "Isolated profile created: $profile_dir"
    echo "$profile_dir"
}

# Launch isolated VSCodium
launch_isolated_vscodium() {
    local profile_dir="$1"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"
    local profile_name=$(basename "$profile_dir")

    log_step "Launching isolated VSCodium..."

    # Create lock file to prevent multiple instances
    local lock_file="$user_data_dir/.isolation-lock"
    echo $$ > "$lock_file"

    # Set up cleanup on exit
    cleanup_on_exit() {
        log_info "Cleaning up isolated session..."
        rm -f "$lock_file"
        # Optionally remove the entire profile on exit
        # rm -rf "$profile_dir"
    }
    trap cleanup_on_exit EXIT INT TERM

    # Build command with isolation flags
    local cmd=(
        "$VSCODIUM_CMD"
        "--user-data-dir=$user_data_dir"
        "--extensions-dir=$extensions_dir"
        "--disable-telemetry"
        "--disable-updates"
        "--disable-crash-reporter"
        "--new-window"
        "--disable-gpu-sandbox"
        "--no-sandbox"
        "--disable-dev-shm-usage"
        "--disable-background-timer-throttling"
        "--disable-backgrounding-occluded-windows"
        "--disable-renderer-backgrounding"
    )

    if [[ -d "$WORKSPACE_PATH" ]]; then
        cmd+=("$WORKSPACE_PATH")
    fi

    log_info "Starting isolated VSCodium session..."
    log_info "Session: $SESSION_NAME"
    log_info "Profile: $profile_name"
    log_info "Workspace: $WORKSPACE_PATH"
    log_info "Privacy Level: $PRIVACY_LEVEL"
    log_info "Lock file: $lock_file"
    echo ""
    log_warning "This VSCodium instance is completely isolated:"
    log_warning "• Separate user data and extensions"
    log_warning "• Unique fingerprint and session"
    log_warning "• No interference with other instances"
    log_warning "• Single window mode enforced"
    echo ""

    # Launch VSCodium and wait for it
    "${cmd[@]}" &
    local vscodium_pid=$!

    # Wait a moment for VSCodium to start
    sleep 3

    if kill -0 "$vscodium_pid" 2>/dev/null; then
        log_success "Isolated VSCodium launched successfully (PID: $vscodium_pid)"
        log_info "Fingerprint details: $user_data_dir/User/globalStorage/fingerprint.json"
        echo ""
        log_info "Press Ctrl+C to close this isolated session"

        # Wait for VSCodium to exit
        wait "$vscodium_pid"
        log_info "Isolated VSCodium session ended"
    else
        log_error "Failed to start isolated VSCodium"
        exit 1
    fi
}

# Main execution
main() {
    log_info "Isolated VSCodium Runner - Single Window Mode"
    echo ""

    # Handle special modes
    if [[ "$LIST_MODE" == "true" ]]; then
        list_fingerprints
        exit 0
    fi

    if [[ "$CLEAN_MODE" == "true" ]]; then
        cleanup
        exit 0
    fi

    # Find VSCodium
    VSCODIUM_CMD=$(find_vscodium)
    log_success "Found VSCodium: $VSCODIUM_CMD"

    # Kill existing instances if requested
    if [[ "$KILL_EXISTING" == "true" ]]; then
        kill_existing_instances
    fi

    # Determine fingerprint to use
    local fp_id=""
    if [[ "$GENERATE_NEW" == "true" ]]; then
        fp_id=$(generate_fingerprint 2>/dev/null | tail -1 | sed 's/\x1b\[[0-9;]*m//g' | tr -d '\n')
        # Validate the fingerprint ID format
        if [[ ! "$fp_id" =~ ^isolated_fp_[0-9]{8}_[0-9]{6}$ ]]; then
            log_error "Invalid fingerprint ID generated: '$fp_id'"
            fp_id="isolated_fp_$(date +%Y%m%d_%H%M%S)"
        fi
    elif [[ -n "$FINGERPRINT_ID" ]]; then
        fp_id="$FINGERPRINT_ID"
        if [[ ! -f "$FINGERPRINTS_DIR/${fp_id}.json" ]]; then
            log_error "Fingerprint not found: $fp_id"
            list_fingerprints
            exit 1
        fi
    else
        log_error "No fingerprint specified. Use --new-fingerprint or --fingerprint-id"
        show_help
        exit 1
    fi

    log_info "Using fingerprint: $fp_id"

    # Create isolated profile and launch
    local profile_dir=$(create_isolated_profile "$fp_id")
    launch_isolated_vscodium "$profile_dir"
}

# Run main function
main "$@"
