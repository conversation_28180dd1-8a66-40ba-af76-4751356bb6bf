#!/usr/bin/env bash

# Nix Space Management Script
# Manages Nix store to keep it under 5GB

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Source Nix environment
source_nix() {
    if [[ -f '/nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' ]]; then
        source '/nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh'
    else
        log_error "Nix not found. Please install Nix first."
        exit 1
    fi
}

# Check current Nix store size
check_store_size() {
    local size_bytes=$(du -s /nix/store 2>/dev/null | cut -f1)
    local size_mb=$((size_bytes / 1024))
    local size_gb=$((size_mb / 1024))
    
    echo "$size_gb"
}

# Clean up Nix store
cleanup_store() {
    log_info "Cleaning up Nix store..."
    
    # Collect garbage
    nix-collect-garbage -d
    
    # Optimize store
    nix store optimise
    
    log_success "Nix store cleanup completed"
}

# Monitor and enforce 5GB limit
enforce_limit() {
    local current_size=$(check_store_size)
    local limit_gb=5
    
    log_info "Current Nix store size: ${current_size}GB (limit: ${limit_gb}GB)"
    
    if [[ $current_size -gt $limit_gb ]]; then
        log_warning "Nix store exceeds ${limit_gb}GB limit!"
        log_info "Running cleanup to reduce size..."
        cleanup_store
        
        # Check again after cleanup
        current_size=$(check_store_size)
        if [[ $current_size -gt $limit_gb ]]; then
            log_warning "Store still exceeds limit after cleanup: ${current_size}GB"
            log_info "Consider removing unused packages manually"
        else
            log_success "Store size reduced to ${current_size}GB"
        fi
    else
        log_success "Store size is within limit: ${current_size}GB"
    fi
}

# Show store statistics
show_stats() {
    log_info "Nix Store Statistics"
    echo "===================="
    
    local size_gb=$(check_store_size)
    echo "Total size: ${size_gb}GB"
    
    echo ""
    echo "Top 10 largest packages:"
    nix path-info --all --size | sort -k2 -n | tail -10 | while read path size; do
        local size_mb=$((size / 1024 / 1024))
        echo "  ${size_mb}MB - $(basename $path)"
    done
}

# Setup automatic cleanup
setup_auto_cleanup() {
    log_info "Setting up automatic cleanup..."
    
    # Create a simple cleanup script
    cat > ~/.nix-auto-cleanup.sh << 'EOF'
#!/usr/bin/env bash
source /nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh
nix-collect-garbage --delete-older-than 7d
nix store optimise
EOF
    
    chmod +x ~/.nix-auto-cleanup.sh
    
    log_success "Auto-cleanup script created at ~/.nix-auto-cleanup.sh"
    log_info "Run it periodically to maintain space: ~/.nix-auto-cleanup.sh"
}

# Main function
main() {
    case "${1:-status}" in
        "status"|"check")
            source_nix
            enforce_limit
            ;;
        "clean"|"cleanup")
            source_nix
            cleanup_store
            enforce_limit
            ;;
        "stats"|"statistics")
            source_nix
            show_stats
            ;;
        "setup-auto")
            setup_auto_cleanup
            ;;
        "help"|"--help"|"-h")
            echo "Nix Space Management Script"
            echo ""
            echo "Usage: $0 [COMMAND]"
            echo ""
            echo "Commands:"
            echo "  status     Check current store size and enforce 5GB limit"
            echo "  clean      Clean up store and enforce limit"
            echo "  stats      Show detailed store statistics"
            echo "  setup-auto Setup automatic cleanup script"
            echo "  help       Show this help"
            echo ""
            echo "Examples:"
            echo "  $0 status    # Check current status"
            echo "  $0 clean     # Force cleanup"
            echo "  $0 stats     # Show statistics"
            ;;
        *)
            log_error "Unknown command: $1"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

main "$@"
