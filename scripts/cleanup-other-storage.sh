#!/usr/bin/env bash

# macOS "Other" Storage Cleanup Script
# Helps identify and clean up space taken by "Other" storage category

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Convert bytes to human readable
human_readable() {
    local bytes=$1
    if [[ $bytes -gt 1073741824 ]]; then
        echo "$((bytes / 1073741824))GB"
    elif [[ $bytes -gt 1048576 ]]; then
        echo "$((bytes / 1048576))MB"
    elif [[ $bytes -gt 1024 ]]; then
        echo "$((bytes / 1024))KB"
    else
        echo "${bytes}B"
    fi
}

# Check disk usage of common "Other" storage locations
check_other_storage() {
    log_info "Analyzing 'Other' storage locations..."
    echo
    
    # System logs
    if [[ -d "/private/var/log" ]]; then
        local log_size=$(du -sk /private/var/log 2>/dev/null | cut -f1)
        log_size=$((log_size * 1024))
        echo "System logs: $(human_readable $log_size)"
    fi
    
    # Cache directories
    if [[ -d "/Library/Caches" ]]; then
        local cache_size=$(du -sk /Library/Caches 2>/dev/null | cut -f1)
        cache_size=$((cache_size * 1024))
        echo "System caches: $(human_readable $cache_size)"
    fi
    
    # User caches
    if [[ -d "$HOME/Library/Caches" ]]; then
        local user_cache_size=$(du -sk "$HOME/Library/Caches" 2>/dev/null | cut -f1)
        user_cache_size=$((user_cache_size * 1024))
        echo "User caches: $(human_readable $user_cache_size)"
    fi
    
    # Nix store
    if [[ -d "/nix" ]]; then
        local nix_size=$(du -sk /nix 2>/dev/null | cut -f1)
        nix_size=$((nix_size * 1024))
        echo "Nix store: $(human_readable $nix_size)"
    fi
    
    # Docker (if installed)
    if [[ -d "$HOME/Library/Containers/com.docker.docker" ]]; then
        local docker_size=$(du -sk "$HOME/Library/Containers/com.docker.docker" 2>/dev/null | cut -f1)
        docker_size=$((docker_size * 1024))
        echo "Docker: $(human_readable $docker_size)"
    fi
    
    # Xcode derived data
    if [[ -d "$HOME/Library/Developer/Xcode/DerivedData" ]]; then
        local xcode_size=$(du -sk "$HOME/Library/Developer/Xcode/DerivedData" 2>/dev/null | cut -f1)
        xcode_size=$((xcode_size * 1024))
        echo "Xcode DerivedData: $(human_readable $xcode_size)"
    fi
    
    # iOS device backups
    if [[ -d "$HOME/Library/Application Support/MobileSync/Backup" ]]; then
        local backup_size=$(du -sk "$HOME/Library/Application Support/MobileSync/Backup" 2>/dev/null | cut -f1)
        backup_size=$((backup_size * 1024))
        echo "iOS backups: $(human_readable $backup_size)"
    fi
}

# Clean system logs
clean_logs() {
    log_info "Cleaning system logs..."
    
    # Clean system logs older than 7 days
    sudo log collect --last 7d --output /tmp/recent_logs.logarchive 2>/dev/null || true
    sudo rm -rf /private/var/log/*.log.* 2>/dev/null || true
    sudo rm -rf /private/var/log/asl/*.asl 2>/dev/null || true
    
    log_success "System logs cleaned"
}

# Clean user caches
clean_user_caches() {
    log_info "Cleaning user caches..."
    
    # Clean browser caches
    rm -rf "$HOME/Library/Caches/com.apple.Safari" 2>/dev/null || true
    rm -rf "$HOME/Library/Caches/Google/Chrome" 2>/dev/null || true
    rm -rf "$HOME/Library/Caches/Firefox" 2>/dev/null || true
    
    # Clean system caches
    rm -rf "$HOME/Library/Caches/com.apple.dt.Xcode" 2>/dev/null || true
    
    log_success "User caches cleaned"
}

# Clean Xcode derived data
clean_xcode() {
    if [[ -d "$HOME/Library/Developer/Xcode/DerivedData" ]]; then
        log_info "Cleaning Xcode DerivedData..."
        rm -rf "$HOME/Library/Developer/Xcode/DerivedData"/* 2>/dev/null || true
        log_success "Xcode DerivedData cleaned"
    fi
}

# Optimize Nix store
optimize_nix() {
    if command -v nix &> /dev/null; then
        log_info "Optimizing Nix store..."
        source /nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh
        nix-collect-garbage -d
        nix store optimise
        log_success "Nix store optimized"
    fi
}

# Main cleanup function
run_cleanup() {
    log_info "Starting 'Other' storage cleanup..."
    echo
    
    clean_logs
    clean_user_caches
    clean_xcode
    optimize_nix
    
    echo
    log_success "Cleanup completed!"
    log_info "Run 'check' to see the results"
}

# Show storage breakdown
show_breakdown() {
    log_info "macOS Storage Breakdown"
    echo "======================="
    
    # Use system_profiler to get storage info
    system_profiler SPStorageDataType | grep -E "(Volume|Available|Used|Capacity)"
    
    echo
    check_other_storage
}

# Main function
main() {
    case "${1:-help}" in
        "check"|"status")
            check_other_storage
            ;;
        "clean"|"cleanup")
            run_cleanup
            ;;
        "breakdown"|"storage")
            show_breakdown
            ;;
        "nix-only")
            optimize_nix
            ;;
        "help"|"--help"|"-h")
            echo "macOS 'Other' Storage Cleanup Script"
            echo ""
            echo "Usage: $0 [COMMAND]"
            echo ""
            echo "Commands:"
            echo "  check      Check current 'Other' storage usage"
            echo "  clean      Run full cleanup (logs, caches, Xcode, Nix)"
            echo "  breakdown  Show detailed storage breakdown"
            echo "  nix-only   Only optimize Nix store"
            echo "  help       Show this help"
            echo ""
            echo "Examples:"
            echo "  $0 check      # Check current usage"
            echo "  $0 clean      # Run full cleanup"
            echo "  $0 nix-only   # Only clean Nix"
            ;;
        *)
            log_error "Unknown command: $1"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

main "$@"
