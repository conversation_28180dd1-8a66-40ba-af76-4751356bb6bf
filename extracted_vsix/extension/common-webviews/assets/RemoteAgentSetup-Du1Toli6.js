import{S as se,i as ce,s as ie,a as re,b as Te,K as Ge,L as Oe,M as je,N as He,h as b,d as be,O as Ve,g as qe,n as U,j as ye,R as Y,V as k,D as P,E as ue,c as y,e as _,f as N,X as Q,Y as Z,Z as J,u as p,q as G,t as d,r as O,aa as ft,T as De,y as L,z as C,B as A,G as T,H as te,an as ze,a5 as Ae,a7 as gt,w as he,x as we,A as ve,a3 as ge,af as Re,a1 as nt,a0 as un,am as Ke,a8 as Qe,ab as An,a4 as _t,ax as Ze,ag as $t,ad as $n,ae as Je}from"./SpinnerAugment-JC8TPhVf.js";import{B as Rn,A as mn,a as mt,b as pt,C as kn,g as Sn}from"./main-panel-RCGJ0jgP.js";import{d as Lt,T as ht}from"./Content-xvE836E_.js";import"./lodash-C-61Uc4F.js";import{R as In}from"./types-BSMhNRWH.js";import{G as wt}from"./folder-D_G6V62q.js";import{B as Me}from"./ButtonAugment-CRFFE3_i.js";import{C as ot,P as Fn}from"./pen-to-square-3TLxExQu.js";import{T as Fe}from"./TextTooltipAugment-BlDY2tAQ.js";import{f as et}from"./index-CW7fyhvB.js";import{M as vt,R as pn}from"./magnifying-glass-D5YyJVGd.js";import{C as dn,G as bt,T as En}from"./github-Du8Ax-RE.js";import{e as tt,u as fn,o as gn}from"./BaseButton-D8yhCvaJ.js";import{D as le,C as Nn,T as Bn}from"./index-BFtESN_v.js";import{R as rt}from"./open-in-new-window-BX_nUqUb.js";import{I as st}from"./IconButtonAugment-BQL_8yIN.js";import{T as hn}from"./terminal-BBUsFUTj.js";import{A as Dn}from"./arrow-up-right-from-square-q_X4-2Am.js";import{T as wn}from"./Keybindings-lGeuBRaW.js";import{R as Ct}from"./types-Cgd-nZOV.js";import{E as zn}from"./exclamation-triangle-DfKf7sb_.js";import{T as Mn}from"./StatusIndicator-BiyeFzqm.js";import"./layer-group-DyG7sLW7.js";import"./design-system-init-Bf1-mlh4.js";import"./test_service_pb-DM0n7l7E.js";import"./chat-types-NgqNgjwU.js";import"./diff-utils-DXaAmVnZ.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-CK0xjdO4.js";import"./index-DiI90jLk.js";import"./isObjectLike-BA2QYXi-.js";import"./globals-D0QH3NT1.js";import"./await_block-C0teov-5.js";import"./CardAugment-BAO5rOsN.js";import"./ellipsis-ce3_p-Q7.js";import"./keypress-DD1aQVr0.js";import"./file-paths-BcSg4gks.js";import"./Filespan-DeFTcAEj.js";import"./folder-opened-bSDyFrZo.js";import"./MaterialIcon-D8Nb6HkU.js";import"./types-B5Ac2hek.js";import"./TextAreaAugment-DxOmi7vy.js";import"./autofix-state-d-ymFdyn.js";import"./VSCodeCodicon-zeLUoeQd.js";import"./augment-logo-E1jEbeRV.js";import"./chat-flags-model-u-ZFJEJp.js";function At(r){const e=r.match(/github\.com\/([^/]+)\/([^/]+?)(?:\.git|\/|$)/);if(e)return{owner:e[1],name:e[2]}}function Rt(r){return r.replace(/^origin\//,"")}function Pn(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=re(o,t[s]);return{c(){e=Te("svg"),n=new Ge(!0),this.h()},l(s){e=Oe(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=je(e);n=He(c,!0),c.forEach(b),this.h()},h(){n.a=null,be(e,o)},m(s,c){Ve(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M472 224c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24s-24 10.7-24 24v80.1l-20-23.5C387 63.4 325.1 32 256 32 132.3 32 32 132.3 32 256s100.3 224 224 224c50.4 0 97-16.7 134.4-44.8 10.6-8 12.7-23 4.8-33.6s-23-12.7-33.6-4.8C332.2 418.9 295.7 432 256 432c-97.2 0-176-78.8-176-176S158.8 80 256 80c54.3 0 102.9 24.6 135.2 63.4l.1.2 27.6 32.4H328c-13.3 0-24 10.7-24 24s10.7 24 24 24z"/>',e)},p(s,[c]){be(e,o=qe(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&s[0]]))},i:U,o:U,d(s){s&&b(e)}}}function Un(r,e,n){return r.$$set=t=>{n(0,e=re(re({},e),ye(t)))},[e=ye(e)]}class Tn extends se{constructor(e){super(),ce(this,e,Un,Pn,ie,{})}}function Gn(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=re(o,t[s]);return{c(){e=Te("svg"),n=new Ge(!0),this.h()},l(s){e=Oe(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=je(e);n=He(c,!0),c.forEach(b),this.h()},h(){n.a=null,be(e,o)},m(s,c){Ve(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M239 465c9.4 9.4 24.6 9.4 33.9 0L465 273c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-175 175L81 239c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9zM47 81l192 192c9.4 9.4 24.6 9.4 33.9 0L465 81c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-175 175L81 47c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z"/>',e)},p(s,[c]){be(e,o=qe(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&s[0]]))},i:U,o:U,d(s){s&&b(e)}}}function On(r,e,n){return r.$$set=t=>{n(0,e=re(re({},e),ye(t)))},[e=ye(e)]}class jn extends se{constructor(e){super(),ce(this,e,On,Gn,ie,{})}}const Hn=r=>({}),kt=r=>({}),Vn=r=>({}),St=r=>({}),qn=r=>({}),It=r=>({}),Kn=r=>({}),Ft=r=>({});function Xn(r){let e;return{c(){e=T(r[0])},m(n,t){_(n,e,t)},p(n,t){1&t&&te(e,n[0])},d(n){n&&b(e)}}}function Et(r){let e,n;const t=r[3].subtitle,o=Y(t,r,r[4],St),s=o||function(c){let i,a;return i=new De({props:{size:2,$$slots:{default:[Wn]},$$scope:{ctx:c}}}),{c(){L(i.$$.fragment)},m(l,u){C(i,l,u),a=!0},p(l,u){const m={};18&u&&(m.$$scope={dirty:u,ctx:l}),i.$set(m)},i(l){a||(p(i.$$.fragment,l),a=!0)},o(l){d(i.$$.fragment,l),a=!1},d(l){A(i,l)}}}(r);return{c(){e=k("div"),s&&s.c(),y(e,"class","c-card-button__subtitle svelte-z367s9")},m(c,i){_(c,e,i),s&&s.m(e,null),n=!0},p(c,i){o?o.p&&(!n||16&i)&&Q(o,t,c,c[4],n?J(t,c[4],i,Vn):Z(c[4]),St):s&&s.p&&(!n||2&i)&&s.p(c,n?i:-1)},i(c){n||(p(s,c),n=!0)},o(c){d(s,c),n=!1},d(c){c&&b(e),s&&s.d(c)}}}function Wn(r){let e;return{c(){e=T(r[1])},m(n,t){_(n,e,t)},p(n,t){2&t&&te(e,n[1])},d(n){n&&b(e)}}}function Nt(r){let e,n;const t=r[3].iconRight,o=Y(t,r,r[4],kt);return{c(){e=k("div"),o&&o.c(),y(e,"class","c-card-button__icon-right svelte-z367s9")},m(s,c){_(s,e,c),o&&o.m(e,null),n=!0},p(s,c){o&&o.p&&(!n||16&c)&&Q(o,t,s,s[4],n?J(t,s[4],c,Hn):Z(s[4]),kt)},i(s){n||(p(o,s),n=!0)},o(s){d(o,s),n=!1},d(s){s&&b(e),o&&o.d(s)}}}function Yn(r){let e,n,t,o,s,c,i,a;const l=r[3].iconLeft,u=Y(l,r,r[4],Ft),m=r[3].title,g=Y(m,r,r[4],It),f=g||function($){let v,x;return v=new De({props:{size:2,$$slots:{default:[Xn]},$$scope:{ctx:$}}}),{c(){L(v.$$.fragment)},m(I,B){C(v,I,B),x=!0},p(I,B){const z={};17&B&&(z.$$scope={dirty:B,ctx:I}),v.$set(z)},i(I){x||(p(v.$$.fragment,I),x=!0)},o(I){d(v.$$.fragment,I),x=!1},d(I){A(v,I)}}}(r);let h=r[1]&&Et(r),w=r[2].iconRight&&Nt(r);return{c(){e=k("div"),u&&u.c(),n=P(),t=k("div"),o=k("div"),f&&f.c(),s=P(),h&&h.c(),c=P(),w&&w.c(),i=ue(),y(e,"class","c-card-button__icon-left svelte-z367s9"),y(o,"class","c-card-button__title svelte-z367s9"),y(t,"class","c-card-button__content svelte-z367s9")},m($,v){_($,e,v),u&&u.m(e,null),_($,n,v),_($,t,v),N(t,o),f&&f.m(o,null),N(t,s),h&&h.m(t,null),_($,c,v),w&&w.m($,v),_($,i,v),a=!0},p($,[v]){u&&u.p&&(!a||16&v)&&Q(u,l,$,$[4],a?J(l,$[4],v,Kn):Z($[4]),Ft),g?g.p&&(!a||16&v)&&Q(g,m,$,$[4],a?J(m,$[4],v,qn):Z($[4]),It):f&&f.p&&(!a||1&v)&&f.p($,a?v:-1),$[1]?h?(h.p($,v),2&v&&p(h,1)):(h=Et($),h.c(),p(h,1),h.m(t,null)):h&&(G(),d(h,1,1,()=>{h=null}),O()),$[2].iconRight?w?(w.p($,v),4&v&&p(w,1)):(w=Nt($),w.c(),p(w,1),w.m(i.parentNode,i)):w&&(G(),d(w,1,1,()=>{w=null}),O())},i($){a||(p(u,$),p(f,$),p(h),p(w),a=!0)},o($){d(u,$),d(f,$),d(h),d(w),a=!1},d($){$&&(b(e),b(n),b(t),b(c),b(i)),u&&u.d($),f&&f.d($),h&&h.d(),w&&w.d($)}}}function Qn(r,e,n){let{$$slots:t={},$$scope:o}=e;const s=ft(t);let{title:c="Select an option"}=e,{subtitle:i=""}=e;return r.$$set=a=>{"title"in a&&n(0,c=a.title),"subtitle"in a&&n(1,i=a.subtitle),"$$scope"in a&&n(4,o=a.$$scope)},[c,i,s,t,o]}class vn extends se{constructor(e){super(),ce(this,e,Qn,Yn,ie,{title:0,subtitle:1})}}const Zn=r=>({}),Bt=r=>({slot:"iconLeft"}),Jn=r=>({}),Dt=r=>({slot:"iconRight"});function zt(r,e,n){const t=r.slice();return t[19]=e[n],t}const eo=r=>({}),Mt=r=>({}),to=r=>({}),Pt=r=>({}),no=r=>({}),Ut=r=>({slot:"iconLeft"}),oo=r=>({}),Tt=r=>({slot:"title"}),ro=r=>({}),Gt=r=>({slot:"iconRight"});function so(r){let e,n,t,o,s;return n=new vn({props:{title:r[3],subtitle:r[4],$$slots:{iconRight:[ao],iconLeft:[io]},$$scope:{ctx:r}}}),{c(){e=k("button"),L(n.$$.fragment),y(e,"class","c-card-button__display svelte-1km5ln2"),y(e,"type","button"),e.disabled=r[10]},m(c,i){_(c,e,i),C(n,e,null),t=!0,o||(s=[Ae(e,"click",r[16]),Ae(e,"keydown",r[17])],o=!0)},p(c,i){const a={};8&i&&(a.title=c[3]),16&i&&(a.subtitle=c[4]),262144&i&&(a.$$scope={dirty:i,ctx:c}),n.$set(a),(!t||1024&i)&&(e.disabled=c[10])},i(c){t||(p(n.$$.fragment,c),t=!0)},o(c){d(n.$$.fragment,c),t=!1},d(c){c&&b(e),A(n),o=!1,gt(s)}}}function co(r){let e,n,t;function o(c){r[15](c)}let s={onOpenChange:r[9],$$slots:{default:[vo]},$$scope:{ctx:r}};return r[1]!==void 0&&(s.requestClose=r[1]),e=new le.Root({props:s}),he.push(()=>we(e,"requestClose",o)),{c(){L(e.$$.fragment)},m(c,i){C(e,c,i),t=!0},p(c,i){const a={};512&i&&(a.onOpenChange=c[9]),263641&i&&(a.$$scope={dirty:i,ctx:c}),!n&&2&i&&(n=!0,a.requestClose=c[1],ve(()=>n=!1)),e.$set(a)},i(c){t||(p(e.$$.fragment,c),t=!0)},o(c){d(e.$$.fragment,c),t=!1},d(c){A(e,c)}}}function io(r){let e;const n=r[13].iconLeft,t=Y(n,r,r[18],Bt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||262144&s)&&Q(t,n,o,o[18],e?J(n,o[18],s,Zn):Z(o[18]),Bt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function ao(r){let e;const n=r[13].iconRight,t=Y(n,r,r[18],Dt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||262144&s)&&Q(t,n,o,o[18],e?J(n,o[18],s,Jn):Z(o[18]),Dt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function lo(r){let e;const n=r[13].iconLeft,t=Y(n,r,r[18],Ut);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||262144&s)&&Q(t,n,o,o[18],e?J(n,o[18],s,no):Z(o[18]),Ut)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function uo(r){let e;const n=r[13].title,t=Y(n,r,r[18],Tt),o=t||function(s){let c;return{c(){c=T(s[3])},m(i,a){_(i,c,a)},p(i,a){8&a&&te(c,i[3])},d(i){i&&b(c)}}}(r);return{c(){o&&o.c()},m(s,c){o&&o.m(s,c),e=!0},p(s,c){t?t.p&&(!e||262144&c)&&Q(t,n,s,s[18],e?J(n,s[18],c,oo):Z(s[18]),Tt):o&&o.p&&(!e||8&c)&&o.p(s,e?c:-1)},i(s){e||(p(o,s),e=!0)},o(s){d(o,s),e=!1},d(s){o&&o.d(s)}}}function $o(r){let e;const n=r[13].iconRight,t=Y(n,r,r[18],Gt),o=t||function(s){let c,i;return c=new dn({}),{c(){L(c.$$.fragment)},m(a,l){C(c,a,l),i=!0},i(a){i||(p(c.$$.fragment,a),i=!0)},o(a){d(c.$$.fragment,a),i=!1},d(a){A(c,a)}}}();return{c(){o&&o.c()},m(s,c){o&&o.m(s,c),e=!0},p(s,c){t&&t.p&&(!e||262144&c)&&Q(t,n,s,s[18],e?J(n,s[18],c,ro):Z(s[18]),Gt)},i(s){e||(p(o,s),e=!0)},o(s){d(o,s),e=!1},d(s){o&&o.d(s)}}}function mo(r){let e,n,t,o;return n=new vn({props:{subtitle:r[4],$$slots:{iconRight:[$o],title:[uo],iconLeft:[lo]},$$scope:{ctx:r}}}),{c(){e=k("div"),L(n.$$.fragment),y(e,"class","c-card-button__display svelte-1km5ln2"),y(e,"role","button"),y(e,"tabindex",t=r[10]?-1:0),ge(e,"disabled",r[10])},m(s,c){_(s,e,c),C(n,e,null),o=!0},p(s,c){const i={};16&c&&(i.subtitle=s[4]),262152&c&&(i.$$scope={dirty:c,ctx:s}),n.$set(i),(!o||1024&c&&t!==(t=s[10]?-1:0))&&y(e,"tabindex",t),(!o||1024&c)&&ge(e,"disabled",s[10])},i(s){o||(p(n.$$.fragment,s),o=!0)},o(s){d(n.$$.fragment,s),o=!1},d(s){s&&b(e),A(n)}}}function po(r){let e,n;return e=new le.Label({props:{$$slots:{default:[go]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};262400&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function fo(r){let e,n,t=[],o=new Map,s=tt(r[6]);const c=i=>i[7](i[19]);for(let i=0;i<s.length;i+=1){let a=zt(r,s,i),l=c(a);o.set(l,t[i]=Ot(l,a))}return{c(){for(let i=0;i<t.length;i+=1)t[i].c();e=ue()},m(i,a){for(let l=0;l<t.length;l+=1)t[l]&&t[l].m(i,a);_(i,e,a),n=!0},p(i,a){2241&a&&(s=tt(i[6]),G(),t=fn(t,a,c,1,i,s,o,e.parentNode,gn,Ot,e,zt),O())},i(i){if(!n){for(let a=0;a<s.length;a+=1)p(t[a]);n=!0}},o(i){for(let a=0;a<t.length;a+=1)d(t[a]);n=!1},d(i){i&&b(e);for(let a=0;a<t.length;a+=1)t[a].d(i)}}}function go(r){let e;return{c(){e=T(r[8])},m(n,t){_(n,e,t)},p(n,t){256&t&&te(e,n[8])},d(n){n&&b(e)}}}function ho(r){let e,n,t=r[7](r[19])+"";return{c(){e=T(t),n=P()},m(o,s){_(o,e,s),_(o,n,s)},p(o,s){192&s&&t!==(t=o[7](o[19])+"")&&te(e,t)},d(o){o&&(b(e),b(n))}}}function Ot(r,e){let n,t,o;function s(){return e[14](e[19])}return t=new le.Item({props:{onSelect:s,highlight:e[0]===e[19],$$slots:{default:[ho]},$$scope:{ctx:e}}}),{key:r,first:null,c(){n=ue(),L(t.$$.fragment),this.first=n},m(c,i){_(c,n,i),C(t,c,i),o=!0},p(c,i){e=c;const a={};64&i&&(a.onSelect=s),65&i&&(a.highlight=e[0]===e[19]),262336&i&&(a.$$scope={dirty:i,ctx:e}),t.$set(a)},i(c){o||(p(t.$$.fragment,c),o=!0)},o(c){d(t.$$.fragment,c),o=!1},d(c){c&&b(n),A(t,c)}}}function wo(r){let e,n,t,o,s;const c=r[13]["dropdown-top"],i=Y(c,r,r[18],Pt),a=r[13]["dropdown-content"],l=Y(a,r,r[18],Mt),u=l||function(m){let g,f,h,w;const $=[fo,po],v=[];function x(I,B){return I[6].length>0?0:1}return g=x(m),f=v[g]=$[g](m),{c(){f.c(),h=ue()},m(I,B){v[g].m(I,B),_(I,h,B),w=!0},p(I,B){let z=g;g=x(I),g===z?v[g].p(I,B):(G(),d(v[z],1,1,()=>{v[z]=null}),O(),f=v[g],f?f.p(I,B):(f=v[g]=$[g](I),f.c()),p(f,1),f.m(h.parentNode,h))},i(I){w||(p(f),w=!0)},o(I){d(f),w=!1},d(I){I&&b(h),v[g].d(I)}}}(r);return{c(){e=k("div"),n=k("div"),i&&i.c(),t=P(),o=k("div"),u&&u.c(),y(n,"class","c-card-button__dropdown-top svelte-1km5ln2"),y(o,"class","c-card-button__dropdown-content svelte-1km5ln2"),y(e,"class","c-card__dropdown-contents svelte-1km5ln2")},m(m,g){_(m,e,g),N(e,n),i&&i.m(n,null),N(e,t),N(e,o),u&&u.m(o,null),s=!0},p(m,g){i&&i.p&&(!s||262144&g)&&Q(i,c,m,m[18],s?J(c,m[18],g,to):Z(m[18]),Pt),l?l.p&&(!s||262144&g)&&Q(l,a,m,m[18],s?J(a,m[18],g,eo):Z(m[18]),Mt):u&&u.p&&(!s||449&g)&&u.p(m,s?g:-1)},i(m){s||(p(i,m),p(u,m),s=!0)},o(m){d(i,m),d(u,m),s=!1},d(m){m&&b(e),i&&i.d(m),u&&u.d(m)}}}function vo(r){let e,n,t,o;return e=new le.Trigger({props:{$$slots:{default:[mo]},$$scope:{ctx:r}}}),t=new le.Content({props:{align:"start",side:"bottom",$$slots:{default:[wo]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),n=P(),L(t.$$.fragment)},m(s,c){C(e,s,c),_(s,n,c),C(t,s,c),o=!0},p(s,c){const i={};263192&c&&(i.$$scope={dirty:c,ctx:s}),e.$set(i);const a={};262593&c&&(a.$$scope={dirty:c,ctx:s}),t.$set(a)},i(s){o||(p(e.$$.fragment,s),p(t.$$.fragment,s),o=!0)},o(s){d(e.$$.fragment,s),d(t.$$.fragment,s),o=!1},d(s){s&&b(n),A(e,s),A(t,s)}}}function bo(r){let e,n,t,o;const s=[co,so],c=[];function i(a,l){return a[2]==="dropdown"?0:1}return n=i(r),t=c[n]=s[n](r),{c(){e=k("div"),t.c(),y(e,"class","c-card-button svelte-1km5ln2")},m(a,l){_(a,e,l),c[n].m(e,null),o=!0},p(a,[l]){let u=n;n=i(a),n===u?c[n].p(a,l):(G(),d(c[u],1,1,()=>{c[u]=null}),O(),t=c[n],t?t.p(a,l):(t=c[n]=s[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&b(e),c[n].d()}}}function yo(r,e,n){let{$$slots:t={},$$scope:o}=e,{type:s="button"}=e,{title:c="Select an option"}=e,{subtitle:i=""}=e,{onClick:a=()=>{}}=e,{items:l=[]}=e,{selectedItem:u}=e,{formatItemLabel:m=x=>(x==null?void 0:x.toString())||""}=e,{noItemsLabel:g="No items found"}=e,{onDropdownOpenChange:f=()=>{}}=e,{requestClose:h=()=>{}}=e,{disabled:w=!1}=e;function $(x){n(0,u=x),v("select",x)}const v=ze();return r.$$set=x=>{"type"in x&&n(2,s=x.type),"title"in x&&n(3,c=x.title),"subtitle"in x&&n(4,i=x.subtitle),"onClick"in x&&n(5,a=x.onClick),"items"in x&&n(6,l=x.items),"selectedItem"in x&&n(0,u=x.selectedItem),"formatItemLabel"in x&&n(7,m=x.formatItemLabel),"noItemsLabel"in x&&n(8,g=x.noItemsLabel),"onDropdownOpenChange"in x&&n(9,f=x.onDropdownOpenChange),"requestClose"in x&&n(1,h=x.requestClose),"disabled"in x&&n(10,w=x.disabled),"$$scope"in x&&n(18,o=x.$$scope)},[u,h,s,c,i,a,l,m,g,f,w,$,v,t,x=>$(x),function(x){h=x,n(1,h)},()=>{a(),v("click")},x=>{x.key!=="Enter"&&x.key!==" "||(a(),v("click"))},o]}class bn extends se{constructor(e){super(),ce(this,e,yo,bo,ie,{type:2,title:3,subtitle:4,onClick:5,items:6,selectedItem:0,formatItemLabel:7,noItemsLabel:8,onDropdownOpenChange:9,requestClose:1,disabled:10,selectItem:11})}get selectItem(){return this.$$.ctx[11]}}function xo(r){let e,n;return e=new bn({props:{type:"dropdown",title:"Connected to your GitHub account",$$slots:{"dropdown-content":[Io],iconLeft:[Lo]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};4100&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function _o(r){let e,n;return e=new bn({props:{type:"button",title:r[1]?"Cancel":"Connect to GitHub",onClick:r[4],$$slots:{iconRight:[Bo],iconLeft:[Fo]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};2&o&&(s.title=t[1]?"Cancel":"Connect to GitHub"),4098&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Lo(r){let e,n;return e=new bt({props:{slot:"iconLeft"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Co(r){let e,n;return e=new De({props:{size:1,weight:"medium",$$slots:{default:[Ro]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Ao(r){let e,n,t,o;return e=new Ke({props:{slot:"iconLeft",useCurrentColor:!0,size:1}}),t=new De({props:{size:1,weight:"medium",$$slots:{default:[ko]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),n=P(),L(t.$$.fragment)},m(s,c){C(e,s,c),_(s,n,c),C(t,s,c),o=!0},i(s){o||(p(e.$$.fragment,s),p(t.$$.fragment,s),o=!0)},o(s){d(e.$$.fragment,s),d(t.$$.fragment,s),o=!1},d(s){s&&b(n),A(e,s),A(t,s)}}}function Ro(r){let e;return{c(){e=T("Revoke Access")},m(n,t){_(n,e,t)},d(n){n&&b(e)}}}function ko(r){let e;return{c(){e=T("Revoking...")},m(n,t){_(n,e,t)},d(n){n&&b(e)}}}function So(r){let e,n,t,o;const s=[Ao,Co],c=[];function i(a,l){return a[2]?0:1}return e=i(r),n=c[e]=s[e](r),{c(){n.c(),t=ue()},m(a,l){c[e].m(a,l),_(a,t,l),o=!0},p(a,l){let u=e;e=i(a),e!==u&&(G(),d(c[u],1,1,()=>{c[u]=null}),O(),n=c[e],n||(n=c[e]=s[e](a),n.c()),p(n,1),n.m(t.parentNode,t))},i(a){o||(p(n),o=!0)},o(a){d(n),o=!1},d(a){a&&b(t),c[e].d(a)}}}function Io(r){let e,n,t;return n=new le.Item({props:{color:"error",onSelect:r[6],$$slots:{default:[So]},$$scope:{ctx:r}}}),{c(){e=k("div"),L(n.$$.fragment),y(e,"slot","dropdown-content")},m(o,s){_(o,e,s),C(n,e,null),t=!0},p(o,s){const c={};4100&s&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),A(n)}}}function Fo(r){let e,n;return e=new bt({props:{slot:"iconLeft"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Eo(r){let e,n;return e=new Nn({}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function No(r){let e,n;return e=new Ke({props:{size:1,useCurrentColor:!0}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Bo(r){let e,n,t,o;const s=[No,Eo],c=[];function i(a,l){return a[1]?0:1}return n=i(r),t=c[n]=s[n](r),{c(){e=k("div"),t.c(),y(e,"slot","iconRight")},m(a,l){_(a,e,l),c[n].m(e,null),o=!0},p(a,l){let u=n;n=i(a),n!==u&&(G(),d(c[u],1,1,()=>{c[u]=null}),O(),t=c[n],t||(t=c[n]=s[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&b(e),c[n].d()}}}function Do(r){let e,n,t,o,s;const c=[_o,xo],i=[];function a(l,u){return l[0]?1:0}return t=a(r),o=i[t]=c[t](r),{c(){e=k("div"),n=k("div"),o.c(),y(n,"class","github-auth-button"),y(e,"class","github-auth-card svelte-zdlnsr")},m(l,u){_(l,e,u),N(e,n),i[t].m(n,null),s=!0},p(l,[u]){let m=t;t=a(l),t===m?i[t].p(l,u):(G(),d(i[m],1,1,()=>{i[m]=null}),O(),o=i[t],o?o.p(l,u):(o=i[t]=c[t](l),o.c()),p(o,1),o.m(n,null))},i(l){s||(p(o),s=!0)},o(l){d(o),s=!1},d(l){l&&b(e),i[t].d()}}}function zo(r,e,n){const t=ze(),o=Re(wt.key);let s=!1,c=!1,i=!1,a=null,l=null;async function u(){if(!i){n(2,i=!0);try{const m=await o.revokeGithubAccess();m.success?(n(0,s=!1),t("authStateChange",{isAuthenticated:!1})):console.error("Failed to revoke GitHub access:",m.message)}catch(m){console.error("Error revoking GitHub access:",m)}finally{n(2,i=!1)}}}return nt(async()=>{await async function(){try{const m=await o.isGithubAuthenticated();m!==s?(n(0,s=m),t("authStateChange",{isAuthenticated:s})):n(0,s=m)}catch(m){console.error("Failed to check GitHub authentication status:",m),n(0,s=!1),t("authStateChange",{isAuthenticated:!1})}}()}),un(()=>{a&&(clearTimeout(a),a=null),l&&(clearInterval(l),l=null)}),[s,c,i,()=>{},async function(){if(c)return n(1,c=!1),void(a&&(clearTimeout(a),a=null));n(1,c=!0);try{await o.authenticateGithub(),l=setInterval(async()=>{await o.isGithubAuthenticated()&&(n(0,s=!0),n(1,c=!1),t("authStateChange",{isAuthenticated:!0}),l&&clearInterval(l),a&&(clearTimeout(a),a=null))},5e3),a=setTimeout(()=>{l&&clearInterval(l),n(1,c=!1),a=null},6e4)}catch(m){console.error("Failed to authenticate with GitHub:",m),n(1,c=!1)}},u,()=>{u()}]}class Mo extends se{constructor(e){super(),ce(this,e,zo,Do,ie,{})}}const Po=r=>({}),jt=r=>({});function Ht(r,e,n){const t=r.slice();return t[27]=e[n],t[29]=n,t}const Uo=r=>({item:64&r}),Vt=r=>({item:r[27]}),To=r=>({}),qt=r=>({}),Go=r=>({}),Kt=r=>({}),Oo=r=>({}),Xt=r=>({}),jo=r=>({}),Wt=r=>({}),Ho=r=>({}),Yt=r=>({});function Vo(r){let e,n,t,o,s,c,i,a,l,u,m,g,f,h;const w=[Xo,Ko],$=[];function v(z,D){return z[4]?0:1}o=v(r),s=$[o]=w[o](r);const x=[Yo,Wo],I=[];function B(z,D){return z[17].title?0:1}return a=B(r),l=I[a]=x[a](r),g=new dn({}),{c(){e=k("div"),n=k("div"),t=k("div"),s.c(),c=P(),i=k("span"),l.c(),u=P(),m=k("div"),L(g.$$.fragment),y(t,"class","c-searchable-dropdown__icon svelte-jowwyu"),y(i,"class","c-searchable-dropdown__button-text svelte-jowwyu"),y(n,"class","c-searchable-dropdown__icon-text svelte-jowwyu"),y(m,"class","c-searchable-dropdown__chevron svelte-jowwyu"),y(e,"class","c-searchable-dropdown__button svelte-jowwyu"),y(e,"role","button"),y(e,"tabindex",f=r[5]?-1:0),ge(e,"c-searchable-dropdown__button--disabled",r[5])},m(z,D){_(z,e,D),N(e,n),N(n,t),$[o].m(t,null),N(n,c),N(n,i),I[a].m(i,null),N(e,u),N(e,m),C(g,m,null),h=!0},p(z,D){let S=o;o=v(z),o===S?$[o].p(z,D):(G(),d($[S],1,1,()=>{$[S]=null}),O(),s=$[o],s?s.p(z,D):(s=$[o]=w[o](z),s.c()),p(s,1),s.m(t,null));let K=a;a=B(z),a===K?I[a].p(z,D):(G(),d(I[K],1,1,()=>{I[K]=null}),O(),l=I[a],l?l.p(z,D):(l=I[a]=x[a](z),l.c()),p(l,1),l.m(i,null)),(!h||32&D&&f!==(f=z[5]?-1:0))&&y(e,"tabindex",f),(!h||32&D)&&ge(e,"c-searchable-dropdown__button--disabled",z[5])},i(z){h||(p(s),p(l),p(g.$$.fragment,z),h=!0)},o(z){d(s),d(l),d(g.$$.fragment,z),h=!1},d(z){z&&b(e),$[o].d(),I[a].d(),A(g)}}}function qo(r){let e,n,t,o,s,c,i,a;const l=r[18].searchIcon,u=Y(l,r,r[25],Yt),m=u||function(f){let h;const w=f[18].icon,$=Y(w,f,f[25],Wt);return{c(){$&&$.c()},m(v,x){$&&$.m(v,x),h=!0},p(v,x){$&&$.p&&(!h||33554432&x)&&Q($,w,v,v[25],h?J(w,v[25],x,jo):Z(v[25]),Wt)},i(v){h||(p($,v),h=!0)},o(v){d($,v),h=!1},d(v){$&&$.d(v)}}}(r);let g=r[17].inputButton&&Qt(r);return{c(){e=k("div"),n=k("div"),m&&m.c(),t=P(),o=k("input"),s=P(),g&&g.c(),y(n,"class","c-searchable-dropdown__icon svelte-jowwyu"),y(o,"type","text"),y(o,"class","c-searchable-dropdown__trigger-input svelte-jowwyu"),y(o,"placeholder",r[3]),y(e,"class","c-searchable-dropdown__input-container svelte-jowwyu")},m(f,h){_(f,e,h),N(e,n),m&&m.m(n,null),N(e,t),N(e,o),_t(o,r[0]),N(e,s),g&&g.m(e,null),c=!0,i||(a=[Ae(o,"input",r[21]),Ae(o,"input",r[22]),Ae(o,"click",Ze(r[19])),Ae(o,"mousedown",Ze(r[20]))],i=!0)},p(f,h){u?u.p&&(!c||33554432&h)&&Q(u,l,f,f[25],c?J(l,f[25],h,Ho):Z(f[25]),Yt):m&&m.p&&(!c||33554432&h)&&m.p(f,c?h:-1),(!c||8&h)&&y(o,"placeholder",f[3]),1&h&&o.value!==f[0]&&_t(o,f[0]),f[17].inputButton?g?(g.p(f,h),131072&h&&p(g,1)):(g=Qt(f),g.c(),p(g,1),g.m(e,null)):g&&(G(),d(g,1,1,()=>{g=null}),O())},i(f){c||(p(m,f),p(g),c=!0)},o(f){d(m,f),d(g),c=!1},d(f){f&&b(e),m&&m.d(f),g&&g.d(),i=!1,gt(a)}}}function Ko(r){let e;const n=r[18].icon,t=Y(n,r,r[25],Kt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&Q(t,n,o,o[25],e?J(n,o[25],s,Go):Z(o[25]),Kt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function Xo(r){let e,n;return e=new Ke({props:{size:1,useCurrentColor:!0}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Wo(r){let e,n=(r[4]?r[11]:r[2])+"";return{c(){e=T(n)},m(t,o){_(t,e,o)},p(t,o){2068&o&&n!==(n=(t[4]?t[11]:t[2])+"")&&te(e,n)},i:U,o:U,d(t){t&&b(e)}}}function Yo(r){let e;const n=r[18].title,t=Y(n,r,r[25],qt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&Q(t,n,o,o[25],e?J(n,o[25],s,To):Z(o[25]),qt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function Qt(r){let e;const n=r[18].inputButton,t=Y(n,r,r[25],Xt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&Q(t,n,o,o[25],e?J(n,o[25],s,Oo):Z(o[25]),Xt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function Qo(r){let e,n,t,o;const s=[qo,Vo],c=[];function i(a,l){return a[12]?0:1}return e=i(r),n=c[e]=s[e](r),{c(){n.c(),t=ue()},m(a,l){c[e].m(a,l),_(a,t,l),o=!0},p(a,l){let u=e;e=i(a),e===u?c[e].p(a,l):(G(),d(c[u],1,1,()=>{c[u]=null}),O(),n=c[e],n?n.p(a,l):(n=c[e]=s[e](a),n.c()),p(n,1),n.m(t.parentNode,t))},i(a){o||(p(n),o=!0)},o(a){d(n),o=!1},d(a){a&&b(t),c[e].d(a)}}}function Zt(r){let e,n;return e=new le.Content({props:{side:"bottom",align:"start",$$slots:{default:[rr]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};33689298&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Zo(r){let e,n;return e=new le.Item({props:{disabled:!0,$$slots:{default:[tr]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};33555456&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Jo(r){let e,n,t=[],o=new Map,s=tt(r[6]);const c=i=>i[27]===null?`null-item-${i[29]}`:i[8](i[27],i[29]);for(let i=0;i<s.length;i+=1){let a=Ht(r,s,i),l=c(a);o.set(l,t[i]=Jt(l,a))}return{c(){for(let i=0;i<t.length;i+=1)t[i].c();e=ue()},m(i,a){for(let l=0;l<t.length;l+=1)t[l]&&t[l].m(i,a);_(i,e,a),n=!0},p(i,a){33620930&a&&(s=tt(i[6]),G(),t=fn(t,a,c,1,i,s,o,e.parentNode,gn,Jt,e,Ht),O())},i(i){if(!n){for(let a=0;a<s.length;a+=1)p(t[a]);n=!0}},o(i){for(let a=0;a<t.length;a+=1)d(t[a]);n=!1},d(i){i&&b(e);for(let a=0;a<t.length;a+=1)t[a].d(i)}}}function er(r){let e,n;return e=new le.Item({props:{disabled:!0,$$slots:{default:[or]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};33556480&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function tr(r){let e;return{c(){e=T(r[10])},m(n,t){_(n,e,t)},p(n,t){1024&t&&te(e,n[10])},d(n){n&&b(e)}}}function nr(r){let e,n;const t=r[18].item,o=Y(t,r,r[25],Vt),s=o||function(c){let i,a=c[7](c[27])+"";return{c(){i=T(a)},m(l,u){_(l,i,u)},p(l,u){192&u&&a!==(a=l[7](l[27])+"")&&te(i,a)},d(l){l&&b(i)}}}(r);return{c(){s&&s.c(),e=P()},m(c,i){s&&s.m(c,i),_(c,e,i),n=!0},p(c,i){o?o.p&&(!n||33554496&i)&&Q(o,t,c,c[25],n?J(t,c[25],i,Uo):Z(c[25]),Vt):s&&s.p&&(!n||192&i)&&s.p(c,n?i:-1)},i(c){n||(p(s,c),n=!0)},o(c){d(s,c),n=!1},d(c){c&&b(e),s&&s.d(c)}}}function Jt(r,e){let n,t,o;function s(){return e[23](e[27])}return t=new le.Item({props:{onSelect:s,highlight:e[9]?e[9](e[27],e[1]):!!e[1]&&e[7](e[1])===e[7](e[27]),$$slots:{default:[nr]},$$scope:{ctx:e}}}),{key:r,first:null,c(){n=ue(),L(t.$$.fragment),this.first=n},m(c,i){_(c,n,i),C(t,c,i),o=!0},p(c,i){e=c;const a={};64&i&&(a.onSelect=s),706&i&&(a.highlight=e[9]?e[9](e[27],e[1]):!!e[1]&&e[7](e[1])===e[7](e[27])),33554624&i&&(a.$$scope={dirty:i,ctx:e}),t.$set(a)},i(c){o||(p(t.$$.fragment,c),o=!0)},o(c){d(t.$$.fragment,c),o=!1},d(c){c&&b(n),A(t,c)}}}function or(r){let e,n,t,o,s,c;return n=new Ke({props:{size:1,useCurrentColor:!0}}),{c(){e=k("div"),L(n.$$.fragment),t=P(),o=k("span"),s=T(r[11]),y(e,"class","c-searchable-dropdown__loading svelte-jowwyu")},m(i,a){_(i,e,a),C(n,e,null),N(e,t),N(e,o),N(o,s),c=!0},p(i,a){(!c||2048&a)&&te(s,i[11])},i(i){c||(p(n.$$.fragment,i),c=!0)},o(i){d(n.$$.fragment,i),c=!1},d(i){i&&b(e),A(n)}}}function en(r){let e;const n=r[18].footer,t=Y(n,r,r[25],jt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&Q(t,n,o,o[25],e?J(n,o[25],s,Po):Z(o[25]),jt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function rr(r){let e,n,t,o,s,c;const i=[er,Jo,Zo],a=[];function l(m,g){return m[4]?0:m[6].length>0?1:m[10]?2:-1}~(e=l(r))&&(n=a[e]=i[e](r));let u=r[17].footer&&en(r);return{c(){n&&n.c(),t=P(),u&&u.c(),o=P(),s=k("div"),An(s,"margin-bottom","var(--ds-spacing-2)")},m(m,g){~e&&a[e].m(m,g),_(m,t,g),u&&u.m(m,g),_(m,o,g),_(m,s,g),c=!0},p(m,g){let f=e;e=l(m),e===f?~e&&a[e].p(m,g):(n&&(G(),d(a[f],1,1,()=>{a[f]=null}),O()),~e?(n=a[e],n?n.p(m,g):(n=a[e]=i[e](m),n.c()),p(n,1),n.m(t.parentNode,t)):n=null),m[17].footer?u?(u.p(m,g),131072&g&&p(u,1)):(u=en(m),u.c(),p(u,1),u.m(o.parentNode,o)):u&&(G(),d(u,1,1,()=>{u=null}),O())},i(m){c||(p(n),p(u),c=!0)},o(m){d(n),d(u),c=!1},d(m){m&&(b(t),b(o),b(s)),~e&&a[e].d(m),u&&u.d(m)}}}function sr(r){let e,n,t,o;e=new le.Trigger({props:{$$slots:{default:[Qo]},$$scope:{ctx:r}}});let s=!r[5]&&Zt(r);return{c(){L(e.$$.fragment),n=P(),s&&s.c(),t=ue()},m(c,i){C(e,c,i),_(c,n,i),s&&s.m(c,i),_(c,t,i),o=!0},p(c,i){const a={};33691709&i&&(a.$$scope={dirty:i,ctx:c}),e.$set(a),c[5]?s&&(G(),d(s,1,1,()=>{s=null}),O()):s?(s.p(c,i),32&i&&p(s,1)):(s=Zt(c),s.c(),p(s,1),s.m(t.parentNode,t))},i(c){o||(p(e.$$.fragment,c),p(s),o=!0)},o(c){d(e.$$.fragment,c),d(s),o=!1},d(c){c&&(b(n),b(t)),A(e,c),s&&s.d(c)}}}function cr(r){let e,n,t,o;function s(i){r[24](i)}let c={onOpenChange:r[14],$$slots:{default:[sr]},$$scope:{ctx:r}};return r[13]!==void 0&&(c.requestClose=r[13]),n=new le.Root({props:c}),he.push(()=>we(n,"requestClose",s)),{c(){e=k("div"),L(n.$$.fragment),y(e,"class","c-searchable-dropdown svelte-jowwyu")},m(i,a){_(i,e,a),C(n,e,null),o=!0},p(i,[a]){const l={};33693439&a&&(l.$$scope={dirty:a,ctx:i}),!t&&8192&a&&(t=!0,l.requestClose=i[13],ve(()=>t=!1)),n.$set(l)},i(i){o||(p(n.$$.fragment,i),o=!0)},o(i){d(n.$$.fragment,i),o=!1},d(i){i&&b(e),A(n)}}}function ir(r,e,n){let{$$slots:t={},$$scope:o}=e;const s=ft(t);let{title:c=""}=e,{placeholder:i="Search..."}=e,{isLoading:a=!1}=e,{disabled:l=!1}=e,{searchValue:u=""}=e,{items:m=[]}=e,{selectedItem:g=null}=e,{itemLabelFn:f=S=>(S==null?void 0:S.toString())||""}=e,{itemKeyFn:h=S=>(S==null?void 0:S.toString())||""}=e,{isItemSelected:w}=e,{noItemsLabel:$="No items found"}=e,{loadingLabel:v="Loading..."}=e,x=!1,I=()=>{};const B=ze();function z(S){n(0,u=S),B("search",S)}function D(S){n(1,g=S),B("select",S),I()}return r.$$set=S=>{"title"in S&&n(2,c=S.title),"placeholder"in S&&n(3,i=S.placeholder),"isLoading"in S&&n(4,a=S.isLoading),"disabled"in S&&n(5,l=S.disabled),"searchValue"in S&&n(0,u=S.searchValue),"items"in S&&n(6,m=S.items),"selectedItem"in S&&n(1,g=S.selectedItem),"itemLabelFn"in S&&n(7,f=S.itemLabelFn),"itemKeyFn"in S&&n(8,h=S.itemKeyFn),"isItemSelected"in S&&n(9,w=S.isItemSelected),"noItemsLabel"in S&&n(10,$=S.noItemsLabel),"loadingLabel"in S&&n(11,v=S.loadingLabel),"$$scope"in S&&n(25,o=S.$$scope)},[u,g,c,i,a,l,m,f,h,w,$,v,x,I,function(S){if(!l){if(n(12,x=S),S&&g){const K=f(g);n(0,u=K),B("search",""),setTimeout(()=>{const H=document.querySelector(".c-searchable-dropdown__trigger-input");H&&H.select()},0)}else S&&(n(0,u=""),B("search",""));B("openChange",S)}},z,D,s,t,function(S){Qe.call(this,r,S)},function(S){Qe.call(this,r,S)},function(){u=this.value,n(0,u)},S=>z(S.currentTarget.value),S=>D(S),function(S){I=S,n(13,I)},o]}class dt extends se{constructor(e){super(),ce(this,e,ir,cr,ie,{title:2,placeholder:3,isLoading:4,disabled:5,searchValue:0,items:6,selectedItem:1,itemLabelFn:7,itemKeyFn:8,isItemSelected:9,noItemsLabel:10,loadingLabel:11})}}function ar(r){let e,n,t;return n=new ot({props:{color:"warning",variant:"soft",size:2,$$slots:{default:[dr]},$$scope:{ctx:r}}}),{c(){e=k("div"),L(n.$$.fragment),y(e,"class","c-commit-ref-selector__error svelte-btbfel")},m(o,s){_(o,e,s),C(n,e,null),t=!0},p(o,s){const c={};16387&s[0]|16&s[2]&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),A(n)}}}function lr(r){var h,w;let e,n,t,o,s,c,i,a,l;function u($){r[31]($)}let m={title:((h=r[2])==null?void 0:h.name)||"Choose repository...",placeholder:"Search repositories...",itemKeyFn:Sr,isLoading:r[6],disabled:!r[7].length,items:r[8],selectedItem:r[2],itemLabelFn:Ir,noItemsLabel:"No repositories found",loadingLabel:"Loading repositories...",$$slots:{searchIcon:[gr],icon:[fr]},$$scope:{ctx:r}};function g($){r[36]($)}r[12]!==void 0&&(m.searchValue=r[12]),t=new dt({props:m}),he.push(()=>we(t,"searchValue",u)),t.$on("openChange",r[32]),t.$on("search",r[33]),t.$on("select",r[34]);let f={title:((w=r[4])==null?void 0:w.name)||"Choose branch...",itemKeyFn:Fr,placeholder:"Search branches...",isLoading:r[17],disabled:r[15],items:r[9],selectedItem:r[4],itemLabelFn:Er,noItemsLabel:r[5]?"":"No branches found",loadingLabel:"Loading branches...",$$slots:{footer:[Rr],inputButton:[yr],searchIcon:[wr],icon:[hr]},$$scope:{ctx:r}};return r[13]!==void 0&&(f.searchValue=r[13]),i=new dt({props:f}),he.push(()=>we(i,"searchValue",g)),i.$on("openChange",r[37]),i.$on("search",r[38]),i.$on("select",r[39]),{c(){e=k("div"),n=k("div"),L(t.$$.fragment),s=P(),c=k("div"),L(i.$$.fragment),y(n,"class","c-commit-ref-selector__selector svelte-btbfel"),y(c,"class","c-commit-ref-selector__selector svelte-btbfel"),y(e,"class","c-commit-ref-selector__selectors-container svelte-btbfel")},m($,v){_($,e,v),N(e,n),C(t,n,null),N(e,s),N(e,c),C(i,c,null),l=!0},p($,v){var B,z;const x={};4&v[0]&&(x.title=((B=$[2])==null?void 0:B.name)||"Choose repository..."),64&v[0]&&(x.isLoading=$[6]),128&v[0]&&(x.disabled=!$[7].length),256&v[0]&&(x.items=$[8]),4&v[0]&&(x.selectedItem=$[2]),16&v[2]&&(x.$$scope={dirty:v,ctx:$}),!o&&4096&v[0]&&(o=!0,x.searchValue=$[12],ve(()=>o=!1)),t.$set(x);const I={};16&v[0]&&(I.title=((z=$[4])==null?void 0:z.name)||"Choose branch..."),131072&v[0]&&(I.isLoading=$[17]),32768&v[0]&&(I.disabled=$[15]),512&v[0]&&(I.items=$[9]),16&v[0]&&(I.selectedItem=$[4]),32&v[0]&&(I.noItemsLabel=$[5]?"":"No branches found"),3112&v[0]|16&v[2]&&(I.$$scope={dirty:v,ctx:$}),!a&&8192&v[0]&&(a=!0,I.searchValue=$[13],ve(()=>a=!1)),i.$set(I)},i($){l||(p(t.$$.fragment,$),p(i.$$.fragment,$),l=!0)},o($){d(t.$$.fragment,$),d(i.$$.fragment,$),l=!1},d($){$&&b(e),A(t),A(i)}}}function ur(r){let e,n;return e=new Me({props:{variant:"ghost",color:"warning",size:1,loading:r[1],class:"c-commit-ref-selector__fetch-button",$$slots:{iconLeft:[pr],default:[mr]},$$scope:{ctx:r}}}),e.$on("click",r[22]),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};2&o[0]&&(s.loading=t[1]),16&o[2]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function $r(r){let e,n;return e=new Mo({}),e.$on("authStateChange",r[40]),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function mr(r){let e;return{c(){e=T("Reload available repos and branches")},m(n,t){_(n,e,t)},d(n){n&&b(e)}}}function pr(r){let e,n,t;return n=new pn({}),{c(){e=k("span"),L(n.$$.fragment),y(e,"slot","iconLeft"),y(e,"class","svelte-btbfel")},m(o,s){_(o,e,s),C(n,e,null),t=!0},p:U,i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),A(n)}}}function dr(r){let e,n,t,o,s,c,i;const a=[$r,ur],l=[];function u(m,g){return m[14]?1:0}return s=u(r),c=l[s]=a[s](r),{c(){e=k("div"),n=k("div"),t=T(r[0]),o=P(),c.c(),y(n,"class","c-commit-ref-selector__error-message svelte-btbfel"),y(e,"class","c-commit-ref-selector__error-content svelte-btbfel")},m(m,g){_(m,e,g),N(e,n),N(n,t),N(e,o),l[s].m(e,null),i=!0},p(m,g){(!i||1&g[0])&&te(t,m[0]);let f=s;s=u(m),s===f?l[s].p(m,g):(G(),d(l[f],1,1,()=>{l[f]=null}),O(),c=l[s],c?c.p(m,g):(c=l[s]=a[s](m),c.c()),p(c,1),c.m(e,null))},i(m){i||(p(c),i=!0)},o(m){d(c),i=!1},d(m){m&&b(e),l[s].d()}}}function fr(r){let e,n;return e=new bt({props:{slot:"icon"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function gr(r){let e,n;return e=new vt({props:{slot:"searchIcon"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function hr(r){let e,n;return e=new Rn({props:{slot:"icon"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function wr(r){let e,n;return e=new vt({props:{slot:"searchIcon"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function vr(r){let e,n;return e=new Tn({}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function br(r){let e,n;return e=new st({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[vr]},$$scope:{ctx:r}}}),e.$on("click",r[27]),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};16&o[2]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function yr(r){let e,n,t;return n=new Fe({props:{content:"Refresh branches",triggerOn:[ht.Hover],nested:!1,$$slots:{default:[br]},$$scope:{ctx:r}}}),{c(){e=k("div"),L(n.$$.fragment),y(e,"slot","inputButton"),y(e,"class","c-commit-ref-selector__refresh-button svelte-btbfel")},m(o,s){_(o,e,s),C(n,e,null),t=!0},p(o,s){const c={};16&s[2]&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),A(n)}}}function xr(r){let e,n,t,o,s,c,i;return t=new Ke({props:{size:1}}),c=new De({props:{size:2,color:"neutral",class:"c-commit-ref-selector__item-name",$$slots:{default:[Lr]},$$scope:{ctx:r}}}),{c(){e=k("div"),n=k("div"),L(t.$$.fragment),o=P(),s=k("div"),L(c.$$.fragment),y(n,"class","c-commit-ref-selector__item-icon svelte-btbfel"),y(s,"class","c-commit-ref-selector__item-content svelte-btbfel"),y(e,"class","c-commit-ref-selector__item c-commit-ref-selector__item--loading c-commit-ref-selector__item--disabled svelte-btbfel")},m(a,l){_(a,e,l),N(e,n),C(t,n,null),N(e,o),N(e,s),C(c,s,null),i=!0},p(a,l){const u={};16&l[2]&&(u.$$scope={dirty:l,ctx:a}),c.$set(u)},i(a){i||(p(t.$$.fragment,a),p(c.$$.fragment,a),i=!0)},o(a){d(t.$$.fragment,a),d(c.$$.fragment,a),i=!1},d(a){a&&b(e),A(t),A(c)}}}function _r(r){let e,n;return e=new Fe({props:{content:`${r[3].length} branches loaded`,triggerOn:[ht.Hover],nested:!1,$$slots:{default:[Ar]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};8&o[0]&&(s.content=`${t[3].length} branches loaded`),1024&o[0]|16&o[2]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Lr(r){let e;return{c(){e=T("Loading branches...")},m(n,t){_(n,e,t)},d(n){n&&b(e)}}}function Cr(r){let e;return{c(){e=T("Load more branches")},m(n,t){_(n,e,t)},d(n){n&&b(e)}}}function Ar(r){let e,n,t,o,s,c,i,a,l;return t=new jn({}),c=new De({props:{size:2,color:"neutral",class:"c-commit-ref-selector__item-name",$$slots:{default:[Cr]},$$scope:{ctx:r}}}),{c(){e=k("button"),n=k("div"),L(t.$$.fragment),o=P(),s=k("div"),L(c.$$.fragment),y(n,"class","c-commit-ref-selector__item-icon svelte-btbfel"),y(s,"class","c-commit-ref-selector__item-content svelte-btbfel"),y(e,"type","button"),y(e,"class","c-commit-ref-selector__item c-commit-ref-selector__item--loading svelte-btbfel")},m(u,m){_(u,e,m),N(e,n),C(t,n,null),N(e,o),N(e,s),C(c,s,null),i=!0,a||(l=Ae(e,"click",r[35]),a=!0)},p(u,m){const g={};16&m[2]&&(g.$$scope={dirty:m,ctx:u}),c.$set(g)},i(u){i||(p(t.$$.fragment,u),p(c.$$.fragment,u),i=!0)},o(u){d(t.$$.fragment,u),d(c.$$.fragment,u),i=!1},d(u){u&&b(e),A(t),A(c),a=!1,l()}}}function Rr(r){let e,n,t,o;const s=[_r,xr],c=[];function i(a,l){return a[11]&&!a[5]?0:a[5]?1:-1}return~(e=i(r))&&(n=c[e]=s[e](r)),{c(){n&&n.c(),t=ue()},m(a,l){~e&&c[e].m(a,l),_(a,t,l),o=!0},p(a,l){let u=e;e=i(a),e===u?~e&&c[e].p(a,l):(n&&(G(),d(c[u],1,1,()=>{c[u]=null}),O()),~e?(n=c[e],n?n.p(a,l):(n=c[e]=s[e](a),n.c()),p(n,1),n.m(t.parentNode,t)):n=null)},i(a){o||(p(n),o=!0)},o(a){d(n),o=!1},d(a){a&&b(t),~e&&c[e].d(a)}}}function kr(r){let e,n,t,o,s;const c=[lr,ar],i=[];function a(l,u){return l[16]?l[16]?1:-1:0}return~(t=a(r))&&(o=i[t]=c[t](r)),{c(){e=k("div"),n=k("div"),o&&o.c(),y(n,"class","c-commit-ref-selector__content svelte-btbfel"),y(e,"class","c-commit-ref-selector svelte-btbfel")},m(l,u){_(l,e,u),N(e,n),~t&&i[t].m(n,null),s=!0},p(l,u){let m=t;t=a(l),t===m?~t&&i[t].p(l,u):(o&&(G(),d(i[m],1,1,()=>{i[m]=null}),O()),~t?(o=i[t],o?o.p(l,u):(o=i[t]=c[t](l),o.c()),p(o,1),o.m(n,null)):o=null)},i(l){s||(p(o),s=!0)},o(l){d(o),s=!1},d(l){l&&b(e),~t&&i[t].d()}}}const Sr=r=>`${r.owner}-${r.name}`,Ir=r=>`${r.owner}/${r.name}`,Fr=(r,e)=>`${r.name}-${r.commit.sha}=${e}`,Er=r=>r.name.replace("origin/","");function Nr(r,e,n){let t,o,s,c;const i=Re(wt.key),a=ze(),l=Re(rt.key);let u,m,{errorMessage:g=""}=e,{isLoading:f=!1}=e,{lastUsedBranchName:h=null}=e,{lastUsedRepoUrl:w=null}=e,$=[],v=$,x=[],I=x,B=!1,z=!1,D=0,S=!1,K=!1,H=!1,W="",ne="";function ae(F){n(12,W=F),K=!0,yn(F)}function $e(F){n(13,ne=F),n(30,H=!0),xn(F)}const me={noRemoteBranches:"No remote branches found. Remote agents require remote branches to work properly. Please push your current branch to remote with 'git push -u origin <branch>'.",failedToFetchBranches:"Failed to fetch branches. Please try again.",failedToParseRemoteUrl:"Failed to parse remote URL in your local git repo. Please check your remote URL and try again.",failedToFetchFromRemote:"Failed to fetch from remote. Please try again."};async function xe(){n(6,z=!0),n(5,B=!0);const{repos:F,error:j,isDevDeploy:X}=await i.listUserRepos();if(X)return await async function(){console.warn("Fetching branches from local git environment.");const{remoteUrl:fe,error:Ie}=await i.getRemoteUrl();n(1,f=!0);const Be=At(fe);if(!Be||Ie)return R(Ie??me.failedToParseRemoteUrl),void n(1,f=!1);n(2,u={name:Be.name,owner:Be.owner,html_url:fe}),n(7,$=[u]),n(8,v=$);const lt=function(Ee){const Ye=Ee.find(Ce=>Ce.isCurrentBranch),ut=Ee.find(Ce=>Ce.isDefault),Cn=!!Ye&&(Ye==null?void 0:Ye.name)===(ut==null?void 0:ut.name.replace("origin/",""));return Ee.filter(Ce=>(!Cn||!Ce.isDefault)&&(!Ce.isCurrentBranch||!Ce.isRemote)&&!!Ce.isRemote&&Ce.isRemote)}((await i.listBranches()).branches),We=lt.find(Ee=>Ee.isDefault);n(4,m={name:We!=null&&We.name?Rt(We.name):lt[0].name,commit:{sha:"",url:""},protected:!1}),n(3,x=lt.map(Ee=>({name:Rt(Ee.name),commit:{sha:"",url:""},protected:!1}))),n(9,I=x),E(),t||de(),n(1,f=!1)}(),void n(6,z=!1);if(j)return R(`An error occured while fetching your repos. If this continues, please contact support. Error: ${j}`),n(1,f=!1),void n(6,z=!1);if(n(7,$=F),n(8,v=$),!u&&w){const fe=$.find(Ie=>Ie.html_url===w);fe&&n(2,u=fe)}const{remoteUrl:Ue,error:_n}=await i.getRemoteUrl(),Ln=At(Ue);if(_n)return n(1,f=!1),void n(6,z=!1);const{owner:ct,name:it}=Ln||{},at=$.find(fe=>fe.name===it&&fe.owner===ct);if(at&&!u)n(2,u=at);else if(!at&&it&&ct){const fe={name:it,owner:ct,html_url:Ue};try{const{repo:Ie,error:Be}=await i.getGithubRepo(fe);Be?(console.warn("Failed to fetch GitHub repo details:",Be),n(2,u=$[0])):(n(2,u=Ie),n(7,$=[u,...$]))}catch(Ie){console.error("Error fetching GitHub repo:",Ie),n(2,u=$[0])}}else if(!u)return n(1,f=!1),void n(6,z=!1);n(6,z=!1)}async function V(F){if(!u)return;n(5,B=!0);const j=u;do{if(j!==u){n(5,B=!1),n(3,x=[]);break}const X=await i.listRepoBranches(u,F);if(X.error)return R(`Failed to fetch branches for the repo ${u.owner}/${u.name}. Please make sure you have access to this repo on GitHub. If this continues, please contact support. Error: ${X.error}`),void n(1,f=!1);if(n(3,x=[...x,...X.branches]),n(11,S=X.hasNextPage),Ne(),!S)break;F=X.nextPage,n(10,D++,D),oe()}while(D%20!=0&&S);n(5,B=!1)}function Ne(){if(u&&!m){if(h){const F=x.find(j=>j.name===h);if(F)return n(4,m=F),void de()}if(u.default_branch){const F=u.default_branch,j=I.find(X=>X.name===F);if(j)return n(4,m=j),void de()}B||n(4,m=x[0]),de()}}function ke(){u&&async function(){u&&(n(10,D=0),await V(D+1))}().then(()=>{oe(),n(1,f=!1),E(),t||de()}).catch(F=>{console.error("Error fetching all branches:",F),R(`Failed to fetch branches: ${F instanceof Error?F.message:String(F)}`)})}nt(async()=>{await _e()});let ee=!0;const pe=async()=>{try{n(14,ee=await i.isGithubAuthenticated()),ee||R("Please authenticate with GitHub to use this feature.")}catch(F){console.error("Failed to check GitHub authentication status:",F),R("Please authenticate with GitHub to use this feature."),n(14,ee=!1)}};async function _e(){n(1,f=!0);try{await async function(){n(1,f=!0),M();try{if(await pe(),!ee)return void n(1,f=!1);await xe(),t||ke(),E(),t||de()}catch(F){console.error("Error fetching git data:",F),R(me.failedToFetchBranches)}finally{n(1,f=!1)}}()}catch(F){console.error("Error fetching and syncing branches:",F),R("Failed to fetch repos and branches. Please try again. If this continues, please contact support.")}finally{n(1,f=!1)}}async function E(){if(!t&&!o)try{if(!o&&u&&!m&&x.length===0)return void R(me.noRemoteBranches);M()}catch(F){console.error("Error checking git repository:",F),R(me.failedToFetchFromRemote)}}function R(F){console.error("Error:",F),n(16,t=!0),n(0,g=F)}function M(){n(16,t=!1),n(0,g="")}async function oe(F=""){n(16,t=!1);try{if(H&&ne.trim()!=="")n(9,(j=F||ne,I=x.filter(X=>X.name.includes(j.toLowerCase()))));else{let X;n(9,I=x.filter(Ue=>Ue.name!==(u==null?void 0:u.default_branch)||(X=Ue,!1))),X?I.unshift(X):u!=null&&u.default_branch&&I.unshift({name:u.default_branch,commit:{sha:"",url:""},protected:!1})}E()}catch(X){console.error("Error fetching branches:",X),n(9,I=[]),R(me.failedToFetchBranches)}var j}async function q(F){n(4,m=F),n(30,H=!1),Pe((m==null?void 0:m.name)??""),oe();const j=l.creationMetrics;l.setCreationMetrics({changedRepo:(j==null?void 0:j.changedRepo)??!1,changedBranch:!0}),de()}async function Le(F){n(5,B=!0),n(2,u=F),n(4,m=void 0),n(3,x=[]),n(9,I=[]),K=!1,Xe(""),n(8,v=$),ke();const j=l.creationMetrics;l.setCreationMetrics({changedRepo:!0,changedBranch:(j==null?void 0:j.changedBranch)??!1})}function Se(F,j){F||(j==="repo"?K=!1:(j==="branch"||(K=!1),n(30,H=!1)))}function de(){if(!(u!=null&&u.html_url)||!m)return;const F={github_commit_ref:{repository_url:u.html_url,git_ref:m.name}};a("commitRefChange",{commitRef:F,selectedBranch:m})}const Pe=F=>{n(13,ne=F)},Xe=F=>{n(12,W=F)},yn=Lt(async function(F=""){n(16,t=!1);try{K?n(8,(j=F||W,v=$.filter(X=>X.name.includes(j.toLowerCase())||X.owner.includes(j.toLowerCase())))):n(8,v=$)}catch(X){console.error("Error fetching repos:",X),n(8,v=[]),R(me.failedToFetchFromRemote)}var j},300,{leading:!1,trailing:!0}),xn=Lt(oe,300,{leading:!1,trailing:!0});function yt(F){F&&c||Se(F,"branch")}function xt(F){F&&!$.length||Se(F,"repo")}return r.$$set=F=>{"errorMessage"in F&&n(0,g=F.errorMessage),"isLoading"in F&&n(1,f=F.isLoading),"lastUsedBranchName"in F&&n(28,h=F.lastUsedBranchName),"lastUsedRepoUrl"in F&&n(29,w=F.lastUsedRepoUrl)},r.$$.update=()=>{1&r.$$.dirty[0]&&n(16,t=g!==""),98&r.$$.dirty[0]&&(o=f||B||z),52&r.$$.dirty[0]&&n(17,s=u&&B&&!m),1073741840&r.$$.dirty[0]&&Pe(H?"":(m==null?void 0:m.name)??""),1073741836&r.$$.dirty[0]&&n(15,c=!u||!H&&!x.length)},[g,f,u,x,m,B,z,$,v,I,D,S,W,ne,ee,c,t,s,ae,$e,V,pe,_e,q,Le,yt,xt,function(){n(3,x=[]),n(9,I=[]),ke()},h,w,H,function(F){W=F,n(12,W)},F=>xt(F.detail),F=>ae(F.detail),F=>Le(F.detail),()=>{V(D+1)},function(F){ne=F,n(13,ne)},F=>yt(F.detail),F=>$e(F.detail),F=>q(F.detail),async()=>{await pe(),ee&&await _e()}]}class Br extends se{constructor(e){super(),ce(this,e,Nr,kr,ie,{errorMessage:0,isLoading:1,lastUsedBranchName:28,lastUsedRepoUrl:29},null,[-1,-1,-1])}}function Dr(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=re(o,t[s]);return{c(){e=Te("svg"),n=new Ge(!0),this.h()},l(s){e=Oe(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=je(e);n=He(c,!0),c.forEach(b),this.h()},h(){n.a=null,be(e,o)},m(s,c){Ve(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m36.4 360.9-23 78.1L1 481.2c-2.5 8.5-.2 17.6 6 23.8s15.3 8.5 23.7 6.1L73 498.6l78.1-23c12.4-3.6 23.7-9.9 33.4-18.4 1.4-1.2 2.7-2.5 4-3.8l304.2-304.1c21.9-21.9 24.6-55.6 8.2-80.5-2.3-3.5-5.1-6.9-8.2-10l-39.4-39.5c-25-25-65.5-25-90.5 0L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4m46 13.5c1.7-5.6 4.5-10.8 8.4-15.2.6-.6 1.1-1.2 1.7-1.8L321 129l62 62-228.4 228.5c-4.7 4.7-10.6 8.2-17 10.1l-23.4 6.9-54.8 16.1 16.1-54.8z"/>',e)},p(s,[c]){be(e,o=qe(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&s[0]]))},i:U,o:U,d(s){s&&b(e)}}}function zr(r,e,n){return r.$$set=t=>{n(0,e=re(re({},e),ye(t)))},[e=ye(e)]}class Mr extends se{constructor(e){super(),ce(this,e,zr,Dr,ie,{})}}function Pr(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=re(o,t[s]);return{c(){e=Te("svg"),n=new Ge(!0),this.h()},l(s){e=Oe(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=je(e);n=He(c,!0),c.forEach(b),this.h()},h(){n.a=null,be(e,o)},m(s,c){Ve(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M234.7 42.7 197 56.8c-3 1.1-5 4-5 7.2s2 6.1 5 7.2l37.7 14.1 14.1 37.7c1.1 3 4 5 7.2 5s6.1-2 7.2-5l14.1-37.7L315 71.2c3-1.1 5-4 5-7.2s-2-6.1-5-7.2l-37.7-14.1L263.2 5c-1.1-3-4-5-7.2-5s-6.1 2-7.2 5zM461.4 48 496 82.6 386.2 192.3l-34.6-34.6zM80 429.4l237.7-237.7 34.6 34.6L114.6 464zM427.4 14.1 46.1 395.4c-18.7 18.7-18.7 49.1 0 67.9l34.6 34.6c18.7 18.7 49.1 18.7 67.9 0l381.3-381.4c18.7-18.7 18.7-49.1 0-67.9l-34.6-34.5c-18.7-18.7-49.1-18.7-67.9 0M7.5 117.2C3 118.9 0 123.2 0 128s3 9.1 7.5 10.8L64 160l21.2 56.5c1.7 4.5 6 7.5 10.8 7.5s9.1-3 10.8-7.5L128 160l56.5-21.2c4.5-1.7 7.5-6 7.5-10.8s-3-9.1-7.5-10.8L128 96l-21.2-56.5c-1.7-4.5-6-7.5-10.8-7.5s-9.1 3-10.8 7.5L64 96zm352 256c-4.5 1.7-7.5 6-7.5 10.8s3 9.1 7.5 10.8L416 416l21.2 56.5c1.7 4.5 6 7.5 10.8 7.5s9.1-3 10.8-7.5L480 416l56.5-21.2c4.5-1.7 7.5-6 7.5-10.8s-3-9.1-7.5-10.8L480 352l-21.2-56.5c-1.7-4.5-6-7.5-10.8-7.5s-9.1 3-10.8 7.5L416 352z"/>',e)},p(s,[c]){be(e,o=qe(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},1&c&&s[0]]))},i:U,o:U,d(s){s&&b(e)}}}function Ur(r,e,n){return r.$$set=t=>{n(0,e=re(re({},e),ye(t)))},[e=ye(e)]}class Tr extends se{constructor(e){super(),ce(this,e,Ur,Pr,ie,{})}}const Gr=r=>({}),tn=r=>({});function nn(r){let e,n;const t=r[12].icon,o=Y(t,r,r[11],tn);return{c(){e=k("div"),o&&o.c(),y(e,"class","c-setup-script-selector__icon svelte-udt6j8")},m(s,c){_(s,e,c),o&&o.m(e,null),n=!0},p(s,c){o&&o.p&&(!n||2048&c)&&Q(o,t,s,s[11],n?J(t,s[11],c,Gr):Z(s[11]),tn)},i(s){n||(p(o,s),n=!0)},o(s){d(o,s),n=!1},d(s){s&&b(e),o&&o.d(s)}}}function Or(r){let e,n,t,o,s;return{c(){e=k("span"),n=T(r[0]),t=P(),o=k("span"),s=T(r[1]),y(e,"class","c-setup-script-selector__script-name svelte-udt6j8"),y(o,"class","c-setup-script-selector__script-path svelte-udt6j8")},m(c,i){_(c,e,i),N(e,n),_(c,t,i),_(c,o,i),N(o,s)},p(c,i){1&i&&te(n,c[0]),2&i&&te(s,c[1])},i:U,o:U,d(c){c&&(b(e),b(t),b(o))}}}function jr(r){let e,n,t,o,s,c,i,a,l;function u(h){r[15](h)}function m(h){r[16](h)}let g={size:1,variant:"surface"};r[6]!==void 0&&(g.value=r[6]),r[5]!==void 0&&(g.textInput=r[5]),t=new Bn({props:g}),he.push(()=>we(t,"value",u)),he.push(()=>we(t,"textInput",m)),t.$on("keydown",r[8]),t.$on("blur",r[9]);let f=r[7]&&function(h){let w;return{c(){w=k("span"),w.textContent=`${h[7]}`,y(w,"class","c-setup-script-selector__extension svelte-udt6j8")},m($,v){_($,w,v)},p:U,d($){$&&b(w)}}}(r);return{c(){e=k("div"),n=k("div"),L(t.$$.fragment),c=P(),f&&f.c(),y(n,"class","c-setup-script-selector__rename-input-container svelte-udt6j8"),y(n,"role","presentation"),y(e,"class","c-setup-script-selector__rename-input svelte-udt6j8"),y(e,"role","presentation")},m(h,w){_(h,e,w),N(e,n),C(t,n,null),N(n,c),f&&f.m(n,null),i=!0,a||(l=[Ae(e,"click",Ze(r[13])),Ae(e,"mousedown",Ze(r[14]))],a=!0)},p(h,w){const $={};!o&&64&w&&(o=!0,$.value=h[6],ve(()=>o=!1)),!s&&32&w&&(s=!0,$.textInput=h[5],ve(()=>s=!1)),t.$set($),h[7]&&f.p(h,w)},i(h){i||(p(t.$$.fragment,h),i=!0)},o(h){d(t.$$.fragment,h),i=!1},d(h){h&&b(e),A(t),f&&f.d(),a=!1,gt(l)}}}function Hr(r){let e,n,t,o,s,c,i,a,l=r[10].icon&&nn(r);const u=[jr,Or],m=[];function g(w,$){return w[3]?0:1}o=g(r),s=m[o]=u[o](r);const f=r[12].default,h=Y(f,r,r[11],null);return{c(){e=k("div"),l&&l.c(),n=P(),t=k("div"),s.c(),c=P(),i=k("div"),h&&h.c(),y(t,"class","c-setup-script-selector__script-info svelte-udt6j8"),y(i,"class","c-setup-script-selector__script-actions svelte-udt6j8"),y(e,"class","c-setup-script-selector__script-item-content svelte-udt6j8"),y(e,"role","presentation"),ge(e,"c-setup-script-selector__script-item-content--renaming",r[3]),ge(e,"c-setup-script-selector__script-item-content--is-path",r[2]),ge(e,"c-setup-script-selector__script-item-content--selected",r[4])},m(w,$){_(w,e,$),l&&l.m(e,null),N(e,n),N(e,t),m[o].m(t,null),N(e,c),N(e,i),h&&h.m(i,null),a=!0},p(w,[$]){w[10].icon?l?(l.p(w,$),1024&$&&p(l,1)):(l=nn(w),l.c(),p(l,1),l.m(e,n)):l&&(G(),d(l,1,1,()=>{l=null}),O());let v=o;o=g(w),o===v?m[o].p(w,$):(G(),d(m[v],1,1,()=>{m[v]=null}),O(),s=m[o],s?s.p(w,$):(s=m[o]=u[o](w),s.c()),p(s,1),s.m(t,null)),h&&h.p&&(!a||2048&$)&&Q(h,f,w,w[11],a?J(f,w[11],$,null):Z(w[11]),null),(!a||8&$)&&ge(e,"c-setup-script-selector__script-item-content--renaming",w[3]),(!a||4&$)&&ge(e,"c-setup-script-selector__script-item-content--is-path",w[2]),(!a||16&$)&&ge(e,"c-setup-script-selector__script-item-content--selected",w[4])},i(w){a||(p(l),p(s),p(h,w),a=!0)},o(w){d(l),d(s),d(h,w),a=!1},d(w){w&&b(e),l&&l.d(),m[o].d(),h&&h.d(w)}}}function Vr(r,e,n){let{$$slots:t={},$$scope:o}=e;const s=ft(t);let{name:c}=e,{path:i}=e,{isPath:a=!1}=e,{isRenaming:l=!1}=e,{isSelected:u=!1}=e;const m=ze(),{baseName:g,extension:f}=function($){const v=$.lastIndexOf(".");return v===-1?{baseName:$,extension:""}:{baseName:$.substring(0,v),extension:$.substring(v)}}(c);let h,w=g;return r.$$set=$=>{"name"in $&&n(0,c=$.name),"path"in $&&n(1,i=$.path),"isPath"in $&&n(2,a=$.isPath),"isRenaming"in $&&n(3,l=$.isRenaming),"isSelected"in $&&n(4,u=$.isSelected),"$$scope"in $&&n(11,o=$.$$scope)},r.$$.update=()=>{40&r.$$.dirty&&l&&h&&setTimeout(()=>{h==null||h.focus(),h==null||h.select()},0)},[c,i,a,l,u,h,w,f,function($){if($.key!=="ArrowLeft"&&$.key!=="ArrowRight"&&$.key!=="ArrowUp"&&$.key!=="ArrowDown")if($.key==="Enter")if($.preventDefault(),w.trim()&&w!==g){const v=w.trim()+f;m("rename",{oldName:c,newName:v})}else m("cancelRename");else $.key==="Escape"&&($.preventDefault(),$.stopPropagation(),m("cancelRename"));else $.stopPropagation()},function(){m("cancelRename")},s,o,t,function($){Qe.call(this,r,$)},function($){Qe.call(this,r,$)},function($){w=$,n(6,w)},function($){h=$,n(5,h)}]}class qr extends se{constructor(e){super(),ce(this,e,Vr,Hr,ie,{name:0,path:1,isPath:2,isRenaming:3,isSelected:4})}}function on(r){let e,n,t,o,s,c,i,a,l,u;function m(f){r[34](f)}let g={placeholder:"Search scripts...",isLoading:r[1],disabled:!1,items:r[7],selectedItem:r[2],itemLabelFn:_s,itemKeyFn:Ls,isItemSelected:Cs,noItemsLabel:"No scripts found",loadingLabel:"Loading scripts...",$$slots:{item:[us,({item:f})=>({45:f}),({item:f})=>[0,f?16384:0]],searchIcon:[es],icon:[Jr],title:[Zr]},$$scope:{ctx:r}};return r[3]!==void 0&&(g.searchValue=r[3]),t=new dt({props:g}),he.push(()=>we(t,"searchValue",m)),t.$on("openChange",r[35]),t.$on("search",r[36]),t.$on("select",r[37]),i=new Fe({props:{content:r[10],$$slots:{default:[ds]},$$scope:{ctx:r}}}),l=new Fe({props:{content:"Open a new file for you to write a setup script that you can edit directly.",$$slots:{default:[ws]},$$scope:{ctx:r}}}),{c(){e=k("div"),n=k("div"),L(t.$$.fragment),s=P(),c=k("div"),L(i.$$.fragment),a=P(),L(l.$$.fragment),y(c,"class","c-setup-script-selector__action-buttons svelte-3cd2r2"),y(n,"class","c-setup-script-selector__script-line svelte-3cd2r2"),y(e,"class","c-setup-script-selector__script-line-container svelte-3cd2r2")},m(f,h){_(f,e,h),N(e,n),C(t,n,null),N(n,s),N(n,c),C(i,c,null),N(c,a),C(l,c,null),u=!0},p(f,h){const w={};2&h[0]&&(w.isLoading=f[1]),128&h[0]&&(w.items=f[7]),4&h[0]&&(w.selectedItem=f[2]),884&h[0]|49152&h[1]&&(w.$$scope={dirty:h,ctx:f}),!o&&8&h[0]&&(o=!0,w.searchValue=f[3],ve(()=>o=!1)),t.$set(w);const $={};1024&h[0]&&($.content=f[10]),2048&h[0]|32768&h[1]&&($.$$scope={dirty:h,ctx:f}),i.$set($);const v={};32768&h[1]&&(v.$$scope={dirty:h,ctx:f}),l.$set(v)},i(f){u||(p(t.$$.fragment,f),p(i.$$.fragment,f),p(l.$$.fragment,f),u=!0)},o(f){d(t.$$.fragment,f),d(i.$$.fragment,f),d(l.$$.fragment,f),u=!1},d(f){f&&b(e),A(t),A(i),A(l)}}}function Kr(r){let e,n;return e=new wn({props:{$$slots:{text:[Wr]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};16&o[0]|32768&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Xr(r){let e,n;return e=new wn({props:{$$slots:{grayText:[Qr],text:[Yr]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};768&o[0]|32768&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Wr(r){let e,n;return{c(){e=k("span"),n=T(r[4]),y(e,"slot","text")},m(t,o){_(t,e,o),N(e,n)},p(t,o){16&o[0]&&te(n,t[4])},d(t){t&&b(e)}}}function Yr(r){let e,n;return{c(){e=k("span"),n=T(r[9]),y(e,"slot","text")},m(t,o){_(t,e,o),N(e,n)},p(t,o){512&o[0]&&te(n,t[9])},d(t){t&&b(e)}}}function Qr(r){let e,n;return{c(){e=k("span"),n=T(r[8]),y(e,"slot","grayText")},m(t,o){_(t,e,o),N(e,n)},p(t,o){256&o[0]&&te(n,t[8])},d(t){t&&b(e)}}}function Zr(r){let e,n,t,o;const s=[Xr,Kr],c=[];function i(a,l){return a[5]?0:1}return n=i(r),t=c[n]=s[n](r),{c(){e=k("div"),t.c(),y(e,"slot","title")},m(a,l){_(a,e,l),c[n].m(e,null),o=!0},p(a,l){let u=n;n=i(a),n===u?c[n].p(a,l):(G(),d(c[u],1,1,()=>{c[u]=null}),O(),t=c[n],t?t.p(a,l):(t=c[n]=s[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&b(e),c[n].d()}}}function Jr(r){let e,n;return e=new hn({props:{slot:"icon"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function es(r){let e,n;return e=new vt({props:{slot:"searchIcon"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function ts(r){var t;let e,n;return e=new qr({props:{name:r[45].name,path:r[45].path,isPath:!0,isRenaming:((t=r[6])==null?void 0:t.path)===r[45].path,isSelected:!(!r[2]||r[2].path!==r[45].path),$$slots:{default:[ls]},$$scope:{ctx:r}}}),e.$on("rename",function(...o){return r[33](r[45],...o)}),e.$on("cancelRename",r[21]),{c(){L(e.$$.fragment)},m(o,s){C(e,o,s),n=!0},p(o,s){var i;r=o;const c={};16384&s[1]&&(c.name=r[45].name),16384&s[1]&&(c.path=r[45].path),64&s[0]|16384&s[1]&&(c.isRenaming=((i=r[6])==null?void 0:i.path)===r[45].path),4&s[0]|16384&s[1]&&(c.isSelected=!(!r[2]||r[2].path!==r[45].path)),49152&s[1]&&(c.$$scope={dirty:s,ctx:r}),e.$set(c)},i(o){n||(p(e.$$.fragment,o),n=!0)},o(o){d(e.$$.fragment,o),n=!1},d(o){A(e,o)}}}function ns(r){let e,n,t,o;return n=new hn({}),{c(){e=k("div"),L(n.$$.fragment),t=T(`
                  Use basic environment`),y(e,"class","c-setup-script-selector__basic-option svelte-3cd2r2")},m(s,c){_(s,e,c),C(n,e,null),N(e,t),o=!0},p:U,i(s){o||(p(n.$$.fragment,s),o=!0)},o(s){d(n.$$.fragment,s),o=!1},d(s){s&&b(e),A(n)}}}function os(r){let e,n;return e=new Dn({}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function rs(r){let e,n;return e=new st({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[os]},$$scope:{ctx:r}}}),e.$on("click",function(...t){return r[30](r[45],...t)}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){r=t;const s={};32768&o[1]&&(s.$$scope={dirty:o,ctx:r}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function ss(r){let e,n;return e=new Mr({}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function cs(r){let e,n;return e=new st({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[ss]},$$scope:{ctx:r}}}),e.$on("click",function(...t){return r[31](r[45],...t)}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){r=t;const s={};32768&o[1]&&(s.$$scope={dirty:o,ctx:r}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function is(r){let e,n;return e=new En({}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function as(r){let e,n;return e=new st({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[is]},$$scope:{ctx:r}}}),e.$on("click",function(...t){return r[32](r[45],...t)}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){r=t;const s={};32768&o[1]&&(s.$$scope={dirty:o,ctx:r}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function ls(r){let e,n,t,o,s,c;return e=new Fe({props:{content:"Open script in editor",$$slots:{default:[rs]},$$scope:{ctx:r}}}),t=new Fe({props:{content:"Rename script",$$slots:{default:[cs]},$$scope:{ctx:r}}}),s=new Fe({props:{content:"Delete script",$$slots:{default:[as]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),n=P(),L(t.$$.fragment),o=P(),L(s.$$.fragment)},m(i,a){C(e,i,a),_(i,n,a),C(t,i,a),_(i,o,a),C(s,i,a),c=!0},p(i,a){const l={};49152&a[1]&&(l.$$scope={dirty:a,ctx:i}),e.$set(l);const u={};49152&a[1]&&(u.$$scope={dirty:a,ctx:i}),t.$set(u);const m={};49152&a[1]&&(m.$$scope={dirty:a,ctx:i}),s.$set(m)},i(i){c||(p(e.$$.fragment,i),p(t.$$.fragment,i),p(s.$$.fragment,i),c=!0)},o(i){d(e.$$.fragment,i),d(t.$$.fragment,i),d(s.$$.fragment,i),c=!1},d(i){i&&(b(n),b(o)),A(e,i),A(t,i),A(s,i)}}}function us(r){let e,n,t,o;const s=[ns,ts],c=[];function i(a,l){return a[45]===null?0:1}return e=i(r),n=c[e]=s[e](r),{c(){n.c(),t=ue()},m(a,l){c[e].m(a,l),_(a,t,l),o=!0},p(a,l){let u=e;e=i(a),e===u?c[e].p(a,l):(G(),d(c[u],1,1,()=>{c[u]=null}),O(),n=c[e],n?n.p(a,l):(n=c[e]=s[e](a),n.c()),p(n,1),n.m(t.parentNode,t))},i(a){o||(p(n),o=!0)},o(a){d(n),o=!1},d(a){a&&b(t),c[e].d(a)}}}function $s(r){let e,n;return{c(){e=T("Auto-generate"),n=k("span"),n.textContent="a script",y(n,"class","c-setup-script-selector__long-text svelte-3cd2r2")},m(t,o){_(t,e,o),_(t,n,o)},p:U,d(t){t&&(b(e),b(n))}}}function ms(r){let e,n;return e=new Tr({props:{slot:"iconLeft"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function ps(r){let e,n;return e=new mn({props:{slot:"iconRight"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function ds(r){let e,n;return e=new Me({props:{variant:"soft",color:"neutral",size:1,disabled:r[11],$$slots:{iconRight:[ps],iconLeft:[ms],default:[$s]},$$scope:{ctx:r}}}),e.$on("click",r[14]),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};2048&o[0]&&(s.disabled=t[11]),32768&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function fs(r){let e,n,t;return{c(){e=T("Write "),n=k("span"),n.textContent="a script",t=T("by hand"),y(n,"class","c-setup-script-selector__long-text svelte-3cd2r2")},m(o,s){_(o,e,s),_(o,n,s),_(o,t,s)},p:U,d(o){o&&(b(e),b(n),b(t))}}}function gs(r){let e,n;return e=new Fn({props:{slot:"iconLeft"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function hs(r){let e,n;return e=new mn({props:{slot:"iconRight"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function ws(r){let e,n;return e=new Me({props:{variant:"soft",color:"neutral",size:1,highlight:!1,$$slots:{iconRight:[hs],iconLeft:[gs],default:[fs]},$$scope:{ctx:r}}}),e.$on("click",r[15]),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};32768&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function rn(r){let e,n,t;return n=new ot({props:{color:"warning",variant:"soft",size:2,$$slots:{default:[ys]},$$scope:{ctx:r}}}),{c(){e=k("div"),L(n.$$.fragment),y(e,"class","c-setup-script-selector__error svelte-3cd2r2")},m(o,s){_(o,e,s),C(n,e,null),t=!0},p(o,s){const c={};3&s[0]|32768&s[1]&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),A(n)}}}function vs(r){let e;return{c(){e=T("Refresh")},m(n,t){_(n,e,t)},d(n){n&&b(e)}}}function bs(r){let e,n,t;return n=new pn({}),{c(){e=k("span"),L(n.$$.fragment),y(e,"slot","iconLeft")},m(o,s){_(o,e,s),C(n,e,null),t=!0},p:U,i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),A(n)}}}function ys(r){let e,n,t,o,s,c;return s=new Me({props:{variant:"ghost",color:"warning",size:1,loading:r[1],$$slots:{iconLeft:[bs],default:[vs]},$$scope:{ctx:r}}}),s.$on("click",r[17]),{c(){e=k("div"),n=k("div"),t=T(r[0]),o=P(),L(s.$$.fragment),y(n,"class","c-setup-script-selector__error-message svelte-3cd2r2"),y(e,"class","c-setup-script-selector__error-content svelte-3cd2r2")},m(i,a){_(i,e,a),N(e,n),N(n,t),N(e,o),C(s,e,null),c=!0},p(i,a){(!c||1&a[0])&&te(t,i[0]);const l={};2&a[0]&&(l.loading=i[1]),32768&a[1]&&(l.$$scope={dirty:a,ctx:i}),s.$set(l)},i(i){c||(p(s.$$.fragment,i),c=!0)},o(i){d(s.$$.fragment,i),c=!1},d(i){i&&b(e),A(s)}}}function xs(r){let e,n,t,o,s=(!r[12]||r[0]===r[16].noScriptsFound)&&on(r),c=r[12]&&r[0]!==r[16].noScriptsFound&&rn(r);return{c(){e=k("div"),n=k("div"),s&&s.c(),t=P(),c&&c.c(),y(n,"class","c-setup-script-selector__content svelte-3cd2r2"),y(e,"class","c-setup-script-selector svelte-3cd2r2")},m(i,a){_(i,e,a),N(e,n),s&&s.m(n,null),N(n,t),c&&c.m(n,null),o=!0},p(i,a){i[12]&&i[0]!==i[16].noScriptsFound?s&&(G(),d(s,1,1,()=>{s=null}),O()):s?(s.p(i,a),4097&a[0]&&p(s,1)):(s=on(i),s.c(),p(s,1),s.m(n,t)),i[12]&&i[0]!==i[16].noScriptsFound?c?(c.p(i,a),4097&a[0]&&p(c,1)):(c=rn(i),c.c(),p(c,1),c.m(n,null)):c&&(G(),d(c,1,1,()=>{c=null}),O())},i(i){o||(p(s),p(c),o=!0)},o(i){d(s),d(c),o=!1},d(i){i&&b(e),s&&s.d(),c&&c.d()}}}const _s=r=>(r==null?void 0:r.name)||"",Ls=r=>`${r==null?void 0:r.path}-${r==null?void 0:r.location}-${r==null?void 0:r.name}`,Cs=(r,e)=>r===null&&e===null||!(!r||!e)&&r.path===e.path;function As(r,e,n){var _e;let t,o,s,c,i,a,l,u,{errorMessage:m=""}=e,{isLoading:g=!1}=e,{lastUsedScriptPath:f=null}=e,{disableNewAgentCreation:h=!1}=e;const w=Re(rt.key),$=ze(),v=Re("chatModel").extensionClient,x=E=>{v.openFile({repoRoot:"",pathName:E.path,allowOutOfWorkspace:!0,openLocalUri:E.location==="home"})};let I=[],B=((_e=w.newAgentDraft)==null?void 0:_e.setupScript)??null,z="",D=null,S=I,K=!0;const H={noScriptsFound:"No setup scripts found. You can create one in ~/.augment/env/, <git root>/.augment/env/, or <workspace root>/.augment/env/.",failedToFetchScripts:"Failed to fetch setup scripts. Please try again."};async function W(){n(0,m="");try{const E=B==null?void 0:B.path;if(n(28,I=await w.listSetupScripts()),K)if(f&&I.length>0){const R=I.find(M=>M.path===f);R&&(n(2,B=R),pe())}else f===null&&(n(2,B=null),pe());else if(E){const R=I.find(M=>M.path===E);R&&n(2,B=R)}K=!1,I.length===0?n(0,m=H.noScriptsFound):n(0,m="")}catch(E){console.error("Error fetching setup scripts:",E),n(0,m=H.failedToFetchScripts)}}async function ne(E,R){R&&R.stopPropagation();try{const M=await w.deleteSetupScript(E.name,E.location);M.success?((B==null?void 0:B.path)===E.path&&ee(null),await W()):(console.error("Failed to delete script:",M.error),xe(`Failed to delete script: ${M.error||"Unknown error"}`))}catch(M){console.error("Error deleting script:",M),xe(`Error deleting script: ${M instanceof Error?M.message:String(M)}`)}}async function ae(E,R){R&&R.stopPropagation(),n(6,D=E)}async function $e(E,R){const{oldName:M,newName:oe}=R.detail;try{const q=await w.renameSetupScript(M,oe,E.location);if(q.success){await W();const Le=I.find(Se=>Se.path===q.path);Le&&ee(Le)}else console.error("Failed to rename script:",q.error),xe(`Failed to rename script: ${q.error||"Unknown error"}`)}catch(q){console.error("Error renaming script:",q),xe(`Error renaming script: ${q instanceof Error?q.message:String(q)}`)}finally{me()}}function me(){n(6,D=null)}function xe(E){n(0,m=E)}function V(E){n(3,z=E)}function Ne(E){ee(E)}function ke(E){E&&(W(),n(3,z=""))}async function ee(E){n(2,B=E),pe(),w.saveLastRemoteAgentSetup(null,null,(B==null?void 0:B.path)||null)}function pe(){$("setupScriptChange",{script:B})}return nt(async()=>{var E;await W(),f===null?ee(null):(E=w.newAgentDraft)!=null&&E.setupScript&&!B&&ee(w.newAgentDraft.setupScript)}),r.$$set=E=>{"errorMessage"in E&&n(0,m=E.errorMessage),"isLoading"in E&&n(1,g=E.isLoading),"lastUsedScriptPath"in E&&n(26,f=E.lastUsedScriptPath),"disableNewAgentCreation"in E&&n(27,h=E.disableNewAgentCreation)},r.$$.update=()=>{var E,R;if(1&r.$$.dirty[0]&&n(12,t=m!==""),134217728&r.$$.dirty[0]&&n(11,o=h||((E=w.newAgentDraft)==null?void 0:E.isDisabled)||!w.newAgentDraft),134217728&r.$$.dirty[0]&&n(10,s=w.newAgentDraft?(R=w.newAgentDraft)!=null&&R.isDisabled?"Please resolve the issues with your workspace selection":h?"Agent limit reached or other restrictions apply":"An AI agent will automatically generate a setup script for your project.":"Please select a repository and branch first"),268435464&r.$$.dirty[0])if(z.trim()!==""){const M="Use basic environment".toLowerCase().includes(z.toLowerCase()),oe=I.filter(q=>q.name.toLowerCase().includes(z.toLowerCase())||q.path.toLowerCase().includes(z.toLowerCase()));n(7,S=M?[null,...oe]:oe)}else n(7,S=[null,...I]);6&r.$$.dirty[0]&&n(29,c=()=>g?"...":B?B.isGenerateOption?B.name:B.location==="home"?"~/.augment/env/"+B.name:B.path:"Use basic environment"),536870912&r.$$.dirty[0]&&n(4,i=c()),4&r.$$.dirty[0]&&n(5,a=!!(B!=null&&B.path)),48&r.$$.dirty[0]&&n(9,l=a?i.split("/").pop():i),48&r.$$.dirty[0]&&n(8,u=a?i.slice(0,i.lastIndexOf("/")):"")},[m,g,B,z,i,a,D,S,u,l,s,o,t,x,async()=>{try{const E=w.newAgentDraft;E&&w.setNewAgentDraft({...E,isSetupScriptAgent:!0});const R=await w.createRemoteAgentFromDraft("SETUP_MODE");return R&&w.setCurrentAgent(R),R}catch(E){console.error("Failed to select setup script generation:",E)}},async()=>{try{const E="setup.sh",R=`#!/bin/bash

# Setup Script for Remote Agent Environment
#
# This script installs dependencies and configures the environment for your project.
# It runs with sudo privileges when needed.
#
# Examples:
# sudo apt-get update && sudo apt-get install -y package-name
# pip install package-name
# npm install -g package-name
# export ENV_VAR=value

# Add your commands below:

`,M=await w.saveSetupScript(E,R,"home");if(M.success&&M.path){await W();const oe=I.find(q=>q.path===M.path);oe&&(ee(oe),x(oe))}else console.error("Failed to create manual setup script:",M.error),n(0,m=`Failed to create manual setup script: ${M.error||"Unknown error"}`)}catch(E){console.error("Error creating manual setup script:",E),n(0,m=`Error creating manual setup script: ${E instanceof Error?E.message:String(E)}`)}},H,W,ne,ae,$e,me,V,Ne,ke,ee,f,h,I,c,(E,R)=>{R.stopPropagation(),x(E),ee(E)},(E,R)=>{R.stopPropagation(),ae(E)},(E,R)=>{R.stopPropagation(),ne(E)},(E,R)=>$e(E,R),function(E){z=E,n(3,z)},E=>ke(E.detail),E=>V(E.detail),E=>Ne(E.detail)]}class Rs extends se{constructor(e){super(),ce(this,e,As,xs,ie,{errorMessage:0,isLoading:1,lastUsedScriptPath:26,disableNewAgentCreation:27},null,[-1,-1])}}function ks(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=re(o,t[s]);return{c(){e=Te("svg"),n=new Ge(!0),this.h()},l(s){e=Oe(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=je(e);n=He(c,!0),c.forEach(b),this.h()},h(){n.a=null,be(e,o)},m(s,c){Ve(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M48 112v288h48V112zm-48 0c0-26.5 21.5-48 48-48h48c26.5 0 48 21.5 48 48v288c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48zm224 0v288h48V112zm-48 0c0-26.5 21.5-48 48-48h48c26.5 0 48 21.5 48 48v288c0 26.5-21.5 48-48 48h-48c-26.5 0-48-21.5-48-48z"/>',e)},p(s,[c]){be(e,o=qe(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},1&c&&s[0]]))},i:U,o:U,d(s){s&&b(e)}}}function Ss(r,e,n){return r.$$set=t=>{n(0,e=re(re({},e),ye(t)))},[e=ye(e)]}class Is extends se{constructor(e){super(),ce(this,e,Ss,ks,ie,{})}}function sn(r){let e,n;return e=new ot({props:{color:"info",variant:"soft",size:2,$$slots:{icon:[zs],default:[Ds]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};16414&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Fs(r){let e;return{c(){e=T(r[4])},m(n,t){_(n,e,t)},p(n,t){16&t&&te(e,n[4])},d(n){n&&b(e)}}}function Es(r){let e,n;return e=new Mn({}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Ns(r){let e,n;return e=new Is({}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Bs(r){let e,n,t,o;const s=[Ns,Es],c=[];function i(a,l){return a[1]?0:1}return n=i(r),t=c[n]=s[n](r),{c(){e=k("div"),t.c(),y(e,"slot","iconLeft")},m(a,l){_(a,e,l),c[n].m(e,null),o=!0},p(a,l){let u=n;n=i(a),n!==u&&(G(),d(c[u],1,1,()=>{c[u]=null}),O(),t=c[n],t||(t=c[n]=s[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&b(e),c[n].d()}}}function Ds(r){let e,n,t,o,s,c,i=(r[2]?mt:pt).replace("%MAX_AGENTS%",(r[2]?r[3].maxRemoteAgents:r[3].maxActiveRemoteAgents).toString())+"";return s=new Me({props:{variant:"soft",color:"neutral",size:1,$$slots:{iconLeft:[Bs],default:[Fs]},$$scope:{ctx:r}}}),s.$on("click",r[11]),{c(){e=k("div"),n=k("p"),t=T(i),o=P(),L(s.$$.fragment),y(n,"class","svelte-f3wuoa"),y(e,"class","agent-limit-message svelte-f3wuoa")},m(a,l){_(a,e,l),N(e,n),N(n,t),N(e,o),C(s,e,null),c=!0},p(a,l){(!c||12&l)&&i!==(i=(a[2]?mt:pt).replace("%MAX_AGENTS%",(a[2]?a[3].maxRemoteAgents:a[3].maxActiveRemoteAgents).toString())+"")&&te(t,i);const u={};16402&l&&(u.$$scope={dirty:l,ctx:a}),s.$set(u)},i(a){c||(p(s.$$.fragment,a),c=!0)},o(a){d(s.$$.fragment,a),c=!1},d(a){a&&b(e),A(s)}}}function zs(r){let e,n;return e=new zn({props:{slot:"icon"}}),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Ms(r){let e,n,t=!!r[0]&&sn(r);return{c(){t&&t.c(),e=ue()},m(o,s){t&&t.m(o,s),_(o,e,s),n=!0},p(o,[s]){o[0]?t?(t.p(o,s),1&s&&p(t,1)):(t=sn(o),t.c(),p(t,1),t.m(e.parentNode,e)):t&&(G(),d(t,1,1,()=>{t=null}),O())},i(o){n||(p(t),n=!0)},o(o){d(t),n=!1},d(o){o&&b(e),t&&t.d(o)}}}function cn(r){if(!r)return;const e=r.is_setup_script_agent?"Setup script generation":r.session_summary||"";return{id:r.remote_agent_id,title:e.length>30?e.substring(0,27)+"...":e}}function an(r,e){return r.replace("%MAX_AGENTS%",e.toString())}function Ps(r,e,n){let t,o,s,{agentLimitErrorMessage:c}=e;const i=Re(rt.key);$t(r,i,$=>n(3,s=$));let a,l,u,m=!1,g=[];function f(){return s.agentOverviews.sort(($,v)=>new Date($.started_at).getTime()-new Date(v.started_at).getTime())}async function h(){if(!m&&(a!=null&&a.id))try{m=!0,await i.deleteAgent(a.id)}catch($){console.error("Failed to delete oldest agent:",$)}finally{m=!1}}async function w(){if(!m&&(l!=null&&l.id))try{m=!0,await i.pauseRemoteAgentWorkspace(l.id)}catch($){console.error("Failed to pause oldest active agent:",$)}finally{m=!1}}return r.$$set=$=>{"agentLimitErrorMessage"in $&&n(0,c=$.agentLimitErrorMessage)},r.$$.update=()=>{if(8&r.$$.dirty&&n(2,t=!!s.maxRemoteAgents&&s.agentOverviews.length>=s.maxRemoteAgents),8&r.$$.dirty&&n(1,o=!!s.maxActiveRemoteAgents&&s.agentOverviews.filter($=>$.workspace_status===Ct.workspaceRunning).length>=s.maxActiveRemoteAgents),1806&r.$$.dirty)if(t)n(10,g=f()),n(8,a=cn(g[0])),n(0,c=an(mt,s.maxRemoteAgents)),n(4,u="Delete Oldest Agent"+(a?`: ${a.title}`:""));else if(o){n(10,g=f());const $=g.filter(v=>v.workspace_status===Ct.workspaceRunning);n(9,l=cn($[0])),n(0,c=an(pt,s.maxActiveRemoteAgents)),n(4,u="Pause Oldest Agent"+(l?`: ${l.title}`:""))}else n(8,a=void 0),n(0,c=void 0)},[c,o,t,s,u,i,h,w,a,l,g,()=>{o?w():h()}]}class Us extends se{constructor(e){super(),ce(this,e,Ps,Ms,ie,{agentLimitErrorMessage:0})}}function ln(r){let e,n,t,o;return n=new ot({props:{color:"error",variant:"soft",size:2,$$slots:{default:[Ts]},$$scope:{ctx:r}}}),{c(){e=k("div"),L(n.$$.fragment),y(e,"class","error-message svelte-1klrgvd")},m(s,c){_(s,e,c),C(n,e,null),o=!0},p(s,c){const i={};33554496&c&&(i.$$scope={dirty:c,ctx:s}),n.$set(i)},i(s){o||(p(n.$$.fragment,s),s&&$n(()=>{o&&(t||(t=Je(e,et,{y:10},!0)),t.run(1))}),o=!0)},o(s){d(n.$$.fragment,s),s&&(t||(t=Je(e,et,{y:10},!1)),t.run(0)),o=!1},d(s){s&&b(e),A(n),s&&t&&t.end()}}}function Ts(r){let e,n=r[6].remoteAgentCreationError+"";return{c(){e=T(n)},m(t,o){_(t,e,o)},p(t,o){64&o&&n!==(n=t[6].remoteAgentCreationError+"")&&te(e,n)},d(t){t&&b(e)}}}function Gs(r){let e;return{c(){e=T("Create agent")},m(n,t){_(n,e,t)},d(n){n&&b(e)}}}function Os(r){let e,n;return e=new Me({props:{variant:"solid",color:"accent",size:2,loading:r[10],disabled:r[11],$$slots:{default:[Gs]},$$scope:{ctx:r}}}),e.$on("click",r[16]),{c(){L(e.$$.fragment)},m(t,o){C(e,t,o),n=!0},p(t,o){const s={};1024&o&&(s.loading=t[10]),2048&o&&(s.disabled=t[11]),33554432&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function js(r){var pe,_e,E;let e,n,t,o,s,c,i,a,l,u,m,g,f,h,w,$,v,x,I,B,z,D,S,K,H,W,ne,ae,$e;function me(R){r[18](R)}let xe={};r[2]!==void 0&&(xe.agentLimitErrorMessage=r[2]),i=new Us({props:xe}),he.push(()=>we(i,"agentLimitErrorMessage",me));let V=r[6].remoteAgentCreationError&&ln(r);function Ne(R){r[19](R)}function ke(R){r[20](R)}let ee={lastUsedRepoUrl:r[7],lastUsedBranchName:r[8]};return r[0]!==void 0&&(ee.errorMessage=r[0]),r[1]!==void 0&&(ee.isLoading=r[1]),w=new Br({props:ee}),he.push(()=>we(w,"errorMessage",Ne)),he.push(()=>we(w,"isLoading",ke)),w.$on("commitRefChange",r[14]),D=new Rs({props:{lastUsedScriptPath:r[9],disableNewAgentCreation:!!r[2]||!((pe=r[3])!=null&&pe.name)||!((E=(_e=r[4])==null?void 0:_e.github_commit_ref)!=null&&E.repository_url)}}),D.$on("setupScriptChange",r[15]),H=new kn({props:{editable:!0,hasSendButton:!1}}),ae=new Fe({props:{class:"full-width-button",content:r[5],triggerOn:[ht.Hover],$$slots:{default:[Os]},$$scope:{ctx:r}}}),{c(){e=k("div"),n=k("div"),t=k("div"),t.innerHTML=`<p>Kick off a remote agent to work <strong class="svelte-1klrgvd">in parallel</strong>, in an
        <strong class="svelte-1klrgvd">isolated environment</strong>
        that will keep running, <strong class="svelte-1klrgvd">even when you shut off your laptop</strong>.</p>`,o=P(),s=k("div"),c=k("div"),L(i.$$.fragment),u=P(),V&&V.c(),m=P(),g=k("div"),g.textContent="Start from any GitHub repo and branch:",f=P(),h=k("div"),L(w.$$.fragment),x=P(),I=k("div"),I.textContent=`Select a setup script to prepare the remote environment, so the agent can make better
        changes by running scripts, tests, and building your code:`,B=P(),z=k("div"),L(D.$$.fragment),S=P(),K=k("div"),L(H.$$.fragment),W=P(),ne=k("div"),L(ae.$$.fragment),y(t,"class","main-description svelte-1klrgvd"),y(c,"class","error-message svelte-1klrgvd"),y(g,"class","description svelte-1klrgvd"),y(h,"class","commit-ref-selector svelte-1klrgvd"),y(I,"class","description svelte-1klrgvd"),y(z,"class","setup-script svelte-1klrgvd"),y(K,"class","chat svelte-1klrgvd"),y(ne,"class","create-button svelte-1klrgvd"),y(s,"class","form-fields"),y(n,"class","content svelte-1klrgvd"),y(e,"class","remote-agent-setup svelte-1klrgvd")},m(R,M){_(R,e,M),N(e,n),N(n,t),N(n,o),N(n,s),N(s,c),C(i,c,null),N(s,u),V&&V.m(s,null),N(s,m),N(s,g),N(s,f),N(s,h),C(w,h,null),N(s,x),N(s,I),N(s,B),N(s,z),C(D,z,null),N(s,S),N(s,K),C(H,K,null),N(s,W),N(s,ne),C(ae,ne,null),$e=!0},p(R,[M]){var de,Pe,Xe;const oe={};!a&&4&M&&(a=!0,oe.agentLimitErrorMessage=R[2],ve(()=>a=!1)),i.$set(oe),R[6].remoteAgentCreationError?V?(V.p(R,M),64&M&&p(V,1)):(V=ln(R),V.c(),p(V,1),V.m(s,m)):V&&(G(),d(V,1,1,()=>{V=null}),O());const q={};128&M&&(q.lastUsedRepoUrl=R[7]),256&M&&(q.lastUsedBranchName=R[8]),!$&&1&M&&($=!0,q.errorMessage=R[0],ve(()=>$=!1)),!v&&2&M&&(v=!0,q.isLoading=R[1],ve(()=>v=!1)),w.$set(q);const Le={};512&M&&(Le.lastUsedScriptPath=R[9]),28&M&&(Le.disableNewAgentCreation=!!R[2]||!((de=R[3])!=null&&de.name)||!((Xe=(Pe=R[4])==null?void 0:Pe.github_commit_ref)!=null&&Xe.repository_url)),D.$set(Le);const Se={};32&M&&(Se.content=R[5]),33557504&M&&(Se.$$scope={dirty:M,ctx:R}),ae.$set(Se)},i(R){$e||(p(i.$$.fragment,R),R&&$n(()=>{$e&&(l||(l=Je(c,et,{y:10},!0)),l.run(1))}),p(V),p(w.$$.fragment,R),p(D.$$.fragment,R),p(H.$$.fragment,R),p(ae.$$.fragment,R),$e=!0)},o(R){d(i.$$.fragment,R),R&&(l||(l=Je(c,et,{y:10},!1)),l.run(0)),d(V),d(w.$$.fragment,R),d(D.$$.fragment,R),d(H.$$.fragment,R),d(ae.$$.fragment,R),$e=!1},d(R){R&&b(e),A(i),R&&l&&l.end(),V&&V.d(),A(w),A(D),A(H),A(ae)}}}function Hs(r,e,n){let t,o,s,c,i,a,l,u,m;const g=Re(rt.key);$t(r,g,D=>n(6,m=D));const f=Re("chatModel");$t(r,f,D=>n(22,u=D));const h=Re(wt.key);let w,$="",v=!1,x=null,I=null,B=null;nt(async()=>{try{const D=await g.getLastRemoteAgentSetup();n(7,x=D.lastRemoteAgentGitRepoUrl),n(8,I=D.lastRemoteAgentGitBranch),n(9,B=D.lastRemoteAgentSetupScript),g.setHasEverUsedRemoteAgent(!0),await g.reportRemoteAgentEvent({eventName:In.setupPageOpened,remoteAgentId:"",eventData:{setupPageOpened:{}}})}catch(D){console.error("Failed to load last remote agent setup:",D)}}),un(()=>{g.setNewAgentDraft(null),g.setCreationMetrics(void 0)});const z=Sn(g,u.currentConversationModel,h);return r.$$.update=()=>{var D,S,K;64&r.$$.dirty&&n(4,t=((D=m.newAgentDraft)==null?void 0:D.commitRef)??null),64&r.$$.dirty&&n(3,o=((S=m.newAgentDraft)==null?void 0:S.selectedBranch)??null),64&r.$$.dirty&&(s=((K=m.newAgentDraft)==null?void 0:K.setupScript)??null),31&r.$$.dirty&&n(5,a=(()=>{var ne;const H=(ne=t==null?void 0:t.github_commit_ref)==null?void 0:ne.repository_url,W=o==null?void 0:o.name;return $||w||(v?"Loading repos and branches...":"")||!H&&"Please select a repository"||!W&&"Please select a branch"||(!(!v&&H&&W)&&H&&W?"Loading branch data...":"")||""})()),32&r.$$.dirty&&n(17,l=!!a),131072&r.$$.dirty&&n(11,c=l),64&r.$$.dirty&&n(10,i=m.isCreatingAgent),131136&r.$$.dirty&&g.newAgentDraft&&!m.isCreatingAgent&&g.newAgentDraft.isDisabled!==l&&g.setNewAgentDraft({...g.newAgentDraft,isDisabled:l})},[$,v,w,o,t,a,m,x,I,B,i,c,g,f,async function(D){g.setRemoteAgentCreationError(null);const S=g.newAgentDraft;S?g.setNewAgentDraft({...S,commitRef:D.detail.commitRef,selectedBranch:D.detail.selectedBranch}):g.setNewAgentDraft({commitRef:D.detail.commitRef,selectedBranch:D.detail.selectedBranch,setupScript:null,isDisabled:l,enableNotification:!0})},function(D){g.setRemoteAgentCreationError(null);const S=g.newAgentDraft;S?g.setNewAgentDraft({...S,setupScript:D.detail.script}):g.setNewAgentDraft({commitRef:null,selectedBranch:null,setupScript:D.detail.script,isDisabled:l,enableNotification:!0})},async function(){try{z(),g.saveLastRemoteAgentSetup((t==null?void 0:t.github_commit_ref.repository_url)||null,(o==null?void 0:o.name)||null,(s==null?void 0:s.path)||null)}catch(D){console.error("Failed to create agent:",D)}},l,function(D){w=D,n(2,w)},function(D){$=D,n(0,$)},function(D){v=D,n(1,v)}]}class Pc extends se{constructor(e){super(),ce(this,e,Hs,js,ie,{})}}export{Pc as default};
