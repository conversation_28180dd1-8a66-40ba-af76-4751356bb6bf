import{aU as $t,aV as Yt,aW as qt,aX as Ft,aY as Dn,aZ as Xe,a_ as Cn,_ as y,a$ as q,d as ve,s as Mn,g as _n,q as Sn,r as $n,c as Yn,b as Fn,x as An,m as Un,l as je,j as Qe,k as En,e as Ln,v as In}from"./AugmentMessage-LN57HyfM.js";import{J as gt}from"./SpinnerAugment-JC8TPhVf.js";import{b as Wn,t as At,c as Hn,a as On,l as Pn}from"./linear-BUuNqdz7.js";import{i as zn}from"./init-g68aIKmP.js";import"./pen-to-square-3TLxExQu.js";import"./TextTooltipAugment-BlDY2tAQ.js";import"./BaseButton-D8yhCvaJ.js";import"./IconButtonAugment-BQL_8yIN.js";import"./Content-xvE836E_.js";import"./globals-D0QH3NT1.js";import"./lodash-C-61Uc4F.js";import"./test_service_pb-DM0n7l7E.js";import"./chat-types-NgqNgjwU.js";import"./file-paths-BcSg4gks.js";import"./folder-D_G6V62q.js";import"./github-Du8Ax-RE.js";import"./folder-opened-bSDyFrZo.js";import"./types-BSMhNRWH.js";import"./open-in-new-window-BX_nUqUb.js";import"./types-Cgd-nZOV.js";import"./index-DiI90jLk.js";import"./types-B5Ac2hek.js";import"./index-BFtESN_v.js";import"./CardAugment-BAO5rOsN.js";import"./TextAreaAugment-DxOmi7vy.js";import"./diff-utils-DXaAmVnZ.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-CK0xjdO4.js";import"./keypress-DD1aQVr0.js";import"./await_block-C0teov-5.js";import"./ButtonAugment-CRFFE3_i.js";import"./expand-C7dSG_GJ.js";import"./mcp-logo-BPlx6CTu.js";import"./ellipsis-ce3_p-Q7.js";import"./IconFilePath-3SJFMIIx.js";import"./LanguageIcon-CA8dtZ_C.js";import"./next-edit-types-904A5ehg.js";import"./magnifying-glass-D5YyJVGd.js";import"./MaterialIcon-D8Nb6HkU.js";import"./Filespan-DeFTcAEj.js";import"./chevron-down-B-gSyyd4.js";import"./terminal-BBUsFUTj.js";import"./augment-logo-E1jEbeRV.js";function Nn(e,t){let n;if(t===void 0)for(const r of e)r!=null&&(n<r||n===void 0&&r>=r)&&(n=r);else{let r=-1;for(let i of e)(i=t(i,++r,e))!=null&&(n<i||n===void 0&&i>=i)&&(n=i)}return n}function Bn(e,t){let n;if(t===void 0)for(const r of e)r!=null&&(n>r||n===void 0&&r>=r)&&(n=r);else{let r=-1;for(let i of e)(i=t(i,++r,e))!=null&&(n>i||n===void 0&&i>=i)&&(n=i)}return n}function Vn(e){return e}var Ve=1,Je=2,ct=3,Ne=4,Ut=1e-6;function Zn(e){return"translate("+e+",0)"}function jn(e){return"translate(0,"+e+")"}function Gn(e){return t=>+e(t)}function qn(e,t){return t=Math.max(0,e.bandwidth()-2*t)/2,e.round()&&(t=Math.round(t)),n=>+e(n)+t}function Rn(){return!this.__axis}function Et(e,t){var n=[],r=null,i=null,s=6,u=6,D=3,k=typeof window<"u"&&window.devicePixelRatio>1?0:.5,x=e===Ve||e===Ne?-1:1,S=e===Ne||e===Je?"x":"y",L=e===Ve||e===ct?Zn:jn;function M(_){var w=r??(t.ticks?t.ticks.apply(t,n):t.domain()),v=i??(t.tickFormat?t.tickFormat.apply(t,n):Vn),U=Math.max(s,0)+D,E=t.range(),P=+E[0]+k,W=+E[E.length-1]+k,O=(t.bandwidth?qn:Gn)(t.copy(),k),N=_.selection?_.selection():_,b=N.selectAll(".domain").data([null]),Y=N.selectAll(".tick").data(w,t).order(),T=Y.exit(),d=Y.enter().append("g").attr("class","tick"),g=Y.select("line"),m=Y.select("text");b=b.merge(b.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),Y=Y.merge(d),g=g.merge(d.append("line").attr("stroke","currentColor").attr(S+"2",x*s)),m=m.merge(d.append("text").attr("fill","currentColor").attr(S,x*U).attr("dy",e===Ve?"0em":e===ct?"0.71em":"0.32em")),_!==N&&(b=b.transition(_),Y=Y.transition(_),g=g.transition(_),m=m.transition(_),T=T.transition(_).attr("opacity",Ut).attr("transform",function(c){return isFinite(c=O(c))?L(c+k):this.getAttribute("transform")}),d.attr("opacity",Ut).attr("transform",function(c){var l=this.parentNode.__axis;return L((l&&isFinite(l=l(c))?l:O(c))+k)})),T.remove(),b.attr("d",e===Ne||e===Je?u?"M"+x*u+","+P+"H"+k+"V"+W+"H"+x*u:"M"+k+","+P+"V"+W:u?"M"+P+","+x*u+"V"+k+"H"+W+"V"+x*u:"M"+P+","+k+"H"+W),Y.attr("opacity",1).attr("transform",function(c){return L(O(c)+k)}),g.attr(S+"2",x*s),m.attr(S,x*U).text(v),N.filter(Rn).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",e===Je?"start":e===Ne?"end":"middle"),N.each(function(){this.__axis=O})}return M.scale=function(_){return arguments.length?(t=_,M):t},M.ticks=function(){return n=Array.from(arguments),M},M.tickArguments=function(_){return arguments.length?(n=_==null?[]:Array.from(_),M):n.slice()},M.tickValues=function(_){return arguments.length?(r=_==null?null:Array.from(_),M):r&&r.slice()},M.tickFormat=function(_){return arguments.length?(i=_,M):i},M.tickSize=function(_){return arguments.length?(s=u=+_,M):s},M.tickSizeInner=function(_){return arguments.length?(s=+_,M):s},M.tickSizeOuter=function(_){return arguments.length?(u=+_,M):u},M.tickPadding=function(_){return arguments.length?(D=+_,M):D},M.offset=function(_){return arguments.length?(k=+_,M):k},M}const Xn=Math.PI/180,Qn=180/Math.PI,Rt=.96422,Xt=1,Qt=.82521,Jt=4/29,xe=6/29,Kt=3*xe*xe,Jn=xe*xe*xe;function en(e){if(e instanceof K)return new K(e.l,e.a,e.b,e.opacity);if(e instanceof te)return tn(e);e instanceof qt||(e=Dn(e));var t,n,r=nt(e.r),i=nt(e.g),s=nt(e.b),u=Ke((.2225045*r+.7168786*i+.0606169*s)/Xt);return r===i&&i===s?t=n=u:(t=Ke((.4360747*r+.3850649*i+.1430804*s)/Rt),n=Ke((.0139322*r+.0971045*i+.7141733*s)/Qt)),new K(116*u-16,500*(t-u),200*(u-n),e.opacity)}function K(e,t,n,r){this.l=+e,this.a=+t,this.b=+n,this.opacity=+r}function Ke(e){return e>Jn?Math.pow(e,1/3):e/Kt+Jt}function et(e){return e>xe?e*e*e:Kt*(e-Jt)}function tt(e){return 255*(e<=.0031308?12.92*e:1.055*Math.pow(e,1/2.4)-.055)}function nt(e){return(e/=255)<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4)}function lt(e,t,n,r){return arguments.length===1?function(i){if(i instanceof te)return new te(i.h,i.c,i.l,i.opacity);if(i instanceof K||(i=en(i)),i.a===0&&i.b===0)return new te(NaN,0<i.l&&i.l<100?0:NaN,i.l,i.opacity);var s=Math.atan2(i.b,i.a)*Qn;return new te(s<0?s+360:s,Math.sqrt(i.a*i.a+i.b*i.b),i.l,i.opacity)}(e):new te(e,t,n,r??1)}function te(e,t,n,r){this.h=+e,this.c=+t,this.l=+n,this.opacity=+r}function tn(e){if(isNaN(e.h))return new K(e.l,0,0,e.opacity);var t=e.h*Xn;return new K(e.l,Math.cos(t)*e.c,Math.sin(t)*e.c,e.opacity)}$t(K,function(e,t,n,r){return arguments.length===1?en(e):new K(e,t,n,r??1)},Yt(Ft,{brighter(e){return new K(this.l+18*(e??1),this.a,this.b,this.opacity)},darker(e){return new K(this.l-18*(e??1),this.a,this.b,this.opacity)},rgb(){var e=(this.l+16)/116,t=isNaN(this.a)?e:e+this.a/500,n=isNaN(this.b)?e:e-this.b/200;return t=Rt*et(t),e=Xt*et(e),n=Qt*et(n),new qt(tt(3.1338561*t-1.6168667*e-.4906146*n),tt(-.9787684*t+1.9161415*e+.033454*n),tt(.0719453*t-.2289914*e+1.4052427*n),this.opacity)}})),$t(te,lt,Yt(Ft,{brighter(e){return new te(this.h,this.c,this.l+18*(e??1),this.opacity)},darker(e){return new te(this.h,this.c,this.l-18*(e??1),this.opacity)},rgb(){return tn(this).rgb()}}));const Kn=function(e){return function(t,n){var r=e((t=lt(t)).h,(n=lt(n)).h),i=Xe(t.c,n.c),s=Xe(t.l,n.l),u=Xe(t.opacity,n.opacity);return function(D){return t.h=r(D),t.c=i(D),t.l=s(D),t.opacity=u(D),t+""}}}(Cn),rt=new Date,it=new Date;function V(e,t,n,r){function i(s){return e(s=arguments.length===0?new Date:new Date(+s)),s}return i.floor=s=>(e(s=new Date(+s)),s),i.ceil=s=>(e(s=new Date(s-1)),t(s,1),e(s),s),i.round=s=>{const u=i(s),D=i.ceil(s);return s-u<D-s?u:D},i.offset=(s,u)=>(t(s=new Date(+s),u==null?1:Math.floor(u)),s),i.range=(s,u,D)=>{const k=[];if(s=i.ceil(s),D=D==null?1:Math.floor(D),!(s<u&&D>0))return k;let x;do k.push(x=new Date(+s)),t(s,D),e(s);while(x<s&&s<u);return k},i.filter=s=>V(u=>{if(u>=u)for(;e(u),!s(u);)u.setTime(u-1)},(u,D)=>{if(u>=u)if(D<0)for(;++D<=0;)for(;t(u,-1),!s(u););else for(;--D>=0;)for(;t(u,1),!s(u););}),n&&(i.count=(s,u)=>(rt.setTime(+s),it.setTime(+u),e(rt),e(it),Math.floor(n(rt,it))),i.every=s=>(s=Math.floor(s),isFinite(s)&&s>0?s>1?i.filter(r?u=>r(u)%s==0:u=>i.count(0,u)%s==0):i:null)),i}const be=V(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);be.every=e=>(e=Math.floor(e),isFinite(e)&&e>0?e>1?V(t=>{t.setTime(Math.floor(t/e)*e)},(t,n)=>{t.setTime(+t+n*e)},(t,n)=>(n-t)/e):be:null),be.range;const Ye=1e3,oe=6e4,Fe=36e5,Ae=864e5,mt=6048e5,er=2592e6,at=31536e6,se=V(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*Ye)},(e,t)=>(t-e)/Ye,e=>e.getUTCSeconds());se.range;const Ue=V(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*Ye)},(e,t)=>{e.setTime(+e+t*oe)},(e,t)=>(t-e)/oe,e=>e.getMinutes());Ue.range;const tr=V(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*oe)},(e,t)=>(t-e)/oe,e=>e.getUTCMinutes());tr.range;const Ee=V(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*Ye-e.getMinutes()*oe)},(e,t)=>{e.setTime(+e+t*Fe)},(e,t)=>(t-e)/Fe,e=>e.getHours());Ee.range;const nr=V(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*Fe)},(e,t)=>(t-e)/Fe,e=>e.getUTCHours());nr.range;const de=V(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*oe)/Ae,e=>e.getDate()-1);de.range;const yt=V(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/Ae,e=>e.getUTCDate()-1);yt.range;const rr=V(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/Ae,e=>Math.floor(e/Ae));function ge(e){return V(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,n)=>{t.setDate(t.getDate()+7*n)},(t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*oe)/mt)}rr.range;const We=ge(0),Le=ge(1),nn=ge(2),rn=ge(3),he=ge(4),an=ge(5),sn=ge(6);function me(e){return V(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+7*n)},(t,n)=>(n-t)/mt)}We.range,Le.range,nn.range,rn.range,he.range,an.range,sn.range;const on=me(0),Ge=me(1),ir=me(2),ar=me(3),De=me(4),sr=me(5),or=me(6);on.range,Ge.range,ir.range,ar.range,De.range,sr.range,or.range;const Ie=V(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+12*(t.getFullYear()-e.getFullYear()),e=>e.getMonth());Ie.range;const cr=V(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+12*(t.getUTCFullYear()-e.getUTCFullYear()),e=>e.getUTCMonth());cr.range;const ne=V(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());ne.every=e=>isFinite(e=Math.floor(e))&&e>0?V(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,n)=>{t.setFullYear(t.getFullYear()+n*e)}):null,ne.range;const fe=V(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());fe.every=e=>isFinite(e=Math.floor(e))&&e>0?V(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n*e)}):null,fe.range;const[lr,ur]=function(e,t,n,r,i,s){const u=[[se,1,Ye],[se,5,5e3],[se,15,15e3],[se,30,3e4],[s,1,oe],[s,5,3e5],[s,15,9e5],[s,30,18e5],[i,1,Fe],[i,3,108e5],[i,6,216e5],[i,12,432e5],[r,1,Ae],[r,2,1728e5],[n,1,mt],[t,1,er],[t,3,7776e6],[e,1,at]];function D(k,x,S){const L=Math.abs(x-k)/S,M=Wn(([,,v])=>v).right(u,L);if(M===u.length)return e.every(At(k/at,x/at,S));if(M===0)return be.every(Math.max(At(k,x,S),1));const[_,w]=u[L/u[M-1][2]<u[M][2]/L?M-1:M];return _.every(w)}return[function(k,x,S){const L=x<k;L&&([k,x]=[x,k]);const M=S&&typeof S.range=="function"?S:D(k,x,S),_=M?M.range(k,+x+1):[];return L?_.reverse():_},D]}(ne,Ie,We,de,Ee,Ue);function st(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function ot(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function Me(e,t,n){return{y:e,m:t,d:n,H:0,M:0,S:0,L:0}}var _e,qe,Lt={"-":"",_:" ",0:"0"},Z=/^\s*\d+/,dr=/^%/,hr=/[\\^$*+?|[\]().{}]/g;function I(e,t,n){var r=e<0?"-":"",i=(r?-e:e)+"",s=i.length;return r+(s<n?new Array(n-s+1).join(t)+i:i)}function fr(e){return e.replace(hr,"\\$&")}function Se(e){return new RegExp("^(?:"+e.map(fr).join("|")+")","i")}function $e(e){return new Map(e.map((t,n)=>[t.toLowerCase(),n]))}function gr(e,t,n){var r=Z.exec(t.slice(n,n+1));return r?(e.w=+r[0],n+r[0].length):-1}function mr(e,t,n){var r=Z.exec(t.slice(n,n+1));return r?(e.u=+r[0],n+r[0].length):-1}function yr(e,t,n){var r=Z.exec(t.slice(n,n+2));return r?(e.U=+r[0],n+r[0].length):-1}function pr(e,t,n){var r=Z.exec(t.slice(n,n+2));return r?(e.V=+r[0],n+r[0].length):-1}function kr(e,t,n){var r=Z.exec(t.slice(n,n+2));return r?(e.W=+r[0],n+r[0].length):-1}function It(e,t,n){var r=Z.exec(t.slice(n,n+4));return r?(e.y=+r[0],n+r[0].length):-1}function Wt(e,t,n){var r=Z.exec(t.slice(n,n+2));return r?(e.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function Tr(e,t,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(n,n+6));return r?(e.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function vr(e,t,n){var r=Z.exec(t.slice(n,n+1));return r?(e.q=3*r[0]-3,n+r[0].length):-1}function xr(e,t,n){var r=Z.exec(t.slice(n,n+2));return r?(e.m=r[0]-1,n+r[0].length):-1}function Ht(e,t,n){var r=Z.exec(t.slice(n,n+2));return r?(e.d=+r[0],n+r[0].length):-1}function br(e,t,n){var r=Z.exec(t.slice(n,n+3));return r?(e.m=0,e.d=+r[0],n+r[0].length):-1}function Ot(e,t,n){var r=Z.exec(t.slice(n,n+2));return r?(e.H=+r[0],n+r[0].length):-1}function wr(e,t,n){var r=Z.exec(t.slice(n,n+2));return r?(e.M=+r[0],n+r[0].length):-1}function Dr(e,t,n){var r=Z.exec(t.slice(n,n+2));return r?(e.S=+r[0],n+r[0].length):-1}function Cr(e,t,n){var r=Z.exec(t.slice(n,n+3));return r?(e.L=+r[0],n+r[0].length):-1}function Mr(e,t,n){var r=Z.exec(t.slice(n,n+6));return r?(e.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function _r(e,t,n){var r=dr.exec(t.slice(n,n+1));return r?n+r[0].length:-1}function Sr(e,t,n){var r=Z.exec(t.slice(n));return r?(e.Q=+r[0],n+r[0].length):-1}function $r(e,t,n){var r=Z.exec(t.slice(n));return r?(e.s=+r[0],n+r[0].length):-1}function Pt(e,t){return I(e.getDate(),t,2)}function Yr(e,t){return I(e.getHours(),t,2)}function Fr(e,t){return I(e.getHours()%12||12,t,2)}function Ar(e,t){return I(1+de.count(ne(e),e),t,3)}function cn(e,t){return I(e.getMilliseconds(),t,3)}function Ur(e,t){return cn(e,t)+"000"}function Er(e,t){return I(e.getMonth()+1,t,2)}function Lr(e,t){return I(e.getMinutes(),t,2)}function Ir(e,t){return I(e.getSeconds(),t,2)}function Wr(e){var t=e.getDay();return t===0?7:t}function Hr(e,t){return I(We.count(ne(e)-1,e),t,2)}function ln(e){var t=e.getDay();return t>=4||t===0?he(e):he.ceil(e)}function Or(e,t){return e=ln(e),I(he.count(ne(e),e)+(ne(e).getDay()===4),t,2)}function Pr(e){return e.getDay()}function zr(e,t){return I(Le.count(ne(e)-1,e),t,2)}function Nr(e,t){return I(e.getFullYear()%100,t,2)}function Br(e,t){return I((e=ln(e)).getFullYear()%100,t,2)}function Vr(e,t){return I(e.getFullYear()%1e4,t,4)}function Zr(e,t){var n=e.getDay();return I((e=n>=4||n===0?he(e):he.ceil(e)).getFullYear()%1e4,t,4)}function jr(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+I(t/60|0,"0",2)+I(t%60,"0",2)}function zt(e,t){return I(e.getUTCDate(),t,2)}function Gr(e,t){return I(e.getUTCHours(),t,2)}function qr(e,t){return I(e.getUTCHours()%12||12,t,2)}function Rr(e,t){return I(1+yt.count(fe(e),e),t,3)}function un(e,t){return I(e.getUTCMilliseconds(),t,3)}function Xr(e,t){return un(e,t)+"000"}function Qr(e,t){return I(e.getUTCMonth()+1,t,2)}function Jr(e,t){return I(e.getUTCMinutes(),t,2)}function Kr(e,t){return I(e.getUTCSeconds(),t,2)}function ei(e){var t=e.getUTCDay();return t===0?7:t}function ti(e,t){return I(on.count(fe(e)-1,e),t,2)}function dn(e){var t=e.getUTCDay();return t>=4||t===0?De(e):De.ceil(e)}function ni(e,t){return e=dn(e),I(De.count(fe(e),e)+(fe(e).getUTCDay()===4),t,2)}function ri(e){return e.getUTCDay()}function ii(e,t){return I(Ge.count(fe(e)-1,e),t,2)}function ai(e,t){return I(e.getUTCFullYear()%100,t,2)}function si(e,t){return I((e=dn(e)).getUTCFullYear()%100,t,2)}function oi(e,t){return I(e.getUTCFullYear()%1e4,t,4)}function ci(e,t){var n=e.getUTCDay();return I((e=n>=4||n===0?De(e):De.ceil(e)).getUTCFullYear()%1e4,t,4)}function li(){return"+0000"}function Nt(){return"%"}function Bt(e){return+e}function Vt(e){return Math.floor(+e/1e3)}function ui(e){return new Date(e)}function di(e){return e instanceof Date?+e:+new Date(+e)}function hn(e,t,n,r,i,s,u,D,k,x){var S=Hn(),L=S.invert,M=S.domain,_=x(".%L"),w=x(":%S"),v=x("%I:%M"),U=x("%I %p"),E=x("%a %d"),P=x("%b %d"),W=x("%B"),O=x("%Y");function N(b){return(k(b)<b?_:D(b)<b?w:u(b)<b?v:s(b)<b?U:r(b)<b?i(b)<b?E:P:n(b)<b?W:O)(b)}return S.invert=function(b){return new Date(L(b))},S.domain=function(b){return arguments.length?M(Array.from(b,di)):M().map(ui)},S.ticks=function(b){var Y=M();return e(Y[0],Y[Y.length-1],b??10)},S.tickFormat=function(b,Y){return Y==null?N:x(Y)},S.nice=function(b){var Y=M();return b&&typeof b.range=="function"||(b=t(Y[0],Y[Y.length-1],b??10)),b?M(function(T,d){var g,m=0,c=(T=T.slice()).length-1,l=T[m],o=T[c];return o<l&&(g=m,m=c,c=g,g=l,l=o,o=g),T[m]=d.floor(l),T[c]=d.ceil(o),T}(Y,b)):S},S.copy=function(){return On(S,hn(e,t,n,r,i,s,u,D,k,x))},S}function hi(){return zn.apply(hn(lr,ur,ne,Ie,We,de,Ee,Ue,se,qe).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}_e=function(e){var t=e.dateTime,n=e.date,r=e.time,i=e.periods,s=e.days,u=e.shortDays,D=e.months,k=e.shortMonths,x=Se(i),S=$e(i),L=Se(s),M=$e(s),_=Se(u),w=$e(u),v=Se(D),U=$e(D),E=Se(k),P=$e(k),W={a:function(d){return u[d.getDay()]},A:function(d){return s[d.getDay()]},b:function(d){return k[d.getMonth()]},B:function(d){return D[d.getMonth()]},c:null,d:Pt,e:Pt,f:Ur,g:Br,G:Zr,H:Yr,I:Fr,j:Ar,L:cn,m:Er,M:Lr,p:function(d){return i[+(d.getHours()>=12)]},q:function(d){return 1+~~(d.getMonth()/3)},Q:Bt,s:Vt,S:Ir,u:Wr,U:Hr,V:Or,w:Pr,W:zr,x:null,X:null,y:Nr,Y:Vr,Z:jr,"%":Nt},O={a:function(d){return u[d.getUTCDay()]},A:function(d){return s[d.getUTCDay()]},b:function(d){return k[d.getUTCMonth()]},B:function(d){return D[d.getUTCMonth()]},c:null,d:zt,e:zt,f:Xr,g:si,G:ci,H:Gr,I:qr,j:Rr,L:un,m:Qr,M:Jr,p:function(d){return i[+(d.getUTCHours()>=12)]},q:function(d){return 1+~~(d.getUTCMonth()/3)},Q:Bt,s:Vt,S:Kr,u:ei,U:ti,V:ni,w:ri,W:ii,x:null,X:null,y:ai,Y:oi,Z:li,"%":Nt},N={a:function(d,g,m){var c=_.exec(g.slice(m));return c?(d.w=w.get(c[0].toLowerCase()),m+c[0].length):-1},A:function(d,g,m){var c=L.exec(g.slice(m));return c?(d.w=M.get(c[0].toLowerCase()),m+c[0].length):-1},b:function(d,g,m){var c=E.exec(g.slice(m));return c?(d.m=P.get(c[0].toLowerCase()),m+c[0].length):-1},B:function(d,g,m){var c=v.exec(g.slice(m));return c?(d.m=U.get(c[0].toLowerCase()),m+c[0].length):-1},c:function(d,g,m){return T(d,t,g,m)},d:Ht,e:Ht,f:Mr,g:Wt,G:It,H:Ot,I:Ot,j:br,L:Cr,m:xr,M:wr,p:function(d,g,m){var c=x.exec(g.slice(m));return c?(d.p=S.get(c[0].toLowerCase()),m+c[0].length):-1},q:vr,Q:Sr,s:$r,S:Dr,u:mr,U:yr,V:pr,w:gr,W:kr,x:function(d,g,m){return T(d,n,g,m)},X:function(d,g,m){return T(d,r,g,m)},y:Wt,Y:It,Z:Tr,"%":_r};function b(d,g){return function(m){var c,l,o,p=[],f=-1,C=0,a=d.length;for(m instanceof Date||(m=new Date(+m));++f<a;)d.charCodeAt(f)===37&&(p.push(d.slice(C,f)),(l=Lt[c=d.charAt(++f)])!=null?c=d.charAt(++f):l=c==="e"?" ":"0",(o=g[c])&&(c=o(m,l)),p.push(c),C=f+1);return p.push(d.slice(C,f)),p.join("")}}function Y(d,g){return function(m){var c,l,o=Me(1900,void 0,1);if(T(o,d,m+="",0)!=m.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(g&&!("Z"in o)&&(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),o.m===void 0&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(l=(c=ot(Me(o.y,0,1))).getUTCDay(),c=l>4||l===0?Ge.ceil(c):Ge(c),c=yt.offset(c,7*(o.V-1)),o.y=c.getUTCFullYear(),o.m=c.getUTCMonth(),o.d=c.getUTCDate()+(o.w+6)%7):(l=(c=st(Me(o.y,0,1))).getDay(),c=l>4||l===0?Le.ceil(c):Le(c),c=de.offset(c,7*(o.V-1)),o.y=c.getFullYear(),o.m=c.getMonth(),o.d=c.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:"W"in o?1:0),l="Z"in o?ot(Me(o.y,0,1)).getUTCDay():st(Me(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(l+5)%7:o.w+7*o.U-(l+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,ot(o)):st(o)}}function T(d,g,m,c){for(var l,o,p=0,f=g.length,C=m.length;p<f;){if(c>=C)return-1;if((l=g.charCodeAt(p++))===37){if(l=g.charAt(p++),!(o=N[l in Lt?g.charAt(p++):l])||(c=o(d,m,c))<0)return-1}else if(l!=m.charCodeAt(c++))return-1}return c}return W.x=b(n,W),W.X=b(r,W),W.c=b(t,W),O.x=b(n,O),O.X=b(r,O),O.c=b(t,O),{format:function(d){var g=b(d+="",W);return g.toString=function(){return d},g},parse:function(d){var g=Y(d+="",!1);return g.toString=function(){return d},g},utcFormat:function(d){var g=b(d+="",O);return g.toString=function(){return d},g},utcParse:function(d){var g=Y(d+="",!0);return g.toString=function(){return d},g}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]}),qe=_e.format,_e.parse,_e.utcFormat,_e.utcParse;var Be,fi={exports:{}};const gi=gt(fi.exports=(Be="day",function(e,t,n){var r=function(u){return u.add(4-u.isoWeekday(),Be)},i=t.prototype;i.isoWeekYear=function(){return r(this).year()},i.isoWeek=function(u){if(!this.$utils().u(u))return this.add(7*(u-this.isoWeek()),Be);var D,k,x,S=r(this),L=(D=this.isoWeekYear(),x=4-(k=(this.$u?n.utc:n)().year(D).startOf("year")).isoWeekday(),k.isoWeekday()>4&&(x+=7),k.add(x,Be));return S.diff(L,"week")+1},i.isoWeekday=function(u){return this.$utils().u(u)?this.day()||7:this.day(this.day()%7?u:u-7)};var s=i.startOf;i.startOf=function(u,D){var k=this.$utils(),x=!!k.u(D)||D;return k.p(u)==="isoweek"?x?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):s.bind(this)(u,D)}}));var fn={exports:{}};fn.exports=function(){var e={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},t=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,n=/\d/,r=/\d\d/,i=/\d\d?/,s=/\d*[^-_:/,()\s\d]+/,u={},D=function(w){return(w=+w)+(w>68?1900:2e3)},k=function(w){return function(v){this[w]=+v}},x=[/[+-]\d\d:?(\d\d)?|Z/,function(w){(this.zone||(this.zone={})).offset=function(v){if(!v||v==="Z")return 0;var U=v.match(/([+-]|\d\d)/g),E=60*U[1]+(+U[2]||0);return E===0?0:U[0]==="+"?-E:E}(w)}],S=function(w){var v=u[w];return v&&(v.indexOf?v:v.s.concat(v.f))},L=function(w,v){var U,E=u.meridiem;if(E){for(var P=1;P<=24;P+=1)if(w.indexOf(E(P,0,v))>-1){U=P>12;break}}else U=w===(v?"pm":"PM");return U},M={A:[s,function(w){this.afternoon=L(w,!1)}],a:[s,function(w){this.afternoon=L(w,!0)}],Q:[n,function(w){this.month=3*(w-1)+1}],S:[n,function(w){this.milliseconds=100*+w}],SS:[r,function(w){this.milliseconds=10*+w}],SSS:[/\d{3}/,function(w){this.milliseconds=+w}],s:[i,k("seconds")],ss:[i,k("seconds")],m:[i,k("minutes")],mm:[i,k("minutes")],H:[i,k("hours")],h:[i,k("hours")],HH:[i,k("hours")],hh:[i,k("hours")],D:[i,k("day")],DD:[r,k("day")],Do:[s,function(w){var v=u.ordinal,U=w.match(/\d+/);if(this.day=U[0],v)for(var E=1;E<=31;E+=1)v(E).replace(/\[|\]/g,"")===w&&(this.day=E)}],w:[i,k("week")],ww:[r,k("week")],M:[i,k("month")],MM:[r,k("month")],MMM:[s,function(w){var v=S("months"),U=(S("monthsShort")||v.map(function(E){return E.slice(0,3)})).indexOf(w)+1;if(U<1)throw new Error;this.month=U%12||U}],MMMM:[s,function(w){var v=S("months").indexOf(w)+1;if(v<1)throw new Error;this.month=v%12||v}],Y:[/[+-]?\d+/,k("year")],YY:[r,function(w){this.year=D(w)}],YYYY:[/\d{4}/,k("year")],Z:x,ZZ:x};function _(w){var v,U;v=w,U=u&&u.formats;for(var E=(w=v.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(T,d,g){var m=g&&g.toUpperCase();return d||U[g]||e[g]||U[m].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(c,l,o){return l||o.slice(1)})})).match(t),P=E.length,W=0;W<P;W+=1){var O=E[W],N=M[O],b=N&&N[0],Y=N&&N[1];E[W]=Y?{regex:b,parser:Y}:O.replace(/^\[|\]$/g,"")}return function(T){for(var d={},g=0,m=0;g<P;g+=1){var c=E[g];if(typeof c=="string")m+=c.length;else{var l=c.regex,o=c.parser,p=T.slice(m),f=l.exec(p)[0];o.call(d,f),T=T.replace(f,"")}}return function(C){var a=C.afternoon;if(a!==void 0){var $=C.hours;a?$<12&&(C.hours+=12):$===12&&(C.hours=0),delete C.afternoon}}(d),d}}return function(w,v,U){U.p.customParseFormat=!0,w&&w.parseTwoDigitYear&&(D=w.parseTwoDigitYear);var E=v.prototype,P=E.parse;E.parse=function(W){var O=W.date,N=W.utc,b=W.args;this.$u=N;var Y=b[1];if(typeof Y=="string"){var T=b[2]===!0,d=b[3]===!0,g=T||d,m=b[2];d&&(m=b[2]),u=this.$locale(),!T&&m&&(u=U.Ls[m]),this.$d=function(p,f,C,a){try{if(["x","X"].indexOf(f)>-1)return new Date((f==="X"?1e3:1)*p);var $=_(f)(p),h=$.year,F=$.month,H=$.day,z=$.hours,A=$.minutes,Q=$.seconds,ce=$.milliseconds,pe=$.zone,ze=$.week,ke=new Date,j=H||(h||F?1:ke.getDate()),J=h||ke.getFullYear(),G=0;h&&!F||(G=F>0?F-1:ke.getMonth());var le,re=z||0,R=A||0,Te=Q||0,ie=ce||0;return pe?new Date(Date.UTC(J,G,j,re,R,Te,ie+60*pe.offset*1e3)):C?new Date(Date.UTC(J,G,j,re,R,Te,ie)):(le=new Date(J,G,j,re,R,Te,ie),ze&&(le=a(le).week(ze).toDate()),le)}catch{return new Date("")}}(O,Y,N,U),this.init(),m&&m!==!0&&(this.$L=this.locale(m).$L),g&&O!=this.format(Y)&&(this.$d=new Date("")),u={}}else if(Y instanceof Array)for(var c=Y.length,l=1;l<=c;l+=1){b[1]=Y[l-1];var o=U.apply(this,b);if(o.isValid()){this.$d=o.$d,this.$L=o.$L,this.init();break}l===c&&(this.$d=new Date(""))}else P.call(this,W)}}}();const mi=gt(fn.exports);var gn={exports:{}};gn.exports=function(e,t){var n=t.prototype,r=n.format;n.format=function(i){var s=this,u=this.$locale();if(!this.isValid())return r.bind(this)(i);var D=this.$utils(),k=(i||"YYYY-MM-DDTHH:mm:ssZ").replace(/\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,function(x){switch(x){case"Q":return Math.ceil((s.$M+1)/3);case"Do":return u.ordinal(s.$D);case"gggg":return s.weekYear();case"GGGG":return s.isoWeekYear();case"wo":return u.ordinal(s.week(),"W");case"w":case"ww":return D.s(s.week(),x==="w"?1:2,"0");case"W":case"WW":return D.s(s.isoWeek(),x==="W"?1:2,"0");case"k":case"kk":return D.s(String(s.$H===0?24:s.$H),x==="k"?1:2,"0");case"X":return Math.floor(s.$d.getTime()/1e3);case"x":return s.$d.getTime();case"z":return"["+s.offsetName()+"]";case"zzz":return"["+s.offsetName("long")+"]";default:return x}});return r.bind(this)(k)}};const yi=gt(gn.exports);var ut=function(){var e=y(function(l,o,p,f){for(p=p||{},f=l.length;f--;p[l[f]]=o);return p},"o"),t=[6,8,10,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28,29,30,31,33,35,36,38,40],n=[1,26],r=[1,27],i=[1,28],s=[1,29],u=[1,30],D=[1,31],k=[1,32],x=[1,33],S=[1,34],L=[1,9],M=[1,10],_=[1,11],w=[1,12],v=[1,13],U=[1,14],E=[1,15],P=[1,16],W=[1,19],O=[1,20],N=[1,21],b=[1,22],Y=[1,23],T=[1,25],d=[1,35],g={trace:y(function(){},"trace"),yy:{},symbols_:{error:2,start:3,gantt:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NL:10,weekday:11,weekday_monday:12,weekday_tuesday:13,weekday_wednesday:14,weekday_thursday:15,weekday_friday:16,weekday_saturday:17,weekday_sunday:18,weekend:19,weekend_friday:20,weekend_saturday:21,dateFormat:22,inclusiveEndDates:23,topAxis:24,axisFormat:25,tickInterval:26,excludes:27,includes:28,todayMarker:29,title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,section:36,clickStatement:37,taskTxt:38,taskData:39,click:40,callbackname:41,callbackargs:42,href:43,clickStatementDebug:44,$accept:0,$end:1},terminals_:{2:"error",4:"gantt",6:"EOF",8:"SPACE",10:"NL",12:"weekday_monday",13:"weekday_tuesday",14:"weekday_wednesday",15:"weekday_thursday",16:"weekday_friday",17:"weekday_saturday",18:"weekday_sunday",20:"weekend_friday",21:"weekend_saturday",22:"dateFormat",23:"inclusiveEndDates",24:"topAxis",25:"axisFormat",26:"tickInterval",27:"excludes",28:"includes",29:"todayMarker",30:"title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"section",38:"taskTxt",39:"taskData",40:"click",41:"callbackname",42:"callbackargs",43:"href"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[19,1],[19,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,2],[37,2],[37,3],[37,3],[37,4],[37,3],[37,4],[37,2],[44,2],[44,3],[44,3],[44,4],[44,3],[44,4],[44,2]],performAction:y(function(l,o,p,f,C,a,$){var h=a.length-1;switch(C){case 1:return a[h-1];case 2:case 6:case 7:this.$=[];break;case 3:a[h-1].push(a[h]),this.$=a[h-1];break;case 4:case 5:this.$=a[h];break;case 8:f.setWeekday("monday");break;case 9:f.setWeekday("tuesday");break;case 10:f.setWeekday("wednesday");break;case 11:f.setWeekday("thursday");break;case 12:f.setWeekday("friday");break;case 13:f.setWeekday("saturday");break;case 14:f.setWeekday("sunday");break;case 15:f.setWeekend("friday");break;case 16:f.setWeekend("saturday");break;case 17:f.setDateFormat(a[h].substr(11)),this.$=a[h].substr(11);break;case 18:f.enableInclusiveEndDates(),this.$=a[h].substr(18);break;case 19:f.TopAxis(),this.$=a[h].substr(8);break;case 20:f.setAxisFormat(a[h].substr(11)),this.$=a[h].substr(11);break;case 21:f.setTickInterval(a[h].substr(13)),this.$=a[h].substr(13);break;case 22:f.setExcludes(a[h].substr(9)),this.$=a[h].substr(9);break;case 23:f.setIncludes(a[h].substr(9)),this.$=a[h].substr(9);break;case 24:f.setTodayMarker(a[h].substr(12)),this.$=a[h].substr(12);break;case 27:f.setDiagramTitle(a[h].substr(6)),this.$=a[h].substr(6);break;case 28:this.$=a[h].trim(),f.setAccTitle(this.$);break;case 29:case 30:this.$=a[h].trim(),f.setAccDescription(this.$);break;case 31:f.addSection(a[h].substr(8)),this.$=a[h].substr(8);break;case 33:f.addTask(a[h-1],a[h]),this.$="task";break;case 34:this.$=a[h-1],f.setClickEvent(a[h-1],a[h],null);break;case 35:this.$=a[h-2],f.setClickEvent(a[h-2],a[h-1],a[h]);break;case 36:this.$=a[h-2],f.setClickEvent(a[h-2],a[h-1],null),f.setLink(a[h-2],a[h]);break;case 37:this.$=a[h-3],f.setClickEvent(a[h-3],a[h-2],a[h-1]),f.setLink(a[h-3],a[h]);break;case 38:this.$=a[h-2],f.setClickEvent(a[h-2],a[h],null),f.setLink(a[h-2],a[h-1]);break;case 39:this.$=a[h-3],f.setClickEvent(a[h-3],a[h-1],a[h]),f.setLink(a[h-3],a[h-2]);break;case 40:this.$=a[h-1],f.setLink(a[h-1],a[h]);break;case 41:case 47:this.$=a[h-1]+" "+a[h];break;case 42:case 43:case 45:this.$=a[h-2]+" "+a[h-1]+" "+a[h];break;case 44:case 46:this.$=a[h-3]+" "+a[h-2]+" "+a[h-1]+" "+a[h]}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},e(t,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:17,12:n,13:r,14:i,15:s,16:u,17:D,18:k,19:18,20:x,21:S,22:L,23:M,24:_,25:w,26:v,27:U,28:E,29:P,30:W,31:O,33:N,35:b,36:Y,37:24,38:T,40:d},e(t,[2,7],{1:[2,1]}),e(t,[2,3]),{9:36,11:17,12:n,13:r,14:i,15:s,16:u,17:D,18:k,19:18,20:x,21:S,22:L,23:M,24:_,25:w,26:v,27:U,28:E,29:P,30:W,31:O,33:N,35:b,36:Y,37:24,38:T,40:d},e(t,[2,5]),e(t,[2,6]),e(t,[2,17]),e(t,[2,18]),e(t,[2,19]),e(t,[2,20]),e(t,[2,21]),e(t,[2,22]),e(t,[2,23]),e(t,[2,24]),e(t,[2,25]),e(t,[2,26]),e(t,[2,27]),{32:[1,37]},{34:[1,38]},e(t,[2,30]),e(t,[2,31]),e(t,[2,32]),{39:[1,39]},e(t,[2,8]),e(t,[2,9]),e(t,[2,10]),e(t,[2,11]),e(t,[2,12]),e(t,[2,13]),e(t,[2,14]),e(t,[2,15]),e(t,[2,16]),{41:[1,40],43:[1,41]},e(t,[2,4]),e(t,[2,28]),e(t,[2,29]),e(t,[2,33]),e(t,[2,34],{42:[1,42],43:[1,43]}),e(t,[2,40],{41:[1,44]}),e(t,[2,35],{43:[1,45]}),e(t,[2,36]),e(t,[2,38],{42:[1,46]}),e(t,[2,37]),e(t,[2,39])],defaultActions:{},parseError:y(function(l,o){if(!o.recoverable){var p=new Error(l);throw p.hash=o,p}this.trace(l)},"parseError"),parse:y(function(l){var o=this,p=[0],f=[],C=[null],a=[],$=this.table,h="",F=0,H=0,z=a.slice.call(arguments,1),A=Object.create(this.lexer),Q={yy:{}};for(var ce in this.yy)Object.prototype.hasOwnProperty.call(this.yy,ce)&&(Q.yy[ce]=this.yy[ce]);A.setInput(l,Q.yy),Q.yy.lexer=A,Q.yy.parser=this,A.yylloc===void 0&&(A.yylloc={});var pe=A.yylloc;a.push(pe);var ze=A.options&&A.options.ranges;function ke(){var X;return typeof(X=f.pop()||A.lex()||1)!="number"&&(X instanceof Array&&(X=(f=X).pop()),X=o.symbols_[X]||X),X}typeof Q.yy.parseError=="function"?this.parseError=Q.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,y(function(X){p.length=p.length-2*X,C.length=C.length-X,a.length=a.length-X},"popStack"),y(ke,"lex");for(var j,J,G,le,re,R,Te,ie,ue={};;){if(J=p[p.length-1],this.defaultActions[J]?G=this.defaultActions[J]:(j==null&&(j=ke()),G=$[J]&&$[J][j]),G===void 0||!G.length||!G[0]){var St="";for(re in ie=[],$[J])this.terminals_[re]&&re>2&&ie.push("'"+this.terminals_[re]+"'");St=A.showPosition?"Parse error on line "+(F+1)+`:
`+A.showPosition()+`
Expecting `+ie.join(", ")+", got '"+(this.terminals_[j]||j)+"'":"Parse error on line "+(F+1)+": Unexpected "+(j==1?"end of input":"'"+(this.terminals_[j]||j)+"'"),this.parseError(St,{text:A.match,token:this.terminals_[j]||j,line:A.yylineno,loc:pe,expected:ie})}if(G[0]instanceof Array&&G.length>1)throw new Error("Parse Error: multiple actions possible at state: "+J+", token: "+j);switch(G[0]){case 1:p.push(j),C.push(A.yytext),a.push(A.yylloc),p.push(G[1]),j=null,H=A.yyleng,h=A.yytext,F=A.yylineno,pe=A.yylloc;break;case 2:if(R=this.productions_[G[1]][1],ue.$=C[C.length-R],ue._$={first_line:a[a.length-(R||1)].first_line,last_line:a[a.length-1].last_line,first_column:a[a.length-(R||1)].first_column,last_column:a[a.length-1].last_column},ze&&(ue._$.range=[a[a.length-(R||1)].range[0],a[a.length-1].range[1]]),(le=this.performAction.apply(ue,[h,H,F,Q.yy,G[1],C,a].concat(z)))!==void 0)return le;R&&(p=p.slice(0,-1*R*2),C=C.slice(0,-1*R),a=a.slice(0,-1*R)),p.push(this.productions_[G[1]][0]),C.push(ue.$),a.push(ue._$),Te=$[p[p.length-2]][p[p.length-1]],p.push(Te);break;case 3:return!0}}return!0},"parse")},m=function(){return{EOF:1,parseError:y(function(l,o){if(!this.yy.parser)throw new Error(l);this.yy.parser.parseError(l,o)},"parseError"),setInput:y(function(l,o){return this.yy=o||this.yy||{},this._input=l,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:y(function(){var l=this._input[0];return this.yytext+=l,this.yyleng++,this.offset++,this.match+=l,this.matched+=l,l.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),l},"input"),unput:y(function(l){var o=l.length,p=l.split(/(?:\r\n?|\n)/g);this._input=l+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-o),this.offset-=o;var f=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),p.length-1&&(this.yylineno-=p.length-1);var C=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:p?(p.length===f.length?this.yylloc.first_column:0)+f[f.length-p.length].length-p[0].length:this.yylloc.first_column-o},this.options.ranges&&(this.yylloc.range=[C[0],C[0]+this.yyleng-o]),this.yyleng=this.yytext.length,this},"unput"),more:y(function(){return this._more=!0,this},"more"),reject:y(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:y(function(l){this.unput(this.match.slice(l))},"less"),pastInput:y(function(){var l=this.matched.substr(0,this.matched.length-this.match.length);return(l.length>20?"...":"")+l.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:y(function(){var l=this.match;return l.length<20&&(l+=this._input.substr(0,20-l.length)),(l.substr(0,20)+(l.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:y(function(){var l=this.pastInput(),o=new Array(l.length+1).join("-");return l+this.upcomingInput()+`
`+o+"^"},"showPosition"),test_match:y(function(l,o){var p,f,C;if(this.options.backtrack_lexer&&(C={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(C.yylloc.range=this.yylloc.range.slice(0))),(f=l[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=f.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:f?f[f.length-1].length-f[f.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+l[0].length},this.yytext+=l[0],this.match+=l[0],this.matches=l,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(l[0].length),this.matched+=l[0],p=this.performAction.call(this,this.yy,this,o,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),p)return p;if(this._backtrack){for(var a in C)this[a]=C[a];return!1}return!1},"test_match"),next:y(function(){if(this.done)return this.EOF;var l,o,p,f;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var C=this._currentRules(),a=0;a<C.length;a++)if((p=this._input.match(this.rules[C[a]]))&&(!o||p[0].length>o[0].length)){if(o=p,f=a,this.options.backtrack_lexer){if((l=this.test_match(p,C[a]))!==!1)return l;if(this._backtrack){o=!1;continue}return!1}if(!this.options.flex)break}return o?(l=this.test_match(o,C[f]))!==!1&&l:this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:y(function(){var l=this.next();return l||this.lex()},"lex"),begin:y(function(l){this.conditionStack.push(l)},"begin"),popState:y(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:y(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:y(function(l){return(l=this.conditionStack.length-1-Math.abs(l||0))>=0?this.conditionStack[l]:"INITIAL"},"topState"),pushState:y(function(l){this.begin(l)},"pushState"),stateStackSize:y(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:y(function(l,o,p,f){switch(p){case 0:return this.begin("open_directive"),"open_directive";case 1:return this.begin("acc_title"),31;case 2:return this.popState(),"acc_title_value";case 3:return this.begin("acc_descr"),33;case 4:return this.popState(),"acc_descr_value";case 5:this.begin("acc_descr_multiline");break;case 6:case 15:case 18:case 21:case 24:this.popState();break;case 7:return"acc_descr_multiline_value";case 8:case 9:case 10:case 12:case 13:break;case 11:return 10;case 14:this.begin("href");break;case 16:return 43;case 17:this.begin("callbackname");break;case 19:this.popState(),this.begin("callbackargs");break;case 20:return 41;case 22:return 42;case 23:this.begin("click");break;case 25:return 40;case 26:return 4;case 27:return 22;case 28:return 23;case 29:return 24;case 30:return 25;case 31:return 26;case 32:return 28;case 33:return 27;case 34:return 29;case 35:return 12;case 36:return 13;case 37:return 14;case 38:return 15;case 39:return 16;case 40:return 17;case 41:return 18;case 42:return 20;case 43:return 21;case 44:return"date";case 45:return 30;case 46:return"accDescription";case 47:return 36;case 48:return 38;case 49:return 39;case 50:return":";case 51:return 6;case 52:return"INVALID"}},"anonymous"),rules:[/^(?:%%\{)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:%%(?!\{)*[^\n]*)/i,/^(?:[^\}]%%*[^\n]*)/i,/^(?:%%*[^\n]*[\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:%[^\n]*)/i,/^(?:href[\s]+["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:call[\s]+)/i,/^(?:\([\s]*\))/i,/^(?:\()/i,/^(?:[^(]*)/i,/^(?:\))/i,/^(?:[^)]*)/i,/^(?:click[\s]+)/i,/^(?:[\s\n])/i,/^(?:[^\s\n]*)/i,/^(?:gantt\b)/i,/^(?:dateFormat\s[^#\n;]+)/i,/^(?:inclusiveEndDates\b)/i,/^(?:topAxis\b)/i,/^(?:axisFormat\s[^#\n;]+)/i,/^(?:tickInterval\s[^#\n;]+)/i,/^(?:includes\s[^#\n;]+)/i,/^(?:excludes\s[^#\n;]+)/i,/^(?:todayMarker\s[^\n;]+)/i,/^(?:weekday\s+monday\b)/i,/^(?:weekday\s+tuesday\b)/i,/^(?:weekday\s+wednesday\b)/i,/^(?:weekday\s+thursday\b)/i,/^(?:weekday\s+friday\b)/i,/^(?:weekday\s+saturday\b)/i,/^(?:weekday\s+sunday\b)/i,/^(?:weekend\s+friday\b)/i,/^(?:weekend\s+saturday\b)/i,/^(?:\d\d\d\d-\d\d-\d\d\b)/i,/^(?:title\s[^\n]+)/i,/^(?:accDescription\s[^#\n;]+)/i,/^(?:section\s[^\n]+)/i,/^(?:[^:\n]+)/i,/^(?::[^#\n;]+)/i,/^(?::)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[6,7],inclusive:!1},acc_descr:{rules:[4],inclusive:!1},acc_title:{rules:[2],inclusive:!1},callbackargs:{rules:[21,22],inclusive:!1},callbackname:{rules:[18,19,20],inclusive:!1},href:{rules:[15,16],inclusive:!1},click:{rules:[24,25],inclusive:!1},INITIAL:{rules:[0,1,3,5,8,9,10,11,12,13,14,17,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],inclusive:!0}}}}();function c(){this.yy={}}return g.lexer=m,y(c,"Parser"),c.prototype=g,g.Parser=c,new c}();ut.parser=ut;var pi=ut;q.extend(gi),q.extend(mi),q.extend(yi);var dt,Ze,Zt={friday:5,saturday:6},ee="",pt="",kt=void 0,Tt="",He=[],Oe=[],vt=new Map,xt=[],bt=[],Ce="",wt="",mn=["active","done","crit","milestone"],Dt=[],Pe=!1,Ct=!1,Mt="sunday",Re="saturday",ht=0,ki=y(function(){xt=[],bt=[],Ce="",Dt=[],vn=0,dt=void 0,Ze=void 0,B=[],ee="",pt="",wt="",kt=void 0,Tt="",He=[],Oe=[],Pe=!1,Ct=!1,ht=0,vt=new Map,An(),Mt="sunday",Re="saturday"},"clear"),Ti=y(function(e){pt=e},"setAxisFormat"),vi=y(function(){return pt},"getAxisFormat"),xi=y(function(e){kt=e},"setTickInterval"),bi=y(function(){return kt},"getTickInterval"),wi=y(function(e){Tt=e},"setTodayMarker"),Di=y(function(){return Tt},"getTodayMarker"),Ci=y(function(e){ee=e},"setDateFormat"),Mi=y(function(){Pe=!0},"enableInclusiveEndDates"),_i=y(function(){return Pe},"endDatesAreInclusive"),Si=y(function(){Ct=!0},"enableTopAxis"),$i=y(function(){return Ct},"topAxisEnabled"),Yi=y(function(e){wt=e},"setDisplayMode"),Fi=y(function(){return wt},"getDisplayMode"),Ai=y(function(){return ee},"getDateFormat"),Ui=y(function(e){He=e.toLowerCase().split(/[\s,]+/)},"setIncludes"),Ei=y(function(){return He},"getIncludes"),Li=y(function(e){Oe=e.toLowerCase().split(/[\s,]+/)},"setExcludes"),Ii=y(function(){return Oe},"getExcludes"),Wi=y(function(){return vt},"getLinks"),Hi=y(function(e){Ce=e,xt.push(e)},"addSection"),Oi=y(function(){return xt},"getSections"),Pi=y(function(){let e=jt(),t=0;for(;!e&&t<10;)e=jt(),t++;return bt=B},"getTasks"),yn=y(function(e,t,n,r){return!r.includes(e.format(t.trim()))&&(!(!n.includes("weekends")||e.isoWeekday()!==Zt[Re]&&e.isoWeekday()!==Zt[Re]+1)||!!n.includes(e.format("dddd").toLowerCase())||n.includes(e.format(t.trim())))},"isInvalidDate"),zi=y(function(e){Mt=e},"setWeekday"),Ni=y(function(){return Mt},"getWeekday"),Bi=y(function(e){Re=e},"setWeekend"),pn=y(function(e,t,n,r){if(!n.length||e.manualEndTime)return;let i,s;i=e.startTime instanceof Date?q(e.startTime):q(e.startTime,t,!0),i=i.add(1,"d"),s=e.endTime instanceof Date?q(e.endTime):q(e.endTime,t,!0);const[u,D]=Vi(i,s,t,n,r);e.endTime=u.toDate(),e.renderEndTime=D},"checkTaskDates"),Vi=y(function(e,t,n,r,i){let s=!1,u=null;for(;e<=t;)s||(u=t.toDate()),s=yn(e,n,r,i),s&&(t=t.add(1,"d")),e=e.add(1,"d");return[t,u]},"fixTaskDates"),ft=y(function(e,t,n){n=n.trim();const r=/^after\s+(?<ids>[\d\w- ]+)/.exec(n);if(r!==null){let s=null;for(const D of r.groups.ids.split(" ")){let k=ye(D);k!==void 0&&(!s||k.endTime>s.endTime)&&(s=k)}if(s)return s.endTime;const u=new Date;return u.setHours(0,0,0,0),u}let i=q(n,t.trim(),!0);if(i.isValid())return i.toDate();{je.debug("Invalid date:"+n),je.debug("With date format:"+t.trim());const s=new Date(n);if(s===void 0||isNaN(s.getTime())||s.getFullYear()<-1e4||s.getFullYear()>1e4)throw new Error("Invalid date:"+n);return s}},"getStartDate"),kn=y(function(e){const t=/^(\d+(?:\.\d+)?)([Mdhmswy]|ms)$/.exec(e.trim());return t!==null?[Number.parseFloat(t[1]),t[2]]:[NaN,"ms"]},"parseDuration"),Tn=y(function(e,t,n,r=!1){n=n.trim();const i=/^until\s+(?<ids>[\d\w- ]+)/.exec(n);if(i!==null){let x=null;for(const L of i.groups.ids.split(" ")){let M=ye(L);M!==void 0&&(!x||M.startTime<x.startTime)&&(x=M)}if(x)return x.startTime;const S=new Date;return S.setHours(0,0,0,0),S}let s=q(n,t.trim(),!0);if(s.isValid())return r&&(s=s.add(1,"d")),s.toDate();let u=q(e);const[D,k]=kn(n);if(!Number.isNaN(D)){const x=u.add(D,k);x.isValid()&&(u=x)}return u.toDate()},"getEndDate"),vn=0,we=y(function(e){return e===void 0?"task"+(vn+=1):e},"parseId"),Zi=y(function(e,t){let n;n=t.substr(0,1)===":"?t.substr(1,t.length):t;const r=n.split(","),i={};_t(r,i,mn);for(let u=0;u<r.length;u++)r[u]=r[u].trim();let s="";switch(r.length){case 1:i.id=we(),i.startTime=e.endTime,s=r[0];break;case 2:i.id=we(),i.startTime=ft(void 0,ee,r[0]),s=r[1];break;case 3:i.id=we(r[0]),i.startTime=ft(void 0,ee,r[1]),s=r[2]}return s&&(i.endTime=Tn(i.startTime,ee,s,Pe),i.manualEndTime=q(s,"YYYY-MM-DD",!0).isValid(),pn(i,ee,Oe,He)),i},"compileData"),ji=y(function(e,t){let n;n=t.substr(0,1)===":"?t.substr(1,t.length):t;const r=n.split(","),i={};_t(r,i,mn);for(let s=0;s<r.length;s++)r[s]=r[s].trim();switch(r.length){case 1:i.id=we(),i.startTime={type:"prevTaskEnd",id:e},i.endTime={data:r[0]};break;case 2:i.id=we(),i.startTime={type:"getStartDate",startData:r[0]},i.endTime={data:r[1]};break;case 3:i.id=we(r[0]),i.startTime={type:"getStartDate",startData:r[1]},i.endTime={data:r[2]}}return i},"parseData"),B=[],xn={},Gi=y(function(e,t){const n={section:Ce,type:Ce,processed:!1,manualEndTime:!1,renderEndTime:null,raw:{data:t},task:e,classes:[]},r=ji(Ze,t);n.raw.startTime=r.startTime,n.raw.endTime=r.endTime,n.id=r.id,n.prevTaskId=Ze,n.active=r.active,n.done=r.done,n.crit=r.crit,n.milestone=r.milestone,n.order=ht,ht++;const i=B.push(n);Ze=n.id,xn[n.id]=i-1},"addTask"),ye=y(function(e){const t=xn[e];return B[t]},"findTaskById"),qi=y(function(e,t){const n={section:Ce,type:Ce,description:e,task:e,classes:[]},r=Zi(dt,t);n.startTime=r.startTime,n.endTime=r.endTime,n.id=r.id,n.active=r.active,n.done=r.done,n.crit=r.crit,n.milestone=r.milestone,dt=n,bt.push(n)},"addTaskOrg"),jt=y(function(){const e=y(function(n){const r=B[n];let i="";switch(B[n].raw.startTime.type){case"prevTaskEnd":{const s=ye(r.prevTaskId);r.startTime=s.endTime;break}case"getStartDate":i=ft(void 0,ee,B[n].raw.startTime.startData),i&&(B[n].startTime=i)}return B[n].startTime&&(B[n].endTime=Tn(B[n].startTime,ee,B[n].raw.endTime.data,Pe),B[n].endTime&&(B[n].processed=!0,B[n].manualEndTime=q(B[n].raw.endTime.data,"YYYY-MM-DD",!0).isValid(),pn(B[n],ee,Oe,He))),B[n].processed},"compileTask");let t=!0;for(const[n,r]of B.entries())e(n),t=t&&r.processed;return t},"compileTasks"),Ri=y(function(e,t){let n=t;ve().securityLevel!=="loose"&&(n=Un(t)),e.split(",").forEach(function(r){ye(r)!==void 0&&(wn(r,()=>{window.open(n,"_self")}),vt.set(r,n))}),bn(e,"clickable")},"setLink"),bn=y(function(e,t){e.split(",").forEach(function(n){let r=ye(n);r!==void 0&&r.classes.push(t)})},"setClass"),Xi=y(function(e,t,n){if(ve().securityLevel!=="loose"||t===void 0)return;let r=[];if(typeof n=="string"){r=n.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let i=0;i<r.length;i++){let s=r[i].trim();s.startsWith('"')&&s.endsWith('"')&&(s=s.substr(1,s.length-2)),r[i]=s}}r.length===0&&r.push(e),ye(e)!==void 0&&wn(e,()=>{In.runFunc(t,...r)})},"setClickFun"),wn=y(function(e,t){Dt.push(function(){const n=document.querySelector(`[id="${e}"]`);n!==null&&n.addEventListener("click",function(){t()})},function(){const n=document.querySelector(`[id="${e}-text"]`);n!==null&&n.addEventListener("click",function(){t()})})},"pushFun"),Qi=y(function(e,t,n){e.split(",").forEach(function(r){Xi(r,t,n)}),bn(e,"clickable")},"setClickEvent"),Ji=y(function(e){Dt.forEach(function(t){t(e)})},"bindFunctions"),Ki={getConfig:y(()=>ve().gantt,"getConfig"),clear:ki,setDateFormat:Ci,getDateFormat:Ai,enableInclusiveEndDates:Mi,endDatesAreInclusive:_i,enableTopAxis:Si,topAxisEnabled:$i,setAxisFormat:Ti,getAxisFormat:vi,setTickInterval:xi,getTickInterval:bi,setTodayMarker:wi,getTodayMarker:Di,setAccTitle:Mn,getAccTitle:_n,setDiagramTitle:Sn,getDiagramTitle:$n,setDisplayMode:Yi,getDisplayMode:Fi,setAccDescription:Yn,getAccDescription:Fn,addSection:Hi,getSections:Oi,getTasks:Pi,addTask:Gi,findTaskById:ye,addTaskOrg:qi,setIncludes:Ui,getIncludes:Ei,setExcludes:Li,getExcludes:Ii,setClickEvent:Qi,setLink:Ri,getLinks:Wi,bindFunctions:Ji,parseDuration:kn,isInvalidDate:yn,setWeekday:zi,getWeekday:Ni,setWeekend:Bi};function _t(e,t,n){let r=!0;for(;r;)r=!1,n.forEach(function(i){const s=new RegExp("^\\s*"+i+"\\s*$");e[0].match(s)&&(t[i]=!0,e.shift(1),r=!0)})}y(_t,"getTaskTags");var ae,ea=y(function(){je.debug("Something is calling, setConf, remove the call")},"setConf"),Gt={monday:Le,tuesday:nn,wednesday:rn,thursday:he,friday:an,saturday:sn,sunday:We},ta=y((e,t)=>{let n=[...e].map(()=>-1/0),r=[...e].sort((s,u)=>s.startTime-u.startTime||s.order-u.order),i=0;for(const s of r)for(let u=0;u<n.length;u++)if(s.startTime>=n[u]){n[u]=s.endTime,s.order=u+t,u>i&&(i=u);break}return i},"getMaxIntersections"),Ga={parser:pi,db:Ki,renderer:{setConf:ea,draw:y(function(e,t,n,r){const i=ve().gantt,s=ve().securityLevel;let u;s==="sandbox"&&(u=Qe("#i"+t));const D=Qe(s==="sandbox"?u.nodes()[0].contentDocument.body:"body"),k=s==="sandbox"?u.nodes()[0].contentDocument:document,x=k.getElementById(t);(ae=x.parentElement.offsetWidth)===void 0&&(ae=1200),i.useWidth!==void 0&&(ae=i.useWidth);const S=r.db.getTasks();let L=[];for(const T of S)L.push(T.type);L=Y(L);const M={};let _=2*i.topPadding;if(r.db.getDisplayMode()==="compact"||i.displayMode==="compact"){const T={};for(const g of S)T[g.section]===void 0?T[g.section]=[g]:T[g.section].push(g);let d=0;for(const g of Object.keys(T)){const m=ta(T[g],d)+1;d+=m,_+=m*(i.barHeight+i.barGap),M[g]=m}}else{_+=S.length*(i.barHeight+i.barGap);for(const T of L)M[T]=S.filter(d=>d.type===T).length}x.setAttribute("viewBox","0 0 "+ae+" "+_);const w=D.select(`[id="${t}"]`),v=hi().domain([Bn(S,function(T){return T.startTime}),Nn(S,function(T){return T.endTime})]).rangeRound([0,ae-i.leftPadding-i.rightPadding]);function U(T,d){const g=T.startTime,m=d.startTime;let c=0;return g>m?c=1:g<m&&(c=-1),c}function E(T,d,g){const m=i.barHeight,c=m+i.barGap,l=i.topPadding,o=i.leftPadding,p=Pn().domain([0,L.length]).range(["#00B9FA","#F95002"]).interpolate(Kn);W(c,l,o,d,g,T,r.db.getExcludes(),r.db.getIncludes()),O(o,l,d,g),P(T,c,l,o,m,p,d),N(c,l),b(o,l,d,g)}function P(T,d,g,m,c,l,o){const p=[...new Set(T.map(a=>a.order))].map(a=>T.find($=>$.order===a));w.append("g").selectAll("rect").data(p).enter().append("rect").attr("x",0).attr("y",function(a,$){return a.order*d+g-2}).attr("width",function(){return o-i.rightPadding/2}).attr("height",d).attr("class",function(a){for(const[$,h]of L.entries())if(a.type===h)return"section section"+$%i.numberSectionStyles;return"section section0"});const f=w.append("g").selectAll("rect").data(T).enter(),C=r.db.getLinks();if(f.append("rect").attr("id",function(a){return a.id}).attr("rx",3).attr("ry",3).attr("x",function(a){return a.milestone?v(a.startTime)+m+.5*(v(a.endTime)-v(a.startTime))-.5*c:v(a.startTime)+m}).attr("y",function(a,$){return a.order*d+g}).attr("width",function(a){return a.milestone?c:v(a.renderEndTime||a.endTime)-v(a.startTime)}).attr("height",c).attr("transform-origin",function(a,$){return $=a.order,(v(a.startTime)+m+.5*(v(a.endTime)-v(a.startTime))).toString()+"px "+($*d+g+.5*c).toString()+"px"}).attr("class",function(a){let $="";a.classes.length>0&&($=a.classes.join(" "));let h=0;for(const[H,z]of L.entries())a.type===z&&(h=H%i.numberSectionStyles);let F="";return a.active?a.crit?F+=" activeCrit":F=" active":a.done?F=a.crit?" doneCrit":" done":a.crit&&(F+=" crit"),F.length===0&&(F=" task"),a.milestone&&(F=" milestone "+F),F+=h,F+=" "+$,"task"+F}),f.append("text").attr("id",function(a){return a.id+"-text"}).text(function(a){return a.task}).attr("font-size",i.fontSize).attr("x",function(a){let $=v(a.startTime),h=v(a.renderEndTime||a.endTime);a.milestone&&($+=.5*(v(a.endTime)-v(a.startTime))-.5*c),a.milestone&&(h=$+c);const F=this.getBBox().width;return F>h-$?h+F+1.5*i.leftPadding>o?$+m-5:h+m+5:(h-$)/2+$+m}).attr("y",function(a,$){return a.order*d+i.barHeight/2+(i.fontSize/2-2)+g}).attr("text-height",c).attr("class",function(a){const $=v(a.startTime);let h=v(a.endTime);a.milestone&&(h=$+c);const F=this.getBBox().width;let H="";a.classes.length>0&&(H=a.classes.join(" "));let z=0;for(const[Q,ce]of L.entries())a.type===ce&&(z=Q%i.numberSectionStyles);let A="";return a.active&&(A=a.crit?"activeCritText"+z:"activeText"+z),a.done?A=a.crit?A+" doneCritText"+z:A+" doneText"+z:a.crit&&(A=A+" critText"+z),a.milestone&&(A+=" milestoneText"),F>h-$?h+F+1.5*i.leftPadding>o?H+" taskTextOutsideLeft taskTextOutside"+z+" "+A:H+" taskTextOutsideRight taskTextOutside"+z+" "+A+" width-"+F:H+" taskText taskText"+z+" "+A+" width-"+F}),ve().securityLevel==="sandbox"){let a;a=Qe("#i"+t);const $=a.nodes()[0].contentDocument;f.filter(function(h){return C.has(h.id)}).each(function(h){var F=$.querySelector("#"+h.id),H=$.querySelector("#"+h.id+"-text");const z=F.parentNode;var A=$.createElement("a");A.setAttribute("xlink:href",C.get(h.id)),A.setAttribute("target","_top"),z.appendChild(A),A.appendChild(F),A.appendChild(H)})}}function W(T,d,g,m,c,l,o,p){if(o.length===0&&p.length===0)return;let f,C;for(const{startTime:H,endTime:z}of l)(f===void 0||H<f)&&(f=H),(C===void 0||z>C)&&(C=z);if(!f||!C)return;if(q(C).diff(q(f),"year")>5)return void je.warn("The difference between the min and max time is more than 5 years. This will cause performance issues. Skipping drawing exclude days.");const a=r.db.getDateFormat(),$=[];let h=null,F=q(f);for(;F.valueOf()<=C;)r.db.isInvalidDate(F,a,o,p)?h?h.end=F:h={start:F,end:F}:h&&($.push(h),h=null),F=F.add(1,"d");w.append("g").selectAll("rect").data($).enter().append("rect").attr("id",function(H){return"exclude-"+H.start.format("YYYY-MM-DD")}).attr("x",function(H){return v(H.start)+g}).attr("y",i.gridLineStartPadding).attr("width",function(H){const z=H.end.add(1,"day");return v(z)-v(H.start)}).attr("height",c-d-i.gridLineStartPadding).attr("transform-origin",function(H,z){return(v(H.start)+g+.5*(v(H.end)-v(H.start))).toString()+"px "+(z*T+.5*c).toString()+"px"}).attr("class","exclude-range")}function O(T,d,g,m){let c=(l=v,Et(ct,l)).tickSize(-m+d+i.gridLineStartPadding).tickFormat(qe(r.db.getAxisFormat()||i.axisFormat||"%Y-%m-%d"));var l;const o=/^([1-9]\d*)(millisecond|second|minute|hour|day|week|month)$/.exec(r.db.getTickInterval()||i.tickInterval);if(o!==null){const p=o[1],f=o[2],C=r.db.getWeekday()||i.weekday;switch(f){case"millisecond":c.ticks(be.every(p));break;case"second":c.ticks(se.every(p));break;case"minute":c.ticks(Ue.every(p));break;case"hour":c.ticks(Ee.every(p));break;case"day":c.ticks(de.every(p));break;case"week":c.ticks(Gt[C].every(p));break;case"month":c.ticks(Ie.every(p))}}if(w.append("g").attr("class","grid").attr("transform","translate("+T+", "+(m-50)+")").call(c).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10).attr("dy","1em"),r.db.topAxisEnabled()||i.topAxis){let p=function(f){return Et(Ve,f)}(v).tickSize(-m+d+i.gridLineStartPadding).tickFormat(qe(r.db.getAxisFormat()||i.axisFormat||"%Y-%m-%d"));if(o!==null){const f=o[1],C=o[2],a=r.db.getWeekday()||i.weekday;switch(C){case"millisecond":p.ticks(be.every(f));break;case"second":p.ticks(se.every(f));break;case"minute":p.ticks(Ue.every(f));break;case"hour":p.ticks(Ee.every(f));break;case"day":p.ticks(de.every(f));break;case"week":p.ticks(Gt[a].every(f));break;case"month":p.ticks(Ie.every(f))}}w.append("g").attr("class","grid").attr("transform","translate("+T+", "+d+")").call(p).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10)}}function N(T,d){let g=0;const m=Object.keys(M).map(c=>[c,M[c]]);w.append("g").selectAll("text").data(m).enter().append(function(c){const l=c[0].split(Ln.lineBreakRegex),o=-(l.length-1)/2,p=k.createElementNS("http://www.w3.org/2000/svg","text");p.setAttribute("dy",o+"em");for(const[f,C]of l.entries()){const a=k.createElementNS("http://www.w3.org/2000/svg","tspan");a.setAttribute("alignment-baseline","central"),a.setAttribute("x","10"),f>0&&a.setAttribute("dy","1em"),a.textContent=C,p.appendChild(a)}return p}).attr("x",10).attr("y",function(c,l){if(!(l>0))return c[1]*T/2+d;for(let o=0;o<l;o++)return g+=m[l-1][1],c[1]*T/2+g*T+d}).attr("font-size",i.sectionFontSize).attr("class",function(c){for(const[l,o]of L.entries())if(c[0]===o)return"sectionTitle sectionTitle"+l%i.numberSectionStyles;return"sectionTitle"})}function b(T,d,g,m){const c=r.db.getTodayMarker();if(c==="off")return;const l=w.append("g").attr("class","today"),o=new Date,p=l.append("line");p.attr("x1",v(o)+T).attr("x2",v(o)+T).attr("y1",i.titleTopMargin).attr("y2",m-i.titleTopMargin).attr("class","today"),c!==""&&p.attr("style",c.replace(/,/g,";"))}function Y(T){const d={},g=[];for(let m=0,c=T.length;m<c;++m)Object.prototype.hasOwnProperty.call(d,T[m])||(d[T[m]]=!0,g.push(T[m]));return g}y(U,"taskCompare"),S.sort(U),E(S,ae,_),En(w,_,ae,i.useMaxWidth),w.append("text").text(r.db.getDiagramTitle()).attr("x",ae/2).attr("y",i.titleTopMargin).attr("class","titleText"),y(E,"makeGantt"),y(P,"drawRects"),y(W,"drawExcludeDays"),y(O,"makeGrid"),y(N,"vertLabels"),y(b,"drawToday"),y(Y,"checkUnique")},"draw")},styles:y(e=>`
  .mermaid-main-font {
    font-family: var(--mermaid-font-family, "trebuchet ms", verdana, arial, sans-serif);
  }

  .exclude-range {
    fill: ${e.excludeBkgColor};
  }

  .section {
    stroke: none;
    opacity: 0.2;
  }

  .section0 {
    fill: ${e.sectionBkgColor};
  }

  .section2 {
    fill: ${e.sectionBkgColor2};
  }

  .section1,
  .section3 {
    fill: ${e.altSectionBkgColor};
    opacity: 0.2;
  }

  .sectionTitle0 {
    fill: ${e.titleColor};
  }

  .sectionTitle1 {
    fill: ${e.titleColor};
  }

  .sectionTitle2 {
    fill: ${e.titleColor};
  }

  .sectionTitle3 {
    fill: ${e.titleColor};
  }

  .sectionTitle {
    text-anchor: start;
    font-family: var(--mermaid-font-family, "trebuchet ms", verdana, arial, sans-serif);
  }


  /* Grid and axis */

  .grid .tick {
    stroke: ${e.gridColor};
    opacity: 0.8;
    shape-rendering: crispEdges;
  }

  .grid .tick text {
    font-family: ${e.fontFamily};
    fill: ${e.textColor};
  }

  .grid path {
    stroke-width: 0;
  }


  /* Today line */

  .today {
    fill: none;
    stroke: ${e.todayLineColor};
    stroke-width: 2px;
  }


  /* Task styling */

  /* Default task */

  .task {
    stroke-width: 2;
  }

  .taskText {
    text-anchor: middle;
    font-family: var(--mermaid-font-family, "trebuchet ms", verdana, arial, sans-serif);
  }

  .taskTextOutsideRight {
    fill: ${e.taskTextDarkColor};
    text-anchor: start;
    font-family: var(--mermaid-font-family, "trebuchet ms", verdana, arial, sans-serif);
  }

  .taskTextOutsideLeft {
    fill: ${e.taskTextDarkColor};
    text-anchor: end;
  }


  /* Special case clickable */

  .task.clickable {
    cursor: pointer;
  }

  .taskText.clickable {
    cursor: pointer;
    fill: ${e.taskTextClickableColor} !important;
    font-weight: bold;
  }

  .taskTextOutsideLeft.clickable {
    cursor: pointer;
    fill: ${e.taskTextClickableColor} !important;
    font-weight: bold;
  }

  .taskTextOutsideRight.clickable {
    cursor: pointer;
    fill: ${e.taskTextClickableColor} !important;
    font-weight: bold;
  }


  /* Specific task settings for the sections*/

  .taskText0,
  .taskText1,
  .taskText2,
  .taskText3 {
    fill: ${e.taskTextColor};
  }

  .task0,
  .task1,
  .task2,
  .task3 {
    fill: ${e.taskBkgColor};
    stroke: ${e.taskBorderColor};
  }

  .taskTextOutside0,
  .taskTextOutside2
  {
    fill: ${e.taskTextOutsideColor};
  }

  .taskTextOutside1,
  .taskTextOutside3 {
    fill: ${e.taskTextOutsideColor};
  }


  /* Active task */

  .active0,
  .active1,
  .active2,
  .active3 {
    fill: ${e.activeTaskBkgColor};
    stroke: ${e.activeTaskBorderColor};
  }

  .activeText0,
  .activeText1,
  .activeText2,
  .activeText3 {
    fill: ${e.taskTextDarkColor} !important;
  }


  /* Completed task */

  .done0,
  .done1,
  .done2,
  .done3 {
    stroke: ${e.doneTaskBorderColor};
    fill: ${e.doneTaskBkgColor};
    stroke-width: 2;
  }

  .doneText0,
  .doneText1,
  .doneText2,
  .doneText3 {
    fill: ${e.taskTextDarkColor} !important;
  }


  /* Tasks on the critical line */

  .crit0,
  .crit1,
  .crit2,
  .crit3 {
    stroke: ${e.critBorderColor};
    fill: ${e.critBkgColor};
    stroke-width: 2;
  }

  .activeCrit0,
  .activeCrit1,
  .activeCrit2,
  .activeCrit3 {
    stroke: ${e.critBorderColor};
    fill: ${e.activeTaskBkgColor};
    stroke-width: 2;
  }

  .doneCrit0,
  .doneCrit1,
  .doneCrit2,
  .doneCrit3 {
    stroke: ${e.critBorderColor};
    fill: ${e.doneTaskBkgColor};
    stroke-width: 2;
    cursor: pointer;
    shape-rendering: crispEdges;
  }

  .milestone {
    transform: rotate(45deg) scale(0.8,0.8);
  }

  .milestoneText {
    font-style: italic;
  }
  .doneCritText0,
  .doneCritText1,
  .doneCritText2,
  .doneCritText3 {
    fill: ${e.taskTextDarkColor} !important;
  }

  .activeCritText0,
  .activeCritText1,
  .activeCritText2,
  .activeCritText3 {
    fill: ${e.taskTextDarkColor} !important;
  }

  .titleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${e.titleColor||e.textColor};
    font-family: var(--mermaid-font-family, "trebuchet ms", verdana, arial, sans-serif);
  }
`,"getStyles")};export{Ga as diagram};
