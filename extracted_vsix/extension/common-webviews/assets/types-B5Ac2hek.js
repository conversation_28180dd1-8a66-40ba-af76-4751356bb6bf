import{F as c,R as u,a as l}from"./types-Cgd-nZOV.js";import{C as x,a as E}from"./chat-types-NgqNgjwU.js";function T(a){return function(t){try{if(isNaN(t.getTime()))return"Unknown time";const o=new Date().getTime()-t.getTime(),e=Math.floor(o/1e3),r=Math.floor(e/60),n=Math.floor(r/60),s=Math.floor(n/24);return e<60?`${e}s ago`:r<60?`${r}m ago`:n<24?`${n}h ago`:s<30?`${s}d ago`:t.toLocaleDateString()}catch(o){return console.error("Error formatting date:",o),"Unknown time"}}(new Date(a))}function I(a,t){const o=setInterval(()=>{const e=a.getTime()-Date.now();if(e<=0)return void clearInterval(o);const r=Math.floor(e/1e3),n=Math.floor(r/60),s=Math.floor(n/60),i=Math.floor(s/24);t(r<60?`${r}s`:n<60?`${n}m ${r%60}s`:s<24?`${s}h`:i<30?`${i}d`:"1mo")},1e3);return()=>clearInterval(o)}function S(a){if(a===void 0)return"neutral";switch(a){case l.agentPending:case l.agentStarting:case l.agentRunning:return"info";case l.agentIdle:return"success";case l.agentFailed:return"error";default:return"neutral"}}function $(a){if(a===void 0)return"neutral";switch(a){case u.workspaceRunning:return"info";case u.workspacePausing:case u.workspacePaused:case u.workspaceResuming:default:return"neutral"}}function k(a,t,o){if(t===u.workspaceResuming)return"Resuming";switch(a){case l.agentStarting:return"Starting";case l.agentRunning:return"Running";case l.agentIdle:return o?"Unread":"Idle";case l.agentPending:return"Pending";case l.agentFailed:return"Failed";default:return"Unknown"}}const d=(a,t=!1)=>{if(t)return M(a);let o={};for(const e of a){const r=e.change_type===c.deleted?e.old_path:e.new_path,n=o[r];n?e.change_type===c.deleted?o[r]=e:o[r]={...n,new_contents:e.new_contents,new_path:e.new_path}:o[r]=e}return Object.values(o).filter(e=>!e.old_path||!e.new_path||e.old_path!==e.new_path||e.old_contents!==e.new_contents)},M=a=>{const t={};for(const n of a){const s=n.old_path||n.new_path;if(t[s])t[s].finalExists=n.new_path!=="",t[s].finalContent=n.new_path!==""?n.new_contents:"",t[s].finalPath=n.new_path||n.old_path,t[s].latestChange=n,n.change_type===c.deleted&&n.old_contents!==""&&(t[s].originalContent=n.old_contents);else{const i=n.old_path!=="";t[s]={originalExists:i,originalContent:i?n.old_contents:"",finalExists:n.new_path!=="",finalContent:n.new_path!==""?n.new_contents:"",finalPath:n.new_path||n.old_path,latestChange:n}}}const o=[];for(const[n,s]of Object.entries(t))if(s.originalExists!==s.finalExists||s.originalExists&&s.finalExists&&s.originalContent!==s.finalContent){const i={id:s.latestChange.id,old_path:s.originalExists?n:"",new_path:s.finalExists?s.finalPath:"",old_contents:s.originalContent,new_contents:s.finalContent,change_type:(e=s.originalExists,r=s.finalExists,!e&&r?c.added:e&&!r?c.deleted:c.modified)};o.push(i)}var e,r;return o},v=a=>{const t=a.flatMap(o=>o.changed_files);return d(t,!0)},P=(a,t)=>{var e;const o=h(a,t);return((e=a[o])==null?void 0:e.exchange.request_message)??""},h=(a,t)=>{var o;return t<0||t>=a.length?-1:(o=a[t])!=null&&o.exchange.request_message?t:a.slice(0,t).findLastIndex(e=>e.exchange.request_message)},m=(a,t)=>{const o=a.slice(t+1).findIndex(e=>e.exchange.request_message);return o===-1?a.length:t+o+1},y=(a,t)=>{if(t<0||t>=a.length)return[];if(h(a,t)===-1){const r=a.flatMap(n=>n.changed_files);return d(r,!1)}const o=((r,n)=>{const s=h(r,n);let i=m(r,n);const g=s===-1?0:s+1;return r.slice(g,i)})(a,t),e=o.flatMap(r=>r.changed_files);return d(e,!1)},L=(a,t)=>{var i,g;const o=m(a,t),e=a.slice(t,o),r=(i=a[t].exchange.response_nodes)==null?void 0:i.find(f=>f.type===x.TOOL_USE);if(!r)return[];const n=(g=r.tool_use)==null?void 0:g.tool_use_id;if(!n)return[];if(!e.find(f=>{var _;return(_=f.exchange.request_nodes)==null?void 0:_.some(p=>{var w;return p.type===E.TOOL_RESULT&&((w=p.tool_result_node)==null?void 0:w.tool_use_id)===n})}))return[];const s=e.flatMap(f=>f.changed_files);return d(s,!1)},O="STREAM_CANCELLED",U="STREAM_TIMEOUT";export{O as S,U as a,v as b,S as c,$ as d,T as e,L as f,k as g,P as h,y as i,I as s};
