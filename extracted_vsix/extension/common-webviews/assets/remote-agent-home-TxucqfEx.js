var Gt=Object.defineProperty;var jt=(c,t,n)=>t in c?Gt(c,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):c[t]=n;var L=(c,t,n)=>jt(c,typeof t!="symbol"?t+"":t,n);import{S as O,i as U,s as G,a as lt,b as Jt,K as Vt,L as Kt,M as Qt,N as Xt,h as $,d as ht,O as Yt,g as Zt,n as Z,j as yt,T as z,V as I,y as w,D as B,c as x,e as p,z as _,f as M,u as m,t as u,B as v,a0 as te,G as N,H as j,E as T,q as H,r as q,a3 as nt,af as dt,ag as Ot,a5 as ee,ao as wt,a1 as ne}from"./SpinnerAugment-JC8TPhVf.js";import"./design-system-init-Bf1-mlh4.js";import{s as se}from"./index-yERhhNs7.js";import"./design-system-init-CajyFoaO.js";import{W as R,e as C,u as J,o as V,h as ae}from"./BaseButton-D8yhCvaJ.js";import{T as st,M as re}from"./TextTooltipAugment-BlDY2tAQ.js";import{S as gt,a as oe,T as ie,b as ce,v as le,c as de}from"./StatusIndicator-BiyeFzqm.js";import{a as F,R as ct}from"./types-Cgd-nZOV.js";import{T as at}from"./Content-xvE836E_.js";import{C as ge}from"./CardAugment-BAO5rOsN.js";import{I as mt}from"./IconButtonAugment-BQL_8yIN.js";import{T as Ut}from"./terminal-BBUsFUTj.js";import{S as me,a as ue}from"./types-B5Ac2hek.js";import{A as $e}from"./augment-logo-E1jEbeRV.js";import"./index-CW7fyhvB.js";import"./globals-D0QH3NT1.js";import"./chat-types-NgqNgjwU.js";class pe{constructor(t,n=void 0,e,a){L(this,"subscribers",new Set);this._msgBroker=t,this._state=n,this.validateState=e,this._storeId=a,n&&this.setStateInternal(n)}subscribe(t){return this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}}notifySubscribers(){this.subscribers.forEach(t=>t(this))}get state(){return this._state}get storeId(){return this._storeId}shouldAcceptMessage(t,n){return t.id===this.storeId&&this.validateState(n)}update(t){const n=t(this._state);n!==void 0&&this.setStateInternal(n)}setState(t){this.setStateInternal(t)}async setStateInternal(t){JSON.stringify(this._state)!==JSON.stringify(t)&&(this._state=t,this._msgBroker.postMessage({type:R.updateSharedWebviewState,data:t,id:this.storeId}))}async fetchStateFromExtension(){const t=await this._msgBroker.send({type:R.getSharedWebviewState,id:this.storeId,data:{}});t.type===R.getSharedWebviewStateResponse&&this.shouldAcceptMessage(t,t.data)&&(this._state=t.data,this.notifySubscribers())}handleMessageFromExtension(t){switch(t.data.type){case R.updateSharedWebviewState:case R.getSharedWebviewStateResponse:return!!this.shouldAcceptMessage(t.data,t.data.data)&&(this._state=t.data.data,this.notifySubscribers(),!0);default:return!1}}}function fe(c){let t,n,e=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},c[0]],a={};for(let r=0;r<e.length;r+=1)a=lt(a,e[r]);return{c(){t=Jt("svg"),n=new Vt(!0),this.h()},l(r){t=Kt(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Qt(t);n=Xt(o,!0),o.forEach($),this.h()},h(){n.a=null,ht(t,a)},m(r,o){Yt(r,t,o),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2s-6.3 25.5 4.1 33.7l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L481.4 352c9.4-.4 18.1-4.9 23.9-12.3 6.1-7.8 8.2-17.9 5.8-27.5l-6.2-25c-10.3-41.3-35.4-75.7-68.7-98.3L428.9 96l-3.7-48H456c4.4 0 8.6-1.2 12.2-3.3 7-4.2 11.8-11.9 11.8-20.7 0-13.3-10.7-24-24-24H184c-13.3 0-24 10.7-24 24 0 8.8 4.8 16.5 11.8 20.7 3.6 2.1 7.7 3.3 12.2 3.3h30.8l-3.7 48-3.2 41.6zm214.5 168.1 9.3-121.5c.1-1.2.1-2.5.1-3.7h114.5c0 1.2 0 2.5.1 3.7l10.8 140.9c1.1 14.6 8.8 27.8 20.9 36 23.9 16.2 41.7 40.8 49.1 70.2l1.3 5.1H420l-76-59.6V216c0-13.3-10.7-24-24-24-10.4 0-19.2 6.6-22.6 15.8l-44.2-34.6zM344 367l-80-63h-83.5l1.3-5.1c4-16.1 11.2-30.7 20.7-43.3l-37.7-29.7c-13.7 17.8-23.9 38.6-29.6 61.4l-6.2 25c-2.4 9.6-.2 19.7 5.8 27.5s15.4 12.3 25.2 12.3h136v136c0 13.3 10.7 24 24 24s24-10.7 24-24v-121z"/>',t)},p(r,[o]){ht(t,a=Zt(e,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},1&o&&r[0]]))},i:Z,o:Z,d(r){r&&$(t)}}}function he(c,t,n){return c.$$set=e=>{n(0,t=lt(lt({},t),yt(e)))},[t=yt(t)]}class ye extends O{constructor(t){super(),U(this,t,he,fe,G,{})}}class we extends Error{constructor(t){super(t),this.name="StreamRetryExhaustedError"}}class _e{constructor(t,n,e,a,r=5,o=4e3){L(this,"_isCancelled",!1);L(this,"streamId");this.agentId=t,this.lastProcessedSequenceId=n,this.startStreamFn=e,this.cancelStreamFn=a,this.maxRetries=r,this.baseDelay=o,this.streamId=crypto.randomUUID()}get isCancelled(){return this._isCancelled}async cancel(){this._isCancelled=!0,await this.cancelStreamFn(this.streamId)}async*getStream(){let t=0;for(;!this._isCancelled;){const n=this.startStreamFn(this.agentId,this.streamId,this.lastProcessedSequenceId);try{for await(const e of n){if(this._isCancelled)return;t=0,yield e}return}catch(e){const a=e instanceof Error?e.message:String(e);if(a===me&&(this._isCancelled=!0),this._isCancelled)return;if(t++,t>this.maxRetries)throw new we(`Failed after ${this.maxRetries} attempts: ${a}`);let r=this.baseDelay*2**(t-1);a===ue?r=0:yield{errorMessage:"There was an error connecting to the remote agent.",retryAt:new Date(Date.now()+r)},console.warn(`Retrying remote agent history stream in ${r/1e3} seconds... (Attempt ${t} of ${this.maxRetries})`),await new Promise(o=>setTimeout(o,r));continue}}}}class rt{constructor(t){L(this,"_msgBroker");L(this,"_activeRetryStreams",new Map);this._msgBroker=t}hasActiveHistoryStream(t){return this._activeRetryStreams.has(t)}getActiveHistoryStream(t){return this._activeRetryStreams.get(t)}get activeHistoryStreams(){return this._activeRetryStreams}async sshToRemoteAgent(t){const n=await this._msgBroker.send({type:R.remoteAgentSshRequest,data:{agentId:t}},1e4);return!!n.data.success||(console.error("Failed to connect to remote agent:",n.data.error),!1)}async deleteRemoteAgent(t,n=!1){return(await this._msgBroker.send({type:R.deleteRemoteAgentRequest,data:{agentId:t,doSkipConfirmation:n}},1e4)).data.success}showRemoteAgentHomePanel(){this._msgBroker.postMessage({type:R.showRemoteAgentHomePanel})}closeRemoteAgentHomePanel(){this._msgBroker.postMessage({type:R.closeRemoteAgentHomePanel})}async getRemoteAgentNotificationEnabled(t){return(await this._msgBroker.send({type:R.getRemoteAgentNotificationEnabledRequest,data:{agentIds:t}})).data}async setRemoteAgentNotificationEnabled(t,n){await this._msgBroker.send({type:R.setRemoteAgentNotificationEnabled,data:{agentId:t,enabled:n}})}async deleteRemoteAgentNotificationEnabled(t){await this._msgBroker.send({type:R.deleteRemoteAgentNotificationEnabled,data:{agentId:t}})}async notifyRemoteAgentReady(t){await this._msgBroker.send({type:R.remoteAgentNotifyReady,data:{agentId:t}})}showRemoteAgentDiffPanel(t){this._msgBroker.postMessage({type:R.showRemoteAgentDiffPanel,data:t})}closeRemoteAgentDiffPanel(){this._msgBroker.postMessage({type:R.closeRemoteAgentDiffPanel})}async getRemoteAgentChatHistory(t,n,e=1e4){return await this._msgBroker.send({type:R.getRemoteAgentChatHistoryRequest,data:{agentId:t,lastProcessedSequenceId:n}},e)}async sendRemoteAgentChatRequest(t,n,e=9e4){return this._msgBroker.send({type:R.remoteAgentChatRequest,data:{agentId:t,requestDetails:n,timeoutMs:e}},e)}async interruptRemoteAgent(t,n=1e4){return await this._msgBroker.send({type:R.remoteAgentInterruptRequest,data:{agentId:t}},n)}async createRemoteAgent(t,n,e,a,r,o,d=1e4){return await this._msgBroker.send({type:R.createRemoteAgentRequest,data:{prompt:t,workspaceSetup:n,setupScript:e,isSetupScriptAgent:a,modelId:r,remoteAgentCreationMetrics:o}},d)}async getRemoteAgentOverviews(t=1e4){return await this._msgBroker.send({type:R.getRemoteAgentOverviewsRequest},t)}async listSetupScripts(t=5e3){return await this._msgBroker.send({type:R.listSetupScriptsRequest},t)}async saveSetupScript(t,n,e,a=5e3){return await this._msgBroker.send({type:R.saveSetupScriptRequest,data:{name:t,content:n,location:e}},a)}async deleteSetupScript(t,n,e=5e3){return await this._msgBroker.send({type:R.deleteSetupScriptRequest,data:{name:t,location:n}},e)}async renameSetupScript(t,n,e,a=5e3){return await this._msgBroker.send({type:R.renameSetupScriptRequest,data:{oldName:t,newName:n,location:e}},a)}async getRemoteAgentWorkspaceLogs(t,n,e,a=1e4){return await this._msgBroker.send({type:R.remoteAgentWorkspaceLogsRequest,data:{agentId:t,lastProcessedStep:n,lastProcessedSequenceId:e}},a)}async saveLastRemoteAgentSetup(t,n,e){return await this._msgBroker.send({type:R.saveLastRemoteAgentSetupRequest,data:{lastRemoteAgentGitRepoUrl:t,lastRemoteAgentGitBranch:n,lastRemoteAgentSetupScript:e}})}async getLastRemoteAgentSetup(){return await this._msgBroker.send({type:R.getLastRemoteAgentSetupRequest})}async*startRemoteAgentHistoryStream(t,n,e,a=6e4,r=3e5){const o={type:R.remoteAgentHistoryStreamRequest,data:{streamId:n,agentId:t,lastProcessedSequenceId:e}},d=this._msgBroker.stream(o,a,r);for await(const l of d)yield l.data}async*startRemoteAgentHistoryStreamWithRetry(t,n,e=5,a=4e3){var o;const r=new _e(t,n,(d,l,s)=>this.startRemoteAgentHistoryStream(d,l,s),d=>this._closeRemoteAgentHistoryStream(d),e,a);(o=this._activeRetryStreams.get(t))==null||o.cancel(),this._activeRetryStreams.set(t,r);try{yield*r.getStream()}finally{r.isCancelled||this._activeRetryStreams.delete(t)}}cancelRemoteAgentHistoryStream(t){const n=this._activeRetryStreams.get(t);n&&(n.cancel(),this._activeRetryStreams.delete(t))}async _closeRemoteAgentHistoryStream(t){await this._msgBroker.send({type:R.cancelRemoteAgentHistoryStreamRequest,data:{streamId:t}})}cancelAllRemoteAgentHistoryStreams(){this._activeRetryStreams.forEach(t=>{t.cancel()}),this._activeRetryStreams.clear()}dispose(){this.cancelAllRemoteAgentHistoryStreams()}async getPinnedAgentsFromStore(){try{return(await this._msgBroker.send({type:R.getRemoteAgentPinnedStatusRequest,data:{}})).data}catch(t){return console.error("Failed to get pinned agents from store:",t),{}}}async savePinnedAgentToStore(t,n){try{await this._msgBroker.send({type:R.setRemoteAgentPinnedStatus,data:{agentId:t,isPinned:n}})}catch(e){console.error("Failed to save pinned agent to store:",e)}}async deletePinnedAgentFromStore(t){try{await this._msgBroker.send({type:R.deleteRemoteAgentPinnedStatus,data:{agentId:t}})}catch(n){console.error("Failed to delete pinned agent from store:",n)}}async openDiffInBuffer(t,n,e){return await this._msgBroker.send({type:R.openDiffInBuffer,data:{oldContents:t,newContents:n,filePath:e}})}async pauseRemoteAgentWorkspace(t){return await this._msgBroker.send({type:R.remoteAgentPauseRequest,data:{agentId:t}},3e4)}async resumeRemoteAgentWorkspace(t){return await this._msgBroker.send({type:R.remoteAgentResumeRequest,data:{agentId:t}},9e4)}async reportRemoteAgentEvent(t){await this._msgBroker.send({type:R.reportRemoteAgentEvent,data:t})}}L(rt,"key","remoteAgentsClient");function _t(c){return function(t){try{if(isNaN(t.getTime()))return"Unknown time";const n=new Date().getTime()-t.getTime(),e=Math.floor(n/1e3),a=Math.floor(e/60),r=Math.floor(a/60),o=Math.floor(r/24);return e<60?`${e}s ago`:a<60?`${a}m ago`:r<24?`${r}h ago`:o<30?`${o}d ago`:t.toLocaleDateString()}catch(n){return console.error("Error formatting date:",n),"Unknown time"}}(new Date(c))}function ve(c){let t,n=c[0]?"Running in the cloud":"Running locally";return{c(){t=N(n)},m(e,a){p(e,t,a)},p(e,a){1&a&&n!==(n=e[0]?"Running in the cloud":"Running locally")&&j(t,n)},d(e){e&&$(t)}}}function Se(c){let t;return{c(){t=N("Unknown time")},m(n,e){p(n,t,e)},p:Z,d(n){n&&$(t)}}}function Re(c){let t;return{c(){t=N(c[3])},m(n,e){p(n,t,e)},p(n,e){8&e&&j(t,n[3])},d(n){n&&$(t)}}}function Ae(c){let t,n,e,a=c[1]===F.agentRunning?"Last updated":"Started";function r(l,s){return l[2]?Re:Se}let o=r(c),d=o(c);return{c(){t=N(a),n=B(),d.c(),e=T()},m(l,s){p(l,t,s),p(l,n,s),d.m(l,s),p(l,e,s)},p(l,s){2&s&&a!==(a=l[1]===F.agentRunning?"Last updated":"Started")&&j(t,a),o===(o=r(l))&&d?d.p(l,s):(d.d(1),d=o(l),d&&(d.c(),d.m(e.parentNode,e)))},d(l){l&&($(t),$(n),$(e)),d.d(l)}}}function ke(c){let t,n,e,a,r,o;return n=new z({props:{size:1,color:"secondary",class:"location-text",$$slots:{default:[ve]},$$scope:{ctx:c}}}),r=new z({props:{size:1,color:"secondary",class:"time-text",$$slots:{default:[Ae]},$$scope:{ctx:c}}}),{c(){t=I("div"),w(n.$$.fragment),e=B(),a=I("div"),w(r.$$.fragment),x(a,"class","time-container"),x(t,"class","agent-card-footer svelte-1qwlkoj")},m(d,l){p(d,t,l),_(n,t,null),M(t,e),M(t,a),_(r,a,null),o=!0},p(d,[l]){const s={};33&l&&(s.$$scope={dirty:l,ctx:d}),n.$set(s);const i={};46&l&&(i.$$scope={dirty:l,ctx:d}),r.$set(i)},i(d){o||(m(n.$$.fragment,d),m(r.$$.fragment,d),o=!0)},o(d){u(n.$$.fragment,d),u(r.$$.fragment,d),o=!1},d(d){d&&$(t),v(n),v(r)}}}function xe(c,t,n){let{isRemote:e=!1}=t,{status:a}=t,{timestamp:r}=t,o=_t(r);const d=function(l,s){let i=1e3;const g=new Date(l),y=setInterval(()=>{const A=Math.floor((new Date().getTime()-g.getTime())/1e3/60);A>=1&&(i=6e4),A>=60&&(i=36e5),A>=1440&&(i=864e5),s(_t(l))},i);return()=>clearInterval(y)}(r,l=>{n(3,o=l)});return te(()=>{d()}),c.$$set=l=>{"isRemote"in l&&n(0,e=l.isRemote),"status"in l&&n(1,a=l.status),"timestamp"in l&&n(2,r=l.timestamp)},[e,a,r,o]}class Ie extends O{constructor(t){super(),U(this,t,xe,ke,G,{isRemote:0,status:1,timestamp:2})}}function be(c){let t;return{c(){t=N(c[0])},m(n,e){p(n,t,e)},p(n,e){1&e&&j(t,n[0])},d(n){n&&$(t)}}}function Be(c){let t,n,e;return n=new z({props:{size:1,color:"secondary",$$slots:{default:[be]},$$scope:{ctx:c}}}),{c(){t=I("div"),w(n.$$.fragment),x(t,"class","task-text-container svelte-1tatwxk")},m(a,r){p(a,t,r),_(n,t,null),e=!0},p(a,r){const o={};9&r&&(o.$$scope={dirty:r,ctx:a}),n.$set(o)},i(a){e||(m(n.$$.fragment,a),e=!0)},o(a){u(n.$$.fragment,a),e=!1},d(a){a&&$(t),v(n)}}}function vt(c){let t,n,e;return n=new z({props:{size:1,color:c[1]==="error"?"error":"neutral",$$slots:{default:[Pe]},$$scope:{ctx:c}}}),{c(){t=I("div"),w(n.$$.fragment),x(t,"class","task-status-indicator svelte-1tatwxk")},m(a,r){p(a,t,r),_(n,t,null),e=!0},p(a,r){const o={};2&r&&(o.color=a[1]==="error"?"error":"neutral"),10&r&&(o.$$scope={dirty:r,ctx:a}),n.$set(o)},i(a){e||(m(n.$$.fragment,a),e=!0)},o(a){u(n.$$.fragment,a),e=!1},d(a){a&&$(t),v(n)}}}function Pe(c){let t,n=c[1]==="error"?"!":c[1]==="warning"?"⚠":"";return{c(){t=N(n)},m(e,a){p(e,t,a)},p(e,a){2&a&&n!==(n=e[1]==="error"?"!":e[1]==="warning"?"⚠":"")&&j(t,n)},d(e){e&&$(t)}}}function He(c){let t,n,e,a,r,o,d;r=new st({props:{content:c[0],triggerOn:[at.Hover],maxWidth:"400px",$$slots:{default:[Be]},$$scope:{ctx:c}}});let l=(c[1]==="error"||c[1]==="warning")&&vt(c);return{c(){t=I("div"),n=I("div"),a=B(),w(r.$$.fragment),o=B(),l&&l.c(),x(n,"class",e="bullet-point "+c[2]+" svelte-1tatwxk"),x(t,"class","task-item svelte-1tatwxk")},m(s,i){p(s,t,i),M(t,n),M(t,a),_(r,t,null),M(t,o),l&&l.m(t,null),d=!0},p(s,[i]){(!d||4&i&&e!==(e="bullet-point "+s[2]+" svelte-1tatwxk"))&&x(n,"class",e);const g={};1&i&&(g.content=s[0]),9&i&&(g.$$scope={dirty:i,ctx:s}),r.$set(g),s[1]==="error"||s[1]==="warning"?l?(l.p(s,i),2&i&&m(l,1)):(l=vt(s),l.c(),m(l,1),l.m(t,null)):l&&(H(),u(l,1,1,()=>{l=null}),q())},i(s){d||(m(r.$$.fragment,s),m(l),d=!0)},o(s){u(r.$$.fragment,s),u(l),d=!1},d(s){s&&$(t),v(r),l&&l.d()}}}function qe(c,t,n){let e,{text:a}=t,{status:r="info"}=t;return c.$$set=o=>{"text"in o&&n(0,a=o.text),"status"in o&&n(1,r=o.status)},c.$$.update=()=>{2&c.$$.dirty&&n(2,e=function(o){switch(o){case"success":return"task-success";case"warning":return"task-warning";case"error":return"task-error";default:return"task-info"}}(r))},[a,r,e]}class Me extends O{constructor(t){super(),U(this,t,qe,He,G,{text:0,status:1})}}function St(c,t,n){const e=c.slice();return e[20]=t[n],e[22]=n,e}function Ce(c){let t,n;return t=new z({props:{size:2,weight:"medium",class:"session-text",$$slots:{default:[Ne]},$$scope:{ctx:c}}}),{c(){w(t.$$.fragment)},m(e,a){_(t,e,a),n=!0},p(e,a){const r={};8388609&a&&(r.$$scope={dirty:a,ctx:e}),t.$set(r)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){v(t,e)}}}function Ee(c){let t,n,e,a,r,o;return e=new Ut({}),r=new z({props:{size:2,weight:"medium",$$slots:{default:[Te]},$$scope:{ctx:c}}}),{c(){t=I("div"),n=I("div"),w(e.$$.fragment),a=B(),w(r.$$.fragment),x(n,"class","setup-script-badge svelte-td16df"),x(t,"class","setup-script-title-container svelte-td16df")},m(d,l){p(d,t,l),M(t,n),_(e,n,null),M(t,a),_(r,t,null),o=!0},p(d,l){const s={};8388608&l&&(s.$$scope={dirty:l,ctx:d}),r.$set(s)},i(d){o||(m(e.$$.fragment,d),m(r.$$.fragment,d),o=!0)},o(d){u(e.$$.fragment,d),u(r.$$.fragment,d),o=!1},d(d){d&&$(t),v(e),v(r)}}}function Ne(c){let t,n=c[0].session_summary+"";return{c(){t=N(n)},m(e,a){p(e,t,a)},p(e,a){1&a&&n!==(n=e[0].session_summary+"")&&j(t,n)},d(e){e&&$(t)}}}function Te(c){let t;return{c(){t=I("span"),t.textContent="Generate a setup script",x(t,"class","setup-script-title svelte-td16df")},m(n,e){p(n,t,e)},p:Z,d(n){n&&$(t)}}}function Rt(c){let t,n,e=[],a=new Map,r=C(c[6].slice(0,3));const o=d=>d[22];for(let d=0;d<r.length;d+=1){let l=St(c,r,d),s=o(l);a.set(s,e[d]=At(s,l))}return{c(){t=I("div");for(let d=0;d<e.length;d+=1)e[d].c();x(t,"class","tasks-list svelte-td16df")},m(d,l){p(d,t,l);for(let s=0;s<e.length;s+=1)e[s]&&e[s].m(t,null);n=!0},p(d,l){64&l&&(r=C(d[6].slice(0,3)),H(),e=J(e,l,o,1,d,r,a,t,V,At,null,St),q())},i(d){if(!n){for(let l=0;l<r.length;l+=1)m(e[l]);n=!0}},o(d){for(let l=0;l<e.length;l+=1)u(e[l]);n=!1},d(d){d&&$(t);for(let l=0;l<e.length;l+=1)e[l].d()}}}function At(c,t){let n,e,a;return e=new Me({props:{text:t[20],status:"success"}}),{key:c,first:null,c(){n=T(),w(e.$$.fragment),this.first=n},m(r,o){p(r,n,o),_(e,r,o),a=!0},p(r,o){t=r;const d={};64&o&&(d.text=t[20]),e.$set(d)},i(r){a||(m(e.$$.fragment,r),a=!0)},o(r){u(e.$$.fragment,r),a=!1},d(r){r&&$(n),v(e,r)}}}function Fe(c){let t,n;return t=new ce({}),{c(){w(t.$$.fragment)},m(e,a){_(t,e,a),n=!0},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){v(t,e)}}}function De(c){let t,n;return t=new ye({}),{c(){w(t.$$.fragment)},m(e,a){_(t,e,a),n=!0},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){v(t,e)}}}function ze(c){let t,n,e,a;const r=[De,Fe],o=[];function d(l,s){return l[5]?0:1}return t=d(c),n=o[t]=r[t](c),{c(){n.c(),e=T()},m(l,s){o[t].m(l,s),p(l,e,s),a=!0},p(l,s){let i=t;t=d(l),t!==i&&(H(),u(o[i],1,1,()=>{o[i]=null}),q(),n=o[t],n||(n=o[t]=r[t](l),n.c()),m(n,1),n.m(e.parentNode,e))},i(l){a||(m(n),a=!0)},o(l){u(n),a=!1},d(l){l&&$(e),o[t].d(l)}}}function We(c){let t,n;return t=new mt({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[ze]},$$scope:{ctx:c}}}),t.$on("click",c[13]),{c(){w(t.$$.fragment)},m(e,a){_(t,e,a),n=!0},p(e,a){const r={};8388640&a&&(r.$$scope={dirty:a,ctx:e}),t.$set(r)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){v(t,e)}}}function Le(c){let t,n;return t=new Ut({}),{c(){w(t.$$.fragment)},m(e,a){_(t,e,a),n=!0},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){v(t,e)}}}function Oe(c){let t,n;return t=new mt({props:{disabled:!c[3],variant:"ghost",color:"neutral",size:1,title:c[3]?"SSH to agent":"SSH to agent (agent must be running or idle)",$$slots:{default:[Le]},$$scope:{ctx:c}}}),t.$on("click",c[14]),{c(){w(t.$$.fragment)},m(e,a){_(t,e,a),n=!0},p(e,a){const r={};8&a&&(r.disabled=!e[3]),8&a&&(r.title=e[3]?"SSH to agent":"SSH to agent (agent must be running or idle)"),8388608&a&&(r.$$scope={dirty:a,ctx:e}),t.$set(r)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){v(t,e)}}}function Ue(c){let t,n;return t=new ie({}),{c(){w(t.$$.fragment)},m(e,a){_(t,e,a),n=!0},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){v(t,e)}}}function Ge(c){let t,n;return t=new mt({props:{variant:"ghost",color:"neutral",size:1,title:"Delete agent",$$slots:{default:[Ue]},$$scope:{ctx:c}}}),t.$on("click",c[15]),{c(){w(t.$$.fragment)},m(e,a){_(t,e,a),n=!0},p(e,a){const r={};8388608&a&&(r.$$scope={dirty:a,ctx:e}),t.$set(r)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){v(t,e)}}}function je(c){let t,n,e,a,r,o,d,l,s,i,g,y,A,f,S,X,k,W,E,Y;const ut=[Ee,Ce],D=[];function $t(h,b){return h[0].is_setup_script_agent?0:1}e=$t(c),a=D[e]=ut[e](c),l=new oe({props:{status:c[0].status,workspaceStatus:c[0].workspace_status,isExpanded:!0,hasUpdates:c[0].has_updates}});let P=c[6].length>0&&Rt(c);return A=new st({props:{content:c[5]?"Unpin agent":"Pin agent",triggerOn:[at.Hover],side:"top",$$slots:{default:[We]},$$scope:{ctx:c}}}),S=new st({props:{content:"SSH to agent",triggerOn:[at.Hover],side:"top",$$slots:{default:[Oe]},$$scope:{ctx:c}}}),k=new st({props:{content:"Delete agent",triggerOn:[at.Hover],side:"top",$$slots:{default:[Ge]},$$scope:{ctx:c}}}),E=new Ie({props:{isRemote:c[4],status:c[0].status,timestamp:c[0].updated_at||c[0].started_at}}),{c(){t=I("div"),n=I("div"),a.c(),o=B(),d=I("div"),w(l.$$.fragment),s=B(),i=I("div"),P&&P.c(),g=B(),y=I("div"),w(A.$$.fragment),f=B(),w(S.$$.fragment),X=B(),w(k.$$.fragment),W=B(),w(E.$$.fragment),x(n,"class","session-summary-container svelte-td16df"),x(n,"title",r=c[0].is_setup_script_agent?"Generate a setup script":c[0].session_summary),x(d,"class","card-info"),x(t,"class","card-header svelte-td16df"),x(i,"class","card-content svelte-td16df"),x(y,"class","card-actions svelte-td16df")},m(h,b){p(h,t,b),M(t,n),D[e].m(n,null),M(t,o),M(t,d),_(l,d,null),p(h,s,b),p(h,i,b),P&&P.m(i,null),p(h,g,b),p(h,y,b),_(A,y,null),M(y,f),_(S,y,null),M(y,X),_(k,y,null),p(h,W,b),_(E,h,b),Y=!0},p(h,b){let ot=e;e=$t(h),e===ot?D[e].p(h,b):(H(),u(D[ot],1,1,()=>{D[ot]=null}),q(),a=D[e],a?a.p(h,b):(a=D[e]=ut[e](h),a.c()),m(a,1),a.m(n,null)),(!Y||1&b&&r!==(r=h[0].is_setup_script_agent?"Generate a setup script":h[0].session_summary))&&x(n,"title",r);const tt={};1&b&&(tt.status=h[0].status),1&b&&(tt.workspaceStatus=h[0].workspace_status),1&b&&(tt.hasUpdates=h[0].has_updates),l.$set(tt),h[6].length>0?P?(P.p(h,b),64&b&&m(P,1)):(P=Rt(h),P.c(),m(P,1),P.m(i,null)):P&&(H(),u(P,1,1,()=>{P=null}),q());const it={};32&b&&(it.content=h[5]?"Unpin agent":"Pin agent"),8388641&b&&(it.$$scope={dirty:b,ctx:h}),A.$set(it);const pt={};8388616&b&&(pt.$$scope={dirty:b,ctx:h}),S.$set(pt);const ft={};8388609&b&&(ft.$$scope={dirty:b,ctx:h}),k.$set(ft);const et={};16&b&&(et.isRemote=h[4]),1&b&&(et.status=h[0].status),1&b&&(et.timestamp=h[0].updated_at||h[0].started_at),E.$set(et)},i(h){Y||(m(a),m(l.$$.fragment,h),m(P),m(A.$$.fragment,h),m(S.$$.fragment,h),m(k.$$.fragment,h),m(E.$$.fragment,h),Y=!0)},o(h){u(a),u(l.$$.fragment,h),u(P),u(A.$$.fragment,h),u(S.$$.fragment,h),u(k.$$.fragment,h),u(E.$$.fragment,h),Y=!1},d(h){h&&($(t),$(s),$(i),$(g),$(y),$(W)),D[e].d(),v(l),P&&P.d(),v(A),v(S),v(k),v(E,h)}}}function Je(c){let t,n,e;return n=new ge({props:{variant:"surface",size:2,interactive:!0,class:"agent-card",$$slots:{default:[je]},$$scope:{ctx:c}}}),n.$on("click",c[16]),n.$on("keydown",c[17]),{c(){t=I("div"),w(n.$$.fragment),x(t,"class","card-wrapper svelte-td16df"),nt(t,"selected-card",c[1]),nt(t,"setup-script-card",c[0].is_setup_script_agent)},m(a,r){p(a,t,r),_(n,t,null),e=!0},p(a,[r]){const o={};8388729&r&&(o.$$scope={dirty:r,ctx:a}),n.$set(o),(!e||2&r)&&nt(t,"selected-card",a[1]),(!e||1&r)&&nt(t,"setup-script-card",a[0].is_setup_script_agent)},i(a){e||(m(n.$$.fragment,a),e=!0)},o(a){u(n.$$.fragment,a),e=!1},d(a){a&&$(t),v(n)}}}function Ve(c,t,n){let e,a,r,o,d,l,{agent:s}=t,{selected:i=!1}=t,{onSelect:g}=t;const y=dt(rt.key),A=dt(gt);Ot(c,A,k=>n(12,l=k));async function f(k){await y.deleteRemoteAgent(k)}async function S(k){try{r?await y.deletePinnedAgentFromStore(k):await y.savePinnedAgentToStore(k,!0);const W=await y.getPinnedAgentsFromStore();A.update(E=>{if(E)return{...E,pinnedAgents:W}})}catch(W){console.error("Failed to toggle pinned status:",W)}}function X(){o&&(async k=>{await y.sshToRemoteAgent(k.remote_agent_id)})(s)}return c.$$set=k=>{"agent"in k&&n(0,s=k.agent),"selected"in k&&n(1,i=k.selected),"onSelect"in k&&n(2,g=k.onSelect)},c.$$.update=()=>{var k;1&c.$$.dirty&&n(6,e=s.turn_summaries||[]),4096&c.$$.dirty&&n(11,a=((k=l.state)==null?void 0:k.pinnedAgents)||{}),2049&c.$$.dirty&&n(5,r=(a==null?void 0:a[s.remote_agent_id])===!0),1&c.$$.dirty&&n(3,o=s.status===F.agentRunning||s.status===F.agentIdle)},n(4,d=!0),[s,i,g,o,!0,r,e,A,f,S,X,a,l,k=>{k.stopPropagation(),S(s.remote_agent_id)},k=>{k.stopPropagation(),X()},k=>{k.stopPropagation(),f(s.remote_agent_id)},()=>g(s.remote_agent_id),k=>k.key==="Enter"&&g(s.remote_agent_id)]}class K extends O{constructor(t){super(),U(this,t,Ve,Je,G,{agent:0,selected:1,onSelect:2})}}function Ke(c){let t;return{c(){t=N(c[0])},m(n,e){p(n,t,e)},p(n,e){1&e&&j(t,n[0])},d(n){n&&$(t)}}}function Qe(c){let t,n,e;return n=new z({props:{size:2,color:"secondary",$$slots:{default:[Ke]},$$scope:{ctx:c}}}),{c(){t=I("div"),w(n.$$.fragment),x(t,"class","section-header svelte-1tegnqi")},m(a,r){p(a,t,r),_(n,t,null),e=!0},p(a,[r]){const o={};3&r&&(o.$$scope={dirty:r,ctx:a}),n.$set(o)},i(a){e||(m(n.$$.fragment,a),e=!0)},o(a){u(n.$$.fragment,a),e=!1},d(a){a&&$(t),v(n)}}}function Xe(c,t,n){let{title:e}=t;return c.$$set=a=>{"title"in a&&n(0,e=a.title)},[e]}class Q extends O{constructor(t){super(),U(this,t,Xe,Qe,G,{title:0})}}function kt(c,t,n){const e=c.slice();return e[6]=t[n],e[8]=n,e}function xt(c,t,n){const e=c.slice();return e[6]=t[n],e[8]=n,e}function It(c,t,n){const e=c.slice();return e[6]=t[n],e[8]=n,e}function bt(c,t,n){const e=c.slice();return e[6]=t[n],e[8]=n,e}function Bt(c,t,n){const e=c.slice();return e[6]=t[n],e[8]=n,e}function Pt(c,t,n){const e=c.slice();return e[6]=t[n],e[8]=n,e}function Ye(c){let t,n,e,a,r,o,d,l=c[1].pinned.length>0&&Ht(c),s=c[1].readyToReview.length>0&&Mt(c),i=c[1].running.length>0&&Et(c),g=c[1].idle.length>0&&Tt(c),y=c[1].failed.length>0&&Dt(c),A=c[1].additional.length>0&&Wt(c);return{c(){l&&l.c(),t=B(),s&&s.c(),n=B(),i&&i.c(),e=B(),g&&g.c(),a=B(),y&&y.c(),r=B(),A&&A.c(),o=T()},m(f,S){l&&l.m(f,S),p(f,t,S),s&&s.m(f,S),p(f,n,S),i&&i.m(f,S),p(f,e,S),g&&g.m(f,S),p(f,a,S),y&&y.m(f,S),p(f,r,S),A&&A.m(f,S),p(f,o,S),d=!0},p(f,S){f[1].pinned.length>0?l?(l.p(f,S),2&S&&m(l,1)):(l=Ht(f),l.c(),m(l,1),l.m(t.parentNode,t)):l&&(H(),u(l,1,1,()=>{l=null}),q()),f[1].readyToReview.length>0?s?(s.p(f,S),2&S&&m(s,1)):(s=Mt(f),s.c(),m(s,1),s.m(n.parentNode,n)):s&&(H(),u(s,1,1,()=>{s=null}),q()),f[1].running.length>0?i?(i.p(f,S),2&S&&m(i,1)):(i=Et(f),i.c(),m(i,1),i.m(e.parentNode,e)):i&&(H(),u(i,1,1,()=>{i=null}),q()),f[1].idle.length>0?g?(g.p(f,S),2&S&&m(g,1)):(g=Tt(f),g.c(),m(g,1),g.m(a.parentNode,a)):g&&(H(),u(g,1,1,()=>{g=null}),q()),f[1].failed.length>0?y?(y.p(f,S),2&S&&m(y,1)):(y=Dt(f),y.c(),m(y,1),y.m(r.parentNode,r)):y&&(H(),u(y,1,1,()=>{y=null}),q()),f[1].additional.length>0?A?(A.p(f,S),2&S&&m(A,1)):(A=Wt(f),A.c(),m(A,1),A.m(o.parentNode,o)):A&&(H(),u(A,1,1,()=>{A=null}),q())},i(f){d||(m(l),m(s),m(i),m(g),m(y),m(A),d=!0)},o(f){u(l),u(s),u(i),u(g),u(y),u(A),d=!1},d(f){f&&($(t),$(n),$(e),$(a),$(r),$(o)),l&&l.d(f),s&&s.d(f),i&&i.d(f),g&&g.d(f),y&&y.d(f),A&&A.d(f)}}}function Ze(c){let t,n,e;return n=new z({props:{size:3,color:"secondary",$$slots:{default:[tn]},$$scope:{ctx:c}}}),{c(){t=I("div"),w(n.$$.fragment),x(t,"class","empty-state svelte-5a9boh")},m(a,r){p(a,t,r),_(n,t,null),e=!0},p(a,r){const o={};16384&r&&(o.$$scope={dirty:r,ctx:a}),n.$set(o)},i(a){e||(m(n.$$.fragment,a),e=!0)},o(a){u(n.$$.fragment,a),e=!1},d(a){a&&$(t),v(n)}}}function Ht(c){let t,n,e,a,r=[],o=new Map;t=new Q({props:{title:"Pinned"}});let d=C(c[1].pinned);const l=s=>s[6].remote_agent_id+s[8];for(let s=0;s<d.length;s+=1){let i=Pt(c,d,s),g=l(i);o.set(g,r[s]=qt(g,i))}return{c(){w(t.$$.fragment),n=B(),e=I("div");for(let s=0;s<r.length;s+=1)r[s].c();x(e,"class","agent-grid svelte-5a9boh")},m(s,i){_(t,s,i),p(s,n,i),p(s,e,i);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(e,null);a=!0},p(s,i){11&i&&(d=C(s[1].pinned),H(),r=J(r,i,l,1,s,d,o,e,V,qt,null,Pt),q())},i(s){if(!a){m(t.$$.fragment,s);for(let i=0;i<d.length;i+=1)m(r[i]);a=!0}},o(s){u(t.$$.fragment,s);for(let i=0;i<r.length;i+=1)u(r[i]);a=!1},d(s){s&&($(n),$(e)),v(t,s);for(let i=0;i<r.length;i+=1)r[i].d()}}}function qt(c,t){var r;let n,e,a;return e=new K({props:{agent:t[6],selected:t[6].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:c,first:null,c(){n=T(),w(e.$$.fragment),this.first=n},m(o,d){p(o,n,d),_(e,o,d),a=!0},p(o,d){var s;t=o;const l={};2&d&&(l.agent=t[6]),3&d&&(l.selected=t[6].remote_agent_id===((s=t[0].state)==null?void 0:s.selectedAgentId)),e.$set(l)},i(o){a||(m(e.$$.fragment,o),a=!0)},o(o){u(e.$$.fragment,o),a=!1},d(o){o&&$(n),v(e,o)}}}function Mt(c){let t,n,e,a,r=[],o=new Map;t=new Q({props:{title:"Ready to review"}});let d=C(c[1].readyToReview);const l=s=>s[6].remote_agent_id+s[8];for(let s=0;s<d.length;s+=1){let i=Bt(c,d,s),g=l(i);o.set(g,r[s]=Ct(g,i))}return{c(){w(t.$$.fragment),n=B(),e=I("div");for(let s=0;s<r.length;s+=1)r[s].c();x(e,"class","agent-grid svelte-5a9boh")},m(s,i){_(t,s,i),p(s,n,i),p(s,e,i);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(e,null);a=!0},p(s,i){11&i&&(d=C(s[1].readyToReview),H(),r=J(r,i,l,1,s,d,o,e,V,Ct,null,Bt),q())},i(s){if(!a){m(t.$$.fragment,s);for(let i=0;i<d.length;i+=1)m(r[i]);a=!0}},o(s){u(t.$$.fragment,s);for(let i=0;i<r.length;i+=1)u(r[i]);a=!1},d(s){s&&($(n),$(e)),v(t,s);for(let i=0;i<r.length;i+=1)r[i].d()}}}function Ct(c,t){var r;let n,e,a;return e=new K({props:{agent:t[6],selected:t[6].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:c,first:null,c(){n=T(),w(e.$$.fragment),this.first=n},m(o,d){p(o,n,d),_(e,o,d),a=!0},p(o,d){var s;t=o;const l={};2&d&&(l.agent=t[6]),3&d&&(l.selected=t[6].remote_agent_id===((s=t[0].state)==null?void 0:s.selectedAgentId)),e.$set(l)},i(o){a||(m(e.$$.fragment,o),a=!0)},o(o){u(e.$$.fragment,o),a=!1},d(o){o&&$(n),v(e,o)}}}function Et(c){let t,n,e,a,r=[],o=new Map;t=new Q({props:{title:"Running agents"}});let d=C(c[1].running);const l=s=>s[6].remote_agent_id+s[8];for(let s=0;s<d.length;s+=1){let i=bt(c,d,s),g=l(i);o.set(g,r[s]=Nt(g,i))}return{c(){w(t.$$.fragment),n=B(),e=I("div");for(let s=0;s<r.length;s+=1)r[s].c();x(e,"class","agent-grid svelte-5a9boh")},m(s,i){_(t,s,i),p(s,n,i),p(s,e,i);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(e,null);a=!0},p(s,i){11&i&&(d=C(s[1].running),H(),r=J(r,i,l,1,s,d,o,e,V,Nt,null,bt),q())},i(s){if(!a){m(t.$$.fragment,s);for(let i=0;i<d.length;i+=1)m(r[i]);a=!0}},o(s){u(t.$$.fragment,s);for(let i=0;i<r.length;i+=1)u(r[i]);a=!1},d(s){s&&($(n),$(e)),v(t,s);for(let i=0;i<r.length;i+=1)r[i].d()}}}function Nt(c,t){var r;let n,e,a;return e=new K({props:{agent:t[6],selected:t[6].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:c,first:null,c(){n=T(),w(e.$$.fragment),this.first=n},m(o,d){p(o,n,d),_(e,o,d),a=!0},p(o,d){var s;t=o;const l={};2&d&&(l.agent=t[6]),3&d&&(l.selected=t[6].remote_agent_id===((s=t[0].state)==null?void 0:s.selectedAgentId)),e.$set(l)},i(o){a||(m(e.$$.fragment,o),a=!0)},o(o){u(e.$$.fragment,o),a=!1},d(o){o&&$(n),v(e,o)}}}function Tt(c){let t,n,e,a,r=[],o=new Map;t=new Q({props:{title:"Idle agents"}});let d=C(c[1].idle);const l=s=>s[6].remote_agent_id+s[8];for(let s=0;s<d.length;s+=1){let i=It(c,d,s),g=l(i);o.set(g,r[s]=Ft(g,i))}return{c(){w(t.$$.fragment),n=B(),e=I("div");for(let s=0;s<r.length;s+=1)r[s].c();x(e,"class","agent-grid svelte-5a9boh")},m(s,i){_(t,s,i),p(s,n,i),p(s,e,i);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(e,null);a=!0},p(s,i){11&i&&(d=C(s[1].idle),H(),r=J(r,i,l,1,s,d,o,e,V,Ft,null,It),q())},i(s){if(!a){m(t.$$.fragment,s);for(let i=0;i<d.length;i+=1)m(r[i]);a=!0}},o(s){u(t.$$.fragment,s);for(let i=0;i<r.length;i+=1)u(r[i]);a=!1},d(s){s&&($(n),$(e)),v(t,s);for(let i=0;i<r.length;i+=1)r[i].d()}}}function Ft(c,t){var r;let n,e,a;return e=new K({props:{agent:t[6],selected:t[6].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:c,first:null,c(){n=T(),w(e.$$.fragment),this.first=n},m(o,d){p(o,n,d),_(e,o,d),a=!0},p(o,d){var s;t=o;const l={};2&d&&(l.agent=t[6]),3&d&&(l.selected=t[6].remote_agent_id===((s=t[0].state)==null?void 0:s.selectedAgentId)),e.$set(l)},i(o){a||(m(e.$$.fragment,o),a=!0)},o(o){u(e.$$.fragment,o),a=!1},d(o){o&&$(n),v(e,o)}}}function Dt(c){let t,n,e,a,r=[],o=new Map;t=new Q({props:{title:"Failed agents"}});let d=C(c[1].failed);const l=s=>s[6].remote_agent_id+s[8];for(let s=0;s<d.length;s+=1){let i=xt(c,d,s),g=l(i);o.set(g,r[s]=zt(g,i))}return{c(){w(t.$$.fragment),n=B(),e=I("div");for(let s=0;s<r.length;s+=1)r[s].c();x(e,"class","agent-grid svelte-5a9boh")},m(s,i){_(t,s,i),p(s,n,i),p(s,e,i);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(e,null);a=!0},p(s,i){11&i&&(d=C(s[1].failed),H(),r=J(r,i,l,1,s,d,o,e,V,zt,null,xt),q())},i(s){if(!a){m(t.$$.fragment,s);for(let i=0;i<d.length;i+=1)m(r[i]);a=!0}},o(s){u(t.$$.fragment,s);for(let i=0;i<r.length;i+=1)u(r[i]);a=!1},d(s){s&&($(n),$(e)),v(t,s);for(let i=0;i<r.length;i+=1)r[i].d()}}}function zt(c,t){var r;let n,e,a;return e=new K({props:{agent:t[6],selected:t[6].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:c,first:null,c(){n=T(),w(e.$$.fragment),this.first=n},m(o,d){p(o,n,d),_(e,o,d),a=!0},p(o,d){var s;t=o;const l={};2&d&&(l.agent=t[6]),3&d&&(l.selected=t[6].remote_agent_id===((s=t[0].state)==null?void 0:s.selectedAgentId)),e.$set(l)},i(o){a||(m(e.$$.fragment,o),a=!0)},o(o){u(e.$$.fragment,o),a=!1},d(o){o&&$(n),v(e,o)}}}function Wt(c){let t,n,e,a,r=[],o=new Map;t=new Q({props:{title:"Other agents"}});let d=C(c[1].additional);const l=s=>s[6].remote_agent_id+s[8];for(let s=0;s<d.length;s+=1){let i=kt(c,d,s),g=l(i);o.set(g,r[s]=Lt(g,i))}return{c(){w(t.$$.fragment),n=B(),e=I("div");for(let s=0;s<r.length;s+=1)r[s].c();x(e,"class","agent-grid svelte-5a9boh")},m(s,i){_(t,s,i),p(s,n,i),p(s,e,i);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(e,null);a=!0},p(s,i){11&i&&(d=C(s[1].additional),H(),r=J(r,i,l,1,s,d,o,e,V,Lt,null,kt),q())},i(s){if(!a){m(t.$$.fragment,s);for(let i=0;i<d.length;i+=1)m(r[i]);a=!0}},o(s){u(t.$$.fragment,s);for(let i=0;i<r.length;i+=1)u(r[i]);a=!1},d(s){s&&($(n),$(e)),v(t,s);for(let i=0;i<r.length;i+=1)r[i].d()}}}function Lt(c,t){var r;let n,e,a;return e=new K({props:{agent:t[6],selected:t[6].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:c,first:null,c(){n=T(),w(e.$$.fragment),this.first=n},m(o,d){p(o,n,d),_(e,o,d),a=!0},p(o,d){var s;t=o;const l={};2&d&&(l.agent=t[6]),3&d&&(l.selected=t[6].remote_agent_id===((s=t[0].state)==null?void 0:s.selectedAgentId)),e.$set(l)},i(o){a||(m(e.$$.fragment,o),a=!0)},o(o){u(e.$$.fragment,o),a=!1},d(o){o&&$(n),v(e,o)}}}function tn(c){let t;return{c(){t=N("No agents available")},m(n,e){p(n,t,e)},d(n){n&&$(t)}}}function en(c){let t,n,e,a;const r=[Ze,Ye],o=[];function d(l,s){var i;return((i=l[0].state)==null?void 0:i.agentOverviews.length)===0?0:1}return n=d(c),e=o[n]=r[n](c),{c(){t=I("div"),e.c(),x(t,"class","agent-list svelte-5a9boh")},m(l,s){p(l,t,s),o[n].m(t,null),a=!0},p(l,[s]){let i=n;n=d(l),n===i?o[n].p(l,s):(H(),u(o[i],1,1,()=>{o[i]=null}),q(),e=o[n],e?e.p(l,s):(e=o[n]=r[n](l),e.c()),m(e,1),e.m(t,null))},i(l){a||(m(e),a=!0)},o(l){u(e),a=!1},d(l){l&&$(t),o[n].d()}}}function nn(c,t,n){let e,a,r,o;const d=dt(gt);return Ot(c,d,l=>n(0,o=l)),c.$$.update=()=>{var l,s;1&c.$$.dirty&&n(5,e=se(((l=o.state)==null?void 0:l.agentOverviews)||[])),1&c.$$.dirty&&n(4,a=((s=o.state)==null?void 0:s.pinnedAgents)||{}),48&c.$$.dirty&&n(1,r=e.reduce((i,g)=>((a==null?void 0:a[g.remote_agent_id])===!0?i.pinned.push(g):g.status===F.agentIdle&&g.has_updates?i.readyToReview.push(g):g.status===F.agentRunning||g.status===F.agentStarting||g.workspace_status===ct.workspaceResuming?i.running.push(g):g.status===F.agentFailed?i.failed.push(g):g.status===F.agentIdle||g.workspace_status===ct.workspacePaused||g.workspace_status===ct.workspacePausing?i.idle.push(g):i.additional.push(g),i),{pinned:[],readyToReview:[],running:[],idle:[],failed:[],additional:[]}))},[o,r,d,function(l){d.update(s=>{if(s)return{...s,selectedAgentId:l}})},a,e]}class sn extends O{constructor(t){super(),U(this,t,nn,en,G,{})}}function an(c){let t,n,e,a,r,o,d,l,s,i;return a=new $e({}),d=new sn({}),{c(){t=I("div"),n=I("h1"),e=I("span"),w(a.$$.fragment),r=N(`
    Remote Agents`),o=B(),w(d.$$.fragment),x(e,"class","l-main__title-logo svelte-1941nw6"),x(n,"class","l-main__title svelte-1941nw6"),x(t,"class","l-main svelte-1941nw6")},m(g,y){p(g,t,y),M(t,n),M(n,e),_(a,e,null),M(n,r),M(t,o),_(d,t,null),l=!0,s||(i=ee(window,"message",c[0].onMessageFromExtension),s=!0)},p:Z,i(g){l||(m(a.$$.fragment,g),m(d.$$.fragment,g),l=!0)},o(g){u(a.$$.fragment,g),u(d.$$.fragment,g),l=!1},d(g){g&&$(t),v(a),v(d),s=!1,i()}}}function rn(c){const t=new re(ae),n=new pe(t,void 0,le,de);t.registerConsumer(n),wt(gt,n);const e=new rt(t);return wt(rt.key,e),ne(()=>(n.fetchStateFromExtension().then(()=>{n.update(a=>{if(!a)return;const r=[...a.activeWebviews,"home"];return a.pinnedAgents?{...a,activeWebviews:r}:{...a,activeWebviews:r,pinnedAgents:{}}})}),()=>{t.dispose(),e.dispose()})),[t]}new class extends O{constructor(c){super(),U(this,c,rn,an,G,{})}}({target:document.getElementById("app")});
