var on=Object.defineProperty;var sn=(e,t,n)=>t in e?on(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var k=(e,t,n)=>sn(e,typeof t!="symbol"?t+"":t,n);import{R as re}from"./chat-types-NgqNgjwU.js";function Ee(e,t){return!(e===null||typeof e!="object"||!("$typeName"in e)||typeof e.$typeName!="string")&&(t===void 0||t.typeName===e.$typeName)}var c;function un(){let e=0,t=0;for(let r=0;r<28;r+=7){let o=this.buf[this.pos++];if(e|=(127&o)<<r,!(128&o))return this.assertBounds(),[e,t]}let n=this.buf[this.pos++];if(e|=(15&n)<<28,t=(112&n)>>4,!(128&n))return this.assertBounds(),[e,t];for(let r=3;r<=31;r+=7){let o=this.buf[this.pos++];if(t|=(127&o)<<r,!(128&o))return this.assertBounds(),[e,t]}throw new Error("invalid varint")}function ae(e,t,n){for(let a=0;a<28;a+=7){const s=e>>>a,i=!(!(s>>>7)&&t==0),l=255&(i?128|s:s);if(n.push(l),!i)return}const r=e>>>28&15|(7&t)<<4,o=!!(t>>3);if(n.push(255&(o?128|r:r)),o){for(let a=3;a<31;a+=7){const s=t>>>a,i=!!(s>>>7),l=255&(i?128|s:s);if(n.push(l),!i)return}n.push(t>>>31&1)}}(function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"})(c||(c={}));const X=4294967296;function Pe(e){const t=e[0]==="-";t&&(e=e.slice(1));const n=1e6;let r=0,o=0;function a(s,i){const l=Number(e.slice(s,i));o*=n,r=r*n+l,r>=X&&(o+=r/X|0,r%=X)}return a(-24,-18),a(-18,-12),a(-12,-6),a(-6),t?Tt(r,o):Te(r,o)}function $e(e,t){if({lo:e,hi:t}=function(l,u){return{lo:l>>>0,hi:u>>>0}}(e,t),t<=2097151)return String(X*t+e);const n=16777215&(e>>>24|t<<8),r=t>>16&65535;let o=(16777215&e)+6777216*n+6710656*r,a=n+8147497*r,s=2*r;const i=1e7;return o>=i&&(a+=Math.floor(o/i),o%=i),a>=i&&(s+=Math.floor(a/i),a%=i),s.toString()+xe(a)+xe(o)}function Te(e,t){return{lo:0|e,hi:0|t}}function Tt(e,t){return t=~t,e?e=1+~e:t+=1,Te(e,t)}const xe=e=>{const t=String(e);return"0000000".slice(t.length)+t};function Ye(e,t){if(e>=0){for(;e>127;)t.push(127&e|128),e>>>=7;t.push(e)}else{for(let n=0;n<9;n++)t.push(127&e|128),e>>=7;t.push(1)}}function ln(){let e=this.buf[this.pos++],t=127&e;if(!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<7,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<14,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<21,!(128&e))return this.assertBounds(),t;e=this.buf[this.pos++],t|=(15&e)<<28;for(let n=5;128&e&&n<10;n++)e=this.buf[this.pos++];if(128&e)throw new Error("invalid varint");return this.assertBounds(),t>>>0}var Ge={};const b=cn();function cn(){const e=new DataView(new ArrayBuffer(8));if(typeof BigInt=="function"&&typeof e.getBigInt64=="function"&&typeof e.getBigUint64=="function"&&typeof e.setBigInt64=="function"&&typeof e.setBigUint64=="function"&&(typeof process!="object"||typeof Ge!="object"||Ge.BUF_BIGINT_DISABLE!=="1")){const t=BigInt("-9223372036854775808"),n=BigInt("9223372036854775807"),r=BigInt("0"),o=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(a){const s=typeof a=="bigint"?a:BigInt(a);if(s>n||s<t)throw new Error(`invalid int64: ${a}`);return s},uParse(a){const s=typeof a=="bigint"?a:BigInt(a);if(s>o||s<r)throw new Error(`invalid uint64: ${a}`);return s},enc(a){return e.setBigInt64(0,this.parse(a),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},uEnc(a){return e.setBigInt64(0,this.uParse(a),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},dec:(a,s)=>(e.setInt32(0,a,!0),e.setInt32(4,s,!0),e.getBigInt64(0,!0)),uDec:(a,s)=>(e.setInt32(0,a,!0),e.setInt32(4,s,!0),e.getBigUint64(0,!0))}}return{zero:"0",supported:!1,parse:t=>(typeof t!="string"&&(t=t.toString()),Ve(t),t),uParse:t=>(typeof t!="string"&&(t=t.toString()),Ce(t),t),enc:t=>(typeof t!="string"&&(t=t.toString()),Ve(t),Pe(t)),uEnc:t=>(typeof t!="string"&&(t=t.toString()),Ce(t),Pe(t)),dec:(t,n)=>function(r,o){let a=Te(r,o);const s=2147483648&a.hi;s&&(a=Tt(a.lo,a.hi));const i=$e(a.lo,a.hi);return s?"-"+i:i}(t,n),uDec:(t,n)=>$e(t,n)}}function Ve(e){if(!/^-?[0-9]+$/.test(e))throw new Error("invalid int64: "+e)}function Ce(e){if(!/^[0-9]+$/.test(e))throw new Error("invalid uint64: "+e)}function L(e,t){switch(e){case c.STRING:return"";case c.BOOL:return!1;case c.DOUBLE:case c.FLOAT:return 0;case c.INT64:case c.UINT64:case c.SFIXED64:case c.FIXED64:case c.SINT64:return t?"0":b.zero;case c.BYTES:return new Uint8Array(0);default:return 0}}const O=Symbol.for("reflect unsafe local");function vt(e,t){const n=e[t.localName].case;return n===void 0?n:t.fields.find(r=>r.localName===n)}function mn(e,t){const n=t.localName;if(t.oneof)return e[t.oneof.localName].case===n;if(t.presence!=2)return e[n]!==void 0&&Object.prototype.hasOwnProperty.call(e,n);switch(t.fieldKind){case"list":return e[n].length>0;case"map":return Object.keys(e[n]).length>0;case"scalar":return!function(r,o){switch(r){case c.BOOL:return o===!1;case c.STRING:return o==="";case c.BYTES:return o instanceof Uint8Array&&!o.byteLength;default:return o==0}}(t.scalar,e[n]);case"enum":return e[n]!==t.enum.values[0].number}throw new Error("message field with implicit presence")}function V(e,t){return Object.prototype.hasOwnProperty.call(e,t)&&e[t]!==void 0}function It(e,t){if(t.oneof){const n=e[t.oneof.localName];return n.case===t.localName?n.value:void 0}return e[t.localName]}function _t(e,t,n){t.oneof?e[t.oneof.localName]={case:t.localName,value:n}:e[t.localName]=n}function A(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)}function de(e,t){var n,r,o,a;if(A(e)&&O in e&&"add"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const s=t,i=e.field();return s.listKind==i.listKind&&s.scalar===i.scalar&&((n=s.message)===null||n===void 0?void 0:n.typeName)===((r=i.message)===null||r===void 0?void 0:r.typeName)&&((o=s.enum)===null||o===void 0?void 0:o.typeName)===((a=i.enum)===null||a===void 0?void 0:a.typeName)}return!0}return!1}function pe(e,t){var n,r,o,a;if(A(e)&&O in e&&"has"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const s=t,i=e.field();return s.mapKey===i.mapKey&&s.mapKind==i.mapKind&&s.scalar===i.scalar&&((n=s.message)===null||n===void 0?void 0:n.typeName)===((r=i.message)===null||r===void 0?void 0:r.typeName)&&((o=s.enum)===null||o===void 0?void 0:o.typeName)===((a=i.enum)===null||a===void 0?void 0:a.typeName)}return!0}return!1}function ve(e,t){return A(e)&&O in e&&"desc"in e&&A(e.desc)&&e.desc.kind==="message"&&(t===void 0||e.desc.typeName==t.typeName)}function te(e){const t=e.fields[0];return St(e.typeName)&&t!==void 0&&t.fieldKind=="scalar"&&t.name=="value"&&t.number==1}function St(e){return e.startsWith("google.protobuf.")&&["DoubleValue","FloatValue","Int64Value","UInt64Value","Int32Value","UInt32Value","BoolValue","StringValue","BytesValue"].includes(e.substring(16))}const dn=999,pn=998,K=2;function w(e,t){if(Ee(t,e))return t;const n=function(r){let o;if(function(a){switch(a.file.edition){case dn:return!1;case pn:return!0;default:return a.fields.some(s=>s.presence!=K&&s.fieldKind!="message"&&!s.oneof)}}(r)){const a=Ke.get(r);let s,i;if(a)({prototype:s,members:i}=a);else{s={},i=new Set;for(const l of r.members)l.kind!="oneof"&&(l.fieldKind!="scalar"&&l.fieldKind!="enum"||l.presence!=K&&(i.add(l),s[l.localName]=oe(l)));Ke.set(r,{prototype:s,members:i})}o=Object.create(s),o.$typeName=r.typeName;for(const l of r.members)if(!i.has(l)){if(l.kind=="field"&&(l.fieldKind=="message"||(l.fieldKind=="scalar"||l.fieldKind=="enum")&&l.presence!=K))continue;o[l.localName]=oe(l)}}else{o={$typeName:r.typeName};for(const a of r.members)a.kind!="oneof"&&a.presence!=K||(o[a.localName]=oe(a))}return o}(e);return t!==void 0&&function(r,o,a){for(const s of r.members){let i,l=a[s.localName];if(l!=null){if(s.kind=="oneof"){const u=vt(a,s);if(!u)continue;i=u,l=It(a,u)}else i=s;switch(i.fieldKind){case"message":l=Ie(i,l);break;case"scalar":l=Ot(i,l);break;case"list":l=bn(i,l);break;case"map":l=fn(i,l)}_t(o,i,l)}}}(e,n,t),n}function Ot(e,t){return e.scalar==c.BYTES?_e(t):t}function fn(e,t){if(A(t)){if(e.scalar==c.BYTES)return Me(t,_e);if(e.mapKind=="message")return Me(t,n=>Ie(e,n))}return t}function bn(e,t){if(Array.isArray(t)){if(e.scalar==c.BYTES)return t.map(_e);if(e.listKind=="message")return t.map(n=>Ie(e,n))}return t}function Ie(e,t){if(e.fieldKind=="message"&&!e.oneof&&te(e.message))return Ot(e.message.fields[0],t);if(A(t)){if(e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!=="google.protobuf.Value")return t;if(!Ee(t,e.message))return w(e.message,t)}return t}function _e(e){return Array.isArray(e)?new Uint8Array(e):e}function Me(e,t){const n={};for(const r of Object.entries(e))n[r[0]]=t(r[1]);return n}const gn=Symbol(),Ke=new WeakMap;function oe(e){if(e.kind=="oneof")return{case:void 0};if(e.fieldKind=="list")return[];if(e.fieldKind=="map")return{};if(e.fieldKind=="message")return gn;const t=e.getDefaultValue();return t!==void 0?e.fieldKind=="scalar"&&e.longAsString?t.toString():t:e.fieldKind=="scalar"?L(e.scalar,e.longAsString):e.enum.values[0].number}const yn=["FieldValueInvalidError","FieldListRangeError","ForeignFieldError"];class T extends Error{constructor(t,n,r="FieldValueInvalidError"){super(n),this.name=r,this.field=()=>t}}const se=Symbol.for("@bufbuild/protobuf/text-encoding");function Se(){if(globalThis[se]==null){const e=new globalThis.TextEncoder,t=new globalThis.TextDecoder;globalThis[se]={encodeUtf8:n=>e.encode(n),decodeUtf8:n=>t.decode(n),checkUtf8(n){try{return encodeURIComponent(n),!0}catch{return!1}}}}return globalThis[se]}var h;(function(e){e[e.Varint=0]="Varint",e[e.Bit64=1]="Bit64",e[e.LengthDelimited=2]="LengthDelimited",e[e.StartGroup=3]="StartGroup",e[e.EndGroup=4]="EndGroup",e[e.Bit32=5]="Bit32"})(h||(h={}));const wt=34028234663852886e22,Rt=-34028234663852886e22,At=4294967295,Dt=2147483647,kt=-2147483648;class Ft{constructor(t=Se().encodeUtf8){this.encodeUtf8=t,this.stack=[],this.chunks=[],this.buf=[]}finish(){this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]);let t=0;for(let o=0;o<this.chunks.length;o++)t+=this.chunks[o].length;let n=new Uint8Array(t),r=0;for(let o=0;o<this.chunks.length;o++)n.set(this.chunks[o],r),r+=this.chunks[o].length;return this.chunks=[],n}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let t=this.finish(),n=this.stack.pop();if(!n)throw new Error("invalid state, fork stack empty");return this.chunks=n.chunks,this.buf=n.buf,this.uint32(t.byteLength),this.raw(t)}tag(t,n){return this.uint32((t<<3|n)>>>0)}raw(t){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(t),this}uint32(t){for(Be(t);t>127;)this.buf.push(127&t|128),t>>>=7;return this.buf.push(t),this}int32(t){return ie(t),Ye(t,this.buf),this}bool(t){return this.buf.push(t?1:0),this}bytes(t){return this.uint32(t.byteLength),this.raw(t)}string(t){let n=this.encodeUtf8(t);return this.uint32(n.byteLength),this.raw(n)}float(t){(function(r){if(typeof r=="string"){const o=r;if(r=Number(r),Number.isNaN(r)&&o!=="NaN")throw new Error("invalid float32: "+o)}else if(typeof r!="number")throw new Error("invalid float32: "+typeof r);if(Number.isFinite(r)&&(r>wt||r<Rt))throw new Error("invalid float32: "+r)})(t);let n=new Uint8Array(4);return new DataView(n.buffer).setFloat32(0,t,!0),this.raw(n)}double(t){let n=new Uint8Array(8);return new DataView(n.buffer).setFloat64(0,t,!0),this.raw(n)}fixed32(t){Be(t);let n=new Uint8Array(4);return new DataView(n.buffer).setUint32(0,t,!0),this.raw(n)}sfixed32(t){ie(t);let n=new Uint8Array(4);return new DataView(n.buffer).setInt32(0,t,!0),this.raw(n)}sint32(t){return ie(t),Ye(t=(t<<1^t>>31)>>>0,this.buf),this}sfixed64(t){let n=new Uint8Array(8),r=new DataView(n.buffer),o=b.enc(t);return r.setInt32(0,o.lo,!0),r.setInt32(4,o.hi,!0),this.raw(n)}fixed64(t){let n=new Uint8Array(8),r=new DataView(n.buffer),o=b.uEnc(t);return r.setInt32(0,o.lo,!0),r.setInt32(4,o.hi,!0),this.raw(n)}int64(t){let n=b.enc(t);return ae(n.lo,n.hi,this.buf),this}sint64(t){const n=b.enc(t),r=n.hi>>31;return ae(n.lo<<1^r,(n.hi<<1|n.lo>>>31)^r,this.buf),this}uint64(t){const n=b.uEnc(t);return ae(n.lo,n.hi,this.buf),this}}class Lt{constructor(t,n=Se().decodeUtf8){this.decodeUtf8=n,this.varint64=un,this.uint32=ln,this.buf=t,this.len=t.length,this.pos=0,this.view=new DataView(t.buffer,t.byteOffset,t.byteLength)}tag(){let t=this.uint32(),n=t>>>3,r=7&t;if(n<=0||r<0||r>5)throw new Error("illegal tag: field no "+n+" wire type "+r);return[n,r]}skip(t,n){let r=this.pos;switch(t){case h.Varint:for(;128&this.buf[this.pos++];);break;case h.Bit64:this.pos+=4;case h.Bit32:this.pos+=4;break;case h.LengthDelimited:let o=this.uint32();this.pos+=o;break;case h.StartGroup:for(;;){const[a,s]=this.tag();if(s===h.EndGroup){if(n!==void 0&&a!==n)throw new Error("invalid end group tag");break}this.skip(s,a)}break;default:throw new Error("cant skip wire type "+t)}return this.assertBounds(),this.buf.subarray(r,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let t=this.uint32();return t>>>1^-(1&t)}int64(){return b.dec(...this.varint64())}uint64(){return b.uDec(...this.varint64())}sint64(){let[t,n]=this.varint64(),r=-(1&t);return t=(t>>>1|(1&n)<<31)^r,n=n>>>1^r,b.dec(t,n)}bool(){let[t,n]=this.varint64();return t!==0||n!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return b.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return b.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let t=this.uint32(),n=this.pos;return this.pos+=t,this.assertBounds(),this.buf.subarray(n,n+t)}string(){return this.decodeUtf8(this.bytes())}}function ie(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid int32: "+typeof e);if(!Number.isInteger(e)||e>Dt||e<kt)throw new Error("invalid int32: "+e)}function Be(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid uint32: "+typeof e);if(!Number.isInteger(e)||e>At||e<0)throw new Error("invalid uint32: "+e)}function je(e,t,n){const r=Oe(e,n);if(r!==!0)return new T(e,`list item #${t+1}: ${q(e,n,r)}`)}function Oe(e,t){return e.scalar!==void 0?Ut(t,e.scalar):e.enum!==void 0?e.enum.open?Number.isInteger(t):e.enum.values.some(n=>n.number===t):ve(t,e.message)}function Ut(e,t){switch(t){case c.DOUBLE:return typeof e=="number";case c.FLOAT:return typeof e=="number"&&(!(!Number.isNaN(e)&&Number.isFinite(e))||!(e>wt||e<Rt)||`${e.toFixed()} out of range`);case c.INT32:case c.SFIXED32:case c.SINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>Dt||e<kt)||`${e.toFixed()} out of range`);case c.FIXED32:case c.UINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>At||e<0)||`${e.toFixed()} out of range`);case c.BOOL:return typeof e=="boolean";case c.STRING:return typeof e=="string"&&(Se().checkUtf8(e)||"invalid UTF8");case c.BYTES:return e instanceof Uint8Array;case c.INT64:case c.SFIXED64:case c.SINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return b.parse(e),!0}catch{return`${e} out of range`}return!1;case c.FIXED64:case c.UINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return b.uParse(e),!0}catch{return`${e} out of range`}return!1}}function q(e,t,n){return n=typeof n=="string"?`: ${n}`:`, got ${E(t)}`,e.scalar!==void 0?`expected ${function(r){switch(r){case c.STRING:return"string";case c.BOOL:return"boolean";case c.INT64:case c.SINT64:case c.SFIXED64:return"bigint (int64)";case c.UINT64:case c.FIXED64:return"bigint (uint64)";case c.BYTES:return"Uint8Array";case c.DOUBLE:return"number (float64)";case c.FLOAT:return"number (float32)";case c.FIXED32:case c.UINT32:return"number (uint32)";case c.INT32:case c.SFIXED32:case c.SINT32:return"number (int32)"}}(e.scalar)}`+n:e.enum!==void 0?`expected ${e.enum.toString()}`+n:`expected ${Pt(e.message)}`+n}function E(e){switch(typeof e){case"object":return e===null?"null":e instanceof Uint8Array?`Uint8Array(${e.length})`:Array.isArray(e)?`Array(${e.length})`:de(e)?$t(e.field()):pe(e)?xt(e.field()):ve(e)?Pt(e.desc):Ee(e)?`message ${e.$typeName}`:"object";case"string":return e.length>30?"string":`"${e.split('"').join('\\"')}"`;case"boolean":case"number":return String(e);case"bigint":return String(e)+"n";default:return typeof e}}function Pt(e){return`ReflectMessage (${e.typeName})`}function $t(e){switch(e.listKind){case"message":return`ReflectList (${e.message.toString()})`;case"enum":return`ReflectList (${e.enum.toString()})`;case"scalar":return`ReflectList (${c[e.scalar]})`}}function xt(e){switch(e.mapKind){case"message":return`ReflectMap (${c[e.mapKey]}, ${e.message.toString()})`;case"enum":return`ReflectMap (${c[e.mapKey]}, ${e.enum.toString()})`;case"scalar":return`ReflectMap (${c[e.mapKey]}, ${c[e.scalar]})`}}function _(e,t,n=!0){return new Yt(e,t,n)}class Yt{get sortedFields(){var t;return(t=this._sortedFields)!==null&&t!==void 0?t:this._sortedFields=this.desc.fields.concat().sort((n,r)=>n.number-r.number)}constructor(t,n,r=!0){this.lists=new Map,this.maps=new Map,this.check=r,this.desc=t,this.message=this[O]=n??w(t),this.fields=t.fields,this.oneofs=t.oneofs,this.members=t.members}findNumber(t){return this._fieldsByNumber||(this._fieldsByNumber=new Map(this.desc.fields.map(n=>[n.number,n]))),this._fieldsByNumber.get(t)}oneofCase(t){return Y(this.message,t),vt(this.message,t)}isSet(t){return Y(this.message,t),mn(this.message,t)}clear(t){Y(this.message,t),function(n,r){const o=r.localName;if(r.oneof){const a=r.oneof.localName;n[a].case===o&&(n[a]={case:void 0})}else if(r.presence!=2)delete n[o];else switch(r.fieldKind){case"map":n[o]={};break;case"list":n[o]=[];break;case"enum":n[o]=r.enum.values[0].number;break;case"scalar":n[o]=L(r.scalar,r.longAsString)}}(this.message,t)}get(t){Y(this.message,t);const n=It(this.message,t);switch(t.fieldKind){case"list":let r=this.lists.get(t);return r&&r[O]===n||this.lists.set(t,r=new hn(t,n,this.check)),r;case"map":let o=this.maps.get(t);return o&&o[O]===n||this.maps.set(t,o=new Nn(t,n,this.check)),o;case"message":return Re(t,n,this.check);case"scalar":return n===void 0?L(t.scalar,!1):Ae(t,n);case"enum":return n??t.enum.values[0].number}}set(t,n){if(Y(this.message,t),this.check){const o=function(a,s){const i=a.fieldKind=="list"?de(s,a):a.fieldKind=="map"?pe(s,a):Oe(a,s);if(i===!0)return;let l;switch(a.fieldKind){case"list":l=`expected ${$t(a)}, got ${E(s)}`;break;case"map":l=`expected ${xt(a)}, got ${E(s)}`;break;default:l=q(a,s,i)}return new T(a,l)}(t,n);if(o)throw o}let r;r=t.fieldKind=="message"?we(t,n):pe(n)||de(n)?n[O]:De(t,n),_t(this.message,t,r)}getUnknown(){return this.message.$unknown}setUnknown(t){this.message.$unknown=t}}function Y(e,t){if(t.parent.typeName!==e.$typeName)throw new T(t,`cannot use ${t.toString()} with message ${e.$typeName}`,"ForeignFieldError")}class hn{field(){return this._field}get size(){return this._arr.length}constructor(t,n,r){this._field=t,this._arr=this[O]=n,this.check=r}get(t){const n=this._arr[t];return n===void 0?void 0:ue(this._field,n,this.check)}set(t,n){if(t<0||t>=this._arr.length)throw new T(this._field,`list item #${t+1}: out of range`);if(this.check){const r=je(this._field,t,n);if(r)throw r}this._arr[t]=Xe(this._field,n)}add(t){if(this.check){const n=je(this._field,this._arr.length,t);if(n)throw n}this._arr.push(Xe(this._field,t))}clear(){this._arr.splice(0,this._arr.length)}[Symbol.iterator](){return this.values()}keys(){return this._arr.keys()}*values(){for(const t of this._arr)yield ue(this._field,t,this.check)}*entries(){for(let t=0;t<this._arr.length;t++)yield[t,ue(this._field,this._arr[t],this.check)]}}class Nn{constructor(t,n,r=!0){this.obj=this[O]=n??{},this.check=r,this._field=t}field(){return this._field}set(t,n){if(this.check){const r=function(o,a,s){const i=Ut(a,o.mapKey);if(i!==!0)return new T(o,`invalid map key: ${q({scalar:o.mapKey},a,i)}`);const l=Oe(o,s);return l!==!0?new T(o,`map entry ${E(a)}: ${q(o,s,l)}`):void 0}(this._field,t,n);if(r)throw r}return this.obj[B(t)]=function(r,o){return r.mapKind=="message"?we(r,o):De(r,o)}(this._field,n),this}delete(t){const n=B(t),r=Object.prototype.hasOwnProperty.call(this.obj,n);return r&&delete this.obj[n],r}clear(){for(const t of Object.keys(this.obj))delete this.obj[t]}get(t){let n=this.obj[B(t)];return n!==void 0&&(n=le(this._field,n,this.check)),n}has(t){return Object.prototype.hasOwnProperty.call(this.obj,B(t))}*keys(){for(const t of Object.keys(this.obj))yield We(t,this._field.mapKey)}*entries(){for(const t of Object.entries(this.obj))yield[We(t[0],this._field.mapKey),le(this._field,t[1],this.check)]}[Symbol.iterator](){return this.entries()}get size(){return Object.keys(this.obj).length}*values(){for(const t of Object.values(this.obj))yield le(this._field,t,this.check)}forEach(t,n){for(const r of this.entries())t.call(n,r[1],r[0],this)}}function we(e,t){return ve(t)?St(t.message.$typeName)&&!e.oneof&&e.fieldKind=="message"?t.message.value:t.desc.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"?Vt(t.message):t.message:t}function Re(e,t,n){return t!==void 0&&(te(e.message)&&!e.oneof&&e.fieldKind=="message"?t={$typeName:e.message.typeName,value:Ae(e.message.fields[0],t)}:e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"&&A(t)&&(t=Gt(t))),new Yt(e.message,t,n)}function Xe(e,t){return e.listKind=="message"?we(e,t):De(e,t)}function ue(e,t,n){return e.listKind=="message"?Re(e,t,n):Ae(e,t)}function le(e,t,n){return e.mapKind=="message"?Re(e,t,n):t}function B(e){return typeof e=="string"||typeof e=="number"?e:String(e)}function We(e,t){switch(t){case c.STRING:return e;case c.INT32:case c.FIXED32:case c.UINT32:case c.SFIXED32:case c.SINT32:{const n=Number.parseInt(e);if(Number.isFinite(n))return n;break}case c.BOOL:switch(e){case"true":return!0;case"false":return!1}break;case c.UINT64:case c.FIXED64:try{return b.uParse(e)}catch{}break;default:try{return b.parse(e)}catch{}}return e}function Ae(e,t){switch(e.scalar){case c.INT64:case c.SFIXED64:case c.SINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=b.parse(t));break;case c.FIXED64:case c.UINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=b.uParse(t))}return t}function De(e,t){switch(e.scalar){case c.INT64:case c.SFIXED64:case c.SINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=b.parse(t));break;case c.FIXED64:case c.UINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=b.uParse(t))}return t}function Gt(e){const t={$typeName:"google.protobuf.Struct",fields:{}};if(A(e))for(const[n,r]of Object.entries(e))t.fields[n]=Mt(r);return t}function Vt(e){const t={};for(const[n,r]of Object.entries(e.fields))t[n]=Ct(r);return t}function Ct(e){switch(e.kind.case){case"structValue":return Vt(e.kind.value);case"listValue":return e.kind.value.values.map(Ct);case"nullValue":case void 0:return null;default:return e.kind.value}}function Mt(e){const t={$typeName:"google.protobuf.Value",kind:{case:void 0}};switch(typeof e){case"number":t.kind={case:"numberValue",value:e};break;case"string":t.kind={case:"stringValue",value:e};break;case"boolean":t.kind={case:"boolValue",value:e};break;case"object":if(e===null)t.kind={case:"nullValue",value:0};else if(Array.isArray(e)){const n={$typeName:"google.protobuf.ListValue",values:[]};if(Array.isArray(e))for(const r of e)n.values.push(Mt(r));t.kind={case:"listValue",value:n}}else t.kind={case:"structValue",value:Gt(e)}}return t}function Kt(e){const t=function(){if(!U){U=[];const l=function(u){return j||(j="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),j.slice(0,-2).concat("-","_")),j}();for(let u=0;u<l.length;u++)U[l[u].charCodeAt(0)]=u;U[45]=l.indexOf("+"),U[95]=l.indexOf("/")}return U}();let n=3*e.length/4;e[e.length-2]=="="?n-=2:e[e.length-1]=="="&&(n-=1);let r,o=new Uint8Array(n),a=0,s=0,i=0;for(let l=0;l<e.length;l++){if(r=t[e.charCodeAt(l)],r===void 0)switch(e[l]){case"=":s=0;case`
`:case"\r":case"	":case" ":continue;default:throw Error("invalid base64 string")}switch(s){case 0:i=r,s=1;break;case 1:o[a++]=i<<2|(48&r)>>4,i=r,s=2;break;case 2:o[a++]=(15&i)<<4|(60&r)>>2,i=r,s=3;break;case 3:o[a++]=(3&i)<<6|r,s=0}}if(s==1)throw Error("invalid base64 string");return o.subarray(0,a)}let j,U;function H(e){let t=!1;const n=[];for(let r=0;r<e.length;r++){let o=e.charAt(r);switch(o){case"_":t=!0;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n.push(o),t=!1;break;default:t&&(t=!1,o=o.toUpperCase()),n.push(o)}}return n.join("")}const En=new Set(["constructor","toString","toJSON","valueOf"]);function C(e){return En.has(e)?e+"$":e}function ke(e){for(const t of e.field)V(t,"jsonName")||(t.jsonName=H(t.name));e.nestedType.forEach(ke)}function Tn(e,t){switch(e){case c.STRING:return t;case c.BYTES:{const n=function(r){const o=[],a={tail:r,c:"",next(){return this.tail.length!=0&&(this.c=this.tail[0],this.tail=this.tail.substring(1),!0)},take(s){if(this.tail.length>=s){const i=this.tail.substring(0,s);return this.tail=this.tail.substring(s),i}return!1}};for(;a.next();)if(a.c==="\\"){if(a.next())switch(a.c){case"\\":o.push(a.c.charCodeAt(0));break;case"b":o.push(8);break;case"f":o.push(12);break;case"n":o.push(10);break;case"r":o.push(13);break;case"t":o.push(9);break;case"v":o.push(11);break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":{const s=a.c,i=a.take(2);if(i===!1)return!1;const l=parseInt(s+i,8);if(Number.isNaN(l))return!1;o.push(l);break}case"x":{const s=a.c,i=a.take(2);if(i===!1)return!1;const l=parseInt(s+i,16);if(Number.isNaN(l))return!1;o.push(l);break}case"u":{const s=a.c,i=a.take(4);if(i===!1)return!1;const l=parseInt(s+i,16);if(Number.isNaN(l))return!1;const u=new Uint8Array(4);new DataView(u.buffer).setInt32(0,l,!0),o.push(u[0],u[1],u[2],u[3]);break}case"U":{const s=a.c,i=a.take(8);if(i===!1)return!1;const l=b.uEnc(s+i),u=new Uint8Array(8),m=new DataView(u.buffer);m.setInt32(0,l.lo,!0),m.setInt32(4,l.hi,!0),o.push(u[0],u[1],u[2],u[3],u[4],u[5],u[6],u[7]);break}}}else o.push(a.c.charCodeAt(0));return new Uint8Array(o)}(t);if(n===!1)throw new Error(`cannot parse ${c[e]} default value: ${t}`);return n}case c.INT64:case c.SFIXED64:case c.SINT64:return b.parse(t);case c.UINT64:case c.FIXED64:return b.uParse(t);case c.DOUBLE:case c.FLOAT:switch(t){case"inf":return Number.POSITIVE_INFINITY;case"-inf":return Number.NEGATIVE_INFINITY;case"nan":return Number.NaN;default:return parseFloat(t)}case c.BOOL:return t==="true";case c.INT32:case c.UINT32:case c.SINT32:case c.FIXED32:case c.SFIXED32:return parseInt(t,10)}}function*fe(e){switch(e.kind){case"file":for(const t of e.messages)yield t,yield*fe(t);yield*e.enums,yield*e.services,yield*e.extensions;break;case"message":for(const t of e.nestedMessages)yield t,yield*fe(t);yield*e.nestedEnums,yield*e.nestedExtensions}}function Bt(...e){const t=function(){const n=new Map,r=new Map,o=new Map;return{kind:"registry",types:n,extendees:r,[Symbol.iterator]:()=>n.values(),get files(){return o.values()},addFile(a,s,i){if(o.set(a.proto.name,a),!s)for(const l of fe(a))this.add(l);if(i)for(const l of a.dependencies)this.addFile(l,s,i)},add(a){if(a.kind=="extension"){let s=r.get(a.extendee.typeName);s||r.set(a.extendee.typeName,s=new Map),s.set(a.number,a)}n.set(a.typeName,a)},get:a=>n.get(a),getFile:a=>o.get(a),getMessage(a){const s=n.get(a);return(s==null?void 0:s.kind)=="message"?s:void 0},getEnum(a){const s=n.get(a);return(s==null?void 0:s.kind)=="enum"?s:void 0},getExtension(a){const s=n.get(a);return(s==null?void 0:s.kind)=="extension"?s:void 0},getExtensionFor(a,s){var i;return(i=r.get(a.typeName))===null||i===void 0?void 0:i.get(s)},getService(a){const s=n.get(a);return(s==null?void 0:s.kind)=="service"?s:void 0}}}();if(!e.length)return t;if("$typeName"in e[0]&&e[0].$typeName=="google.protobuf.FileDescriptorSet"){for(const n of e[0].file)qe(n,t);return t}if("$typeName"in e[0]){let a=function(s){const i=[];for(const l of s.dependency){if(t.getFile(l)!=null||o.has(l))continue;const u=r(l);if(!u)throw new Error(`Unable to resolve ${l}, imported by ${s.name}`);"kind"in u?t.addFile(u,!1,!0):(o.add(u.name),i.push(u))}return i.concat(...i.map(a))};const n=e[0],r=e[1],o=new Set;for(const s of[n,...a(n)].reverse())qe(s,t)}else for(const n of e)for(const r of n.files)t.addFile(r);return t}const vn=998,In=999,_n=9,W=10,G=11,Sn=12,Ze=14,be=3,On=2,Je=1,wn=0,Rn=1,An=2,Dn=3,kn=1,Fn=2,Ln=1,jt={998:{fieldPresence:1,enumType:2,repeatedFieldEncoding:2,utf8Validation:3,messageEncoding:1,jsonFormat:2,enforceNamingStyle:2},999:{fieldPresence:2,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2},1e3:{fieldPresence:1,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2}};function qe(e,t){var n,r;const o={kind:"file",proto:e,deprecated:(r=(n=e.options)===null||n===void 0?void 0:n.deprecated)!==null&&r!==void 0&&r,edition:$n(e),name:e.name.replace(/\.proto$/,""),dependencies:xn(e,t),enums:[],messages:[],extensions:[],services:[],toString:()=>`file ${e.name}`},a=new Map,s={get:i=>a.get(i),add(i){var l;I(((l=i.proto.options)===null||l===void 0?void 0:l.mapEntry)===!0),a.set(i.typeName,i)}};for(const i of e.enumType)Xt(i,o,void 0,t);for(const i of e.messageType)Wt(i,o,void 0,t,s);for(const i of e.service)Un(i,o,t);ge(o,t);for(const i of a.values())ye(i,t,s);for(const i of o.messages)ye(i,t,s),ge(i,t);t.addFile(o,!0)}function ge(e,t){switch(e.kind){case"file":for(const n of e.proto.extension){const r=he(n,e,t);e.extensions.push(r),t.add(r)}break;case"message":for(const n of e.proto.extension){const r=he(n,e,t);e.nestedExtensions.push(r),t.add(r)}for(const n of e.nestedMessages)ge(n,t)}}function ye(e,t,n){const r=e.proto.oneofDecl.map(a=>function(s,i){return{kind:"oneof",proto:s,deprecated:!1,parent:i,fields:[],name:s.name,localName:C(H(s.name)),toString(){return`oneof ${i.typeName}.${this.name}`}}}(a,e)),o=new Set;for(const a of e.proto.field){const s=Yn(a,r),i=he(a,e,t,s,n);e.fields.push(i),e.field[i.localName]=i,s===void 0?e.members.push(i):(s.fields.push(i),o.has(s)||(o.add(s),e.members.push(s)))}for(const a of r.filter(s=>o.has(s)))e.oneofs.push(a);for(const a of e.nestedMessages)ye(a,t,n)}function Xt(e,t,n,r){var o,a,s,i,l;const u=function(d,p){const f=(g=d,(g.substring(0,1)+g.substring(1).replace(/[A-Z]/g,y=>"_"+y)).toLowerCase()+"_");var g;for(const y of p){if(!y.name.toLowerCase().startsWith(f))return;const N=y.name.substring(f.length);if(N.length==0||/^\d/.test(N))return}return f}(e.name,e.value),m={kind:"enum",proto:e,deprecated:(a=(o=e.options)===null||o===void 0?void 0:o.deprecated)!==null&&a!==void 0&&a,file:t,parent:n,open:!0,name:e.name,typeName:ne(e,n,t),value:{},values:[],sharedPrefix:u,toString(){return`enum ${this.typeName}`}};m.open=function(d){var p;return Ln==x("enumType",{proto:d.proto,parent:(p=d.parent)!==null&&p!==void 0?p:d.file})}(m),r.add(m);for(const d of e.value){const p=d.name;m.values.push(m.value[d.number]={kind:"enum_value",proto:d,deprecated:(i=(s=e.options)===null||s===void 0?void 0:s.deprecated)!==null&&i!==void 0&&i,parent:m,name:p,localName:C(u==null?p:p.substring(u.length)),number:d.number,toString:()=>`enum value ${m.typeName}.${p}`})}((l=n==null?void 0:n.nestedEnums)!==null&&l!==void 0?l:t.enums).push(m)}function Wt(e,t,n,r,o){var a,s,i,l;const u={kind:"message",proto:e,deprecated:(s=(a=e.options)===null||a===void 0?void 0:a.deprecated)!==null&&s!==void 0&&s,file:t,parent:n,name:e.name,typeName:ne(e,n,t),fields:[],field:{},oneofs:[],members:[],nestedEnums:[],nestedMessages:[],nestedExtensions:[],toString(){return`message ${this.typeName}`}};((i=e.options)===null||i===void 0?void 0:i.mapEntry)===!0?o.add(u):(((l=n==null?void 0:n.nestedMessages)!==null&&l!==void 0?l:t.messages).push(u),r.add(u));for(const m of e.enumType)Xt(m,t,u,r);for(const m of e.nestedType)Wt(m,t,u,r,o)}function Un(e,t,n){var r,o;const a={kind:"service",proto:e,deprecated:(o=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&o!==void 0&&o,file:t,name:e.name,typeName:ne(e,void 0,t),methods:[],method:{},toString(){return`service ${this.typeName}`}};t.services.push(a),n.add(a);for(const s of e.method){const i=Pn(s,a,n);a.methods.push(i),a.method[i.localName]=i}}function Pn(e,t,n){var r,o,a,s;let i;i=e.clientStreaming&&e.serverStreaming?"bidi_streaming":e.clientStreaming?"client_streaming":e.serverStreaming?"server_streaming":"unary";const l=n.getMessage(S(e.inputType)),u=n.getMessage(S(e.outputType));I(l,`invalid MethodDescriptorProto: input_type ${e.inputType} not found`),I(u,`invalid MethodDescriptorProto: output_type ${e.inputType} not found`);const m=e.name;return{kind:"rpc",proto:e,deprecated:(o=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&o!==void 0&&o,parent:t,name:m,localName:C(m.length?C(m[0].toLowerCase()+m.substring(1)):m),methodKind:i,input:l,output:u,idempotency:(s=(a=e.options)===null||a===void 0?void 0:a.idempotencyLevel)!==null&&s!==void 0?s:wn,toString:()=>`rpc ${t.typeName}.${m}`}}function he(e,t,n,r,o){var a,s,i;const l=o===void 0,u={kind:"field",proto:e,deprecated:(s=(a=e.options)===null||a===void 0?void 0:a.deprecated)!==null&&s!==void 0&&s,name:e.name,number:e.number,scalar:void 0,message:void 0,enum:void 0,presence:Gn(e,r,l,t),listKind:void 0,mapKind:void 0,mapKey:void 0,delimitedEncoding:void 0,packed:void 0,longAsString:!1,getDefaultValue:void 0};if(l){const f=t.kind=="file"?t:t.file,g=t.kind=="file"?void 0:t,y=ne(e,g,f);u.kind="extension",u.file=f,u.parent=g,u.oneof=void 0,u.typeName=y,u.jsonName=`[${y}]`,u.toString=()=>`extension ${y}`;const N=n.getMessage(S(e.extendee));I(N,`invalid FieldDescriptorProto: extendee ${e.extendee} not found`),u.extendee=N}else{const f=t;I(f.kind=="message"),u.parent=f,u.oneof=r,u.localName=r?H(e.name):C(H(e.name)),u.jsonName=e.jsonName,u.toString=()=>`field ${f.typeName}.${e.name}`}const m=e.label,d=e.type,p=(i=e.options)===null||i===void 0?void 0:i.jstype;if(m===be){const f=d==G?o==null?void 0:o.get(S(e.typeName)):void 0;if(f){u.fieldKind="map";const{key:g,value:y}=function(N){const v=N.fields.find(R=>R.number===1),D=N.fields.find(R=>R.number===2);return I(v&&v.fieldKind=="scalar"&&v.scalar!=c.BYTES&&v.scalar!=c.FLOAT&&v.scalar!=c.DOUBLE&&D&&D.fieldKind!="list"&&D.fieldKind!="map"),{key:v,value:D}}(f);return u.mapKey=g.scalar,u.mapKind=y.fieldKind,u.message=y.message,u.delimitedEncoding=!1,u.enum=y.enum,u.scalar=y.scalar,u}switch(u.fieldKind="list",d){case G:case W:u.listKind="message",u.message=n.getMessage(S(e.typeName)),I(u.message),u.delimitedEncoding=He(e,t);break;case Ze:u.listKind="enum",u.enum=n.getEnum(S(e.typeName)),I(u.enum);break;default:u.listKind="scalar",u.scalar=d,u.longAsString=p==Je}return u.packed=function(g,y){if(g.label!=be)return!1;switch(g.type){case _n:case Sn:case W:case G:return!1}const N=g.options;return N&&V(N,"packed")?N.packed:kn==x("repeatedFieldEncoding",{proto:g,parent:y})}(e,t),u}switch(d){case G:case W:u.fieldKind="message",u.message=n.getMessage(S(e.typeName)),I(u.message,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),u.delimitedEncoding=He(e,t),u.getDefaultValue=()=>{};break;case Ze:{const f=n.getEnum(S(e.typeName));I(f!==void 0,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),u.fieldKind="enum",u.enum=n.getEnum(S(e.typeName)),u.getDefaultValue=()=>V(e,"defaultValue")?function(g,y){const N=g.values.find(v=>v.name===y);if(!N)throw new Error(`cannot parse ${g} default value: ${y}`);return N.number}(f,e.defaultValue):void 0;break}default:u.fieldKind="scalar",u.scalar=d,u.longAsString=p==Je,u.getDefaultValue=()=>V(e,"defaultValue")?Tn(d,e.defaultValue):void 0}return u}function $n(e){switch(e.syntax){case"":case"proto2":return vn;case"proto3":return In;case"editions":if(e.edition in jt)return e.edition;throw new Error(`${e.name}: unsupported edition`);default:throw new Error(`${e.name}: unsupported syntax "${e.syntax}"`)}}function xn(e,t){return e.dependency.map(n=>{const r=t.getFile(n);if(!r)throw new Error(`Cannot find ${n}, imported by ${e.name}`);return r})}function ne(e,t,n){let r;return r=t?`${t.typeName}.${e.name}`:n.proto.package.length>0?`${n.proto.package}.${e.name}`:`${e.name}`,r}function S(e){return e.startsWith(".")?e.substring(1):e}function Yn(e,t){if(!V(e,"oneofIndex")||e.proto3Optional)return;const n=t[e.oneofIndex];return I(n,`invalid FieldDescriptorProto: oneof #${e.oneofIndex} for field #${e.number} not found`),n}function Gn(e,t,n,r){return e.label==On?Dn:e.label==be?An:t||e.proto3Optional||e.type==G||n?Rn:x("fieldPresence",{proto:e,parent:r})}function He(e,t){return e.type==W||Fn==x("messageEncoding",{proto:e,parent:t})}function x(e,t){var n,r;const o=(n=t.proto.options)===null||n===void 0?void 0:n.features;if(o){const a=o[e];if(a!=0)return a}if("kind"in t){if(t.kind=="message")return x(e,(r=t.parent)!==null&&r!==void 0?r:t.file);const a=jt[t.edition];if(!a)throw new Error(`feature default for edition ${t.edition} not found`);return a[e]}return x(e,t.parent)}function I(e,t){if(!e)throw new Error(t)}function Vn(e){const t=function(n){return Object.assign(Object.create({syntax:"",edition:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FileDescriptorProto",dependency:[],publicDependency:[],weakDependency:[],service:[],extension:[]},n),{messageType:n.messageType.map(Zt),enumType:n.enumType.map(Jt)}))}(e);return t.messageType.forEach(ke),Bt(t,()=>{}).getFile(t.name)}function Zt(e){var t,n,r,o,a,s,i,l;return{$typeName:"google.protobuf.DescriptorProto",name:e.name,field:(n=(t=e.field)===null||t===void 0?void 0:t.map(Cn))!==null&&n!==void 0?n:[],extension:[],nestedType:(o=(r=e.nestedType)===null||r===void 0?void 0:r.map(Zt))!==null&&o!==void 0?o:[],enumType:(s=(a=e.enumType)===null||a===void 0?void 0:a.map(Jt))!==null&&s!==void 0?s:[],extensionRange:(l=(i=e.extensionRange)===null||i===void 0?void 0:i.map(u=>Object.assign({$typeName:"google.protobuf.DescriptorProto.ExtensionRange"},u)))!==null&&l!==void 0?l:[],oneofDecl:[],reservedRange:[],reservedName:[]}}function Cn(e){return Object.assign(Object.create({label:1,typeName:"",extendee:"",defaultValue:"",oneofIndex:0,jsonName:"",proto3Optional:!1}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldDescriptorProto"},e),{options:e.options?Mn(e.options):void 0}))}function Mn(e){var t,n,r;return Object.assign(Object.create({ctype:0,packed:!1,jstype:0,lazy:!1,unverifiedLazy:!1,deprecated:!1,weak:!1,debugRedact:!1,retention:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldOptions"},e),{targets:(t=e.targets)!==null&&t!==void 0?t:[],editionDefaults:(r=(n=e.editionDefaults)===null||n===void 0?void 0:n.map(a=>Object.assign({$typeName:"google.protobuf.FieldOptions.EditionDefault"},a)))!==null&&r!==void 0?r:[],uninterpretedOption:[]}))}function Jt(e){return{$typeName:"google.protobuf.EnumDescriptorProto",name:e.name,reservedName:[],reservedRange:[],value:e.value.map(t=>Object.assign({$typeName:"google.protobuf.EnumValueDescriptorProto"},t))}}function M(e,t,...n){return n.reduce((r,o)=>r.nestedMessages[o],e.messages[t])}const Kn=M(Vn({name:"google/protobuf/descriptor.proto",package:"google.protobuf",messageType:[{name:"FileDescriptorSet",field:[{name:"file",number:1,type:11,label:3,typeName:".google.protobuf.FileDescriptorProto"}],extensionRange:[{start:536e6,end:536000001}]},{name:"FileDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"package",number:2,type:9,label:1},{name:"dependency",number:3,type:9,label:3},{name:"public_dependency",number:10,type:5,label:3},{name:"weak_dependency",number:11,type:5,label:3},{name:"message_type",number:4,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:5,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"service",number:6,type:11,label:3,typeName:".google.protobuf.ServiceDescriptorProto"},{name:"extension",number:7,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FileOptions"},{name:"source_code_info",number:9,type:11,label:1,typeName:".google.protobuf.SourceCodeInfo"},{name:"syntax",number:12,type:9,label:1},{name:"edition",number:14,type:14,label:1,typeName:".google.protobuf.Edition"}]},{name:"DescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"field",number:2,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"extension",number:6,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"nested_type",number:3,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"extension_range",number:5,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ExtensionRange"},{name:"oneof_decl",number:8,type:11,label:3,typeName:".google.protobuf.OneofDescriptorProto"},{name:"options",number:7,type:11,label:1,typeName:".google.protobuf.MessageOptions"},{name:"reserved_range",number:9,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ReservedRange"},{name:"reserved_name",number:10,type:9,label:3}],nestedType:[{name:"ExtensionRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ExtensionRangeOptions"}]},{name:"ReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"ExtensionRangeOptions",field:[{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"},{name:"declaration",number:2,type:11,label:3,typeName:".google.protobuf.ExtensionRangeOptions.Declaration",options:{retention:2}},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"verification",number:3,type:14,label:1,typeName:".google.protobuf.ExtensionRangeOptions.VerificationState",defaultValue:"UNVERIFIED",options:{retention:2}}],nestedType:[{name:"Declaration",field:[{name:"number",number:1,type:5,label:1},{name:"full_name",number:2,type:9,label:1},{name:"type",number:3,type:9,label:1},{name:"reserved",number:5,type:8,label:1},{name:"repeated",number:6,type:8,label:1}]}],enumType:[{name:"VerificationState",value:[{name:"DECLARATION",number:0},{name:"UNVERIFIED",number:1}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:3,type:5,label:1},{name:"label",number:4,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Label"},{name:"type",number:5,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Type"},{name:"type_name",number:6,type:9,label:1},{name:"extendee",number:2,type:9,label:1},{name:"default_value",number:7,type:9,label:1},{name:"oneof_index",number:9,type:5,label:1},{name:"json_name",number:10,type:9,label:1},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FieldOptions"},{name:"proto3_optional",number:17,type:8,label:1}],enumType:[{name:"Type",value:[{name:"TYPE_DOUBLE",number:1},{name:"TYPE_FLOAT",number:2},{name:"TYPE_INT64",number:3},{name:"TYPE_UINT64",number:4},{name:"TYPE_INT32",number:5},{name:"TYPE_FIXED64",number:6},{name:"TYPE_FIXED32",number:7},{name:"TYPE_BOOL",number:8},{name:"TYPE_STRING",number:9},{name:"TYPE_GROUP",number:10},{name:"TYPE_MESSAGE",number:11},{name:"TYPE_BYTES",number:12},{name:"TYPE_UINT32",number:13},{name:"TYPE_ENUM",number:14},{name:"TYPE_SFIXED32",number:15},{name:"TYPE_SFIXED64",number:16},{name:"TYPE_SINT32",number:17},{name:"TYPE_SINT64",number:18}]},{name:"Label",value:[{name:"LABEL_OPTIONAL",number:1},{name:"LABEL_REPEATED",number:3},{name:"LABEL_REQUIRED",number:2}]}]},{name:"OneofDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"options",number:2,type:11,label:1,typeName:".google.protobuf.OneofOptions"}]},{name:"EnumDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"value",number:2,type:11,label:3,typeName:".google.protobuf.EnumValueDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumOptions"},{name:"reserved_range",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto.EnumReservedRange"},{name:"reserved_name",number:5,type:9,label:3}],nestedType:[{name:"EnumReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"EnumValueDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumValueOptions"}]},{name:"ServiceDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"method",number:2,type:11,label:3,typeName:".google.protobuf.MethodDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ServiceOptions"}]},{name:"MethodDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"input_type",number:2,type:9,label:1},{name:"output_type",number:3,type:9,label:1},{name:"options",number:4,type:11,label:1,typeName:".google.protobuf.MethodOptions"},{name:"client_streaming",number:5,type:8,label:1,defaultValue:"false"},{name:"server_streaming",number:6,type:8,label:1,defaultValue:"false"}]},{name:"FileOptions",field:[{name:"java_package",number:1,type:9,label:1},{name:"java_outer_classname",number:8,type:9,label:1},{name:"java_multiple_files",number:10,type:8,label:1,defaultValue:"false"},{name:"java_generate_equals_and_hash",number:20,type:8,label:1,options:{deprecated:!0}},{name:"java_string_check_utf8",number:27,type:8,label:1,defaultValue:"false"},{name:"optimize_for",number:9,type:14,label:1,typeName:".google.protobuf.FileOptions.OptimizeMode",defaultValue:"SPEED"},{name:"go_package",number:11,type:9,label:1},{name:"cc_generic_services",number:16,type:8,label:1,defaultValue:"false"},{name:"java_generic_services",number:17,type:8,label:1,defaultValue:"false"},{name:"py_generic_services",number:18,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:23,type:8,label:1,defaultValue:"false"},{name:"cc_enable_arenas",number:31,type:8,label:1,defaultValue:"true"},{name:"objc_class_prefix",number:36,type:9,label:1},{name:"csharp_namespace",number:37,type:9,label:1},{name:"swift_prefix",number:39,type:9,label:1},{name:"php_class_prefix",number:40,type:9,label:1},{name:"php_namespace",number:41,type:9,label:1},{name:"php_metadata_namespace",number:44,type:9,label:1},{name:"ruby_package",number:45,type:9,label:1},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"OptimizeMode",value:[{name:"SPEED",number:1},{name:"CODE_SIZE",number:2},{name:"LITE_RUNTIME",number:3}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"MessageOptions",field:[{name:"message_set_wire_format",number:1,type:8,label:1,defaultValue:"false"},{name:"no_standard_descriptor_accessor",number:2,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"map_entry",number:7,type:8,label:1},{name:"deprecated_legacy_json_field_conflicts",number:11,type:8,label:1,options:{deprecated:!0}},{name:"features",number:12,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldOptions",field:[{name:"ctype",number:1,type:14,label:1,typeName:".google.protobuf.FieldOptions.CType",defaultValue:"STRING"},{name:"packed",number:2,type:8,label:1},{name:"jstype",number:6,type:14,label:1,typeName:".google.protobuf.FieldOptions.JSType",defaultValue:"JS_NORMAL"},{name:"lazy",number:5,type:8,label:1,defaultValue:"false"},{name:"unverified_lazy",number:15,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"weak",number:10,type:8,label:1,defaultValue:"false"},{name:"debug_redact",number:16,type:8,label:1,defaultValue:"false"},{name:"retention",number:17,type:14,label:1,typeName:".google.protobuf.FieldOptions.OptionRetention"},{name:"targets",number:19,type:14,label:3,typeName:".google.protobuf.FieldOptions.OptionTargetType"},{name:"edition_defaults",number:20,type:11,label:3,typeName:".google.protobuf.FieldOptions.EditionDefault"},{name:"features",number:21,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"feature_support",number:22,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],nestedType:[{name:"EditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"value",number:2,type:9,label:1}]},{name:"FeatureSupport",field:[{name:"edition_introduced",number:1,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"edition_deprecated",number:2,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"deprecation_warning",number:3,type:9,label:1},{name:"edition_removed",number:4,type:14,label:1,typeName:".google.protobuf.Edition"}]}],enumType:[{name:"CType",value:[{name:"STRING",number:0},{name:"CORD",number:1},{name:"STRING_PIECE",number:2}]},{name:"JSType",value:[{name:"JS_NORMAL",number:0},{name:"JS_STRING",number:1},{name:"JS_NUMBER",number:2}]},{name:"OptionRetention",value:[{name:"RETENTION_UNKNOWN",number:0},{name:"RETENTION_RUNTIME",number:1},{name:"RETENTION_SOURCE",number:2}]},{name:"OptionTargetType",value:[{name:"TARGET_TYPE_UNKNOWN",number:0},{name:"TARGET_TYPE_FILE",number:1},{name:"TARGET_TYPE_EXTENSION_RANGE",number:2},{name:"TARGET_TYPE_MESSAGE",number:3},{name:"TARGET_TYPE_FIELD",number:4},{name:"TARGET_TYPE_ONEOF",number:5},{name:"TARGET_TYPE_ENUM",number:6},{name:"TARGET_TYPE_ENUM_ENTRY",number:7},{name:"TARGET_TYPE_SERVICE",number:8},{name:"TARGET_TYPE_METHOD",number:9}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"OneofOptions",field:[{name:"features",number:1,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumOptions",field:[{name:"allow_alias",number:2,type:8,label:1},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"deprecated_legacy_json_field_conflicts",number:6,type:8,label:1,options:{deprecated:!0}},{name:"features",number:7,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumValueOptions",field:[{name:"deprecated",number:1,type:8,label:1,defaultValue:"false"},{name:"features",number:2,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"debug_redact",number:3,type:8,label:1,defaultValue:"false"},{name:"feature_support",number:4,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"ServiceOptions",field:[{name:"features",number:34,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"MethodOptions",field:[{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"idempotency_level",number:34,type:14,label:1,typeName:".google.protobuf.MethodOptions.IdempotencyLevel",defaultValue:"IDEMPOTENCY_UNKNOWN"},{name:"features",number:35,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"IdempotencyLevel",value:[{name:"IDEMPOTENCY_UNKNOWN",number:0},{name:"NO_SIDE_EFFECTS",number:1},{name:"IDEMPOTENT",number:2}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"UninterpretedOption",field:[{name:"name",number:2,type:11,label:3,typeName:".google.protobuf.UninterpretedOption.NamePart"},{name:"identifier_value",number:3,type:9,label:1},{name:"positive_int_value",number:4,type:4,label:1},{name:"negative_int_value",number:5,type:3,label:1},{name:"double_value",number:6,type:1,label:1},{name:"string_value",number:7,type:12,label:1},{name:"aggregate_value",number:8,type:9,label:1}],nestedType:[{name:"NamePart",field:[{name:"name_part",number:1,type:9,label:2},{name:"is_extension",number:2,type:8,label:2}]}]},{name:"FeatureSet",field:[{name:"field_presence",number:1,type:14,label:1,typeName:".google.protobuf.FeatureSet.FieldPresence",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPLICIT",edition:900},{value:"IMPLICIT",edition:999},{value:"EXPLICIT",edition:1e3}]}},{name:"enum_type",number:2,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnumType",options:{retention:1,targets:[6,1],editionDefaults:[{value:"CLOSED",edition:900},{value:"OPEN",edition:999}]}},{name:"repeated_field_encoding",number:3,type:14,label:1,typeName:".google.protobuf.FeatureSet.RepeatedFieldEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPANDED",edition:900},{value:"PACKED",edition:999}]}},{name:"utf8_validation",number:4,type:14,label:1,typeName:".google.protobuf.FeatureSet.Utf8Validation",options:{retention:1,targets:[4,1],editionDefaults:[{value:"NONE",edition:900},{value:"VERIFY",edition:999}]}},{name:"message_encoding",number:5,type:14,label:1,typeName:".google.protobuf.FeatureSet.MessageEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"LENGTH_PREFIXED",edition:900}]}},{name:"json_format",number:6,type:14,label:1,typeName:".google.protobuf.FeatureSet.JsonFormat",options:{retention:1,targets:[3,6,1],editionDefaults:[{value:"LEGACY_BEST_EFFORT",edition:900},{value:"ALLOW",edition:999}]}},{name:"enforce_naming_style",number:7,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnforceNamingStyle",options:{retention:2,targets:[1,2,3,4,5,6,7,8,9],editionDefaults:[{value:"STYLE_LEGACY",edition:900},{value:"STYLE2024",edition:1001}]}}],enumType:[{name:"FieldPresence",value:[{name:"FIELD_PRESENCE_UNKNOWN",number:0},{name:"EXPLICIT",number:1},{name:"IMPLICIT",number:2},{name:"LEGACY_REQUIRED",number:3}]},{name:"EnumType",value:[{name:"ENUM_TYPE_UNKNOWN",number:0},{name:"OPEN",number:1},{name:"CLOSED",number:2}]},{name:"RepeatedFieldEncoding",value:[{name:"REPEATED_FIELD_ENCODING_UNKNOWN",number:0},{name:"PACKED",number:1},{name:"EXPANDED",number:2}]},{name:"Utf8Validation",value:[{name:"UTF8_VALIDATION_UNKNOWN",number:0},{name:"VERIFY",number:2},{name:"NONE",number:3}]},{name:"MessageEncoding",value:[{name:"MESSAGE_ENCODING_UNKNOWN",number:0},{name:"LENGTH_PREFIXED",number:1},{name:"DELIMITED",number:2}]},{name:"JsonFormat",value:[{name:"JSON_FORMAT_UNKNOWN",number:0},{name:"ALLOW",number:1},{name:"LEGACY_BEST_EFFORT",number:2}]},{name:"EnforceNamingStyle",value:[{name:"ENFORCE_NAMING_STYLE_UNKNOWN",number:0},{name:"STYLE2024",number:1},{name:"STYLE_LEGACY",number:2}]}],extensionRange:[{start:1e3,end:9995},{start:9995,end:1e4},{start:1e4,end:10001}]},{name:"FeatureSetDefaults",field:[{name:"defaults",number:1,type:11,label:3,typeName:".google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault"},{name:"minimum_edition",number:4,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"maximum_edition",number:5,type:14,label:1,typeName:".google.protobuf.Edition"}],nestedType:[{name:"FeatureSetEditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"overridable_features",number:4,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"fixed_features",number:5,type:11,label:1,typeName:".google.protobuf.FeatureSet"}]}]},{name:"SourceCodeInfo",field:[{name:"location",number:1,type:11,label:3,typeName:".google.protobuf.SourceCodeInfo.Location"}],nestedType:[{name:"Location",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"span",number:2,type:5,label:3,options:{packed:!0}},{name:"leading_comments",number:3,type:9,label:1},{name:"trailing_comments",number:4,type:9,label:1},{name:"leading_detached_comments",number:6,type:9,label:3}]}],extensionRange:[{start:536e6,end:536000001}]},{name:"GeneratedCodeInfo",field:[{name:"annotation",number:1,type:11,label:3,typeName:".google.protobuf.GeneratedCodeInfo.Annotation"}],nestedType:[{name:"Annotation",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"source_file",number:2,type:9,label:1},{name:"begin",number:3,type:5,label:1},{name:"end",number:4,type:5,label:1},{name:"semantic",number:5,type:14,label:1,typeName:".google.protobuf.GeneratedCodeInfo.Annotation.Semantic"}],enumType:[{name:"Semantic",value:[{name:"NONE",number:0},{name:"SET",number:1},{name:"ALIAS",number:2}]}]}]}],enumType:[{name:"Edition",value:[{name:"EDITION_UNKNOWN",number:0},{name:"EDITION_LEGACY",number:900},{name:"EDITION_PROTO2",number:998},{name:"EDITION_PROTO3",number:999},{name:"EDITION_2023",number:1e3},{name:"EDITION_2024",number:1001},{name:"EDITION_1_TEST_ONLY",number:1},{name:"EDITION_2_TEST_ONLY",number:2},{name:"EDITION_99997_TEST_ONLY",number:99997},{name:"EDITION_99998_TEST_ONLY",number:99998},{name:"EDITION_99999_TEST_ONLY",number:99999},{name:"EDITION_MAX",number:2147483647}]}]}),1);var ze,Qe,et,tt,nt,rt,at,ot,st,it,ut,lt,ct,mt,dt,pt,ft,bt;(function(e){e[e.DECLARATION=0]="DECLARATION",e[e.UNVERIFIED=1]="UNVERIFIED"})(ze||(ze={})),function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.GROUP=10]="GROUP",e[e.MESSAGE=11]="MESSAGE",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.ENUM=14]="ENUM",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"}(Qe||(Qe={})),function(e){e[e.OPTIONAL=1]="OPTIONAL",e[e.REPEATED=3]="REPEATED",e[e.REQUIRED=2]="REQUIRED"}(et||(et={})),function(e){e[e.SPEED=1]="SPEED",e[e.CODE_SIZE=2]="CODE_SIZE",e[e.LITE_RUNTIME=3]="LITE_RUNTIME"}(tt||(tt={})),function(e){e[e.STRING=0]="STRING",e[e.CORD=1]="CORD",e[e.STRING_PIECE=2]="STRING_PIECE"}(nt||(nt={})),function(e){e[e.JS_NORMAL=0]="JS_NORMAL",e[e.JS_STRING=1]="JS_STRING",e[e.JS_NUMBER=2]="JS_NUMBER"}(rt||(rt={})),function(e){e[e.RETENTION_UNKNOWN=0]="RETENTION_UNKNOWN",e[e.RETENTION_RUNTIME=1]="RETENTION_RUNTIME",e[e.RETENTION_SOURCE=2]="RETENTION_SOURCE"}(at||(at={})),function(e){e[e.TARGET_TYPE_UNKNOWN=0]="TARGET_TYPE_UNKNOWN",e[e.TARGET_TYPE_FILE=1]="TARGET_TYPE_FILE",e[e.TARGET_TYPE_EXTENSION_RANGE=2]="TARGET_TYPE_EXTENSION_RANGE",e[e.TARGET_TYPE_MESSAGE=3]="TARGET_TYPE_MESSAGE",e[e.TARGET_TYPE_FIELD=4]="TARGET_TYPE_FIELD",e[e.TARGET_TYPE_ONEOF=5]="TARGET_TYPE_ONEOF",e[e.TARGET_TYPE_ENUM=6]="TARGET_TYPE_ENUM",e[e.TARGET_TYPE_ENUM_ENTRY=7]="TARGET_TYPE_ENUM_ENTRY",e[e.TARGET_TYPE_SERVICE=8]="TARGET_TYPE_SERVICE",e[e.TARGET_TYPE_METHOD=9]="TARGET_TYPE_METHOD"}(ot||(ot={})),function(e){e[e.IDEMPOTENCY_UNKNOWN=0]="IDEMPOTENCY_UNKNOWN",e[e.NO_SIDE_EFFECTS=1]="NO_SIDE_EFFECTS",e[e.IDEMPOTENT=2]="IDEMPOTENT"}(st||(st={})),function(e){e[e.FIELD_PRESENCE_UNKNOWN=0]="FIELD_PRESENCE_UNKNOWN",e[e.EXPLICIT=1]="EXPLICIT",e[e.IMPLICIT=2]="IMPLICIT",e[e.LEGACY_REQUIRED=3]="LEGACY_REQUIRED"}(it||(it={})),function(e){e[e.ENUM_TYPE_UNKNOWN=0]="ENUM_TYPE_UNKNOWN",e[e.OPEN=1]="OPEN",e[e.CLOSED=2]="CLOSED"}(ut||(ut={})),function(e){e[e.REPEATED_FIELD_ENCODING_UNKNOWN=0]="REPEATED_FIELD_ENCODING_UNKNOWN",e[e.PACKED=1]="PACKED",e[e.EXPANDED=2]="EXPANDED"}(lt||(lt={})),function(e){e[e.UTF8_VALIDATION_UNKNOWN=0]="UTF8_VALIDATION_UNKNOWN",e[e.VERIFY=2]="VERIFY",e[e.NONE=3]="NONE"}(ct||(ct={})),function(e){e[e.MESSAGE_ENCODING_UNKNOWN=0]="MESSAGE_ENCODING_UNKNOWN",e[e.LENGTH_PREFIXED=1]="LENGTH_PREFIXED",e[e.DELIMITED=2]="DELIMITED"}(mt||(mt={})),function(e){e[e.JSON_FORMAT_UNKNOWN=0]="JSON_FORMAT_UNKNOWN",e[e.ALLOW=1]="ALLOW",e[e.LEGACY_BEST_EFFORT=2]="LEGACY_BEST_EFFORT"}(dt||(dt={})),function(e){e[e.ENFORCE_NAMING_STYLE_UNKNOWN=0]="ENFORCE_NAMING_STYLE_UNKNOWN",e[e.STYLE2024=1]="STYLE2024",e[e.STYLE_LEGACY=2]="STYLE_LEGACY"}(pt||(pt={})),function(e){e[e.NONE=0]="NONE",e[e.SET=1]="SET",e[e.ALIAS=2]="ALIAS"}(ft||(ft={})),function(e){e[e.EDITION_UNKNOWN=0]="EDITION_UNKNOWN",e[e.EDITION_LEGACY=900]="EDITION_LEGACY",e[e.EDITION_PROTO2=998]="EDITION_PROTO2",e[e.EDITION_PROTO3=999]="EDITION_PROTO3",e[e.EDITION_2023=1e3]="EDITION_2023",e[e.EDITION_2024=1001]="EDITION_2024",e[e.EDITION_1_TEST_ONLY=1]="EDITION_1_TEST_ONLY",e[e.EDITION_2_TEST_ONLY=2]="EDITION_2_TEST_ONLY",e[e.EDITION_99997_TEST_ONLY=99997]="EDITION_99997_TEST_ONLY",e[e.EDITION_99998_TEST_ONLY=99998]="EDITION_99998_TEST_ONLY",e[e.EDITION_99999_TEST_ONLY=99999]="EDITION_99999_TEST_ONLY",e[e.EDITION_MAX=2147483647]="EDITION_MAX"}(bt||(bt={}));const gt={readUnknownFields:!0};function Bn(e,t,n){const r=_(e,void 0,!1);return qt(r,new Lt(t),function(o){return o?Object.assign(Object.assign({},gt),o):gt}(n),!1,t.byteLength),r.message}function qt(e,t,n,r,o){var a;const s=r?t.len:t.pos+o;let i,l;const u=(a=e.getUnknown())!==null&&a!==void 0?a:[];for(;t.pos<s&&([i,l]=t.tag(),!r||l!=h.EndGroup);){const m=e.findNumber(i);if(m)jn(e,t,m,l,n);else{const d=t.skip(l,i);n.readUnknownFields&&u.push({no:i,wireType:l,data:d})}}if(r&&(l!=h.EndGroup||i!==o))throw new Error("invalid end group tag");u.length>0&&e.setUnknown(u)}function jn(e,t,n,r,o){switch(n.fieldKind){case"scalar":e.set(n,P(t,n.scalar));break;case"enum":e.set(n,P(t,c.INT32));break;case"message":e.set(n,ce(t,o,n,e.get(n)));break;case"list":(function(a,s,i,l){var u;const m=i.field();if(m.listKind==="message")return void i.add(ce(a,l,m));const d=(u=m.scalar)!==null&&u!==void 0?u:c.INT32;if(!(s==h.LengthDelimited&&d!=c.STRING&&d!=c.BYTES))return void i.add(P(a,d));const f=a.uint32()+a.pos;for(;a.pos<f;)i.add(P(a,d))})(t,r,e.get(n),o);break;case"map":(function(a,s,i){const l=s.field();let u,m;const d=a.pos+a.uint32();for(;a.pos<d;){const[p]=a.tag();switch(p){case 1:u=P(a,l.mapKey);break;case 2:switch(l.mapKind){case"scalar":m=P(a,l.scalar);break;case"enum":m=a.int32();break;case"message":m=ce(a,i,l)}}}if(u===void 0&&(u=L(l.mapKey,!1)),m===void 0)switch(l.mapKind){case"scalar":m=L(l.scalar,!1);break;case"enum":m=l.enum.values[0].number;break;case"message":m=_(l.message,void 0,!1)}s.set(u,m)})(t,e.get(n),o)}}function ce(e,t,n,r){const o=n.delimitedEncoding,a=r??_(n.message,void 0,!1);return qt(a,e,t,o,o?n.number:e.uint32()),a}function P(e,t){switch(t){case c.STRING:return e.string();case c.BOOL:return e.bool();case c.DOUBLE:return e.double();case c.FLOAT:return e.float();case c.INT32:return e.int32();case c.INT64:return e.int64();case c.UINT64:return e.uint64();case c.FIXED64:return e.fixed64();case c.BYTES:return e.bytes();case c.FIXED32:return e.fixed32();case c.SFIXED32:return e.sfixed32();case c.SFIXED64:return e.sfixed64();case c.SINT64:return e.sint64();case c.UINT32:return e.uint32();case c.SINT32:return e.sint32()}}function Fe(e,t){const n=Bn(Kn,Kt(e));return n.messageType.forEach(ke),n.dependency=[],Bt(n,r=>{}).getFile(n.name)}const Xn=M(Fe("Chlnb29nbGUvcHJvdG9idWYvYW55LnByb3RvEg9nb29nbGUucHJvdG9idWYiJgoDQW55EhAKCHR5cGVfdXJsGAEgASgJEg0KBXZhbHVlGAIgASgMQnYKE2NvbS5nb29nbGUucHJvdG9idWZCCEFueVByb3RvUAFaLGdvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL2FueXBiogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),0),Wn=3,yt={writeUnknownFields:!0};function Zn(e,t,n){return z(new Ft,function(r){return r?Object.assign(Object.assign({},yt),r):yt}(n),_(e,t)).finish()}function z(e,t,n){var r;for(const o of n.sortedFields)if(n.isSet(o))Ht(e,t,n,o);else if(o.presence==Wn)throw new Error(`cannot encode ${o} to binary: required field not set`);if(t.writeUnknownFields)for(const{no:o,wireType:a,data:s}of(r=n.getUnknown())!==null&&r!==void 0?r:[])e.tag(o,a).raw(s);return e}function Ht(e,t,n,r){var o;switch(r.fieldKind){case"scalar":case"enum":Q(e,n.desc.typeName,r.name,(o=r.scalar)!==null&&o!==void 0?o:c.INT32,r.number,n.get(r));break;case"list":(function(a,s,i,l){var u;if(i.listKind=="message"){for(const d of l)ht(a,s,i,d);return}const m=(u=i.scalar)!==null&&u!==void 0?u:c.INT32;if(i.packed){if(!l.size)return;a.tag(i.number,h.LengthDelimited).fork();for(const d of l)zt(a,i.parent.typeName,i.name,m,d);return void a.join()}for(const d of l)Q(a,i.parent.typeName,i.name,m,i.number,d)})(e,t,r,n.get(r));break;case"message":ht(e,t,r,n.get(r));break;case"map":for(const[a,s]of n.get(r))Jn(e,t,r,a,s)}}function Q(e,t,n,r,o,a){zt(e.tag(o,function(s){switch(s){case c.BYTES:case c.STRING:return h.LengthDelimited;case c.DOUBLE:case c.FIXED64:case c.SFIXED64:return h.Bit64;case c.FIXED32:case c.SFIXED32:case c.FLOAT:return h.Bit32;default:return h.Varint}}(r)),t,n,r,a)}function ht(e,t,n,r){n.delimitedEncoding?z(e.tag(n.number,h.StartGroup),t,r).tag(n.number,h.EndGroup):z(e.tag(n.number,h.LengthDelimited).fork(),t,r).join()}function Jn(e,t,n,r,o){var a;switch(e.tag(n.number,h.LengthDelimited).fork(),Q(e,n.parent.typeName,n.name,n.mapKey,1,r),n.mapKind){case"scalar":case"enum":Q(e,n.parent.typeName,n.name,(a=n.scalar)!==null&&a!==void 0?a:c.INT32,2,o);break;case"message":z(e.tag(2,h.LengthDelimited).fork(),t,o).join()}e.join()}function zt(e,t,n,r,o){try{switch(r){case c.STRING:e.string(o);break;case c.BOOL:e.bool(o);break;case c.DOUBLE:e.double(o);break;case c.FLOAT:e.float(o);break;case c.INT32:e.int32(o);break;case c.INT64:e.int64(o);break;case c.UINT64:e.uint64(o);break;case c.FIXED64:e.fixed64(o);break;case c.BYTES:e.bytes(o);break;case c.FIXED32:e.fixed32(o);break;case c.SFIXED32:e.sfixed32(o);break;case c.SFIXED64:e.sfixed64(o);break;case c.SINT64:e.sint64(o);break;case c.UINT32:e.uint32(o);break;case c.SINT32:e.sint32(o)}}catch(a){throw a instanceof Error?new Error(`cannot encode field ${t}.${n} to binary: ${a.message}`):a}}const Le=Fe("Chxnb29nbGUvcHJvdG9idWYvc3RydWN0LnByb3RvEg9nb29nbGUucHJvdG9idWYihAEKBlN0cnVjdBIzCgZmaWVsZHMYASADKAsyIy5nb29nbGUucHJvdG9idWYuU3RydWN0LkZpZWxkc0VudHJ5GkUKC0ZpZWxkc0VudHJ5EgsKA2tleRgBIAEoCRIlCgV2YWx1ZRgCIAEoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZToCOAEi6gEKBVZhbHVlEjAKCm51bGxfdmFsdWUYASABKA4yGi5nb29nbGUucHJvdG9idWYuTnVsbFZhbHVlSAASFgoMbnVtYmVyX3ZhbHVlGAIgASgBSAASFgoMc3RyaW5nX3ZhbHVlGAMgASgJSAASFAoKYm9vbF92YWx1ZRgEIAEoCEgAEi8KDHN0cnVjdF92YWx1ZRgFIAEoCzIXLmdvb2dsZS5wcm90b2J1Zi5TdHJ1Y3RIABIwCgpsaXN0X3ZhbHVlGAYgASgLMhouZ29vZ2xlLnByb3RvYnVmLkxpc3RWYWx1ZUgAQgYKBGtpbmQiMwoJTGlzdFZhbHVlEiYKBnZhbHVlcxgBIAMoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZSobCglOdWxsVmFsdWUSDgoKTlVMTF9WQUxVRRAAQn8KE2NvbS5nb29nbGUucHJvdG9idWZCC1N0cnVjdFByb3RvUAFaL2dvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL3N0cnVjdHBi+AEBogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),qn=M(Le,0),Qt=M(Le,1),Hn=M(Le,2);var Ne;function zn(e,t,n){var r;(function(u,m){if(u.extendee.typeName!=m.$typeName)throw new Error(`extension ${u.typeName} can only be applied to message ${u.extendee.typeName}`)})(t,e);const o=((r=e.$unknown)!==null&&r!==void 0?r:[]).filter(u=>u.no!==t.number),[a,s]=en(t,n),i=new Ft;Ht(i,{writeUnknownFields:!0},a,s);const l=new Lt(i.finish());for(;l.pos<l.len;){const[u,m]=l.tag(),d=l.skip(m,u);o.push({no:u,wireType:m,data:d})}e.$unknown=o}function en(e,t){const n=e.typeName,r=Object.assign(Object.assign({},e),{kind:"field",parent:e.extendee,localName:n}),o=Object.assign(Object.assign({},e.extendee),{fields:[r],members:[r],oneofs:[]}),a=w(o,t!==void 0?{[n]:t}:void 0);return[_(o,a),r,()=>{const s=a[n];if(s===void 0){const i=e.message;return te(i)?L(i.fields[0].scalar,i.fields[0].longAsString):w(i)}return s}]}(function(e){e[e.NULL_VALUE=0]="NULL_VALUE"})(Ne||(Ne={}));const Nt={ignoreUnknownFields:!1};function Qn(e,t,n){const r=_(e);try{$(r,t,function(a){return a?Object.assign(Object.assign({},Nt),a):Nt}(n))}catch(a){throw(o=a)instanceof Error&&yn.includes(o.name)&&"field"in o&&typeof o.field=="function"?new Error(`cannot decode ${a.field()} from JSON: ${a.message}`,{cause:a}):a}var o;return r.message}function $(e,t,n){var r;if(function(s,i,l){if(!s.desc.typeName.startsWith("google.protobuf."))return!1;switch(s.desc.typeName){case"google.protobuf.Any":return function(u,m,d){var p;if(m===null||Array.isArray(m)||typeof m!="object")throw new Error(`cannot decode message ${u.$typeName} from JSON: expected object but got ${E(m)}`);if(Object.keys(m).length==0)return;const f=m["@type"];if(typeof f!="string"||f=="")throw new Error(`cannot decode message ${u.$typeName} from JSON: "@type" is empty`);const g=f.includes("/")?f.substring(f.lastIndexOf("/")+1):f;if(!g.length)throw new Error(`cannot decode message ${u.$typeName} from JSON: "@type" is invalid`);const y=(p=d.registry)===null||p===void 0?void 0:p.getMessage(g);if(!y)throw new Error(`cannot decode message ${u.$typeName} from JSON: ${f} is not in the type registry`);const N=_(y);if(g.startsWith("google.protobuf.")&&Object.prototype.hasOwnProperty.call(m,"value"))$(N,m.value,d);else{const v=Object.assign({},m);delete v["@type"],$(N,v,d)}(function(v,D,R){let an=!1;R||(R=w(Xn),an=!0),R.value=Zn(v,D),R.typeUrl=`type.googleapis.com/${D.$typeName}`})(N.desc,N.message,u)}(s.message,i,l),!0;case"google.protobuf.Timestamp":return function(u,m){if(typeof m!="string")throw new Error(`cannot decode message ${u.$typeName} from JSON: ${E(m)}`);const d=m.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:\.([0-9]{1,9}))?(?:Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!d)throw new Error(`cannot decode message ${u.$typeName} from JSON: invalid RFC 3339 string`);const p=Date.parse(d[1]+"-"+d[2]+"-"+d[3]+"T"+d[4]+":"+d[5]+":"+d[6]+(d[8]?d[8]:"Z"));if(Number.isNaN(p))throw new Error(`cannot decode message ${u.$typeName} from JSON: invalid RFC 3339 string`);if(p<Date.parse("0001-01-01T00:00:00Z")||p>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot decode message ${u.$typeName} from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);u.seconds=b.parse(p/1e3),u.nanos=0,d[7]&&(u.nanos=parseInt("1"+d[7]+"0".repeat(9-d[7].length))-1e9)}(s.message,i),!0;case"google.protobuf.Duration":return function(u,m){if(typeof m!="string")throw new Error(`cannot decode message ${u.$typeName} from JSON: ${E(m)}`);const d=m.match(/^(-?[0-9]+)(?:\.([0-9]+))?s/);if(d===null)throw new Error(`cannot decode message ${u.$typeName} from JSON: ${E(m)}`);const p=Number(d[1]);if(p>315576e6||p<-315576e6)throw new Error(`cannot decode message ${u.$typeName} from JSON: ${E(m)}`);if(u.seconds=b.parse(p),typeof d[2]!="string")return;const f=d[2]+"0".repeat(9-d[2].length);u.nanos=parseInt(f),(p<0||Object.is(p,-0))&&(u.nanos=-u.nanos)}(s.message,i),!0;case"google.protobuf.FieldMask":return function(u,m){if(typeof m!="string")throw new Error(`cannot decode message ${u.$typeName} from JSON: ${E(m)}`);if(m==="")return;function d(p){if(p.includes("_"))throw new Error(`cannot decode message ${u.$typeName} from JSON: path names must be lowerCamelCase`);const f=p.replace(/[A-Z]/g,g=>"_"+g.toLowerCase());return f[0]==="_"?f.substring(1):f}u.paths=m.split(",").map(d)}(s.message,i),!0;case"google.protobuf.Struct":return nn(s.message,i),!0;case"google.protobuf.Value":return Ue(s.message,i),!0;case"google.protobuf.ListValue":return rn(s.message,i),!0;default:if(te(s.desc)){const u=s.desc.fields[0];return i===null?s.clear(u):s.set(u,J(u,i,!0)),!0}return!1}}(e,t,n))return;if(t==null||Array.isArray(t)||typeof t!="object")throw new Error(`cannot decode ${e.desc} from JSON: ${E(t)}`);const o=new Map,a=new Map;for(const s of e.desc.fields)a.set(s.name,s).set(s.jsonName,s);for(const[s,i]of Object.entries(t)){const l=a.get(s);if(l){if(l.oneof){if(i===null&&l.fieldKind=="scalar")continue;const u=o.get(l.oneof);if(u!==void 0)throw new T(l.oneof,`oneof set multiple times by ${u.name} and ${l.name}`);o.set(l.oneof,l)}Et(e,l,i,n)}else{let u;if(s.startsWith("[")&&s.endsWith("]")&&(u=(r=n.registry)===null||r===void 0?void 0:r.getExtension(s.substring(1,s.length-1)))&&u.extendee.typeName===e.desc.typeName){const[m,d,p]=en(u);Et(m,d,i,n),zn(e.message,u,p())}if(!u&&!n.ignoreUnknownFields)throw new Error(`cannot decode ${e.desc} from JSON: key "${s}" is unknown`)}}}function Et(e,t,n,r){switch(t.fieldKind){case"scalar":(function(o,a,s){const i=J(a,s,!1);i===ee?o.clear(a):o.set(a,i)})(e,t,n);break;case"enum":(function(o,a,s,i){const l=me(a.enum,s,i.ignoreUnknownFields,!1);l===ee?o.clear(a):l!==Z&&o.set(a,l)})(e,t,n,r);break;case"message":(function(o,a,s,i){if(s===null&&a.message.typeName!="google.protobuf.Value")return void o.clear(a);const l=o.isSet(a)?o.get(a):_(a.message);$(l,s,i),o.set(a,l)})(e,t,n,r);break;case"list":(function(o,a,s){if(a===null)return;const i=o.field();if(!Array.isArray(a))throw new T(i,"expected Array, got "+E(a));for(const l of a){if(l===null)throw new T(i,"list item must not be null");switch(i.listKind){case"message":const u=_(i.message);$(u,l,s),o.add(u);break;case"enum":const m=me(i.enum,l,s.ignoreUnknownFields,!0);m!==Z&&o.add(m);break;case"scalar":o.add(J(i,l,!0))}}})(e.get(t),n,r);break;case"map":(function(o,a,s){if(a===null)return;const i=o.field();if(typeof a!="object"||Array.isArray(a))throw new T(i,"expected object, got "+E(a));for(const[l,u]of Object.entries(a)){if(u===null)throw new T(i,"map value must not be null");let m;switch(i.mapKind){case"message":const p=_(i.message);$(p,u,s),m=p;break;case"enum":if(m=me(i.enum,u,s.ignoreUnknownFields,!0),m===Z)return;break;case"scalar":m=J(i,u,!0)}const d=er(i.mapKey,l);o.set(d,m)}})(e.get(t),n,r)}}const Z=Symbol();function me(e,t,n,r){if(t===null)return e.typeName=="google.protobuf.NullValue"?0:r?e.values[0].number:ee;switch(typeof t){case"number":if(Number.isInteger(t))return t;break;case"string":const o=e.values.find(a=>a.name===t);if(o!==void 0)return o.number;if(n)return Z}throw new Error(`cannot decode ${e} from JSON: ${E(t)}`)}const ee=Symbol();function J(e,t,n){if(t===null)return n?L(e.scalar,!1):ee;switch(e.scalar){case c.DOUBLE:case c.FLOAT:if(t==="NaN")return NaN;if(t==="Infinity")return Number.POSITIVE_INFINITY;if(t==="-Infinity")return Number.NEGATIVE_INFINITY;if(typeof t=="number"){if(Number.isNaN(t))throw new T(e,"unexpected NaN number");if(!Number.isFinite(t))throw new T(e,"unexpected infinite number");break}if(typeof t=="string"){if(t===""||t.trim().length!==t.length)break;const r=Number(t);if(!Number.isFinite(r))break;return r}break;case c.INT32:case c.FIXED32:case c.SFIXED32:case c.SINT32:case c.UINT32:return tn(t);case c.BYTES:if(typeof t=="string"){if(t==="")return new Uint8Array(0);try{return Kt(t)}catch(r){const o=r instanceof Error?r.message:String(r);throw new T(e,o)}}}return t}function er(e,t){switch(e){case c.BOOL:switch(t){case"true":return!0;case"false":return!1}return t;case c.INT32:case c.FIXED32:case c.UINT32:case c.SFIXED32:case c.SINT32:return tn(t);default:return t}}function tn(e){if(typeof e=="string"){if(e===""||e.trim().length!==e.length)return e;const t=Number(e);return Number.isNaN(t)?e:t}return e}function nn(e,t){if(typeof t!="object"||t==null||Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${E(t)}`);for(const[n,r]of Object.entries(t)){const o=w(Qt);Ue(o,r),e.fields[n]=o}}function Ue(e,t){switch(typeof t){case"number":e.kind={case:"numberValue",value:t};break;case"string":e.kind={case:"stringValue",value:t};break;case"boolean":e.kind={case:"boolValue",value:t};break;case"object":if(t===null)e.kind={case:"nullValue",value:Ne.NULL_VALUE};else if(Array.isArray(t)){const n=w(Hn);rn(n,t),e.kind={case:"listValue",value:n}}else{const n=w(qn);nn(n,t),e.kind={case:"structValue",value:n}}break;default:throw new Error(`cannot decode message ${e.$typeName} from JSON ${E(t)}`)}return e}function rn(e,t){if(!Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${E(t)}`);for(const n of t){const r=w(Qt);Ue(r,n),e.values.push(r)}}class tr{constructor(t){k(this,"pendingRequests",new Map);k(this,"cleanup");k(this,"serviceRegistries",new Set);this.target=t,this.cleanup=this.target.onReceiveMessage(this.handleMessage.bind(this))}addServiceRegistry(t){this.serviceRegistries.add(t)}removeServiceRegistry(t){this.serviceRegistries.delete(t)}handleMessage(t){if(!t||typeof t!="object"||!this.isGrpcMessageLike(t))return;const n=t;n.type==="com.augmentcode.client.rpc.request"?this.handleRequest(n):n.type==="com.augmentcode.client.rpc.response"&&this.handleResponse(n)}isGrpcMessageLike(t){return"type"in t&&t.type==="com.augmentcode.client.rpc.request"||t.type==="com.augmentcode.client.rpc.response"}async handleRequest(t){for(const n of this.serviceRegistries)if(n.canHandle(t))try{return void await n.handleRequest(t,r=>{this.target.sendMessage(r)})}catch(r){Array.from(this.serviceRegistries).indexOf(n)===this.serviceRegistries.size-1&&this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:r instanceof Error?r.message:String(r)})}this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:`No handlers registered for service: ${t.serviceTypeName}`})}handleResponse(t){const n=this.pendingRequests.get(t.id);if(n)if(this.pendingRequests.delete(t.id),clearTimeout(n.timeout),t.error)n.reject(new Error(t.error));else try{n.resolve(t)}catch(r){n.reject(r)}}sendRequest(t,n){return new Promise((r,o)=>{let a;n&&(a=setTimeout(()=>{this.pendingRequests.delete(t.id),o(new Error(`Request timed out after ${n}ms: ${t.methodLocalName}`))},n)),this.pendingRequests.set(t.id,{resolve:r,reject:o,timeout:a}),this.target.sendMessage(t)})}async unary(t,n,r,o,a,s){const i=crypto.randomUUID(),l=t.localName,u=t.parent.typeName;if(!u)throw new Error("Service name is required for unary calls");const m=a;if(n!=null&&n.aborted)throw new Error("Request aborted");let d;n&&(d=()=>{const f=this.pendingRequests.get(i);f&&(this.pendingRequests.delete(i),clearTimeout(f.timeout),f.reject(new Error("Request aborted")))},n.addEventListener("abort",d));const p=await this.sendRequest({type:"com.augmentcode.client.rpc.request",id:i,methodLocalName:l,serviceTypeName:u,data:m,timeout:r},r);return n&&d&&n.removeEventListener("abort",d),{stream:!1,method:t,service:t.parent,header:new Headers(o),message:Qn(t.output,p.data),trailer:new Headers}}stream(t,n,r,o,a,s){throw new Error("Streaming is not supported by this transport")}dispose(){this.cleanup();for(const{timeout:t}of this.pendingRequests.values())clearTimeout(t);this.pendingRequests.clear(),this.serviceRegistries.clear()}}k(tr,"PROTOCOL_NAME","com.augmentcode.client.rpc");async function pr(e){const t=await crypto.subtle.digest("SHA-256",e);return Array.from(new Uint8Array(t)).map(n=>n.toString(16).padStart(2,"0")).join("")}var nr=(e=>(e.chat="chat",e))(nr||{}),rr=(e=>(e.chatMentionFolder="chat-mention-folder",e.chatMentionFile="chat-mention-file",e.chatMentionExternalSource="chat-mention-external-source",e.chatClearContext="chat-clear-context",e.chatRestoreDefaultContext="chat-restore-default-context",e.chatUseActionFind="chat-use-action-find",e.chatUseActionExplain="chat-use-action-explain",e.chatUseActionFix="chat-use-action-autofix",e.chatUseActionWriteTest="chat-use-action-write-test",e.chatNewConversation="chat-new-conversation",e.chatNewAutofixConversation="chat-new-autofix-conversation",e.chatEditConversationName="chat-edit-conversation-name",e.chatFailedSmartPasteResolveFile="chat-failed-smart-paste-resolve-file",e.chatPrecomputeSmartPaste="chat-precompute-smart-paste",e.chatSmartPaste="chat-smart-paste",e.chatCodeblockCopy="chat-codeblock-copy",e.chatCodeblockCreate="chat-codeblock-create",e.chatCodeblockGoToFile="chat-codeblock-go-to-file",e.chatCodespanGoToFile="chat-codespan-go-to-file",e.chatCodespanGoToSymbol="chat-codespan-go-to-symbol",e.chatMermaidblockInitialize="chat-mermaidblock-initialize",e.chatMermaidblockToggle="chat-mermaidblock-toggle",e.chatMermaidblockInteract="chat-mermaidblock-interact",e.chatMermaidBlockError="chat-mermaidblock-error",e.chatUseSuggestedQuestion="chat-use-suggested-question",e.chatDisplaySuggestedQuestions="chat-display-suggested-questions",e.setWorkspaceGuidelines="chat-set-workspace-guidelines",e.clearWorkspaceGuidelines="chat-clear-workspace-guidelines",e.setUserGuidelines="chat-set-user-guidelines",e.clearUserGuidelines="chat-clear-user-guidelines",e))(rr||{}),ar=(e=>(e.getEditListRequest="agent-get-edit-list-request",e.getEditListResponse="agent-get-edit-list-response",e.getEditChangesByRequestIdRequest="agent-get-edit-changes-by-request-id-request",e.getEditChangesByRequestIdResponse="agent-get-edit-changes-by-request-id-response",e.setCurrentConversation="agent-set-current-conversation",e.migrateConversationId="agent-migrate-conversation-id",e.revertToTimestamp="revert-to-timestamp",e.chatAgentEditAcceptAll="chat-agent-edit-accept-all",e.reportAgentSessionEvent="report-agent-session-event",e.reportAgentRequestEvent="report-agent-request-event",e.chatReviewAgentFile="chat-review-agent-file",e.getAgentEditContentsByRequestId="get-agent-edit-contents-by-request-id",e.getAgentEditContentsByRequestIdResponse="get-agent-edit-contents-by-request-id-response",e.checkHasEverUsedAgent="check-has-ever-used-agent",e.checkHasEverUsedAgentResponse="check-has-ever-used-agent-response",e.setHasEverUsedAgent="set-has-ever-used-agent",e.checkHasEverUsedRemoteAgent="check-has-ever-used-remote-agent",e.checkHasEverUsedRemoteAgentResponse="check-has-ever-used-remote-agent-response",e.setHasEverUsedRemoteAgent="set-has-ever-used-remote-agent",e))(ar||{}),or=(e=>(e.closeAllToolProcesses="close-all-tool-processes",e.getToolIdentifierRequest="get-tool-identifier-request",e.getToolIdentifierResponse="get-tool-identifier-response",e))(or||{});function fr(e){return e.rootPath+"/"+e.relPath}var sr=(e=>(e.longRunning="longRunning",e.running="running",e.done="done",e))(sr||{}),ir=(e=>(e.initializing="initializing",e.enabled="enabled",e.disabled="disabled",e.partial="partial",e))(ir||{});class F{static hasFrontmatter(t){return this.frontmatterRegex.test(t)}static extractFrontmatter(t){const n=t.match(this.frontmatterRegex);return n&&n[1]?n[1]:null}static extractContent(t){return t.replace(this.frontmatterRegex,"")}static parseBoolean(t,n,r=!0){const o=this.extractFrontmatter(t);if(o){const a=new RegExp(`${n}\\s*:\\s*(true|false)`,"i"),s=o.match(a);if(s&&s[1])return s[1].toLowerCase()==="true"}return r}static parseString(t,n,r=""){const o=this.extractFrontmatter(t);if(o){const a=new RegExp(`${n}\\s*:\\s*["']?([^"'
]*)["']?`,"i"),s=o.match(a);if(s&&s[1])return s[1].trim()}return r}static updateFrontmatter(t,n,r){const o=t.match(this.frontmatterRegex),a=typeof r!="string"||/^(true|false)$/.test(r.toLowerCase())?String(r):`"${r}"`;if(o){const s=o[1],i=new RegExp(`(${n}\\s*:\\s*)([^\\n]*)`,"i");if(s.match(i)){const l=s.replace(i,`$1${a}`);return t.replace(this.frontmatterRegex,`---
${l}---
`)}{const l=`${s.endsWith(`
`)?s:s+`
`}${n}: ${a}
`;return t.replace(this.frontmatterRegex,`---
${l}---
`)}}return`---
${n}: ${a}
---

${t}`}static createFrontmatter(t,n){let r=t;this.hasFrontmatter(r)&&(r=this.extractContent(r));for(const[o,a]of Object.entries(n))r=this.updateFrontmatter(r,o,a);return r}}k(F,"frontmatterRegex",/^---\s*\n([\s\S]*?)\n---\s*\n/);class ur{static parseRuleFile(t,n){const r=F.parseBoolean(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,!0),o=F.extractContent(t);return{type:r?re.ALWAYS_ATTACHED:re.MANUAL,path:n,content:o}}static formatRuleFileForMarkdown(t){return F.updateFrontmatter(t.content,this.ALWAYS_APPLY_FRONTMATTER_KEY,t.type===re.ALWAYS_ATTACHED)}static getAlwaysApplyFrontmatterKey(t){return F.parseBoolean(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,!0)}static extractContent(t){return F.extractContent(t)}static updateAlwaysApplyFrontmatterKey(t,n){return F.updateFrontmatter(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,n)}}k(ur,"ALWAYS_APPLY_FRONTMATTER_KEY","alwaysApply");const br=".augment",gr="rules";var lr=(e=>(e.getHydratedTaskRequest="get-hydrated-task-request",e.getHydratedTaskResponse="get-hydrated-task-response",e.setCurrentRootTaskUuid="set-current-root-task-uuid",e.createTaskRequest="create-task-request",e.createTaskResponse="create-task-response",e.updateTaskRequest="update-task-request",e.updateTaskResponse="update-task-response",e.updateHydratedTaskRequest="update-hydrated-task-request",e.updateHydratedTaskResponse="update-hydrated-task-response",e))(lr||{});function yr(e){return e.replace(/^data:.*?;base64,/,"")}async function hr(e){return new Promise((t,n)=>{const r=new FileReader;r.onload=o=>{var a;return t((a=o.target)==null?void 0:a.result)},r.onerror=n,r.readAsDataURL(e)})}async function Nr(e){return e.length<1e4?Promise.resolve(function(t){const n=atob(t);return Uint8Array.from(n,r=>r.codePointAt(0)||0)}(e)):new Promise((t,n)=>{const r=new Worker(URL.createObjectURL(new Blob([`
            self.onmessage = function(e) {
              try {
                const base64 = e.data;
                const binString = atob(base64);
                const bytes = new Uint8Array(binString.length);
                for (let i = 0; i < binString.length; i++) {
                  bytes[i] = binString.charCodeAt(i);
                }
                self.postMessage(bytes, [bytes.buffer]);
              } catch (error) {
                self.postMessage({ error: error.message });
              }
            };
            `],{type:"application/javascript"})));r.onmessage=function(o){o.data.error?n(new Error(o.data.error)):t(o.data),r.terminate()},r.onerror=function(o){n(o.error),r.terminate()},r.postMessage(e)})}function cr(e,t,...n){if(n.length>0)throw new Error;return e.services[t]}const Er=cr(Fe("Ci5jbGllbnRzL3NpZGVjYXIvbGlicy9wcm90b3MvdGVzdF9zZXJ2aWNlLnByb3RvEgR0ZXN0IhoKC1Rlc3RSZXF1ZXN0EgsKA2ZvbxgBIAEoCSIeCgxUZXN0UmVzcG9uc2USDgoGcmVzdWx0GAEgASgJMngKC1Rlc3RTZXJ2aWNlEjMKClRlc3RNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2USNAoLRXJyb3JNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2ViBnByb3RvMw"),0);export{br as A,rr as C,ur as R,tr as S,lr as T,nr as W,gr as a,ar as b,w as c,or as d,Nr as e,Bn as f,yr as g,Er as h,sr as i,ir as j,fr as p,hr as r,pr as s};
