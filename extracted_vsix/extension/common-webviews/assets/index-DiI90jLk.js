import{I as y,J as W}from"./SpinnerAugment-JC8TPhVf.js";var E="Expected a function",w=NaN,I="[object Symbol]",M=/^\s+|\s+$/g,N=/^[-+]0x[0-9a-f]+$/i,S=/^0b[01]+$/i,D=/^0o[0-7]+$/i,F=parseInt,J=typeof y=="object"&&y&&y.Object===Object&&y,k=typeof self=="object"&&self&&self.Object===Object&&self,q=J||k||Function("return this")(),z=Object.prototype.toString,A=Math.max,B=Math.min,h=function(){return q.Date.now()};function C(e,n,t){var r,o,v,l,u,a,s=0,x=!1,p=!1,b=!0;if(typeof e!="function")throw new TypeError(E);function g(i){var f=r,c=o;return r=o=void 0,s=i,l=e.apply(c,f)}function O(i){var f=i-a;return a===void 0||f>=n||f<0||p&&i-s>=v}function m(){var i=h();if(O(i))return T(i);u=setTimeout(m,function(f){var c=n-(f-a);return p?B(c,v-(f-s)):c}(i))}function T(i){return u=void 0,b&&r?g(i):(r=o=void 0,l)}function j(){var i=h(),f=O(i);if(r=arguments,o=this,a=i,f){if(u===void 0)return function(c){return s=c,u=setTimeout(m,n),x?g(c):l}(a);if(p)return u=setTimeout(m,n),g(a)}return u===void 0&&(u=setTimeout(m,n)),l}return n=$(n)||0,d(t)&&(x=!!t.leading,v=(p="maxWait"in t)?A($(t.maxWait)||0,n):v,b="trailing"in t?!!t.trailing:b),j.cancel=function(){u!==void 0&&clearTimeout(u),s=0,r=a=o=u=void 0},j.flush=function(){return u===void 0?l:T(h())},j}function d(e){var n=typeof e;return!!e&&(n=="object"||n=="function")}function $(e){if(typeof e=="number")return e;if(function(r){return typeof r=="symbol"||function(o){return!!o&&typeof o=="object"}(r)&&z.call(r)==I}(e))return w;if(d(e)){var n=typeof e.valueOf=="function"?e.valueOf():e;e=d(n)?n+"":n}if(typeof e!="string")return e===0?e:+e;e=e.replace(M,"");var t=S.test(e);return t||D.test(e)?F(e.slice(2),t?2:8):N.test(e)?w:+e}const H=W(function(e,n,t){var r=!0,o=!0;if(typeof e!="function")throw new TypeError(E);return d(t)&&(r="leading"in t?!!t.leading:r,o="trailing"in t?!!t.trailing:o),C(e,n,{leading:r,maxWait:n,trailing:o})});export{H as t};
