import{S as we,i as ye,s as Me,V as G,y as C,D as F,c as H,a3 as T,e as _,z as S,f as J,u,q as v,t as $,r as E,h as R,B as k,af as ne,ag as K,a1 as Ce,a6 as Q,ac as ge,F as Se,a7 as ke,n as q,C as O,w as xe,E as j}from"./SpinnerAugment-JC8TPhVf.js";import{e as W,u as Le,o as _e}from"./BaseButton-D8yhCvaJ.js";import{G as Re,g as Ie,t as ve,a as Ee,M as qe,A as De,b as be,R as fe,c as Ae,S as Fe,d as Ne,e as He,P as je,f as Be,h as ze,C as Te,i as We,U as Ge,j as de,E as Pe,k as Ue,l as Ve}from"./RemoteAgentRetry-Bt0XW9rU.js";import"./Content-xvE836E_.js";import{S as X,i as re,a as Ke,b as Oe,c as Je,d as Qe,e as Xe,f as Ye,g as Ze,h as et,j as tt,k as nt,E as rt}from"./lodash-C-61Uc4F.js";import"./folder-D_G6V62q.js";import{R as ot}from"./open-in-new-window-BX_nUqUb.js";import"./isObjectLike-BA2QYXi-.js";import{S as st}from"./main-panel-RCGJ0jgP.js";import{aq as at,ar as lt}from"./AugmentMessage-LN57HyfM.js";import"./types-Cgd-nZOV.js";import"./MaterialIcon-D8Nb6HkU.js";import"./keypress-DD1aQVr0.js";import"./autofix-state-d-ymFdyn.js";import"./index-DiI90jLk.js";import"./Keybindings-lGeuBRaW.js";import"./pen-to-square-3TLxExQu.js";import"./exclamation-triangle-DfKf7sb_.js";import"./CardAugment-BAO5rOsN.js";import"./TextTooltipAugment-BlDY2tAQ.js";import"./IconButtonAugment-BQL_8yIN.js";import"./index-BFtESN_v.js";import"./augment-logo-E1jEbeRV.js";import"./ButtonAugment-CRFFE3_i.js";import"./folder-opened-bSDyFrZo.js";import"./expand-C7dSG_GJ.js";import"./diff-utils-DXaAmVnZ.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-CK0xjdO4.js";import"./layer-group-DyG7sLW7.js";import"./github-Du8Ax-RE.js";import"./types-B5Ac2hek.js";import"./chat-types-NgqNgjwU.js";import"./globals-D0QH3NT1.js";import"./test_service_pb-DM0n7l7E.js";import"./file-paths-BcSg4gks.js";import"./types-BSMhNRWH.js";import"./TextAreaAugment-DxOmi7vy.js";import"./design-system-init-Bf1-mlh4.js";import"./StatusIndicator-BiyeFzqm.js";import"./index-CW7fyhvB.js";import"./await_block-C0teov-5.js";import"./ellipsis-ce3_p-Q7.js";import"./Filespan-DeFTcAEj.js";import"./terminal-BBUsFUTj.js";import"./VSCodeCodicon-zeLUoeQd.js";import"./chat-flags-model-u-ZFJEJp.js";import"./mcp-logo-BPlx6CTu.js";import"./IconFilePath-3SJFMIIx.js";import"./LanguageIcon-CA8dtZ_C.js";import"./next-edit-types-904A5ehg.js";import"./magnifying-glass-D5YyJVGd.js";import"./chevron-down-B-gSyyd4.js";function oe(o,t,n){const e=o.slice();e[36]=t[n],e[39]=n;const r=e[39]+1===e[11].length;return e[37]=r,e}function se(o,t,n){const e=o.slice();e[40]=t[n].turn,e[41]=t[n].idx;const r=e[41]+1===e[12].length;return e[42]=r,e}function ae(o){let t,n;return t=new De({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function it(o){let t,n,e,r;const s=[ht,dt],m=[];function p(l,i){return l[16].enableRichCheckpointInfo?0:1}return t=p(o),n=m[t]=s[t](o),{c(){n.c(),e=j()},m(l,i){m[t].m(l,i),_(l,e,i),r=!0},p(l,i){let a=t;t=p(l),t===a?m[t].p(l,i):(v(),$(m[a],1,1,()=>{m[a]=null}),E(),n=m[t],n?n.p(l,i):(n=m[t]=s[t](l),n.c()),u(n,1),n.m(e.parentNode,e))},i(l){r||(u(n),r=!0)},o(l){$(n),r=!1},d(l){l&&R(e),m[t].d(l)}}}function mt(o){let t,n;return t=new Te({props:{group:o[36],chatModel:o[1],turn:o[40],turnIndex:o[41],isLastTurn:o[42],messageListContainer:o[0]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2048&r[0]&&(s.group=e[36]),2&r[0]&&(s.chatModel=e[1]),2048&r[0]&&(s.turn=e[40]),2048&r[0]&&(s.turnIndex=e[41]),6144&r[0]&&(s.isLastTurn=e[42]),1&r[0]&&(s.messageListContainer=e[0]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function ut(o){let t,n;return t=new We({props:{stage:o[40].stage,iterationId:o[40].iterationId,stageCount:o[40].stageCount}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2048&r[0]&&(s.stage=e[40].stage),2048&r[0]&&(s.iterationId=e[40].iterationId),2048&r[0]&&(s.stageCount=e[40].stageCount),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function ct(o){let t,n;return t=new Ge({props:{chatModel:o[1],msg:o[40].response_text??""}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2&r[0]&&(s.chatModel=e[1]),2048&r[0]&&(s.msg=e[40].response_text??""),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function $t(o){let t,n;return t=new at({props:{group:o[36],markdown:o[40].response_text??"",messageListContainer:o[0]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2048&r[0]&&(s.group=e[36]),2048&r[0]&&(s.markdown=e[40].response_text??""),1&r[0]&&(s.messageListContainer=e[0]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function pt(o){let t,n;function e(){return o[29](o[40])}return t=new de({props:{turn:o[40],preamble:st,resendTurn:e,$$slots:{default:[wt]},$$scope:{ctx:o}}}),{c(){C(t.$$.fragment)},m(r,s){S(t,r,s),n=!0},p(r,s){o=r;const m={};2048&s[0]&&(m.turn=o[40]),2052&s[0]&&(m.resendTurn=e),34816&s[0]|16384&s[1]&&(m.$$scope={dirty:s,ctx:o}),t.$set(m)},i(r){n||(u(t.$$.fragment,r),n=!0)},o(r){$(t.$$.fragment,r),n=!1},d(r){k(t,r)}}}function gt(o){let t,n;return t=new Pe({props:{flagsModel:o[13],turn:o[40]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};8192&r[0]&&(s.flagsModel=e[13]),2048&r[0]&&(s.turn=e[40]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function ft(o){let t,n;return t=new de({props:{turn:o[40]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2048&r[0]&&(s.turn=e[40]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function dt(o){let t,n;return t=new Ue({props:{turn:o[40]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2048&r[0]&&(s.turn=e[40]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function ht(o){let t,n;return t=new Ve({props:{turn:o[40]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2048&r[0]&&(s.turn=e[40]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function wt(o){let t,n;return t=new lt({props:{conversationModel:o[15],turn:o[40]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};32768&r[0]&&(s.conversationModel=e[15]),2048&r[0]&&(s.turn=e[40]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function le(o){let t,n,e,r;function s(){return o[30](o[40])}return{c(){t=G("div"),H(t,"class","c-msg-list__turn-seen")},m(m,p){_(m,t,p),e||(r=Q(n=Ae.call(null,t,{onSeen:s,track:o[40].seen_state!==X.seen})),e=!0)},p(m,p){o=m,n&&ge(n.update)&&2048&p[0]&&n.update.call(null,{onSeen:s,track:o[40].seen_state!==X.seen})},d(m){m&&R(t),e=!1,r()}}}function ie(o,t){let n,e,r,s,m,p,l,i,a,g,c,f,y,h,I=re(t[40]);const b=[ft,gt,pt,$t,ct,ut,mt,it],L=[];function N(w,M){return 2048&M[0]&&(e=null),2048&M[0]&&(r=null),2048&M[0]&&(s=null),2048&M[0]&&(m=null),2048&M[0]&&(p=null),2048&M[0]&&(l=null),2048&M[0]&&(i=null),2048&M[0]&&(a=null),e==null&&(e=!!Ke(w[40])),e?0:(r==null&&(r=!!Oe(w[40])),r?1:(s==null&&(s=!!Je(w[40])),s?2:(m==null&&(m=!!Qe(w[40])),m?3:(p==null&&(p=!!Xe(w[40])),p?4:(l==null&&(l=!!Ye(w[40])),l?5:(i==null&&(i=!!(Ze(w[40])||et(w[40])||tt(w[40]))),i?6:(a==null&&(a=!(!nt(w[40])||w[40].status!==rt.success)),a?7:-1)))))))}~(g=N(t,[-1,-1]))&&(c=L[g]=b[g](t));let x=I&&le(t);return{key:o,first:null,c(){n=j(),c&&c.c(),f=F(),x&&x.c(),y=j(),this.first=n},m(w,M){_(w,n,M),~g&&L[g].m(w,M),_(w,f,M),x&&x.m(w,M),_(w,y,M),h=!0},p(w,M){let D=g;g=N(t=w,M),g===D?~g&&L[g].p(t,M):(c&&(v(),$(L[D],1,1,()=>{L[D]=null}),E()),~g?(c=L[g],c?c.p(t,M):(c=L[g]=b[g](t),c.c()),u(c,1),c.m(f.parentNode,f)):c=null),2048&M[0]&&(I=re(t[40])),I?x?x.p(t,M):(x=le(t),x.c(),x.m(y.parentNode,y)):x&&(x.d(1),x=null)},i(w){h||(u(c),h=!0)},o(w){$(c),h=!1},d(w){w&&(R(n),R(f),R(y)),~g&&L[g].d(w),x&&x.d(w)}}}function me(o){let t,n,e,r,s;const m=[Lt,xt,kt,St,Ct,Mt,yt],p=[];function l(a,g){return a[8]?0:a[5].retryMessage?1:a[5].showResumingRemoteAgent?2:a[5].showPaused?3:a[5].showGeneratingResponse?4:a[5].showAwaitingUserInput?5:a[5].showStopped?6:-1}~(t=l(o))&&(n=p[t]=m[t](o));let i=o[5].showRunningSpacer&&ue();return{c(){n&&n.c(),e=F(),i&&i.c(),r=j()},m(a,g){~t&&p[t].m(a,g),_(a,e,g),i&&i.m(a,g),_(a,r,g),s=!0},p(a,g){let c=t;t=l(a),t===c?~t&&p[t].p(a,g):(n&&(v(),$(p[c],1,1,()=>{p[c]=null}),E()),~t?(n=p[t],n?n.p(a,g):(n=p[t]=m[t](a),n.c()),u(n,1),n.m(e.parentNode,e)):n=null),a[5].showRunningSpacer?i||(i=ue(),i.c(),i.m(r.parentNode,r)):i&&(i.d(1),i=null)},i(a){s||(u(n),s=!0)},o(a){$(n),s=!1},d(a){a&&(R(e),R(r)),~t&&p[t].d(a),i&&i.d(a)}}}function yt(o){let t,n;return t=new Fe({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:q,i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function Mt(o){let t,n;return t=new Ne({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:q,i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function Ct(o){let t,n;return t=new He({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:q,i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function St(o){let t,n;return t=new je({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:q,i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function kt(o){let t,n;return t=new Be({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:q,i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function xt(o){let t,n;return t=new ze({props:{message:o[5].retryMessage}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};32&r[0]&&(s.message=e[5].retryMessage),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function Lt(o){let t,n;return t=new fe({props:{error:o[8].error,onRetry:o[8].onRetry,onDelete:o[8].onDelete}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};256&r[0]&&(s.error=e[8].error),256&r[0]&&(s.onRetry=e[8].onRetry),256&r[0]&&(s.onDelete=e[8].onDelete),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function ue(o){let t;return{c(){t=G("div"),H(t,"class","c-agent-running-spacer svelte-t9khzq")},m(n,e){_(n,t,e)},d(n){n&&R(t)}}}function _t(o){let t,n,e,r=[],s=new Map,m=W(o[36]);const p=i=>i[40].request_id??`no-request-id-${i[41]}`;for(let i=0;i<m.length;i+=1){let a=se(o,m,i),g=p(a);s.set(g,r[i]=ie(g,a))}let l=o[37]&&me(o);return{c(){for(let i=0;i<r.length;i+=1)r[i].c();t=F(),l&&l.c(),n=j()},m(i,a){for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(i,a);_(i,t,a),l&&l.m(i,a),_(i,n,a),e=!0},p(i,a){8501255&a[0]&&(m=W(i[36]),v(),r=Le(r,a,p,1,i,m,s,t.parentNode,_e,ie,t,se),E()),i[37]?l?(l.p(i,a),2048&a[0]&&u(l,1)):(l=me(i),l.c(),u(l,1),l.m(n.parentNode,n)):l&&(v(),$(l,1,1,()=>{l=null}),E())},i(i){if(!e){for(let a=0;a<m.length;a+=1)u(r[a]);u(l),e=!0}},o(i){for(let a=0;a<r.length;a+=1)$(r[a]);$(l),e=!1},d(i){i&&(R(t),R(n));for(let a=0;a<r.length;a+=1)r[a].d(i);l&&l.d(i)}}}function ce(o){let t,n;return t=new be({props:{class:"c-msg-list__item--grouped",chatModel:o[1],isLastItem:o[37],userControlsScroll:o[3],requestId:o[36][0].turn.request_id,releaseScroll:o[31],messageListContainer:o[0],minHeight:o[37]?o[7]:0,$$slots:{default:[_t]},$$scope:{ctx:o}}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2&r[0]&&(s.chatModel=e[1]),2048&r[0]&&(s.isLastItem=e[37]),8&r[0]&&(s.userControlsScroll=e[3]),2048&r[0]&&(s.requestId=e[36][0].turn.request_id),8&r[0]&&(s.releaseScroll=e[31]),1&r[0]&&(s.messageListContainer=e[0]),2176&r[0]&&(s.minHeight=e[37]?e[7]:0),112935&r[0]|16384&r[1]&&(s.$$scope={dirty:r,ctx:e}),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function $e(o){let t,n;return t=new fe({props:{error:o[8].error,onRetry:o[8].onRetry,onDelete:o[8].onDelete}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};256&r[0]&&(s.error=e[8].error),256&r[0]&&(s.onRetry=e[8].onRetry),256&r[0]&&(s.onDelete=e[8].onDelete),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function Rt(o){let t,n,e,r,s,m,p,l=o[9]&&ae(),i=W(o[11]),a=[];for(let f=0;f<i.length;f+=1)a[f]=ce(oe(o,i,f));const g=f=>$(a[f],1,1,()=>{a[f]=null});let c=!o[12].length&&o[8]&&$e(o);return{c(){t=G("div"),l&&l.c(),n=F();for(let f=0;f<a.length;f+=1)a[f].c();e=F(),c&&c.c(),H(t,"class","c-msg-list svelte-t9khzq"),T(t,"c-msg-list--minimal",!o[16].fullFeatured)},m(f,y){_(f,t,y),l&&l.m(t,null),J(t,n);for(let h=0;h<a.length;h+=1)a[h]&&a[h].m(t,null);J(t,e),c&&c.m(t,null),o[32](t),s=!0,m||(p=[Q(ve.call(null,t,{onScrollIntoBottom:o[20],onScrollAwayFromBottom:o[21],onScroll:o[33]})),Q(r=Ee.call(null,t,{onHeightChange:o[34]}))],m=!0)},p(f,y){if(f[9]?l?512&y[0]&&u(l,1):(l=ae(),l.c(),u(l,1),l.m(t,n)):l&&(v(),$(l,1,1,()=>{l=null}),E()),8501679&y[0]){let h;for(i=W(f[11]),h=0;h<i.length;h+=1){const I=oe(f,i,h);a[h]?(a[h].p(I,y),u(a[h],1)):(a[h]=ce(I),a[h].c(),u(a[h],1),a[h].m(t,e))}for(v(),h=i.length;h<a.length;h+=1)g(h);E()}!f[12].length&&f[8]?c?(c.p(f,y),4352&y[0]&&u(c,1)):(c=$e(f),c.c(),u(c,1),c.m(t,null)):c&&(v(),$(c,1,1,()=>{c=null}),E()),r&&ge(r.update)&&16&y[0]&&r.update.call(null,{onHeightChange:f[34]}),(!s||65536&y[0])&&T(t,"c-msg-list--minimal",!f[16].fullFeatured)},i(f){if(!s){u(l);for(let y=0;y<i.length;y+=1)u(a[y]);u(c),s=!0}},o(f){$(l),a=a.filter(Boolean);for(let y=0;y<a.length;y+=1)$(a[y]);$(c),s=!1},d(f){f&&R(t),l&&l.d(),Se(a,f),c&&c.d(),o[32](null),m=!1,ke(p)}}}function pe(o){let t,n;return t=new qe({props:{messageListElement:o[0],showScrollDown:o[6]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};1&r[0]&&(s.messageListElement=e[0]),64&r[0]&&(s.showScrollDown=e[6]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function It(o){let t,n,e,r;n=new Re({props:{$$slots:{default:[Rt]},$$scope:{ctx:o}}});let s=o[10]&&pe(o);return{c(){t=G("div"),C(n.$$.fragment),e=F(),s&&s.c(),H(t,"class","c-msg-list-container svelte-t9khzq"),H(t,"data-testid","chat-message-list"),T(t,"c-msg-list--minimal",!o[16].fullFeatured)},m(m,p){_(m,t,p),S(n,t,null),J(t,e),s&&s.m(t,null),r=!0},p(m,p){const l={};113599&p[0]|16384&p[1]&&(l.$$scope={dirty:p,ctx:m}),n.$set(l),m[10]?s?(s.p(m,p),1024&p[0]&&u(s,1)):(s=pe(m),s.c(),u(s,1),s.m(t,null)):s&&(v(),$(s,1,1,()=>{s=null}),E()),(!r||65536&p[0])&&T(t,"c-msg-list--minimal",!m[16].fullFeatured)},i(m){r||(u(n.$$.fragment,m),u(s),r=!0)},o(m){$(n.$$.fragment,m),$(s),r=!1},d(m){m&&R(t),k(n),s&&s.d()}}}function vt(o,t,n){let e,r,s,m,p,l,i,a,g,c,f,y,h,I,b,L,N,x=q,w=q,M=()=>(w(),w=O(B,d=>n(28,L=d)),B),D=q;o.$$.on_destroy.push(()=>x()),o.$$.on_destroy.push(()=>w()),o.$$.on_destroy.push(()=>D());let{chatModel:B}=t;M();let{onboardingWorkspaceModel:P}=t,{msgListElement:z}=t;const he=ne("agentConversationModel"),{agentExchangeStatus:Y,isCurrConversationAgentic:Z}=he;K(o,Y,d=>n(27,b=d)),K(o,Z,d=>n(26,I=d));const ee=ne(ot.key);K(o,ee,d=>n(25,h=d));let A=!1;function U(){n(3,A=!0)}Ce(()=>{var d;((d=y.lastExchange)==null?void 0:d.seen_state)===X.unseen&&U()});let V=0;const te=d=>y.markSeen(d);return o.$$set=d=>{"chatModel"in d&&M(n(1,B=d.chatModel)),"onboardingWorkspaceModel"in d&&n(2,P=d.onboardingWorkspaceModel),"msgListElement"in d&&n(0,z=d.msgListElement)},o.$$.update=()=>{268435456&o.$$.dirty[0]&&(n(14,e=L.currentConversationModel),x(),x=O(e,d=>n(15,y=d))),268435456&o.$$.dirty[0]&&(n(13,r=L.flags),D(),D=O(r,d=>n(16,N=d))),503316480&o.$$.dirty[0]&&n(24,s=Ie(L,b,I,h)),16777216&o.$$.dirty[0]&&n(12,m=s.chatHistory),16777216&o.$$.dirty[0]&&n(11,p=s.groupedChatHistory),16777216&o.$$.dirty[0]&&n(5,l=s.lastGroupConfig),16777216&o.$$.dirty[0]&&n(10,i=s.doShowFloatingButtons),16777216&o.$$.dirty[0]&&n(9,a=s.doShowAgentSetupLogs),32&o.$$.dirty[0]&&n(8,g=l.remoteAgentErrorConfig),16&o.$$.dirty[0]&&n(7,c=V),8&o.$$.dirty[0]&&n(6,f=A)},[z,B,P,A,V,l,f,c,g,a,i,p,m,r,e,y,N,Y,Z,ee,function(){n(3,A=!1)},function(){n(3,A=!0)},U,te,s,h,I,b,L,d=>P.retryProjectSummary(d),d=>te(d),()=>n(3,A=!0),function(d){xe[d?"unshift":"push"](()=>{z=d,n(0,z)})},d=>{d<=1&&U()},d=>n(4,V=d)]}class qn extends we{constructor(t){super(),ye(this,t,vt,It,Me,{chatModel:1,onboardingWorkspaceModel:2,msgListElement:0},null,[-1,-1])}}export{qn as default};
