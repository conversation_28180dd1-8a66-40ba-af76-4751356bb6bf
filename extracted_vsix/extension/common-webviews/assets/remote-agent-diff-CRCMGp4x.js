var li=Object.defineProperty;var Ht=r=>{throw TypeError(r)};var ai=(r,e,t)=>e in r?li(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var ge=(r,e,t)=>ai(r,typeof e!="symbol"?e+"":e,t),ui=(r,e,t)=>e.has(r)||Ht("Cannot "+t);var Wt=(r,e,t)=>e.has(r)?Ht("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(r):e.set(r,t);var ct=(r,e,t)=>(ui(r,e,"access private method"),t);import{ai as Ve,S as K,i as ee,s as Y,V as b,c as D,a3 as ve,e as g,f as T,n as J,h,w as qe,G as R,H as le,y as L,z as M,u as p,t as $,B as q,D as Z,ab as Ae,q as H,r as W,an as bs,ag as ze,a0 as Es,a1 as rt,F as Le,E as ke,ap as gt,ao as Et,af as ht,x as He,A as We,ah as _s,T as fe,aD as Re,a5 as nt,a7 as Bs,ad as ci,ae as Qt,b as zs,R as be,a as Ls,aB as $t,X as Ee,Y as _e,Z as Be,g as Ms,aA as Gt,aC as di,a8 as pi,ac as fi,am as qt}from"./SpinnerAugment-JC8TPhVf.js";import"./design-system-init-Bf1-mlh4.js";import{g as dt,p as gi,a as hi}from"./index-yERhhNs7.js";import"./design-system-init-CajyFoaO.js";import{W as Ye,e as pe,u as qs,o as Rs,h as Ts}from"./BaseButton-D8yhCvaJ.js";import{T as Ge,M as $i}from"./TextTooltipAugment-BlDY2tAQ.js";import{s as Jt}from"./index-CW7fyhvB.js";import{h as mt,p as mi,j as Pe,M as Di,c as Ns,b as Ss,P as ot,e as Fi,i as xi,f as Ci,k as ki}from"./diff-utils-DXaAmVnZ.js";import{a as Rt,b as Tt,g as Ps,S as wi,M as vi}from"./index-CK0xjdO4.js";import{I as Is,A as yi}from"./IconButtonAugment-BQL_8yIN.js";import{V as lt}from"./VSCodeCodicon-zeLUoeQd.js";import{B as Oe}from"./ButtonAugment-CRFFE3_i.js";import{M as wt}from"./MaterialIcon-D8Nb6HkU.js";import{n as Os,a as Ze,g as ie}from"./file-paths-BcSg4gks.js";import{T as Nt}from"./Content-xvE836E_.js";import{F as Ai}from"./types-Cgd-nZOV.js";import{L as bi}from"./LanguageIcon-CA8dtZ_C.js";import{g as Ei}from"./globals-D0QH3NT1.js";import{E as _i}from"./expand-C7dSG_GJ.js";import{E as js}from"./exclamation-triangle-DfKf7sb_.js";import"./toggleHighContrast-BSg_W9Au.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-DiI90jLk.js";class Dt{constructor(e){ge(this,"_opts",null);ge(this,"_subscribers",new Set);this._asyncMsgSender=e}subscribe(e){return this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}}notifySubscribers(){this._subscribers.forEach(e=>e(this))}get opts(){return this._opts}updateOpts(e){this._opts=e,this.notifySubscribers()}async onPanelLoaded(){try{this.updateOpts(null);const e=await this._asyncMsgSender.send({type:Ye.remoteAgentDiffPanelLoaded});this.updateOpts(e.data)}catch(e){console.error("Failed to load diff panel:",e),this.updateOpts(null)}}handleMessageFromExtension(e){const t=e.data;return!(!t||!t.type)&&t.type===Ye.remoteAgentDiffPanelSetOpts&&(this.updateOpts(t.data),!0)}}ge(Dt,"key","remoteAgentDiffModel");class Ft{constructor(e){ge(this,"_applyingFilePaths",Ve([]));ge(this,"_appliedFilePaths",Ve([]));this._asyncMsgSender=e}get applyingFilePaths(){let e=[];return this._applyingFilePaths.subscribe(t=>{e=t})(),e}get appliedFilePaths(){let e=[];return this._appliedFilePaths.subscribe(t=>{e=t})(),e}async getDiffExplanation(e,t,n=3e4){try{return(await this._asyncMsgSender.send({type:Ye.diffExplanationRequest,data:{changedFiles:e,apikey:t}},n)).data.explanation}catch(s){return console.error("Failed to get diff explanation:",s),[]}}async groupChanges(e,t=!1,n){try{return(await this._asyncMsgSender.send({type:Ye.diffGroupChangesRequest,data:{changedFiles:e,changesById:t,apikey:n}})).data.groupedChanges}catch(s){return console.error("Failed to group changes:",s),[]}}async getDescriptions(e,t){try{return(await this._asyncMsgSender.send({type:Ye.diffDescriptionsRequest,data:{groupedChanges:e,apikey:t}})).data.explanation}catch(n){return console.error("Failed to get descriptions:",n),[]}}async applyChanges(e,t,n){this._applyingFilePaths.update(s=>[...s.filter(i=>i!==e),e]);try{(await this._asyncMsgSender.send({type:Ye.applyChangesRequest,data:{path:e,originalCode:t,newCode:n}},3e4)).data.success&&this._appliedFilePaths.update(s=>[...s.filter(i=>i!==e),e])}catch(s){console.error("applyChanges error",s)}finally{this._applyingFilePaths.update(s=>s.filter(i=>i!==e))}}}ge(Ft,"key","remoteAgentsDiffOpsModel");function Yt(r,e,t,n,s={}){const{context:i=3,generateId:o=!0}=s,l=mt(r,e,t,n,"","",{context:i}),a=e||r;let u;return o?u=`${Pe(a)}-${Pe(t+n)}`:u=Math.random().toString(36).substring(2,15),{id:u,path:a,diff:l,originalCode:t,modifiedCode:n}}function _t(r){const e=r.split(`
`);return{additions:e.filter(t=>t.startsWith("+")&&!t.startsWith("+++")).length,deletions:e.filter(t=>t.startsWith("-")&&!t.startsWith("---")).length}}function Vs(r){return!r.originalCode||r.originalCode.trim()===""}function Zs(r){return!r.modifiedCode||r.modifiedCode.trim()===""}class Bi{static generateDiff(e,t,n,s){return Yt(e,t,n,s)}static generateDiffs(e){return function(t,n={}){return t.map(s=>Yt(s.oldPath,s.newPath,s.oldContent,s.newContent,n))}(e)}static getDiffStats(e){return _t(e)}static getDiffObjectStats(e){return _t(e.diff)}static isNewFile(e){return Vs(e)}static isDeletedFile(e){return Zs(e)}}function zi(r){let e;return{c(){e=R(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&le(e,t[1])},d(t){t&&h(e)}}}function Li(r){let e;return{c(){e=R(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&le(e,t[1])},d(t){t&&h(e)}}}function Mi(r){let e,t,n;function s(l,a){return l[2]?Li:zi}let i=s(r),o=i(r);return{c(){e=b("span"),t=b("code"),o.c(),D(t,"class","markdown-codespan svelte-164mxpf"),D(t,"style",n=r[2]?`background-color: ${r[1]}; color: ${r[3]?"white":"black"}`:""),ve(t,"markdown-string",r[4])},m(l,a){g(l,e,a),T(e,t),o.m(t,null),r[6](e)},p(l,[a]){i===(i=s(l))&&o?o.p(l,a):(o.d(1),o=i(l),o&&(o.c(),o.m(t,null))),14&a&&n!==(n=l[2]?`background-color: ${l[1]}; color: ${l[3]?"white":"black"}`:"")&&D(t,"style",n),16&a&&ve(t,"markdown-string",l[4])},i:J,o:J,d(l){l&&h(e),o.d(),r[6](null)}}}function qi(r,e,t){let n,s,i,o,{token:l}=e,{element:a}=e;return r.$$set=u=>{"token"in u&&t(5,l=u.token),"element"in u&&t(0,a=u.element)},r.$$.update=()=>{32&r.$$.dirty&&t(1,n=l.raw.slice(1,l.raw.length-1)),2&r.$$.dirty&&t(4,s=n.startsWith('"')),2&r.$$.dirty&&t(2,i=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&r.$$.dirty&&t(3,o=i&&function(u){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(u))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let c,d,f;return u.length===4?(c=parseInt(u.charAt(1),16),d=parseInt(u.charAt(2),16),f=parseInt(u.charAt(3),16),c*=17,d*=17,f*=17):(c=parseInt(u.slice(1,3),16),d=parseInt(u.slice(3,5),16),f=parseInt(u.slice(5,7),16)),.299*c+.587*d+.114*f<130}(n))},[a,n,i,o,s,l,function(u){qe[u?"unshift":"push"](()=>{a=u,t(0,a)})}]}let Ri=class extends K{constructor(r){super(),ee(this,r,qi,Mi,Y,{token:5,element:0})}};function Ti(r){let e,t;return e=new Di({props:{markdown:r[1](r[0]),renderers:r[2]}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.markdown=n[1](n[0])),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Ni(r,e,t){let{markdown:n}=e;const s={codespan:Ri};return r.$$set=i=>{"markdown"in i&&t(0,n=i.markdown)},[n,i=>i.replace(/`?#[0-9a-fA-F]{3,6}`?/g,o=>o.startsWith("`")?o:`\`${o}\``),s]}let Us=class extends K{constructor(r){super(),ee(this,r,Ni,Ti,Y,{markdown:0})}};function Xt(r,e,t){const n=r.slice();return n[47]=e[t],n[49]=t,n}function Kt(r){let e,t,n,s,i;t=new Is({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Ii]},$$scope:{ctx:r}}}),t.$on("click",r[24]);let o=pe(r[1]),l=[];for(let u=0;u<o.length;u+=1)l[u]=en(Xt(r,o,u));const a=u=>$(l[u],1,1,()=>{l[u]=null});return{c(){e=b("div"),L(t.$$.fragment),n=Z(),s=b("div");for(let u=0;u<l.length;u+=1)l[u].c();D(e,"class","toggle-button svelte-14s1ghg"),D(s,"class","descriptions svelte-14s1ghg"),Ae(s,"transform","translateY("+-r[4]+"px)")},m(u,c){g(u,e,c),M(t,e,null),g(u,n,c),g(u,s,c);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(s,null);i=!0},p(u,c){const d={};if(1&c[0]|524288&c[1]&&(d.$$scope={dirty:c,ctx:u}),t.$set(d),546&c[0]){let f;for(o=pe(u[1]),f=0;f<o.length;f+=1){const m=Xt(u,o,f);l[f]?(l[f].p(m,c),p(l[f],1)):(l[f]=en(m),l[f].c(),p(l[f],1),l[f].m(s,null))}for(H(),f=o.length;f<l.length;f+=1)a(f);W()}(!i||16&c[0])&&Ae(s,"transform","translateY("+-u[4]+"px)")},i(u){if(!i){p(t.$$.fragment,u);for(let c=0;c<o.length;c+=1)p(l[c]);i=!0}},o(u){$(t.$$.fragment,u),l=l.filter(Boolean);for(let c=0;c<l.length;c+=1)$(l[c]);i=!1},d(u){u&&(h(e),h(n),h(s)),q(t),Le(l,u)}}}function Si(r){let e,t;return e=new lt({props:{icon:"book"}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Pi(r){let e,t;return e=new lt({props:{icon:"x"}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Ii(r){let e,t,n,s;const i=[Pi,Si],o=[];function l(a,u){return a[0]?0:1}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=ke()},m(a,u){o[e].m(a,u),g(a,n,u),s=!0},p(a,u){let c=e;e=l(a),e!==c&&(H(),$(o[c],1,1,()=>{o[c]=null}),W(),t=o[e],t||(t=o[e]=i[e](a),t.c()),p(t,1),t.m(n.parentNode,n))},i(a){s||(p(t),s=!0)},o(a){$(t),s=!1},d(a){a&&h(n),o[e].d(a)}}}function en(r){let e,t,n,s;return t=new Us({props:{markdown:r[47].text}}),{c(){e=b("div"),L(t.$$.fragment),n=Z(),D(e,"class","description svelte-14s1ghg"),Ae(e,"top",(r[5][r[49]]||r[9](r[47]))+"px"),Ae(e,"--ds-panel-solid","transparent")},m(i,o){g(i,e,o),M(t,e,null),T(e,n),s=!0},p(i,o){const l={};2&o[0]&&(l.markdown=i[47].text),t.$set(l),(!s||34&o[0])&&Ae(e,"top",(i[5][i[49]]||i[9](i[47]))+"px")},i(i){s||(p(t.$$.fragment,i),s=!0)},o(i){$(t.$$.fragment,i),s=!1},d(i){i&&h(e),q(t)}}}function Oi(r){let e,t,n,s,i=r[1].length>0&&Kt(r);return{c(){e=b("div"),t=b("div"),n=Z(),i&&i.c(),D(t,"class","editor-container svelte-14s1ghg"),Ae(t,"height",r[3]+"px"),D(e,"class","monaco-diff-container svelte-14s1ghg"),ve(e,"monaco-diff-container-with-descriptions",r[1].length>0&&r[0])},m(o,l){g(o,e,l),T(e,t),r[23](t),T(e,n),i&&i.m(e,null),s=!0},p(o,l){(!s||8&l[0])&&Ae(t,"height",o[3]+"px"),o[1].length>0?i?(i.p(o,l),2&l[0]&&p(i,1)):(i=Kt(o),i.c(),p(i,1),i.m(e,null)):i&&(H(),$(i,1,1,()=>{i=null}),W()),(!s||3&l[0])&&ve(e,"monaco-diff-container-with-descriptions",o[1].length>0&&o[0])},i(o){s||(p(i),s=!0)},o(o){$(i),s=!1},d(o){o&&h(e),r[23](null),i&&i.d()}}}function ji(r,e,t){let n,s,i;const o=bs();let{originalCode:l=""}=e,{modifiedCode:a=""}=e,{path:u}=e,{descriptions:c=[]}=e,{lineOffset:d=0}=e,{extraPrefixLines:f=[]}=e,{extraSuffixLines:m=[]}=e,{theme:A}=e,{areDescriptionsVisible:v=!0}=e,{isNewFile:_=!1}=e,{isDeletedFile:C=!1}=e;const N=Rt.getContext().monaco;let F,x,y,k;ze(r,N,w=>t(22,n=w));let E,S=[];const j=Tt();let O,G=Ve(0);ze(r,G,w=>t(4,s=w));let ce=_?20*a.split(`
`).length+40:100;const ne=n?n.languages.getLanguages().map(w=>w.id):[];function he(w,z){var B,U;if(z){const V=(B=z.split(".").pop())==null?void 0:B.toLowerCase();if(V){const X=(U=n==null?void 0:n.languages.getLanguages().find(de=>{var ae;return(ae=de.extensions)==null?void 0:ae.includes("."+V)}))==null?void 0:U.id;if(X&&ne.includes(X))return X}}return"plaintext"}const $e=Ve({});ze(r,$e,w=>t(5,i=w));let Q=null;function ye(){if(!F)return;S=S.filter(B=>(B.dispose(),!1));const w=F.getOriginalEditor(),z=F.getModifiedEditor();S.push(w.onDidScrollChange(()=>{gt(G,s=w.getScrollTop(),s)}),z.onDidScrollChange(()=>{gt(G,s=z.getScrollTop(),s)}))}function xe(){if(!F||!E)return;const w=F.getOriginalEditor(),z=F.getModifiedEditor();S.push(z.onDidContentSizeChange(()=>j.requestLayout()),w.onDidContentSizeChange(()=>j.requestLayout()),F.onDidUpdateDiff(()=>j.requestLayout()),z.onDidChangeHiddenAreas(()=>j.requestLayout()),w.onDidChangeHiddenAreas(()=>j.requestLayout()),z.onDidLayoutChange(()=>j.requestLayout()),w.onDidLayoutChange(()=>j.requestLayout()),z.onDidFocusEditorWidget(()=>{re(!0)}),w.onDidFocusEditorWidget(()=>{re(!0)}),z.onDidBlurEditorWidget(()=>{re(!1)}),w.onDidBlurEditorWidget(()=>{re(!1)}),z.onDidChangeModelContent(()=>{se=!0,I=Date.now();const B=(k==null?void 0:k.getValue())||"";if(B===a)return;const U=B.replace(f.join(""),"").replace(m.join(""),"");o("codeChange",{modifiedCode:U});const V=setTimeout(()=>{se=!1},500);S.push({dispose:()=>clearTimeout(V)})})),function(){!E||!F||(Q&&clearTimeout(Q),Q=setTimeout(()=>{if(!E.__hasClickListener){const B=U=>{const V=U.target;V&&(V.closest('[title="Show Unchanged Region"]')||V.closest('[title="Hide Unchanged Region"]'))&&we()};E.addEventListener("click",B),t(2,E.__hasClickListener=!0,E),S.push({dispose:()=>{E.removeEventListener("click",B)}})}F&&S.push(F.onDidUpdateDiff(()=>{we()}))},300))}()}Es(()=>{F==null||F.dispose(),x==null||x.dispose(),y==null||y.dispose(),k==null||k.dispose(),S.forEach(w=>w.dispose()),Q&&clearTimeout(Q),O==null||O()});let oe=null;function we(){oe&&clearTimeout(oe),oe=setTimeout(()=>{j.requestLayout(),oe=null},100),oe&&S.push({dispose:()=>{oe&&(clearTimeout(oe),oe=null)}})}function Me(w,z,B,U=[],V=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");y==null||y.dispose(),k==null||k.dispose(),z=z||"",B=B||"";const X=U.join(""),de=V.join("");if(z=_?B.split(`
`).map(()=>" ").join(`
`):X+z+de,B=X+B+de,y=n.editor.createModel(z,void 0,w!==void 0?n.Uri.parse("file://"+w+`#${crypto.randomUUID()}`):void 0),C&&(B=B.split(`
`).map(()=>" ").join(`
`)),t(21,k=n.editor.createModel(B,void 0,w!==void 0?n.Uri.parse("file://"+w+`#${crypto.randomUUID()}`):void 0)),F){F.setModel({original:y,modified:k});const ae=F.getOriginalEditor();ae&&ae.updateOptions({lineNumbers:"off"}),ye(),Q&&clearTimeout(Q),Q=setTimeout(()=>{xe(),Q=null},300)}}rt(()=>{if(n)if(_){t(20,x=n.editor.create(E,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:A,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:U=>`${d-f.length+U}`}));const w=he(0,u);t(21,k=n.editor.createModel(a,w,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),x.setModel(k),S.push(x.onDidChangeModelContent(()=>{se=!0,I=Date.now();const U=(k==null?void 0:k.getValue())||"";if(U===a)return;o("codeChange",{modifiedCode:U});const V=setTimeout(()=>{se=!1},500);S.push({dispose:()=>clearTimeout(V)})})),S.push(x.onDidFocusEditorWidget(()=>{x==null||x.updateOptions({scrollbar:{handleMouseWheel:!0}})}),x.onDidBlurEditorWidget(()=>{x==null||x.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const z=x.getContentHeight();t(3,ce=Math.max(z,60));const B=setTimeout(()=>{x==null||x.layout()},0);S.push({dispose:()=>clearTimeout(B)})}else t(19,F=n.editor.createDiffEditor(E,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:A,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:w=>`${d-f.length+w}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),O&&O(),O=j.registerEditor({editor:F,updateHeight:te,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),Me(u,l,a,f,m),ye(),xe(),Q&&clearTimeout(Q),Q=setTimeout(()=>{j.requestLayout(),Q=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let se=!1,I=0;function P(w,z=!0){return F?(z?F.getModifiedEditor():F.getOriginalEditor()).getTopForLineNumber(w):18*w}function te(){if(!F)return;const w=F.getModel(),z=w==null?void 0:w.original,B=w==null?void 0:w.modified;if(!z||!B)return;const U=F.getOriginalEditor(),V=F.getModifiedEditor(),X=F.getLineChanges()||[];let de;if(X.length===0){const ae=U.getContentHeight(),Ce=V.getContentHeight();de=Math.max(100,ae,Ce)}else{let ae=0,Ce=0;for(const je of X)je.originalEndLineNumber>0&&(ae=Math.max(ae,je.originalEndLineNumber)),je.modifiedEndLineNumber>0&&(Ce=Math.max(Ce,je.modifiedEndLineNumber));ae=Math.min(ae+3,z.getLineCount()),Ce=Math.min(Ce+3,B.getLineCount());const Se=U.getTopForLineNumber(ae),Xe=V.getTopForLineNumber(Ce);de=Math.max(Se,Xe)+60}t(3,ce=Math.min(de,2e4)),F.layout(),me()}function re(w){if(!F)return;const z=F.getOriginalEditor(),B=F.getModifiedEditor();z.updateOptions({scrollbar:{handleMouseWheel:w}}),B.updateOptions({scrollbar:{handleMouseWheel:w}})}function ue(w){if(!F)return 0;const z=F.getModel(),B=z==null?void 0:z.original,U=z==null?void 0:z.modified;if(!B||!U)return 0;const V=P(w.range.start+1,!1),X=P(w.range.start+1,!0);return V&&!X?V:!V&&X?X:Math.min(V,X)}function me(){if(!F||c.length===0)return;const w={};c.forEach((z,B)=>{w[B]=ue(z)}),$e.set(w)}return r.$$set=w=>{"originalCode"in w&&t(10,l=w.originalCode),"modifiedCode"in w&&t(11,a=w.modifiedCode),"path"in w&&t(12,u=w.path),"descriptions"in w&&t(1,c=w.descriptions),"lineOffset"in w&&t(13,d=w.lineOffset),"extraPrefixLines"in w&&t(14,f=w.extraPrefixLines),"extraSuffixLines"in w&&t(15,m=w.extraSuffixLines),"theme"in w&&t(16,A=w.theme),"areDescriptionsVisible"in w&&t(0,v=w.areDescriptionsVisible),"isNewFile"in w&&t(17,_=w.isNewFile),"isDeletedFile"in w&&t(18,C=w.isDeletedFile)},r.$$.update=()=>{if(8051712&r.$$.dirty[0]&&(w=a,!(se||Date.now()-I<1e3||k&&k.getValue()===f.join("")+w+m.join(""))))if(_&&x){if(k)k.setValue(a);else{const z=he(0,u);n&&t(21,k=n.editor.createModel(a,z,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),k&&x.setModel(k)}t(3,ce=20*a.split(`
`).length+40),x.layout()}else!_&&F&&(Me(u,l,a,f,m),j.requestLayout());var w;if(524290&r.$$.dirty[0]&&F&&c.length>0&&me(),1181696&r.$$.dirty[0]&&_&&a&&x){const z=x.getContentHeight();t(3,ce=Math.max(z,60)),x.layout()}},[v,c,E,ce,s,i,N,G,$e,ue,l,a,u,d,f,m,A,_,C,F,x,k,n,function(w){qe[w?"unshift":"push"](()=>{E=w,t(2,E)})},()=>t(0,v=!v)]}let Vi=class extends K{constructor(r){super(),ee(this,r,ji,Oi,Y,{originalCode:10,modifiedCode:11,path:12,descriptions:1,lineOffset:13,extraPrefixLines:14,extraSuffixLines:15,theme:16,areDescriptionsVisible:0,isNewFile:17,isDeletedFile:18},null,[-1,-1])}};const Zi=["png","jpg","jpeg","gif","svg","webp","bmp","ico"],Ui=["zip","tar","gz","7z","rar","pdf","doc","docx","ppt","pptx","xls","xlsx","odt","odp","ods","exe","dll","so","dylib","app","msi","deb","rpm","o","a","class","jar","pyc","wasm","mp3","mp4","avi","mov","wav","mkv","DS_Store","db","sqlite","dat"],Hs=1048576;function St(r){if(!r)return"";const e=r.lastIndexOf(".");return e===-1||e===r.length-1?"":r.substring(e+1).toLowerCase()}function st(r){switch(St(r)){case"png":return"image/png";case"jpg":case"jpeg":return"image/jpeg";case"gif":return"image/gif";case"svg":return"image/svg+xml";case"webp":return"image/webp";case"bmp":return"image/bmp";case"ico":return"image/x-icon";default:return"application/octet-stream"}}function tn(r){const e=St(r);return Zi.includes(e)}function nn(r){return r>Hs}const Ws=Symbol("focusedPath");function Qs(){return ht(Ws)}function Bt(r){return`file-diff-${Pe(r)}`}function Hi(r){let e,t,n;function s(o){r[38](o)}let i={path:r[3],originalCode:r[0].originalCode,modifiedCode:r[6],theme:r[14],descriptions:r[4],isNewFile:r[20],isDeletedFile:r[19]};return r[1]!==void 0&&(i.areDescriptionsVisible=r[1]),e=new Vi({props:i}),qe.push(()=>He(e,"areDescriptionsVisible",s)),e.$on("codeChange",r[25]),{c(){L(e.$$.fragment)},m(o,l){M(e,o,l),n=!0},p(o,l){const a={};8&l[0]&&(a.path=o[3]),1&l[0]&&(a.originalCode=o[0].originalCode),64&l[0]&&(a.modifiedCode=o[6]),16384&l[0]&&(a.theme=o[14]),16&l[0]&&(a.descriptions=o[4]),1048576&l[0]&&(a.isNewFile=o[20]),524288&l[0]&&(a.isDeletedFile=o[19]),!t&&2&l[0]&&(t=!0,a.areDescriptionsVisible=o[1],We(()=>t=!1)),e.$set(a)},i(o){n||(p(e.$$.fragment,o),n=!0)},o(o){$(e.$$.fragment,o),n=!1},d(o){q(e,o)}}}function Wi(r){let e,t,n;return t=new fe({props:{size:1,$$slots:{default:[Ji]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),D(e,"class","too-large-message svelte-1536g7w")},m(s,i){g(s,e,i),M(t,e,null),n=!0},p(s,i){const o={};9984&i[0]|512&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function Qi(r){let e,t,n;return t=new fe({props:{$$slots:{default:[er]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),D(e,"class","binary-file-message svelte-1536g7w")},m(s,i){g(s,e,i),M(t,e,null),n=!0},p(s,i){const o={};1057152&i[0]|512&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function Gi(r){let e,t,n,s;const i=[nr,tr],o=[];function l(a,u){return a[8]?0:a[6]?1:-1}return~(t=l(r))&&(n=o[t]=i[t](r)),{c(){e=b("div"),n&&n.c(),D(e,"class","image-container svelte-1536g7w")},m(a,u){g(a,e,u),~t&&o[t].m(e,null),s=!0},p(a,u){let c=t;t=l(a),t===c?~t&&o[t].p(a,u):(n&&(H(),$(o[c],1,1,()=>{o[c]=null}),W()),~t?(n=o[t],n?n.p(a,u):(n=o[t]=i[t](a),n.c()),p(n,1),n.m(e,null)):n=null)},i(a){s||(p(n),s=!0)},o(a){$(n),s=!1},d(a){a&&h(e),~t&&o[t].d()}}}function Ji(r){let e,t,n,s,i,o,l,a=ie(r[13])+"",u=(r[8]?r[10]:r[9])+"";return{c(){e=R('File "'),t=R(a),n=R('" is too large to display a diff (size: '),s=R(u),i=R(" bytes, max: "),o=R(Hs),l=R(" bytes).")},m(c,d){g(c,e,d),g(c,t,d),g(c,n,d),g(c,s,d),g(c,i,d),g(c,o,d),g(c,l,d)},p(c,d){8192&d[0]&&a!==(a=ie(c[13])+"")&&le(t,a),1792&d[0]&&u!==(u=(c[8]?c[10]:c[9])+"")&&le(s,u)},d(c){c&&(h(e),h(t),h(n),h(s),h(i),h(o),h(l))}}}function Yi(r){let e,t,n,s=ie(r[13])+"";return{c(){e=R("Binary file modified: "),t=R(s),n=R(".")},m(i,o){g(i,e,o),g(i,t,o),g(i,n,o)},p(i,o){8192&o[0]&&s!==(s=ie(i[13])+"")&&le(t,s)},d(i){i&&(h(e),h(t),h(n))}}}function Xi(r){let e,t,n,s=ie(r[13])+"";return{c(){e=R("Binary file deleted: "),t=R(s),n=R(".")},m(i,o){g(i,e,o),g(i,t,o),g(i,n,o)},p(i,o){8192&o[0]&&s!==(s=ie(i[13])+"")&&le(t,s)},d(i){i&&(h(e),h(t),h(n))}}}function Ki(r){let e,t,n,s=ie(r[13])+"";return{c(){e=R("Binary file added: "),t=R(s),n=R(".")},m(i,o){g(i,e,o),g(i,t,o),g(i,n,o)},p(i,o){8192&o[0]&&s!==(s=ie(i[13])+"")&&le(t,s)},d(i){i&&(h(e),h(t),h(n))}}}function er(r){let e;function t(i,o){return i[20]||i[7]?Ki:i[8]?Xi:Yi}let n=t(r),s=n(r);return{c(){s.c(),e=R(`
            No text preview available.`)},m(i,o){s.m(i,o),g(i,e,o)},p(i,o){n===(n=t(i))&&s?s.p(i,o):(s.d(1),s=n(i),s&&(s.c(),s.m(e.parentNode,e)))},d(i){i&&h(e),s.d(i)}}}function tr(r){let e,t,n,s,i,o,l,a;e=new fe({props:{class:"image-info-text",$$slots:{default:[rr]},$$scope:{ctx:r}}});let u=r[0].originalCode&&r[6]!==r[0].originalCode&&!r[20]&&sn(r);return{c(){L(e.$$.fragment),t=Z(),n=b("img"),o=Z(),u&&u.c(),l=ke(),Re(n.src,s="data:"+r[18]+";base64,"+btoa(r[6]))||D(n,"src",s),D(n,"alt",i="Current "+ie(r[13])),D(n,"class","image-preview svelte-1536g7w")},m(c,d){M(e,c,d),g(c,t,d),g(c,n,d),g(c,o,d),u&&u.m(c,d),g(c,l,d),a=!0},p(c,d){const f={};1056896&d[0]|512&d[1]&&(f.$$scope={dirty:d,ctx:c}),e.$set(f),(!a||262208&d[0]&&!Re(n.src,s="data:"+c[18]+";base64,"+btoa(c[6])))&&D(n,"src",s),(!a||8192&d[0]&&i!==(i="Current "+ie(c[13])))&&D(n,"alt",i),c[0].originalCode&&c[6]!==c[0].originalCode&&!c[20]?u?(u.p(c,d),1048641&d[0]&&p(u,1)):(u=sn(c),u.c(),p(u,1),u.m(l.parentNode,l)):u&&(H(),$(u,1,1,()=>{u=null}),W())},i(c){a||(p(e.$$.fragment,c),p(u),a=!0)},o(c){$(e.$$.fragment,c),$(u),a=!1},d(c){c&&(h(t),h(n),h(o),h(l)),q(e,c),u&&u.d(c)}}}function nr(r){let e,t,n,s;e=new fe({props:{class:"image-info-text",$$slots:{default:[lr]},$$scope:{ctx:r}}});let i=r[0].originalCode&&rn(r);return{c(){L(e.$$.fragment),t=Z(),i&&i.c(),n=ke()},m(o,l){M(e,o,l),g(o,t,l),i&&i.m(o,l),g(o,n,l),s=!0},p(o,l){const a={};8192&l[0]|512&l[1]&&(a.$$scope={dirty:l,ctx:o}),e.$set(a),o[0].originalCode?i?(i.p(o,l),1&l[0]&&p(i,1)):(i=rn(o),i.c(),p(i,1),i.m(n.parentNode,n)):i&&(H(),$(i,1,1,()=>{i=null}),W())},i(o){s||(p(e.$$.fragment,o),p(i),s=!0)},o(o){$(e.$$.fragment,o),$(i),s=!1},d(o){o&&(h(t),h(n)),q(e,o),i&&i.d(o)}}}function sr(r){let e;return{c(){e=R("Image modified")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function ir(r){let e;return{c(){e=R("New image added")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function rr(r){let e,t,n=ie(r[13])+"";function s(l,a){return l[20]||l[7]?ir:sr}let i=s(r),o=i(r);return{c(){o.c(),e=R(": "),t=R(n)},m(l,a){o.m(l,a),g(l,e,a),g(l,t,a)},p(l,a){i!==(i=s(l))&&(o.d(1),o=i(l),o&&(o.c(),o.m(e.parentNode,e))),8192&a[0]&&n!==(n=ie(l[13])+"")&&le(t,n)},d(l){l&&(h(e),h(t)),o.d(l)}}}function sn(r){let e,t,n,s,i,o;return e=new fe({props:{class:"image-info-text",$$slots:{default:[or]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=Z(),n=b("img"),Re(n.src,s="data:"+st(r[3])+";base64,"+btoa(r[0].originalCode))||D(n,"src",s),D(n,"alt",i="Original "+ie(r[13])),D(n,"class","image-preview image-preview--previous svelte-1536g7w")},m(l,a){M(e,l,a),g(l,t,a),g(l,n,a),o=!0},p(l,a){const u={};512&a[1]&&(u.$$scope={dirty:a,ctx:l}),e.$set(u),(!o||9&a[0]&&!Re(n.src,s="data:"+st(l[3])+";base64,"+btoa(l[0].originalCode)))&&D(n,"src",s),(!o||8192&a[0]&&i!==(i="Original "+ie(l[13])))&&D(n,"alt",i)},i(l){o||(p(e.$$.fragment,l),o=!0)},o(l){$(e.$$.fragment,l),o=!1},d(l){l&&(h(t),h(n)),q(e,l)}}}function or(r){let e;return{c(){e=R("Previous version:")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function lr(r){let e,t,n=ie(r[13])+"";return{c(){e=R("Image deleted: "),t=R(n)},m(s,i){g(s,e,i),g(s,t,i)},p(s,i){8192&i[0]&&n!==(n=ie(s[13])+"")&&le(t,n)},d(s){s&&(h(e),h(t))}}}function rn(r){let e,t,n,s,i,o;return e=new fe({props:{class:"image-info-text",$$slots:{default:[ar]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=Z(),n=b("img"),Re(n.src,s="data:"+st(r[3])+";base64,"+btoa(r[0].originalCode))||D(n,"src",s),D(n,"alt",i="Original "+ie(r[13])),D(n,"class","image-preview svelte-1536g7w")},m(l,a){M(e,l,a),g(l,t,a),g(l,n,a),o=!0},p(l,a){const u={};512&a[1]&&(u.$$scope={dirty:a,ctx:l}),e.$set(u),(!o||9&a[0]&&!Re(n.src,s="data:"+st(l[3])+";base64,"+btoa(l[0].originalCode)))&&D(n,"src",s),(!o||8192&a[0]&&i!==(i="Original "+ie(l[13])))&&D(n,"alt",i)},i(l){o||(p(e.$$.fragment,l),o=!0)},o(l){$(e.$$.fragment,l),o=!1},d(l){l&&(h(t),h(n)),q(e,l)}}}function ar(r){let e;return{c(){e=R("Previous version:")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function ur(r){let e,t,n,s;const i=[Gi,Qi,Wi,Hi],o=[];function l(a,u){return a[17]?0:a[16]?1:a[15]?2:3}return t=l(r),n=o[t]=i[t](r),{c(){e=b("div"),n.c(),D(e,"class","changes svelte-1536g7w")},m(a,u){g(a,e,u),o[t].m(e,null),s=!0},p(a,u){let c=t;t=l(a),t===c?o[t].p(a,u):(H(),$(o[c],1,1,()=>{o[c]=null}),W(),n=o[t],n?n.p(a,u):(n=o[t]=i[t](a),n.c()),p(n,1),n.m(e,null))},i(a){s||(p(n),s=!0)},o(a){$(n),s=!1},d(a){a&&h(e),o[t].d()}}}function cr(r){let e,t=ie(r[13])+"";return{c(){e=R(t)},m(n,s){g(n,e,s)},p(n,s){8192&s[0]&&t!==(t=ie(n[13])+"")&&le(e,t)},d(n){n&&h(e)}}}function dr(r){let e,t;return e=new Oe({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[cr]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};8192&s[0]|512&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function on(r){let e,t,n=Ze(r[13])+"";return{c(){e=b("span"),t=R(n),D(e,"class","c-directory svelte-1536g7w")},m(s,i){g(s,e,i),T(e,t)},p(s,i){8192&i[0]&&n!==(n=Ze(s[13])+"")&&le(t,n)},d(s){s&&h(e)}}}function pr(r){let e,t,n,s=r[22]>0&&ln(r),i=r[21]>0&&an(r);return{c(){e=b("div"),s&&s.c(),t=Z(),i&&i.c(),D(e,"class","changes-indicator svelte-1536g7w")},m(o,l){g(o,e,l),s&&s.m(e,null),T(e,t),i&&i.m(e,null),n=!0},p(o,l){o[22]>0?s?(s.p(o,l),4194304&l[0]&&p(s,1)):(s=ln(o),s.c(),p(s,1),s.m(e,t)):s&&(H(),$(s,1,1,()=>{s=null}),W()),o[21]>0?i?(i.p(o,l),2097152&l[0]&&p(i,1)):(i=an(o),i.c(),p(i,1),i.m(e,null)):i&&(H(),$(i,1,1,()=>{i=null}),W())},i(o){n||(p(s),p(i),n=!0)},o(o){$(s),$(i),n=!1},d(o){o&&h(e),s&&s.d(),i&&i.d()}}}function fr(r){let e;return{c(){e=b("span"),e.textContent="New File",D(e,"class","new-file-badge svelte-1536g7w")},m(t,n){g(t,e,n)},p:J,i:J,o:J,d(t){t&&h(e)}}}function ln(r){let e,t,n;return t=new fe({props:{size:1,$$slots:{default:[gr]},$$scope:{ctx:r}}}),{c(){e=b("span"),L(t.$$.fragment),D(e,"class","additions svelte-1536g7w")},m(s,i){g(s,e,i),M(t,e,null),n=!0},p(s,i){const o={};4194304&i[0]|512&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function gr(r){let e,t;return{c(){e=R("+"),t=R(r[22])},m(n,s){g(n,e,s),g(n,t,s)},p(n,s){4194304&s[0]&&le(t,n[22])},d(n){n&&(h(e),h(t))}}}function an(r){let e,t,n;return t=new fe({props:{size:1,$$slots:{default:[hr]},$$scope:{ctx:r}}}),{c(){e=b("span"),L(t.$$.fragment),D(e,"class","deletions svelte-1536g7w")},m(s,i){g(s,e,i),M(t,e,null),n=!0},p(s,i){const o={};2097152&i[0]|512&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function hr(r){let e,t;return{c(){e=R("-"),t=R(r[21])},m(n,s){g(n,e,s),g(n,t,s)},p(n,s){2097152&s[0]&&le(t,n[21])},d(n){n&&(h(e),h(t))}}}function $r(r){let e,t;return e=new Ge({props:{content:r[11],$$slots:{default:[Fr]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};2048&s[0]&&(i.content=n[11]),4096&s[0]|512&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function mr(r){let e,t,n;return t=new fe({props:{size:1,$$slots:{default:[xr]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),D(e,"class","applied svelte-1536g7w")},m(s,i){g(s,e,i),M(t,e,null),n=!0},p(s,i){const o={};512&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function Dr(r){let e,t,n,s;return n=new ot({}),{c(){e=R(`Apply
            `),t=b("div"),L(n.$$.fragment),D(t,"class","applied__icon svelte-1536g7w")},m(i,o){g(i,e,o),g(i,t,o),M(n,t,null),s=!0},p:J,i(i){s||(p(n.$$.fragment,i),s=!0)},o(i){$(n.$$.fragment,i),s=!1},d(i){i&&(h(e),h(t)),q(n)}}}function Fr(r){let e,t;return e=new Oe({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[12],$$slots:{default:[Dr]},$$scope:{ctx:r}}}),e.$on("click",r[26]),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};4096&s[0]&&(i.disabled=n[12]),512&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function xr(r){let e,t,n;return t=new wt({props:{iconName:"check"}}),{c(){e=R(`Applied
            `),L(t.$$.fragment)},m(s,i){g(s,e,i),M(t,s,i),n=!0},p:J,i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t,s)}}}function Cr(r){let e,t,n,s,i,o,l,a,u,c,d,f,m,A=Ze(r[13]);t=new Ss({}),i=new Ge({props:{content:r[13],triggerOn:[Nt.Hover],$$slots:{default:[dr]},$$scope:{ctx:r}}});let v=A&&on(r);const _=[fr,pr],C=[];function N(k,E){return k[20]?0:1}a=N(r),u=C[a]=_[a](r);const F=[mr,$r],x=[];function y(k,E){return k[5]?0:1}return d=y(r),f=x[d]=F[d](r),{c(){e=b("div"),L(t.$$.fragment),n=Z(),s=b("div"),L(i.$$.fragment),o=Z(),v&&v.c(),l=Z(),u.c(),c=Z(),f.c(),D(s,"class","c-path svelte-1536g7w"),D(e,"slot","header"),D(e,"class","header svelte-1536g7w")},m(k,E){g(k,e,E),M(t,e,null),T(e,n),T(e,s),M(i,s,null),T(s,o),v&&v.m(s,null),T(e,l),C[a].m(e,null),T(e,c),x[d].m(e,null),m=!0},p(k,E){const S={};8192&E[0]&&(S.content=k[13]),8192&E[0]|512&E[1]&&(S.$$scope={dirty:E,ctx:k}),i.$set(S),8192&E[0]&&(A=Ze(k[13])),A?v?v.p(k,E):(v=on(k),v.c(),v.m(s,null)):v&&(v.d(1),v=null);let j=a;a=N(k),a===j?C[a].p(k,E):(H(),$(C[j],1,1,()=>{C[j]=null}),W(),u=C[a],u?u.p(k,E):(u=C[a]=_[a](k),u.c()),p(u,1),u.m(e,c));let O=d;d=y(k),d===O?x[d].p(k,E):(H(),$(x[O],1,1,()=>{x[O]=null}),W(),f=x[d],f?f.p(k,E):(f=x[d]=F[d](k),f.c()),p(f,1),f.m(e,null))},i(k){m||(p(t.$$.fragment,k),p(i.$$.fragment,k),p(u),p(f),m=!0)},o(k){$(t.$$.fragment,k),$(i.$$.fragment,k),$(u),$(f),m=!1},d(k){k&&h(e),q(t),q(i),v&&v.d(),C[a].d(),x[d].d()}}}function kr(r){let e,t,n,s,i;function o(a){r[39](a)}let l={stickyHeader:!0,$$slots:{header:[Cr],default:[ur]},$$scope:{ctx:r}};return r[2]!==void 0&&(l.collapsed=r[2]),t=new Ns({props:l}),qe.push(()=>He(t,"collapsed",o)),{c(){e=b("div"),L(t.$$.fragment),D(e,"class","c svelte-1536g7w"),D(e,"id",s=Bt(r[3])),ve(e,"focused",r[23]===r[3])},m(a,u){g(a,e,u),M(t,e,null),i=!0},p(a,u){const c={};8388603&u[0]|512&u[1]&&(c.$$scope={dirty:u,ctx:a}),!n&&4&u[0]&&(n=!0,c.collapsed=a[2],We(()=>n=!1)),t.$set(c),(!i||8&u[0]&&s!==(s=Bt(a[3])))&&D(e,"id",s),(!i||8388616&u[0])&&ve(e,"focused",a[23]===a[3])},i(a){i||(p(t.$$.fragment,a),i=!0)},o(a){$(t.$$.fragment,a),i=!1},d(a){a&&h(e),q(t)}}}function wr(r,e,t){let n,s,i,o,l,a,u,c,d,f,m,A,v,_,C,N,F,x,y,k,E,S,j;ze(r,_s,I=>t(37,S=I));let{path:O}=e,{change:G}=e,{descriptions:ce=[]}=e,{areDescriptionsVisible:ne=!0}=e,{isExpandedDefault:he}=e,{isCollapsed:$e=!he}=e,{isApplying:Q}=e,{hasApplied:ye}=e,{onApplyChanges:xe}=e,{onCodeChange:oe}=e,{isAgentFromDifferentRepo:we=!1}=e;const Me=Qs();ze(r,Me,I=>t(23,j=I));let se=G.modifiedCode;return r.$$set=I=>{"path"in I&&t(3,O=I.path),"change"in I&&t(0,G=I.change),"descriptions"in I&&t(4,ce=I.descriptions),"areDescriptionsVisible"in I&&t(1,ne=I.areDescriptionsVisible),"isExpandedDefault"in I&&t(27,he=I.isExpandedDefault),"isCollapsed"in I&&t(2,$e=I.isCollapsed),"isApplying"in I&&t(28,Q=I.isApplying),"hasApplied"in I&&t(5,ye=I.hasApplied),"onApplyChanges"in I&&t(29,xe=I.onApplyChanges),"onCodeChange"in I&&t(30,oe=I.onCodeChange),"isAgentFromDifferentRepo"in I&&t(31,we=I.isAgentFromDifferentRepo)},r.$$.update=()=>{var I;1&r.$$.dirty[0]&&t(6,se=G.modifiedCode),1&r.$$.dirty[0]&&t(36,n=_t(G.diff)),32&r.$$.dirty[1]&&t(22,s=n.additions),32&r.$$.dirty[1]&&t(21,i=n.deletions),1&r.$$.dirty[0]&&t(20,o=Vs(G)),1&r.$$.dirty[0]&&t(19,l=Zs(G)),8&r.$$.dirty[0]&&t(35,a=tn(O)),8&r.$$.dirty[0]&&t(18,u=st(O)),8&r.$$.dirty[0]&&t(34,c=function(P){if(tn(P))return!1;const te=St(P);return Ui.includes(te)}(O)),1&r.$$.dirty[0]&&t(10,d=((I=G.originalCode)==null?void 0:I.length)||0),64&r.$$.dirty[0]&&t(9,f=(se==null?void 0:se.length)||0),1024&r.$$.dirty[0]&&t(33,m=nn(d)),512&r.$$.dirty[0]&&t(32,A=nn(f)),65&r.$$.dirty[0]&&t(8,v=!se&&!!G.originalCode),65&r.$$.dirty[0]&&t(7,_=!!se&&!G.originalCode),16&r.$$.dirty[1]&&t(17,C=a),24&r.$$.dirty[1]&&t(16,N=!a&&c),384&r.$$.dirty[0]|30&r.$$.dirty[1]&&t(15,F=!a&&!c&&(A||v&&m||_&&A)),64&r.$$.dirty[1]&&t(14,x=Ps(S==null?void 0:S.category,S==null?void 0:S.intensity)),8&r.$$.dirty[0]&&t(13,y=Os(O)),268435456&r.$$.dirty[0]|1&r.$$.dirty[1]&&t(12,k=Q||we),268435456&r.$$.dirty[0]|1&r.$$.dirty[1]&&t(11,E=Q?"Applying changes...":we?"Cannot apply changes from a different repository locally":"Apply changes to local file")},[G,ne,$e,O,ce,ye,se,_,v,f,d,E,k,y,x,F,N,C,u,l,o,i,s,j,Me,function(I){t(6,se=I.detail.modifiedCode),oe==null||oe(se)},function(){t(0,G.modifiedCode=se,G),oe==null||oe(se),xe==null||xe()},he,Q,xe,oe,we,A,m,c,a,n,S,function(I){ne=I,t(1,ne)},function(I){$e=I,t(2,$e)}]}let vr=class extends K{constructor(r){super(),ee(this,r,wr,kr,Y,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:27,isCollapsed:2,isApplying:28,hasApplied:5,onApplyChanges:29,onCodeChange:30,isAgentFromDifferentRepo:31},null,[-1,-1])}};function un(r,e,t){const n=r.slice();return n[6]=e[t],n}function yr(r){let e,t;return e=new bi({props:{filename:r[0].name}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.filename=n[0].name),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Ar(r){let e,t;return e=new lt({props:{icon:r[0].isExpanded?"chevron-down":"chevron-right"}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.icon=n[0].isExpanded?"chevron-down":"chevron-right"),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function br(r){let e,t,n=(r[0].displayName||r[0].name)+"";return{c(){e=b("span"),t=R(n),D(e,"class","full-path-text svelte-qnxoj")},m(s,i){g(s,e,i),T(e,t)},p(s,i){1&i&&n!==(n=(s[0].displayName||s[0].name)+"")&&le(t,n)},d(s){s&&h(e)}}}function cn(r){let e,t,n=pe(Array.from(r[0].children.values()).sort(pn)),s=[];for(let o=0;o<n.length;o+=1)s[o]=dn(un(r,n,o));const i=o=>$(s[o],1,1,()=>{s[o]=null});return{c(){e=b("div");for(let o=0;o<s.length;o+=1)s[o].c();D(e,"class","tree-node__children svelte-qnxoj"),D(e,"role","group")},m(o,l){g(o,e,l);for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(e,null);t=!0},p(o,l){if(3&l){let a;for(n=pe(Array.from(o[0].children.values()).sort(pn)),a=0;a<n.length;a+=1){const u=un(o,n,a);s[a]?(s[a].p(u,l),p(s[a],1)):(s[a]=dn(u),s[a].c(),p(s[a],1),s[a].m(e,null))}for(H(),a=n.length;a<s.length;a+=1)i(a);W()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)p(s[l]);t=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)$(s[l]);t=!1},d(o){o&&h(e),Le(s,o)}}}function dn(r){let e,t;return e=new Gs({props:{node:r[6],indentLevel:r[1]+1}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.node=n[6]),2&s&&(i.indentLevel=n[1]+1),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Er(r){let e,t,n,s,i,o,l,a,u,c,d,f,m,A,v,_,C;const N=[Ar,yr],F=[];function x(k,E){return k[0].isFile?1:0}o=x(r),l=F[o]=N[o](r),c=new fe({props:{size:1,$$slots:{default:[br]},$$scope:{ctx:r}}});let y=!r[0].isFile&&r[0].isExpanded&&r[0].children.size>0&&cn(r);return{c(){e=b("div"),t=b("div"),n=b("div"),s=Z(),i=b("div"),l.c(),a=Z(),u=b("span"),L(c.$$.fragment),A=Z(),y&&y.c(),D(n,"class","tree-node__indent svelte-qnxoj"),Ae(n,"width",6*r[1]+"px"),D(i,"class","tree-node__icon-container svelte-qnxoj"),D(u,"class","tree-node__label svelte-qnxoj"),D(u,"title",d=r[0].displayName||r[0].name),ve(u,"full-path",r[0].displayName),D(t,"class","tree-node__content svelte-qnxoj"),D(t,"role","treeitem"),D(t,"tabindex","0"),D(t,"aria-selected",f=r[0].path===r[2]),D(t,"aria-expanded",m=r[0].isFile?void 0:r[0].isExpanded),ve(t,"selected",r[0].path===r[2]),ve(t,"collapsed-folder",r[0].displayName&&!r[0].isFile),D(e,"class","tree-node svelte-qnxoj")},m(k,E){g(k,e,E),T(e,t),T(t,n),T(t,s),T(t,i),F[o].m(i,null),T(t,a),T(t,u),M(c,u,null),T(e,A),y&&y.m(e,null),v=!0,_||(C=[nt(t,"click",r[4]),nt(t,"keydown",r[5])],_=!0)},p(k,[E]){(!v||2&E)&&Ae(n,"width",6*k[1]+"px");let S=o;o=x(k),o===S?F[o].p(k,E):(H(),$(F[S],1,1,()=>{F[S]=null}),W(),l=F[o],l?l.p(k,E):(l=F[o]=N[o](k),l.c()),p(l,1),l.m(i,null));const j={};513&E&&(j.$$scope={dirty:E,ctx:k}),c.$set(j),(!v||1&E&&d!==(d=k[0].displayName||k[0].name))&&D(u,"title",d),(!v||1&E)&&ve(u,"full-path",k[0].displayName),(!v||5&E&&f!==(f=k[0].path===k[2]))&&D(t,"aria-selected",f),(!v||1&E&&m!==(m=k[0].isFile?void 0:k[0].isExpanded))&&D(t,"aria-expanded",m),(!v||5&E)&&ve(t,"selected",k[0].path===k[2]),(!v||1&E)&&ve(t,"collapsed-folder",k[0].displayName&&!k[0].isFile),!k[0].isFile&&k[0].isExpanded&&k[0].children.size>0?y?(y.p(k,E),1&E&&p(y,1)):(y=cn(k),y.c(),p(y,1),y.m(e,null)):y&&(H(),$(y,1,1,()=>{y=null}),W())},i(k){v||(p(l),p(c.$$.fragment,k),p(y),v=!0)},o(k){$(l),$(c.$$.fragment,k),$(y),v=!1},d(k){k&&h(e),F[o].d(),q(c),y&&y.d(),_=!1,Bs(C)}}}const pn=(r,e)=>r.isFile===e.isFile?r.name.localeCompare(e.name):r.isFile?1:-1;function _r(r,e,t){let n,{node:s}=e,{indentLevel:i=0}=e;const o=Qs();function l(){s.isFile?o.set(s.path):t(0,s.isExpanded=!s.isExpanded,s)}return ze(r,o,a=>t(2,n=a)),r.$$set=a=>{"node"in a&&t(0,s=a.node),"indentLevel"in a&&t(1,i=a.indentLevel)},[s,i,n,o,l,a=>a.key==="Enter"&&l()]}class Gs extends K{constructor(e){super(),ee(this,e,_r,Er,Y,{node:0,indentLevel:1})}}function fn(r,e,t){const n=r.slice();return n[4]=e[t],n}function Br(r){let e,t,n=pe(Array.from(r[1].children.values()).sort(hn)),s=[];for(let o=0;o<n.length;o+=1)s[o]=gn(fn(r,n,o));const i=o=>$(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();e=ke()},m(o,l){for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(o,l);g(o,e,l),t=!0},p(o,l){if(2&l){let a;for(n=pe(Array.from(o[1].children.values()).sort(hn)),a=0;a<n.length;a+=1){const u=fn(o,n,a);s[a]?(s[a].p(u,l),p(s[a],1)):(s[a]=gn(u),s[a].c(),p(s[a],1),s[a].m(e.parentNode,e))}for(H(),a=n.length;a<s.length;a+=1)i(a);W()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)p(s[l]);t=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)$(s[l]);t=!1},d(o){o&&h(e),Le(s,o)}}}function zr(r){let e,t,n;return t=new fe({props:{size:1,color:"neutral",$$slots:{default:[Mr]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),D(e,"class","tree-view__empty svelte-1tnd9l7")},m(s,i){g(s,e,i),M(t,e,null),n=!0},p(s,i){const o={};128&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function Lr(r){let e;return{c(){e=b("div"),e.innerHTML='<div class="tree-view__skeleton svelte-1tnd9l7"><div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="width: 70%;"></div></div>',D(e,"class","tree-view__loading svelte-1tnd9l7")},m(t,n){g(t,e,n)},p:J,i:J,o:J,d(t){t&&h(e)}}}function gn(r){let e,t;return e=new Gs({props:{node:r[4],indentLevel:0}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};2&s&&(i.node=n[4]),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Mr(r){let e;return{c(){e=R("No changed files")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function qr(r){let e,t,n,s,i;const o=[Lr,zr,Br],l=[];function a(u,c){return u[0]?0:u[1].children.size===0?1:2}return n=a(r),s=l[n]=o[n](r),{c(){e=b("div"),t=b("div"),s.c(),D(t,"class","tree-view__content svelte-1tnd9l7"),D(t,"role","tree"),D(t,"aria-label","Changed Files"),D(e,"class","tree-view svelte-1tnd9l7")},m(u,c){g(u,e,c),T(e,t),l[n].m(t,null),i=!0},p(u,[c]){let d=n;n=a(u),n===d?l[n].p(u,c):(H(),$(l[d],1,1,()=>{l[d]=null}),W(),s=l[n],s?s.p(u,c):(s=l[n]=o[n](u),s.c()),p(s,1),s.m(t,null))},i(u){i||(p(s),i=!0)},o(u){$(s),i=!1},d(u){u&&h(e),l[n].d()}}}function zt(r,e=!1){if(r.isFile)return;let t="";e&&(t=function(o){let l=o.path.split("/"),a=o;for(;;){const u=Array.from(a.children.values()).filter(d=>!d.isFile),c=Array.from(a.children.values()).filter(d=>d.isFile);if(u.length!==1||c.length!==0)break;a=u[0],l.push(a.name)}return l.join("/")}(r));const n=Array.from(r.children.values()).filter(o=>!o.isFile);for(const o of n)zt(o);const s=Array.from(r.children.values()).filter(o=>!o.isFile),i=Array.from(r.children.values()).filter(o=>o.isFile);if(s.length===1&&i.length===0){const o=s[0],l=o.name;if(e){r.displayName=t||`${r.name}/${l}`;for(const[a,u]of o.children.entries()){const c=`${a}`;r.children.set(c,u)}r.children.delete(l)}else{r.displayName?o.displayName=`${r.displayName}/${l}`:o.displayName=`${r.name}/${l}`;for(const[a,u]of o.children.entries()){const c=`${l}/${a}`;r.children.set(c,u)}r.children.delete(l)}}}const hn=(r,e)=>r.isFile===e.isFile?r.name.localeCompare(e.name):r.isFile?1:-1;function Rr(r,e,t){let n,{changedFiles:s=[]}=e,{isLoading:i=!1}=e;function o(l){const a={name:"",path:"",isFile:!1,children:new Map,isExpanded:!0};return l.forEach(u=>{const c=u.change_type===Ai.deleted?u.old_path:u.new_path;c&&function(d,f){const m=f.split("/");let A=d;for(let v=0;v<m.length;v++){const _=m[v],C=v===m.length-1,N=m.slice(0,v+1).join("/");A.children.has(_)||A.children.set(_,{name:_,path:N,isFile:C,children:new Map,isExpanded:!0}),A=A.children.get(_)}}(a,c)}),function(u){if(!u.isFile)if(u.path!=="")zt(u);else{const c=Array.from(u.children.values()).filter(d=>!d.isFile);for(const d of c)zt(d,!0)}}(a),a}return r.$$set=l=>{"changedFiles"in l&&t(2,s=l.changedFiles),"isLoading"in l&&t(0,i=l.isLoading)},r.$$.update=()=>{4&r.$$.dirty&&t(1,n=o(s))},[i,n,s]}class Js extends K{constructor(e){super(),ee(this,e,Rr,qr,Y,{changedFiles:2,isLoading:0})}}function $n(r,e,t){const n=r.slice();return n[17]=e[t],n}function Tr(r){let e;return{c(){e=R("Changed files")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Nr(r){let e,t,n;return t=new fe({props:{size:1,color:"neutral",$$slots:{default:[Pr]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),D(e,"class","c-edits-list c-edits-list--empty svelte-6iqvaj")},m(s,i){g(s,e,i),M(t,e,null),n=!0},p(s,i){const o={};1048576&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function Sr(r){let e,t,n,s,i,o,l=[],a=new Map,u=r[8].length>0&&mn(r),c=pe(r[8]);const d=f=>f[17].qualifiedPathName.relPath;for(let f=0;f<c.length;f+=1){let m=$n(r,c,f),A=d(m);a.set(A,l[f]=Dn(A,m))}return{c(){e=b("div"),t=b("div"),u&&u.c(),n=Z(),s=b("div"),i=b("div");for(let f=0;f<l.length;f+=1)l[f].c();D(t,"class","c-edits-list-controls svelte-6iqvaj"),D(e,"class","c-edits-list-header svelte-6iqvaj"),D(i,"class","c-edits-section svelte-6iqvaj"),D(s,"class","c-edits-list svelte-6iqvaj")},m(f,m){g(f,e,m),T(e,t),u&&u.m(t,null),g(f,n,m),g(f,s,m),T(s,i);for(let A=0;A<l.length;A+=1)l[A]&&l[A].m(i,null);o=!0},p(f,m){f[8].length>0?u?(u.p(f,m),256&m&&p(u,1)):(u=mn(f),u.c(),p(u,1),u.m(t,null)):u&&(H(),$(u,1,1,()=>{u=null}),W()),1326&m&&(c=pe(f[8]),H(),l=qs(l,m,d,1,f,c,a,i,Rs,Dn,null,$n),W())},i(f){if(!o){p(u);for(let m=0;m<c.length;m+=1)p(l[m]);o=!0}},o(f){$(u);for(let m=0;m<l.length;m+=1)$(l[m]);o=!1},d(f){f&&(h(e),h(n),h(s)),u&&u.d();for(let m=0;m<l.length;m+=1)l[m].d()}}}function Pr(r){let e;return{c(){e=R("No changes to show")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function mn(r){let e,t;return e=new Oe({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[6]||r[7]||r[2].length>0||!r[9],$$slots:{default:[Vr]},$$scope:{ctx:r}}}),e.$on("click",r[11]),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};708&s&&(i.disabled=n[6]||n[7]||n[2].length>0||!n[9]),1048768&s&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Ir(r){let e;return{c(){e=R("Apply all")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Or(r){let e;return{c(){e=R("All applied")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function jr(r){let e;return{c(){e=R("Applying...")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Vr(r){let e,t,n,s;function i(a,u){return a[6]?jr:a[7]?Or:Ir}let o=i(r),l=o(r);return n=new ot({}),{c(){l.c(),e=Z(),t=b("div"),L(n.$$.fragment),D(t,"class","c-edits-list-controls__icon svelte-6iqvaj")},m(a,u){l.m(a,u),g(a,e,u),g(a,t,u),M(n,t,null),s=!0},p(a,u){o!==(o=i(a))&&(l.d(1),l=o(a),l&&(l.c(),l.m(e.parentNode,e)))},i(a){s||(p(n.$$.fragment,a),s=!0)},o(a){$(n.$$.fragment,a),s=!1},d(a){a&&(h(e),h(t)),l.d(a),q(n)}}}function Dn(r,e){let t,n,s,i,o;function l(...u){return e[15](e[17],...u)}function a(){return e[16](e[17])}return n=new vr({props:{path:e[17].qualifiedPathName.relPath,change:e[17].diff,isApplying:e[2].includes(e[17].qualifiedPathName.relPath),hasApplied:e[3].includes(e[17].qualifiedPathName.relPath),onCodeChange:l,onApplyChanges:a,isExpandedDefault:!0}}),{key:r,first:null,c(){t=b("div"),L(n.$$.fragment),s=Z(),D(t,"class",""),this.first=t},m(u,c){g(u,t,c),M(n,t,null),T(t,s),o=!0},p(u,c){e=u;const d={};256&c&&(d.path=e[17].qualifiedPathName.relPath),256&c&&(d.change=e[17].diff),260&c&&(d.isApplying=e[2].includes(e[17].qualifiedPathName.relPath)),264&c&&(d.hasApplied=e[3].includes(e[17].qualifiedPathName.relPath)),256&c&&(d.onCodeChange=l),290&c&&(d.onApplyChanges=a),n.$set(d)},i(u){o||(p(n.$$.fragment,u),u&&ci(()=>{o&&(i||(i=Qt(t,Jt,{},!0)),i.run(1))}),o=!0)},o(u){$(n.$$.fragment,u),u&&(i||(i=Qt(t,Jt,{},!1)),i.run(0)),o=!1},d(u){u&&h(t),q(n),u&&i&&i.end()}}}function Zr(r){let e,t,n,s,i,o,l,a,u,c,d,f;i=new fe({props:{size:1,class:"c-file-explorer__tree__header__label",$$slots:{default:[Tr]},$$scope:{ctx:r}}}),l=new Js({props:{changedFiles:r[0],isLoading:r[4]}});const m=[Sr,Nr],A=[];function v(_,C){return _[8].length>0?0:1}return c=v(r),d=A[c]=m[c](r),{c(){e=b("div"),t=b("div"),n=b("div"),s=b("div"),L(i.$$.fragment),o=Z(),L(l.$$.fragment),a=Z(),u=b("div"),d.c(),D(s,"class","c-file-explorer__tree__header svelte-6iqvaj"),D(n,"class","c-file-explorer__tree svelte-6iqvaj"),D(u,"class","c-file-explorer__details svelte-6iqvaj"),D(t,"class","c-file-explorer__layout svelte-6iqvaj"),D(e,"class","c-edits-list-container svelte-6iqvaj")},m(_,C){g(_,e,C),T(e,t),T(t,n),T(n,s),M(i,s,null),T(s,o),M(l,s,null),T(t,a),T(t,u),A[c].m(u,null),f=!0},p(_,[C]){const N={};1048576&C&&(N.$$scope={dirty:C,ctx:_}),i.$set(N);const F={};1&C&&(F.changedFiles=_[0]),16&C&&(F.isLoading=_[4]),l.$set(F);let x=c;c=v(_),c===x?A[c].p(_,C):(H(),$(A[x],1,1,()=>{A[x]=null}),W(),d=A[c],d?d.p(_,C):(d=A[c]=m[c](_),d.c()),p(d,1),d.m(u,null))},i(_){f||(p(i.$$.fragment,_),p(l.$$.fragment,_),p(d),f=!0)},o(_){$(i.$$.fragment,_),$(l.$$.fragment,_),$(d),f=!1},d(_){_&&h(e),q(i),q(l),A[c].d()}}}function Ur(r,e,t){let n,s,i,o,l,{changedFiles:a}=e,{onApplyChanges:u}=e,{pendingFiles:c=[]}=e,{appliedFiles:d=[]}=e,{isLoadingTreeView:f=!1}=e,m={},A=!1,v=!1;function _(C,N){t(5,m[C]=N,m)}return r.$$set=C=>{"changedFiles"in C&&t(0,a=C.changedFiles),"onApplyChanges"in C&&t(1,u=C.onApplyChanges),"pendingFiles"in C&&t(2,c=C.pendingFiles),"appliedFiles"in C&&t(3,d=C.appliedFiles),"isLoadingTreeView"in C&&t(4,f=C.isLoadingTreeView)},r.$$.update=()=>{if(1&r.$$.dirty&&t(14,n=JSON.stringify(a)),8&r.$$.dirty&&t(12,s=JSON.stringify(d)),4&r.$$.dirty&&t(13,i=JSON.stringify(c)),16384&r.$$.dirty&&n&&(t(5,m={}),t(6,A=!1),t(7,v=!1)),33&r.$$.dirty&&t(8,l=a.map(C=>{const N=C.new_path||C.old_path,F=C.old_contents||"",x=C.new_contents||"",y=Bi.generateDiff(C.old_path,C.new_path,F,x),k=function(E,S){const j=mt("oldFile","newFile",E,S,"","",{context:3}),O=mi(j);let G=0,ce=0,ne=[];for(const he of O)for(const $e of he.hunks)for(const Q of $e.lines){const ye=Q.startsWith("+"),xe=Q.startsWith("-");ye&&G++,xe&&ce++,ne.push({value:Q,added:ye,removed:xe})}return{totalAddedLines:G,totalRemovedLines:ce,changes:ne,diff:j}}(F,x);return m[N]||t(5,m[N]=x,m),{qualifiedPathName:{rootPath:"",relPath:N},lineChanges:k,oldContents:F,newContents:x,diff:y}})),28940&r.$$.dirty&&t(9,o=(()=>{if(n&&s&&i){const C=l.map(N=>N.qualifiedPathName.relPath);return C.length!==0&&C.some(N=>!d.includes(N)&&!c.includes(N))}return!1})()),332&r.$$.dirty&&A){const C=l.map(N=>N.qualifiedPathName.relPath);C.filter(N=>!d.includes(N)&&!c.includes(N)).length===0&&C.every(N=>d.includes(N)||c.includes(N))&&c.length===0&&d.length>0&&(t(6,A=!1),t(7,v=!0))}if(4552&r.$$.dirty&&l.length>0&&!A&&s){const C=l.map(N=>N.qualifiedPathName.relPath);if(C.length>0){const N=C.every(F=>d.includes(F));N&&d.length>0?t(7,v=!0):!N&&v&&t(7,v=!1)}}},[a,u,c,d,f,m,A,v,l,o,_,function(){if(!u)return;const C=l.map(F=>F.qualifiedPathName.relPath);if(C.every(F=>d.includes(F)))return void t(7,v=!0);const N=C.filter(F=>!d.includes(F)&&!c.includes(F));N.length!==0&&(t(6,A=!0),t(7,v=!1),N.forEach(F=>{const x=l.find(y=>y.qualifiedPathName.relPath===F);if(x){const y=m[F]||x.newContents;u(F,x.oldContents,y)}}))},s,i,n,(C,N)=>{_(C.qualifiedPathName.relPath,N)},C=>{const N=m[C.qualifiedPathName.relPath]||C.newContents;u(C.qualifiedPathName.relPath,C.oldContents,N)}]}class Hr extends K{constructor(e){super(),ee(this,e,Ur,Zr,Y,{changedFiles:0,onApplyChanges:1,pendingFiles:2,appliedFiles:3,isLoadingTreeView:4})}}function Fn(r,e,t){const n=r.slice();return n[3]=e[t],n}function xn(r){let e,t=pe(r[1].paths),n=[];for(let s=0;s<t.length;s+=1)n[s]=Cn(Fn(r,t,s));return{c(){for(let s=0;s<n.length;s+=1)n[s].c();e=ke()},m(s,i){for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(s,i);g(s,e,i)},p(s,i){if(2&i){let o;for(t=pe(s[1].paths),o=0;o<t.length;o+=1){const l=Fn(s,t,o);n[o]?n[o].p(l,i):(n[o]=Cn(l),n[o].c(),n[o].m(e.parentNode,e))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},d(s){s&&h(e),Le(n,s)}}}function Cn(r){let e,t;return{c(){e=zs("path"),D(e,"d",t=r[3]),D(e,"fill-rule","evenodd"),D(e,"clip-rule","evenodd")},m(n,s){g(n,e,s)},p(n,s){2&s&&t!==(t=n[3])&&D(e,"d",t)},d(n){n&&h(e)}}}function Wr(r){let e,t=r[1]&&xn(r);return{c(){e=zs("svg"),t&&t.c(),D(e,"width","14"),D(e,"viewBox","0 0 20 20"),D(e,"fill","currentColor"),D(e,"class","svelte-10h4f31")},m(n,s){g(n,e,s),t&&t.m(e,null)},p(n,s){n[1]?t?t.p(n,s):(t=xn(n),t.c(),t.m(e,null)):t&&(t.d(1),t=null)},d(n){n&&h(e),t&&t.d()}}}function Qr(r){let e,t;return e=new Ge({props:{content:`This is a ${r[0]} change`,triggerOn:[Nt.Hover],$$slots:{default:[Wr]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.content=`This is a ${n[0]} change`),66&s&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Gr(r,e,t){let n,{type:s}=e;const i={fix:{paths:["M6.56 1.14a.75.75 0 0 1 .177 1.045 3.989 3.989 0 0 0-.464.86c.185.17.382.329.59.473A3.993 3.993 0 0 1 10 2c1.272 0 2.405.594 3.137 1.518.208-.144.405-.302.59-.473a3.989 3.989 0 0 0-.464-.86.75.75 0 0 1 1.222-.869c.369.519.65 1.105.822 1.736a.75.75 0 0 1-.174.707 7.03 7.03 0 0 1-1.299 1.098A4 4 0 0 1 14 6c0 .52-.301.963-.723 1.187a6.961 6.961 0 0 1-1.158.486c.13.208.231.436.296.679 1.413-.174 2.779-.5 4.081-.96a19.655 19.655 0 0 0-.09-2.319.75.75 0 1 1 1.493-.146 21.239 21.239 0 0 1 .08 3.028.75.75 0 0 1-.482.667 20.873 20.873 0 0 1-5.153 1.249 2.521 2.521 0 0 1-.107.247 20.945 20.945 0 0 1 5.252 1.257.75.75 0 0 1 .482.74 20.945 20.945 0 0 1-.908 5.107.75.75 0 0 1-1.433-.444c.415-1.34.69-2.743.806-4.191-.495-.173-1-.327-1.512-.46.05.284.076.575.076.873 0 1.814-.517 3.312-1.426 4.37A4.639 4.639 0 0 1 10 19a4.639 4.639 0 0 1-3.574-1.63C5.516 16.311 5 14.813 5 13c0-.298.026-.59.076-.873-.513.133-1.017.287-1.512.46.116 1.448.39 2.85.806 4.191a.75.75 0 1 1-1.433.444 20.94 20.94 0 0 1-.908-5.107.75.75 0 0 1 .482-.74 20.838 20.838 0 0 1 5.252-1.257 2.493 2.493 0 0 1-.107-.247 20.874 20.874 0 0 1-5.153-1.249.75.75 0 0 1-.482-.667 21.342 21.342 0 0 1 .08-3.028.75.75 0 1 1 1.493.146 19.745 19.745 0 0 0-.09 2.319c1.302.46 2.668.786 4.08.96.066-.243.166-.471.297-.679a6.962 6.962 0 0 1-1.158-.486A1.348 1.348 0 0 1 6 6a4 4 0 0 1 .166-1.143 7.032 7.032 0 0 1-1.3-1.098.75.75 0 0 1-.173-.707 5.48 5.48 0 0 1 .822-1.736.75.75 0 0 1 1.046-.177Z"],color:"var(--ds-color-warning-9)"},feature:{paths:["M14 6a2.5 2.5 0 0 0-4-3 2.5 2.5 0 0 0-4 3H3.25C2.56 6 2 6.56 2 7.25v.5C2 8.44 2.56 9 3.25 9h6V6h1.5v3h6C17.44 9 18 8.44 18 7.75v-.5C18 6.56 17.44 6 16.75 6H14Zm-1-1.5a1 1 0 0 1-1 1h-1v-1a1 1 0 1 1 2 0Zm-6 0a1 1 0 0 0 1 1h1v-1a1 1 0 0 0-2 0Z","M9.25 10.5H3v4.75A2.75 2.75 0 0 0 5.75 18h3.5v-7.5ZM10.75 18v-7.5H17v4.75A2.75 2.75 0 0 1 14.25 18h-3.5Z"],color:"var(--ds-color-warning-9)"},refactor:{paths:["M8.157 2.176a1.5 1.5 0 0 0-1.147 0l-4.084 1.69A1.5 1.5 0 0 0 2 5.25v10.877a1.5 1.5 0 0 0 2.074 1.386l3.51-1.452 4.26 1.762a1.5 1.5 0 0 0 1.146 0l4.083-1.69A1.5 1.5 0 0 0 18 14.75V3.872a1.5 1.5 0 0 0-2.073-1.386l-3.51 1.452-4.26-1.762ZM7.58 5a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0v-6.5A.75.75 0 0 1 7.58 5Zm5.59 2.75a.75.75 0 0 0-1.5 0v6.5a.75.75 0 0 0 1.5 0v-6.5Z"],color:"var(--ds-color-warning-9)"},documentation:{paths:["M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm2.25 8.5a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Zm0 3a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z"],color:"var(--ds-color-warning-9)"},style:{paths:["M15.993 1.385a1.87 1.87 0 0 1 2.623 2.622l-4.03 5.27a12.749 12.749 0 0 1-4.237 3.562 4.508 4.508 0 0 0-3.188-3.188 12.75 12.75 0 0 1 3.562-4.236l5.27-4.03ZM6 11a3 3 0 0 0-3 3 .5.5 0 0 1-.72.45.75.75 0 0 0-1.035.931A4.001 4.001 0 0 0 9 14.004V14a3.01 3.01 0 0 0-1.66-2.685A2.99 2.99 0 0 0 6 11Z"],color:"var(--ds-color-warning-9)"},test:{paths:["M8.5 3.528v4.644c0 .729-.29 1.428-.805 1.944l-1.217 1.216a8.75 8.75 0 0 1 3.55.621l.502.201a7.25 7.25 0 0 0 4.178.365l-2.403-2.403a2.75 2.75 0 0 1-.805-1.944V3.528a40.205 40.205 0 0 0-3 0Zm4.5.084.19.015a.75.75 0 1 0 .12-1.495 41.364 41.364 0 0 0-6.62 0 .75.75 0 0 0 .12 1.495L7 3.612v4.56c0 .331-.132.649-.366.883L2.6 13.09c-1.496 1.496-.817 4.15 1.403 4.475C5.961 17.852 7.963 18 10 18s4.039-.148 5.997-.436c2.22-.325 2.9-2.979 1.403-4.475l-4.034-4.034A1.25 1.25 0 0 1 13 8.172v-4.56Z"],color:"var(--ds-color-warning-9)"},chore:{paths:["m6.75.98-.884.883a1.25 1.25 0 1 0 1.768 0L6.75.98ZM13.25.98l-.884.883a1.25 1.25 0 1 0 1.768 0L13.25.98ZM10 .98l.884.883a1.25 1.25 0 1 1-1.768 0L10 .98ZM7.5 5.75a.75.75 0 0 0-1.5 0v.464c-1.179.304-2 1.39-2 2.622v.094c.1-.02.202-.038.306-.052A42.867 42.867 0 0 1 10 8.5c1.93 0 3.83.129 5.694.378.104.014.206.032.306.052v-.094c0-1.232-.821-2.317-2-2.622V5.75a.75.75 0 0 0-1.5 0v.318a45.645 45.645 0 0 0-1.75-.062V5.75a.75.75 0 0 0-1.5 0v.256c-.586.01-1.17.03-1.75.062V5.75ZM4.505 10.365A41.36 41.36 0 0 1 10 10c1.863 0 3.697.124 5.495.365C16.967 10.562 18 11.838 18 13.28v.693a3.72 3.72 0 0 1-1.665-.393 5.222 5.222 0 0 0-4.67 0 3.722 3.722 0 0 1-3.33 0 5.222 5.222 0 0 0-4.67 0A3.72 3.72 0 0 1 2 13.972v-.693c0-1.441 1.033-2.717 2.505-2.914ZM15.665 14.92a5.22 5.22 0 0 0 2.335.552V16.5a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 2 16.5v-1.028c.8 0 1.6-.184 2.335-.551a3.722 3.722 0 0 1 3.33 0c1.47.735 3.2.735 4.67 0a3.722 3.722 0 0 1 3.33 0Z"],color:"var(--ds-color-warning-9)"},performance:{paths:["M4.606 12.97a.75.75 0 0 1-.134 1.051 2.494 2.494 0 0 0-.93 2.437 2.494 2.494 0 0 0 2.437-.93.75.75 0 1 1 1.186.918 3.995 3.995 0 0 1-4.482 1.332.75.75 0 0 1-.461-.461 3.994 3.994 0 0 1 1.332-4.482.75.75 0 0 1 1.052.134Z","M5.752 12A13.07 13.07 0 0 0 8 14.248v4.002c0 .414.336.75.75.75a5 5 0 0 0 4.797-6.414 12.984 12.984 0 0 0 5.45-10.848.75.75 0 0 0-.735-.735 12.984 12.984 0 0 0-10.849 5.45A5 5 0 0 0 1 11.25c.001.414.337.75.751.75h4.002ZM13 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"],color:"var(--ds-color-warning-9)"},revert:{paths:["M7.50043 1.37598C7.2023 1.37598 6.91637 1.49431 6.70543 1.70499L6.70521 1.70521L1.70521 6.70521L1.70499 6.70543C1.49431 6.91637 1.37598 7.2023 1.37598 7.50043C1.37598 7.79855 1.49431 8.08449 1.70499 8.29543L1.70521 8.29565L6.69987 13.2903C6.80149 13.3974 6.92322 13.4835 7.05815 13.5436C7.19615 13.6051 7.34512 13.6382 7.49617 13.6408C7.64723 13.6435 7.79727 13.6157 7.93735 13.5591C8.07744 13.5026 8.20469 13.4183 8.31151 13.3115C8.41834 13.2047 8.50256 13.0774 8.55914 12.9374C8.61572 12.7973 8.64351 12.6472 8.64084 12.4962C8.63818 12.3451 8.60511 12.1961 8.54363 12.0581C8.48351 11.9232 8.39743 11.8015 8.29032 11.6999L5.21587 8.62543H12.5004C13.0093 8.62543 13.5132 8.72566 13.9833 8.92039C14.4535 9.11513 14.8806 9.40056 15.2405 9.76039C15.6003 10.1202 15.8857 10.5474 16.0805 11.0175C16.2752 11.4877 16.3754 11.9916 16.3754 12.5004C16.3754 13.0093 16.2752 13.5132 16.0805 13.9833C15.8857 14.4535 15.6003 14.8806 15.2405 15.2405C14.8806 15.6003 14.4535 15.8857 13.9833 16.0805C13.5132 16.2752 13.0093 16.3754 12.5004 16.3754H10.0004C9.70206 16.3754 9.41591 16.494 9.20493 16.7049C8.99395 16.9159 8.87543 17.2021 8.87543 17.5004C8.87543 17.7988 8.99395 18.0849 9.20493 18.2959C9.41591 18.5069 9.70206 18.6254 10.0004 18.6254H12.5004C14.1249 18.6254 15.6828 17.9801 16.8315 16.8315C17.9801 15.6828 18.6254 14.1249 18.6254 12.5004C18.6254 10.876 17.9801 9.31806 16.8315 8.1694C15.6828 7.02074 14.1249 6.37543 12.5004 6.37543H5.21587L8.29565 3.29565L8.29587 3.29543C8.50654 3.08449 8.62488 2.79855 8.62488 2.50043C8.62488 2.2023 8.50654 1.91636 8.29587 1.70543L8.29543 1.70499C8.08449 1.49431 7.79855 1.37598 7.50043 1.37598Z","M7.712 4.818A1.5 1.5 0 0 1 10 6.095v2.972c.104-.13.234-.248.389-.343l6.323-3.906A1.5 1.5 0 0 1 19 6.095v7.81a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.505 1.505 0 0 1-.389-.344v2.973a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.5 1.5 0 0 1 0-2.552l6.323-3.906Z"],color:"var(--ds-color-warning-9)"},other:{paths:["M2 4.25C2 3.65326 2.23705 3.08097 2.65901 2.65901C3.08097 2.23705 3.65326 2 4.25 2H6.75C7.34674 2 7.91903 2.23705 8.34099 2.65901C8.76295 3.08097 9 3.65326 9 4.25V6.75C9 7.34674 8.76295 7.91903 8.34099 8.34099C7.91903 8.76295 7.34674 9 6.75 9H4.25C3.65326 9 3.08097 8.76295 2.65901 8.34099C2.23705 7.91903 2 7.34674 2 6.75V4.25ZM15.25 11.75C15.25 11.5511 15.171 11.3603 15.0303 11.2197C14.8897 11.079 14.6989 11 14.5 11C14.3011 11 14.1103 11.079 13.9697 11.2197C13.829 11.3603 13.75 11.5511 13.75 11.75V13.75H11.75C11.5511 13.75 11.3603 13.829 11.2197 13.9697C11.079 14.1103 11 14.3011 11 14.5C11 14.6989 11.079 14.8897 11.2197 15.0303C11.3603 15.171 11.5511 15.25 11.75 15.25H13.75V17.25C13.75 17.4489 13.829 17.6397 13.9697 17.7803C14.1103 17.921 14.3011 18 14.5 18C14.6989 18 14.8897 17.921 15.0303 17.7803C15.171 17.6397 15.25 17.4489 15.25 17.25V15.25H17.25C17.4489 15.25 17.6397 15.171 17.7803 15.0303C17.921 14.8897 18 14.6989 18 14.5C18 14.3011 17.921 14.1103 17.7803 13.9697C17.6397 13.829 17.4489 13.75 17.25 13.75H15.25V11.75Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M9 14.5C9 16.433 7.433 18 5.5 18C3.567 18 2 16.433 2 14.5C2 12.567 3.567 11 5.5 11C7.433 11 9 12.567 9 14.5Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M 9 14.5 A 3.5 3.5 0 1 1 2 14.5 A 3.5 3.5 0 1 1 9 14.5 Z"],color:"var(--ds-color-warning-9)"}};return r.$$set=o=>{"type"in o&&t(0,s=o.type)},r.$$.update=()=>{1&r.$$.dirty&&t(1,n=i[s]??i.other)},[s,n]}class Jr extends K{constructor(e){super(),ee(this,e,Gr,Qr,Y,{type:0})}}function kn(r,e,t){const n=r.slice();return n[47]=e[t],n[49]=t,n}function wn(r){let e,t,n,s,i;t=new Is({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Kr]},$$scope:{ctx:r}}}),t.$on("click",r[24]);let o=pe(r[1]),l=[];for(let u=0;u<o.length;u+=1)l[u]=vn(kn(r,o,u));const a=u=>$(l[u],1,1,()=>{l[u]=null});return{c(){e=b("div"),L(t.$$.fragment),n=Z(),s=b("div");for(let u=0;u<l.length;u+=1)l[u].c();D(e,"class","toggle-button svelte-14s1ghg"),D(s,"class","descriptions svelte-14s1ghg"),Ae(s,"transform","translateY("+-r[4]+"px)")},m(u,c){g(u,e,c),M(t,e,null),g(u,n,c),g(u,s,c);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(s,null);i=!0},p(u,c){const d={};if(1&c[0]|524288&c[1]&&(d.$$scope={dirty:c,ctx:u}),t.$set(d),546&c[0]){let f;for(o=pe(u[1]),f=0;f<o.length;f+=1){const m=kn(u,o,f);l[f]?(l[f].p(m,c),p(l[f],1)):(l[f]=vn(m),l[f].c(),p(l[f],1),l[f].m(s,null))}for(H(),f=o.length;f<l.length;f+=1)a(f);W()}(!i||16&c[0])&&Ae(s,"transform","translateY("+-u[4]+"px)")},i(u){if(!i){p(t.$$.fragment,u);for(let c=0;c<o.length;c+=1)p(l[c]);i=!0}},o(u){$(t.$$.fragment,u),l=l.filter(Boolean);for(let c=0;c<l.length;c+=1)$(l[c]);i=!1},d(u){u&&(h(e),h(n),h(s)),q(t),Le(l,u)}}}function Yr(r){let e,t;return e=new lt({props:{icon:"book"}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Xr(r){let e,t;return e=new lt({props:{icon:"x"}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Kr(r){let e,t,n,s;const i=[Xr,Yr],o=[];function l(a,u){return a[0]?0:1}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=ke()},m(a,u){o[e].m(a,u),g(a,n,u),s=!0},p(a,u){let c=e;e=l(a),e!==c&&(H(),$(o[c],1,1,()=>{o[c]=null}),W(),t=o[e],t||(t=o[e]=i[e](a),t.c()),p(t,1),t.m(n.parentNode,n))},i(a){s||(p(t),s=!0)},o(a){$(t),s=!1},d(a){a&&h(n),o[e].d(a)}}}function vn(r){let e,t,n,s;return t=new Us({props:{markdown:r[47].text}}),{c(){e=b("div"),L(t.$$.fragment),n=Z(),D(e,"class","description svelte-14s1ghg"),Ae(e,"top",(r[5][r[49]]||r[9](r[47]))+"px"),Ae(e,"--ds-panel-solid","transparent")},m(i,o){g(i,e,o),M(t,e,null),T(e,n),s=!0},p(i,o){const l={};2&o[0]&&(l.markdown=i[47].text),t.$set(l),(!s||34&o[0])&&Ae(e,"top",(i[5][i[49]]||i[9](i[47]))+"px")},i(i){s||(p(t.$$.fragment,i),s=!0)},o(i){$(t.$$.fragment,i),s=!1},d(i){i&&h(e),q(t)}}}function eo(r){let e,t,n,s,i=r[1].length>0&&wn(r);return{c(){e=b("div"),t=b("div"),n=Z(),i&&i.c(),D(t,"class","editor-container svelte-14s1ghg"),Ae(t,"height",r[3]+"px"),D(e,"class","monaco-diff-container svelte-14s1ghg"),ve(e,"monaco-diff-container-with-descriptions",r[1].length>0&&r[0])},m(o,l){g(o,e,l),T(e,t),r[23](t),T(e,n),i&&i.m(e,null),s=!0},p(o,l){(!s||8&l[0])&&Ae(t,"height",o[3]+"px"),o[1].length>0?i?(i.p(o,l),2&l[0]&&p(i,1)):(i=wn(o),i.c(),p(i,1),i.m(e,null)):i&&(H(),$(i,1,1,()=>{i=null}),W()),(!s||3&l[0])&&ve(e,"monaco-diff-container-with-descriptions",o[1].length>0&&o[0])},i(o){s||(p(i),s=!0)},o(o){$(i),s=!1},d(o){o&&h(e),r[23](null),i&&i.d()}}}function to(r,e,t){let n,s,i;const o=bs();let{originalCode:l=""}=e,{modifiedCode:a=""}=e,{path:u}=e,{descriptions:c=[]}=e,{lineOffset:d=0}=e,{extraPrefixLines:f=[]}=e,{extraSuffixLines:m=[]}=e,{theme:A}=e,{areDescriptionsVisible:v=!0}=e,{isNewFile:_=!1}=e,{isDeletedFile:C=!1}=e;const N=Rt.getContext().monaco;let F,x,y,k;ze(r,N,w=>t(22,n=w));let E,S=[];const j=Tt();let O,G=Ve(0);ze(r,G,w=>t(4,s=w));let ce=_?20*a.split(`
`).length+40:100;const ne=n?n.languages.getLanguages().map(w=>w.id):[];function he(w,z){var B,U;if(z){const V=(B=z.split(".").pop())==null?void 0:B.toLowerCase();if(V){const X=(U=n==null?void 0:n.languages.getLanguages().find(de=>{var ae;return(ae=de.extensions)==null?void 0:ae.includes("."+V)}))==null?void 0:U.id;if(X&&ne.includes(X))return X}}return"plaintext"}const $e=Ve({});ze(r,$e,w=>t(5,i=w));let Q=null;function ye(){if(!F)return;S=S.filter(B=>(B.dispose(),!1));const w=F.getOriginalEditor(),z=F.getModifiedEditor();S.push(w.onDidScrollChange(()=>{gt(G,s=w.getScrollTop(),s)}),z.onDidScrollChange(()=>{gt(G,s=z.getScrollTop(),s)}))}function xe(){if(!F||!E)return;const w=F.getOriginalEditor(),z=F.getModifiedEditor();S.push(z.onDidContentSizeChange(()=>j.requestLayout()),w.onDidContentSizeChange(()=>j.requestLayout()),F.onDidUpdateDiff(()=>j.requestLayout()),z.onDidChangeHiddenAreas(()=>j.requestLayout()),w.onDidChangeHiddenAreas(()=>j.requestLayout()),z.onDidLayoutChange(()=>j.requestLayout()),w.onDidLayoutChange(()=>j.requestLayout()),z.onDidFocusEditorWidget(()=>{re(!0)}),w.onDidFocusEditorWidget(()=>{re(!0)}),z.onDidBlurEditorWidget(()=>{re(!1)}),w.onDidBlurEditorWidget(()=>{re(!1)}),z.onDidChangeModelContent(()=>{se=!0,I=Date.now();const B=(k==null?void 0:k.getValue())||"";if(B===a)return;const U=B.replace(f.join(""),"").replace(m.join(""),"");o("codeChange",{modifiedCode:U});const V=setTimeout(()=>{se=!1},500);S.push({dispose:()=>clearTimeout(V)})})),function(){!E||!F||(Q&&clearTimeout(Q),Q=setTimeout(()=>{if(!E.__hasClickListener){const B=U=>{const V=U.target;V&&(V.closest('[title="Show Unchanged Region"]')||V.closest('[title="Hide Unchanged Region"]'))&&we()};E.addEventListener("click",B),t(2,E.__hasClickListener=!0,E),S.push({dispose:()=>{E.removeEventListener("click",B)}})}F&&S.push(F.onDidUpdateDiff(()=>{we()}))},300))}()}Es(()=>{F==null||F.dispose(),x==null||x.dispose(),y==null||y.dispose(),k==null||k.dispose(),S.forEach(w=>w.dispose()),Q&&clearTimeout(Q),O==null||O()});let oe=null;function we(){oe&&clearTimeout(oe),oe=setTimeout(()=>{j.requestLayout(),oe=null},100),oe&&S.push({dispose:()=>{oe&&(clearTimeout(oe),oe=null)}})}function Me(w,z,B,U=[],V=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");y==null||y.dispose(),k==null||k.dispose(),z=z||"",B=B||"";const X=U.join(""),de=V.join("");if(z=_?B.split(`
`).map(()=>" ").join(`
`):X+z+de,B=X+B+de,y=n.editor.createModel(z,void 0,w!==void 0?n.Uri.parse("file://"+w+`#${crypto.randomUUID()}`):void 0),C&&(B=B.split(`
`).map(()=>" ").join(`
`)),t(21,k=n.editor.createModel(B,void 0,w!==void 0?n.Uri.parse("file://"+w+`#${crypto.randomUUID()}`):void 0)),F){F.setModel({original:y,modified:k});const ae=F.getOriginalEditor();ae&&ae.updateOptions({lineNumbers:"off"}),ye(),Q&&clearTimeout(Q),Q=setTimeout(()=>{xe(),Q=null},300)}}rt(()=>{if(n)if(_){t(20,x=n.editor.create(E,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:A,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:U=>`${d-f.length+U}`}));const w=he(0,u);t(21,k=n.editor.createModel(a,w,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),x.setModel(k),S.push(x.onDidChangeModelContent(()=>{se=!0,I=Date.now();const U=(k==null?void 0:k.getValue())||"";if(U===a)return;o("codeChange",{modifiedCode:U});const V=setTimeout(()=>{se=!1},500);S.push({dispose:()=>clearTimeout(V)})})),S.push(x.onDidFocusEditorWidget(()=>{x==null||x.updateOptions({scrollbar:{handleMouseWheel:!0}})}),x.onDidBlurEditorWidget(()=>{x==null||x.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const z=x.getContentHeight();t(3,ce=Math.max(z,60));const B=setTimeout(()=>{x==null||x.layout()},0);S.push({dispose:()=>clearTimeout(B)})}else t(19,F=n.editor.createDiffEditor(E,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:A,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:w=>`${d-f.length+w}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),O&&O(),O=j.registerEditor({editor:F,updateHeight:te,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),Me(u,l,a,f,m),ye(),xe(),Q&&clearTimeout(Q),Q=setTimeout(()=>{j.requestLayout(),Q=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let se=!1,I=0;function P(w,z=!0){return F?(z?F.getModifiedEditor():F.getOriginalEditor()).getTopForLineNumber(w):18*w}function te(){if(!F)return;const w=F.getModel(),z=w==null?void 0:w.original,B=w==null?void 0:w.modified;if(!z||!B)return;const U=F.getOriginalEditor(),V=F.getModifiedEditor(),X=F.getLineChanges()||[];let de;if(X.length===0){const ae=U.getContentHeight(),Ce=V.getContentHeight();de=Math.max(100,ae,Ce)}else{let ae=0,Ce=0;for(const je of X)je.originalEndLineNumber>0&&(ae=Math.max(ae,je.originalEndLineNumber)),je.modifiedEndLineNumber>0&&(Ce=Math.max(Ce,je.modifiedEndLineNumber));ae=Math.min(ae+3,z.getLineCount()),Ce=Math.min(Ce+3,B.getLineCount());const Se=U.getTopForLineNumber(ae),Xe=V.getTopForLineNumber(Ce);de=Math.max(Se,Xe)+60}t(3,ce=Math.min(de,2e4)),F.layout(),me()}function re(w){if(!F)return;const z=F.getOriginalEditor(),B=F.getModifiedEditor();z.updateOptions({scrollbar:{handleMouseWheel:w}}),B.updateOptions({scrollbar:{handleMouseWheel:w}})}function ue(w){if(!F)return 0;const z=F.getModel(),B=z==null?void 0:z.original,U=z==null?void 0:z.modified;if(!B||!U)return 0;const V=P(w.range.start+1,!1),X=P(w.range.start+1,!0);return V&&!X?V:!V&&X?X:Math.min(V,X)}function me(){if(!F||c.length===0)return;const w={};c.forEach((z,B)=>{w[B]=ue(z)}),$e.set(w)}return r.$$set=w=>{"originalCode"in w&&t(10,l=w.originalCode),"modifiedCode"in w&&t(11,a=w.modifiedCode),"path"in w&&t(12,u=w.path),"descriptions"in w&&t(1,c=w.descriptions),"lineOffset"in w&&t(13,d=w.lineOffset),"extraPrefixLines"in w&&t(14,f=w.extraPrefixLines),"extraSuffixLines"in w&&t(15,m=w.extraSuffixLines),"theme"in w&&t(16,A=w.theme),"areDescriptionsVisible"in w&&t(0,v=w.areDescriptionsVisible),"isNewFile"in w&&t(17,_=w.isNewFile),"isDeletedFile"in w&&t(18,C=w.isDeletedFile)},r.$$.update=()=>{if(8051712&r.$$.dirty[0]&&(w=a,!(se||Date.now()-I<1e3||k&&k.getValue()===f.join("")+w+m.join(""))))if(_&&x){if(k)k.setValue(a);else{const z=he(0,u);n&&t(21,k=n.editor.createModel(a,z,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),k&&x.setModel(k)}t(3,ce=20*a.split(`
`).length+40),x.layout()}else!_&&F&&(Me(u,l,a,f,m),j.requestLayout());var w;if(524290&r.$$.dirty[0]&&F&&c.length>0&&me(),1181696&r.$$.dirty[0]&&_&&a&&x){const z=x.getContentHeight();t(3,ce=Math.max(z,60)),x.layout()}},[v,c,E,ce,s,i,N,G,$e,ue,l,a,u,d,f,m,A,_,C,F,x,k,n,function(w){qe[w?"unshift":"push"](()=>{E=w,t(2,E)})},()=>t(0,v=!v)]}class no extends K{constructor(e){super(),ee(this,e,to,eo,Y,{originalCode:10,modifiedCode:11,path:12,descriptions:1,lineOffset:13,extraPrefixLines:14,extraSuffixLines:15,theme:16,areDescriptionsVisible:0,isNewFile:17,isDeletedFile:18},null,[-1,-1])}}const so=["png","jpg","jpeg","gif","svg","webp","bmp","ico"],io=["zip","tar","gz","7z","rar","pdf","doc","docx","ppt","pptx","xls","xlsx","odt","odp","ods","exe","dll","so","dylib","app","msi","deb","rpm","o","a","class","jar","pyc","wasm","mp3","mp4","avi","mov","wav","mkv","DS_Store","db","sqlite","dat"],Ys=1048576;function Pt(r){if(!r)return"";const e=r.lastIndexOf(".");return e===-1||e===r.length-1?"":r.substring(e+1).toLowerCase()}function it(r){switch(Pt(r)){case"png":return"image/png";case"jpg":case"jpeg":return"image/jpeg";case"gif":return"image/gif";case"svg":return"image/svg+xml";case"webp":return"image/webp";case"bmp":return"image/bmp";case"ico":return"image/x-icon";default:return"application/octet-stream"}}function yn(r){const e=Pt(r);return so.includes(e)}function An(r){return r>Ys}const ro=Symbol("focusedPath");function bn(r){return`file-diff-${Pe(r)}`}function oo(r){let e,t,n;function s(o){r[38](o)}let i={path:r[3],originalCode:r[0].originalCode,modifiedCode:r[6],theme:r[14],descriptions:r[4],isNewFile:r[20],isDeletedFile:r[19]};return r[1]!==void 0&&(i.areDescriptionsVisible=r[1]),e=new no({props:i}),qe.push(()=>He(e,"areDescriptionsVisible",s)),e.$on("codeChange",r[25]),{c(){L(e.$$.fragment)},m(o,l){M(e,o,l),n=!0},p(o,l){const a={};8&l[0]&&(a.path=o[3]),1&l[0]&&(a.originalCode=o[0].originalCode),64&l[0]&&(a.modifiedCode=o[6]),16384&l[0]&&(a.theme=o[14]),16&l[0]&&(a.descriptions=o[4]),1048576&l[0]&&(a.isNewFile=o[20]),524288&l[0]&&(a.isDeletedFile=o[19]),!t&&2&l[0]&&(t=!0,a.areDescriptionsVisible=o[1],We(()=>t=!1)),e.$set(a)},i(o){n||(p(e.$$.fragment,o),n=!0)},o(o){$(e.$$.fragment,o),n=!1},d(o){q(e,o)}}}function lo(r){let e,t,n;return t=new fe({props:{size:1,$$slots:{default:[co]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),D(e,"class","too-large-message svelte-1536g7w")},m(s,i){g(s,e,i),M(t,e,null),n=!0},p(s,i){const o={};9984&i[0]|512&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function ao(r){let e,t,n;return t=new fe({props:{$$slots:{default:[ho]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),D(e,"class","binary-file-message svelte-1536g7w")},m(s,i){g(s,e,i),M(t,e,null),n=!0},p(s,i){const o={};1057152&i[0]|512&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function uo(r){let e,t,n,s;const i=[mo,$o],o=[];function l(a,u){return a[8]?0:a[6]?1:-1}return~(t=l(r))&&(n=o[t]=i[t](r)),{c(){e=b("div"),n&&n.c(),D(e,"class","image-container svelte-1536g7w")},m(a,u){g(a,e,u),~t&&o[t].m(e,null),s=!0},p(a,u){let c=t;t=l(a),t===c?~t&&o[t].p(a,u):(n&&(H(),$(o[c],1,1,()=>{o[c]=null}),W()),~t?(n=o[t],n?n.p(a,u):(n=o[t]=i[t](a),n.c()),p(n,1),n.m(e,null)):n=null)},i(a){s||(p(n),s=!0)},o(a){$(n),s=!1},d(a){a&&h(e),~t&&o[t].d()}}}function co(r){let e,t,n,s,i,o,l,a=ie(r[13])+"",u=(r[8]?r[10]:r[9])+"";return{c(){e=R('File "'),t=R(a),n=R('" is too large to display a diff (size: '),s=R(u),i=R(" bytes, max: "),o=R(Ys),l=R(" bytes).")},m(c,d){g(c,e,d),g(c,t,d),g(c,n,d),g(c,s,d),g(c,i,d),g(c,o,d),g(c,l,d)},p(c,d){8192&d[0]&&a!==(a=ie(c[13])+"")&&le(t,a),1792&d[0]&&u!==(u=(c[8]?c[10]:c[9])+"")&&le(s,u)},d(c){c&&(h(e),h(t),h(n),h(s),h(i),h(o),h(l))}}}function po(r){let e,t,n,s=ie(r[13])+"";return{c(){e=R("Binary file modified: "),t=R(s),n=R(".")},m(i,o){g(i,e,o),g(i,t,o),g(i,n,o)},p(i,o){8192&o[0]&&s!==(s=ie(i[13])+"")&&le(t,s)},d(i){i&&(h(e),h(t),h(n))}}}function fo(r){let e,t,n,s=ie(r[13])+"";return{c(){e=R("Binary file deleted: "),t=R(s),n=R(".")},m(i,o){g(i,e,o),g(i,t,o),g(i,n,o)},p(i,o){8192&o[0]&&s!==(s=ie(i[13])+"")&&le(t,s)},d(i){i&&(h(e),h(t),h(n))}}}function go(r){let e,t,n,s=ie(r[13])+"";return{c(){e=R("Binary file added: "),t=R(s),n=R(".")},m(i,o){g(i,e,o),g(i,t,o),g(i,n,o)},p(i,o){8192&o[0]&&s!==(s=ie(i[13])+"")&&le(t,s)},d(i){i&&(h(e),h(t),h(n))}}}function ho(r){let e;function t(i,o){return i[20]||i[7]?go:i[8]?fo:po}let n=t(r),s=n(r);return{c(){s.c(),e=R(`
            No text preview available.`)},m(i,o){s.m(i,o),g(i,e,o)},p(i,o){n===(n=t(i))&&s?s.p(i,o):(s.d(1),s=n(i),s&&(s.c(),s.m(e.parentNode,e)))},d(i){i&&h(e),s.d(i)}}}function $o(r){let e,t,n,s,i,o,l,a;e=new fe({props:{class:"image-info-text",$$slots:{default:[xo]},$$scope:{ctx:r}}});let u=r[0].originalCode&&r[6]!==r[0].originalCode&&!r[20]&&En(r);return{c(){L(e.$$.fragment),t=Z(),n=b("img"),o=Z(),u&&u.c(),l=ke(),Re(n.src,s="data:"+r[18]+";base64,"+btoa(r[6]))||D(n,"src",s),D(n,"alt",i="Current "+ie(r[13])),D(n,"class","image-preview svelte-1536g7w")},m(c,d){M(e,c,d),g(c,t,d),g(c,n,d),g(c,o,d),u&&u.m(c,d),g(c,l,d),a=!0},p(c,d){const f={};1056896&d[0]|512&d[1]&&(f.$$scope={dirty:d,ctx:c}),e.$set(f),(!a||262208&d[0]&&!Re(n.src,s="data:"+c[18]+";base64,"+btoa(c[6])))&&D(n,"src",s),(!a||8192&d[0]&&i!==(i="Current "+ie(c[13])))&&D(n,"alt",i),c[0].originalCode&&c[6]!==c[0].originalCode&&!c[20]?u?(u.p(c,d),1048641&d[0]&&p(u,1)):(u=En(c),u.c(),p(u,1),u.m(l.parentNode,l)):u&&(H(),$(u,1,1,()=>{u=null}),W())},i(c){a||(p(e.$$.fragment,c),p(u),a=!0)},o(c){$(e.$$.fragment,c),$(u),a=!1},d(c){c&&(h(t),h(n),h(o),h(l)),q(e,c),u&&u.d(c)}}}function mo(r){let e,t,n,s;e=new fe({props:{class:"image-info-text",$$slots:{default:[ko]},$$scope:{ctx:r}}});let i=r[0].originalCode&&_n(r);return{c(){L(e.$$.fragment),t=Z(),i&&i.c(),n=ke()},m(o,l){M(e,o,l),g(o,t,l),i&&i.m(o,l),g(o,n,l),s=!0},p(o,l){const a={};8192&l[0]|512&l[1]&&(a.$$scope={dirty:l,ctx:o}),e.$set(a),o[0].originalCode?i?(i.p(o,l),1&l[0]&&p(i,1)):(i=_n(o),i.c(),p(i,1),i.m(n.parentNode,n)):i&&(H(),$(i,1,1,()=>{i=null}),W())},i(o){s||(p(e.$$.fragment,o),p(i),s=!0)},o(o){$(e.$$.fragment,o),$(i),s=!1},d(o){o&&(h(t),h(n)),q(e,o),i&&i.d(o)}}}function Do(r){let e;return{c(){e=R("Image modified")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Fo(r){let e;return{c(){e=R("New image added")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function xo(r){let e,t,n=ie(r[13])+"";function s(l,a){return l[20]||l[7]?Fo:Do}let i=s(r),o=i(r);return{c(){o.c(),e=R(": "),t=R(n)},m(l,a){o.m(l,a),g(l,e,a),g(l,t,a)},p(l,a){i!==(i=s(l))&&(o.d(1),o=i(l),o&&(o.c(),o.m(e.parentNode,e))),8192&a[0]&&n!==(n=ie(l[13])+"")&&le(t,n)},d(l){l&&(h(e),h(t)),o.d(l)}}}function En(r){let e,t,n,s,i,o;return e=new fe({props:{class:"image-info-text",$$slots:{default:[Co]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=Z(),n=b("img"),Re(n.src,s="data:"+it(r[3])+";base64,"+btoa(r[0].originalCode))||D(n,"src",s),D(n,"alt",i="Original "+ie(r[13])),D(n,"class","image-preview image-preview--previous svelte-1536g7w")},m(l,a){M(e,l,a),g(l,t,a),g(l,n,a),o=!0},p(l,a){const u={};512&a[1]&&(u.$$scope={dirty:a,ctx:l}),e.$set(u),(!o||9&a[0]&&!Re(n.src,s="data:"+it(l[3])+";base64,"+btoa(l[0].originalCode)))&&D(n,"src",s),(!o||8192&a[0]&&i!==(i="Original "+ie(l[13])))&&D(n,"alt",i)},i(l){o||(p(e.$$.fragment,l),o=!0)},o(l){$(e.$$.fragment,l),o=!1},d(l){l&&(h(t),h(n)),q(e,l)}}}function Co(r){let e;return{c(){e=R("Previous version:")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function ko(r){let e,t,n=ie(r[13])+"";return{c(){e=R("Image deleted: "),t=R(n)},m(s,i){g(s,e,i),g(s,t,i)},p(s,i){8192&i[0]&&n!==(n=ie(s[13])+"")&&le(t,n)},d(s){s&&(h(e),h(t))}}}function _n(r){let e,t,n,s,i,o;return e=new fe({props:{class:"image-info-text",$$slots:{default:[wo]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=Z(),n=b("img"),Re(n.src,s="data:"+it(r[3])+";base64,"+btoa(r[0].originalCode))||D(n,"src",s),D(n,"alt",i="Original "+ie(r[13])),D(n,"class","image-preview svelte-1536g7w")},m(l,a){M(e,l,a),g(l,t,a),g(l,n,a),o=!0},p(l,a){const u={};512&a[1]&&(u.$$scope={dirty:a,ctx:l}),e.$set(u),(!o||9&a[0]&&!Re(n.src,s="data:"+it(l[3])+";base64,"+btoa(l[0].originalCode)))&&D(n,"src",s),(!o||8192&a[0]&&i!==(i="Original "+ie(l[13])))&&D(n,"alt",i)},i(l){o||(p(e.$$.fragment,l),o=!0)},o(l){$(e.$$.fragment,l),o=!1},d(l){l&&(h(t),h(n)),q(e,l)}}}function wo(r){let e;return{c(){e=R("Previous version:")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function vo(r){let e,t,n,s;const i=[uo,ao,lo,oo],o=[];function l(a,u){return a[17]?0:a[16]?1:a[15]?2:3}return t=l(r),n=o[t]=i[t](r),{c(){e=b("div"),n.c(),D(e,"class","changes svelte-1536g7w")},m(a,u){g(a,e,u),o[t].m(e,null),s=!0},p(a,u){let c=t;t=l(a),t===c?o[t].p(a,u):(H(),$(o[c],1,1,()=>{o[c]=null}),W(),n=o[t],n?n.p(a,u):(n=o[t]=i[t](a),n.c()),p(n,1),n.m(e,null))},i(a){s||(p(n),s=!0)},o(a){$(n),s=!1},d(a){a&&h(e),o[t].d()}}}function yo(r){let e,t=ie(r[13])+"";return{c(){e=R(t)},m(n,s){g(n,e,s)},p(n,s){8192&s[0]&&t!==(t=ie(n[13])+"")&&le(e,t)},d(n){n&&h(e)}}}function Ao(r){let e,t;return e=new Oe({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[yo]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};8192&s[0]|512&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Bn(r){let e,t,n=Ze(r[13])+"";return{c(){e=b("span"),t=R(n),D(e,"class","c-directory svelte-1536g7w")},m(s,i){g(s,e,i),T(e,t)},p(s,i){8192&i[0]&&n!==(n=Ze(s[13])+"")&&le(t,n)},d(s){s&&h(e)}}}function bo(r){let e,t,n,s=r[22]>0&&zn(r),i=r[21]>0&&Ln(r);return{c(){e=b("div"),s&&s.c(),t=Z(),i&&i.c(),D(e,"class","changes-indicator svelte-1536g7w")},m(o,l){g(o,e,l),s&&s.m(e,null),T(e,t),i&&i.m(e,null),n=!0},p(o,l){o[22]>0?s?(s.p(o,l),4194304&l[0]&&p(s,1)):(s=zn(o),s.c(),p(s,1),s.m(e,t)):s&&(H(),$(s,1,1,()=>{s=null}),W()),o[21]>0?i?(i.p(o,l),2097152&l[0]&&p(i,1)):(i=Ln(o),i.c(),p(i,1),i.m(e,null)):i&&(H(),$(i,1,1,()=>{i=null}),W())},i(o){n||(p(s),p(i),n=!0)},o(o){$(s),$(i),n=!1},d(o){o&&h(e),s&&s.d(),i&&i.d()}}}function Eo(r){let e;return{c(){e=b("span"),e.textContent="New File",D(e,"class","new-file-badge svelte-1536g7w")},m(t,n){g(t,e,n)},p:J,i:J,o:J,d(t){t&&h(e)}}}function zn(r){let e,t,n;return t=new fe({props:{size:1,$$slots:{default:[_o]},$$scope:{ctx:r}}}),{c(){e=b("span"),L(t.$$.fragment),D(e,"class","additions svelte-1536g7w")},m(s,i){g(s,e,i),M(t,e,null),n=!0},p(s,i){const o={};4194304&i[0]|512&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function _o(r){let e,t;return{c(){e=R("+"),t=R(r[22])},m(n,s){g(n,e,s),g(n,t,s)},p(n,s){4194304&s[0]&&le(t,n[22])},d(n){n&&(h(e),h(t))}}}function Ln(r){let e,t,n;return t=new fe({props:{size:1,$$slots:{default:[Bo]},$$scope:{ctx:r}}}),{c(){e=b("span"),L(t.$$.fragment),D(e,"class","deletions svelte-1536g7w")},m(s,i){g(s,e,i),M(t,e,null),n=!0},p(s,i){const o={};2097152&i[0]|512&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function Bo(r){let e,t;return{c(){e=R("-"),t=R(r[21])},m(n,s){g(n,e,s),g(n,t,s)},p(n,s){2097152&s[0]&&le(t,n[21])},d(n){n&&(h(e),h(t))}}}function zo(r){let e,t;return e=new Ge({props:{content:r[11],$$slots:{default:[qo]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};2048&s[0]&&(i.content=n[11]),4096&s[0]|512&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Lo(r){let e,t,n;return t=new fe({props:{size:1,$$slots:{default:[Ro]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),D(e,"class","applied svelte-1536g7w")},m(s,i){g(s,e,i),M(t,e,null),n=!0},p(s,i){const o={};512&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function Mo(r){let e,t,n,s;return n=new ot({}),{c(){e=R(`Apply
            `),t=b("div"),L(n.$$.fragment),D(t,"class","applied__icon svelte-1536g7w")},m(i,o){g(i,e,o),g(i,t,o),M(n,t,null),s=!0},p:J,i(i){s||(p(n.$$.fragment,i),s=!0)},o(i){$(n.$$.fragment,i),s=!1},d(i){i&&(h(e),h(t)),q(n)}}}function qo(r){let e,t;return e=new Oe({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[12],$$slots:{default:[Mo]},$$scope:{ctx:r}}}),e.$on("click",r[26]),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};4096&s[0]&&(i.disabled=n[12]),512&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Ro(r){let e,t,n;return t=new wt({props:{iconName:"check"}}),{c(){e=R(`Applied
            `),L(t.$$.fragment)},m(s,i){g(s,e,i),M(t,s,i),n=!0},p:J,i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t,s)}}}function To(r){let e,t,n,s,i,o,l,a,u,c,d,f,m,A=Ze(r[13]);t=new Ss({}),i=new Ge({props:{content:r[13],triggerOn:[Nt.Hover],$$slots:{default:[Ao]},$$scope:{ctx:r}}});let v=A&&Bn(r);const _=[Eo,bo],C=[];function N(k,E){return k[20]?0:1}a=N(r),u=C[a]=_[a](r);const F=[Lo,zo],x=[];function y(k,E){return k[5]?0:1}return d=y(r),f=x[d]=F[d](r),{c(){e=b("div"),L(t.$$.fragment),n=Z(),s=b("div"),L(i.$$.fragment),o=Z(),v&&v.c(),l=Z(),u.c(),c=Z(),f.c(),D(s,"class","c-path svelte-1536g7w"),D(e,"slot","header"),D(e,"class","header svelte-1536g7w")},m(k,E){g(k,e,E),M(t,e,null),T(e,n),T(e,s),M(i,s,null),T(s,o),v&&v.m(s,null),T(e,l),C[a].m(e,null),T(e,c),x[d].m(e,null),m=!0},p(k,E){const S={};8192&E[0]&&(S.content=k[13]),8192&E[0]|512&E[1]&&(S.$$scope={dirty:E,ctx:k}),i.$set(S),8192&E[0]&&(A=Ze(k[13])),A?v?v.p(k,E):(v=Bn(k),v.c(),v.m(s,null)):v&&(v.d(1),v=null);let j=a;a=N(k),a===j?C[a].p(k,E):(H(),$(C[j],1,1,()=>{C[j]=null}),W(),u=C[a],u?u.p(k,E):(u=C[a]=_[a](k),u.c()),p(u,1),u.m(e,c));let O=d;d=y(k),d===O?x[d].p(k,E):(H(),$(x[O],1,1,()=>{x[O]=null}),W(),f=x[d],f?f.p(k,E):(f=x[d]=F[d](k),f.c()),p(f,1),f.m(e,null))},i(k){m||(p(t.$$.fragment,k),p(i.$$.fragment,k),p(u),p(f),m=!0)},o(k){$(t.$$.fragment,k),$(i.$$.fragment,k),$(u),$(f),m=!1},d(k){k&&h(e),q(t),q(i),v&&v.d(),C[a].d(),x[d].d()}}}function No(r){let e,t,n,s,i;function o(a){r[39](a)}let l={stickyHeader:!0,$$slots:{header:[To],default:[vo]},$$scope:{ctx:r}};return r[2]!==void 0&&(l.collapsed=r[2]),t=new Ns({props:l}),qe.push(()=>He(t,"collapsed",o)),{c(){e=b("div"),L(t.$$.fragment),D(e,"class","c svelte-1536g7w"),D(e,"id",s=bn(r[3])),ve(e,"focused",r[23]===r[3])},m(a,u){g(a,e,u),M(t,e,null),i=!0},p(a,u){const c={};8388603&u[0]|512&u[1]&&(c.$$scope={dirty:u,ctx:a}),!n&&4&u[0]&&(n=!0,c.collapsed=a[2],We(()=>n=!1)),t.$set(c),(!i||8&u[0]&&s!==(s=bn(a[3])))&&D(e,"id",s),(!i||8388616&u[0])&&ve(e,"focused",a[23]===a[3])},i(a){i||(p(t.$$.fragment,a),i=!0)},o(a){$(t.$$.fragment,a),i=!1},d(a){a&&h(e),q(t)}}}function So(r,e,t){let n,s,i,o,l,a,u,c,d,f,m,A,v,_,C,N,F,x,y,k,E,S,j;ze(r,_s,I=>t(37,S=I));let{path:O}=e,{change:G}=e,{descriptions:ce=[]}=e,{areDescriptionsVisible:ne=!0}=e,{isExpandedDefault:he}=e,{isCollapsed:$e=!he}=e,{isApplying:Q}=e,{hasApplied:ye}=e,{onApplyChanges:xe}=e,{onCodeChange:oe}=e,{isAgentFromDifferentRepo:we=!1}=e;const Me=ht(ro);ze(r,Me,I=>t(23,j=I));let se=G.modifiedCode;return r.$$set=I=>{"path"in I&&t(3,O=I.path),"change"in I&&t(0,G=I.change),"descriptions"in I&&t(4,ce=I.descriptions),"areDescriptionsVisible"in I&&t(1,ne=I.areDescriptionsVisible),"isExpandedDefault"in I&&t(27,he=I.isExpandedDefault),"isCollapsed"in I&&t(2,$e=I.isCollapsed),"isApplying"in I&&t(28,Q=I.isApplying),"hasApplied"in I&&t(5,ye=I.hasApplied),"onApplyChanges"in I&&t(29,xe=I.onApplyChanges),"onCodeChange"in I&&t(30,oe=I.onCodeChange),"isAgentFromDifferentRepo"in I&&t(31,we=I.isAgentFromDifferentRepo)},r.$$.update=()=>{var I;1&r.$$.dirty[0]&&t(6,se=G.modifiedCode),1&r.$$.dirty[0]&&t(36,n=Fi(G.diff)),32&r.$$.dirty[1]&&t(22,s=n.additions),32&r.$$.dirty[1]&&t(21,i=n.deletions),1&r.$$.dirty[0]&&t(20,o=xi(G)),1&r.$$.dirty[0]&&t(19,l=Ci(G)),8&r.$$.dirty[0]&&t(35,a=yn(O)),8&r.$$.dirty[0]&&t(18,u=it(O)),8&r.$$.dirty[0]&&t(34,c=function(P){if(yn(P))return!1;const te=Pt(P);return io.includes(te)}(O)),1&r.$$.dirty[0]&&t(10,d=((I=G.originalCode)==null?void 0:I.length)||0),64&r.$$.dirty[0]&&t(9,f=(se==null?void 0:se.length)||0),1024&r.$$.dirty[0]&&t(33,m=An(d)),512&r.$$.dirty[0]&&t(32,A=An(f)),65&r.$$.dirty[0]&&t(8,v=!se&&!!G.originalCode),65&r.$$.dirty[0]&&t(7,_=!!se&&!G.originalCode),16&r.$$.dirty[1]&&t(17,C=a),24&r.$$.dirty[1]&&t(16,N=!a&&c),384&r.$$.dirty[0]|30&r.$$.dirty[1]&&t(15,F=!a&&!c&&(A||v&&m||_&&A)),64&r.$$.dirty[1]&&t(14,x=Ps(S==null?void 0:S.category,S==null?void 0:S.intensity)),8&r.$$.dirty[0]&&t(13,y=Os(O)),268435456&r.$$.dirty[0]|1&r.$$.dirty[1]&&t(12,k=Q||we),268435456&r.$$.dirty[0]|1&r.$$.dirty[1]&&t(11,E=Q?"Applying changes...":we?"Cannot apply changes from a different repository locally":"Apply changes to local file")},[G,ne,$e,O,ce,ye,se,_,v,f,d,E,k,y,x,F,N,C,u,l,o,i,s,j,Me,function(I){t(6,se=I.detail.modifiedCode),oe==null||oe(se)},function(){t(0,G.modifiedCode=se,G),oe==null||oe(se),xe==null||xe()},he,Q,xe,oe,we,A,m,c,a,n,S,function(I){ne=I,t(1,ne)},function(I){$e=I,t(2,$e)}]}class Po extends K{constructor(e){super(),ee(this,e,So,No,Y,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:27,isCollapsed:2,isApplying:28,hasApplied:5,onApplyChanges:29,onCodeChange:30,isAgentFromDifferentRepo:31},null,[-1,-1])}}function Mn(r,e,t){const n=r.slice();return n[1]=e[t],n[3]=t,n}function Io(r,e,t){const n=r.slice();return n[1]=e[t],n}function Oo(r,e,t){const n=r.slice();return n[1]=e[t],n}function jo(r){let e;return{c(){e=b("div"),e.innerHTML='<div class="c-skeleton-diff__file-header svelte-1eiztmz"><div class="c-skeleton-diff__file-info svelte-1eiztmz"><div class="c-skeleton-diff__file-icon svelte-1eiztmz"></div> <div class="c-skeleton-diff__file-path svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__file-actions svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__code-block svelte-1eiztmz"><div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 70%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 85%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 60%;"></span></div></div>',D(e,"class","c-skeleton-diff__changes-item svelte-1eiztmz")},m(t,n){g(t,e,n)},p:J,d(t){t&&h(e)}}}function Vo(r){let e,t,n,s,i=pe(Array(2)),o=[];for(let l=0;l<i.length;l+=1)o[l]=jo(Oo(r,i,l));return{c(){e=b("div"),t=b("div"),t.innerHTML='<div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__subtitle svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__icon svelte-1eiztmz"></div>',n=Z(),s=b("div");for(let l=0;l<o.length;l+=1)o[l].c();D(t,"class","c-skeleton-diff__header svelte-1eiztmz"),D(s,"class","c-skeleton-diff__changes svelte-1eiztmz"),D(e,"class","c-skeleton-diff__subsection svelte-1eiztmz")},m(l,a){g(l,e,a),T(e,t),T(e,n),T(e,s);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(s,null)},p:J,d(l){l&&h(e),Le(o,l)}}}function qn(r){let e,t,n,s,i,o,l=r[3]===0&&function(c){let d;return{c(){d=b("div"),d.innerHTML='<div class="c-skeleton-diff__button svelte-1eiztmz"></div>',D(d,"class","c-skeleton-diff__controls svelte-1eiztmz")},m(f,m){g(f,d,m)},d(f){f&&h(d)}}}(),a=pe(Array(2)),u=[];for(let c=0;c<a.length;c+=1)u[c]=Vo(Io(r,a,c));return{c(){e=b("div"),t=b("div"),n=b("div"),n.innerHTML='<div class="c-skeleton-diff__title svelte-1eiztmz"></div> <div class="c-skeleton-diff__description svelte-1eiztmz"><div class="c-skeleton-diff__line svelte-1eiztmz"></div> <div class="c-skeleton-diff__line svelte-1eiztmz" style="width: 85%;"></div></div>',s=Z(),l&&l.c(),i=Z();for(let c=0;c<u.length;c+=1)u[c].c();o=Z(),D(n,"class","c-skeleton-diff__content svelte-1eiztmz"),D(t,"class","c-skeleton-diff__header svelte-1eiztmz"),D(e,"class","c-skeleton-diff__section svelte-1eiztmz")},m(c,d){g(c,e,d),T(e,t),T(t,n),T(t,s),l&&l.m(t,null),T(e,i);for(let f=0;f<u.length;f+=1)u[f]&&u[f].m(e,null);T(e,o)},p(c,d){},d(c){c&&h(e),l&&l.d(),Le(u,c)}}}function Zo(r){let e,t=pe(Array(r[0])),n=[];for(let s=0;s<t.length;s+=1)n[s]=qn(Mn(r,t,s));return{c(){e=b("div");for(let s=0;s<n.length;s+=1)n[s].c();D(e,"class","c-skeleton-diff svelte-1eiztmz")},m(s,i){g(s,e,i);for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(e,null)},p(s,[i]){if(1&i){let o;for(t=pe(Array(s[0])),o=0;o<t.length;o+=1){const l=Mn(s,t,o);n[o]?n[o].p(l,i):(n[o]=qn(l),n[o].c(),n[o].m(e,null))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},i:J,o:J,d(s){s&&h(e),Le(n,s)}}}function Uo(r,e,t){let{count:n=2}=e;return r.$$set=s=>{"count"in s&&t(0,n=s.count)},[n]}class Ho extends K{constructor(e){super(),ee(this,e,Uo,Zo,Y,{count:0})}}function Rn(...r){return"/"+r.flatMap(e=>e.split("/")).filter(e=>!!e).join("/")}function Tn(r){return r.startsWith("/")||r.startsWith("#")}function At(r){let e,t;const n=r[5].default,s=be(n,r,r[4],null);let i=[{id:r[1]}],o={};for(let l=0;l<i.length;l+=1)o=Ls(o,i[l]);return{c(){e=b(`h${r[0].depth}`),s&&s.c(),$t(`h${r[0].depth}`)(e,o)},m(l,a){g(l,e,a),s&&s.m(e,null),t=!0},p(l,a){s&&s.p&&(!t||16&a)&&Ee(s,n,l,l[4],t?Be(n,l[4],a,null):_e(l[4]),null),$t(`h${l[0].depth}`)(e,o=Ms(i,[(!t||2&a)&&{id:l[1]}]))},i(l){t||(p(s,l),t=!0)},o(l){$(s,l),t=!1},d(l){l&&h(e),s&&s.d(l)}}}function Wo(r){let e,t,n=`h${r[0].depth}`,s=`h${r[0].depth}`&&At(r);return{c(){s&&s.c(),e=ke()},m(i,o){s&&s.m(i,o),g(i,e,o),t=!0},p(i,[o]){`h${i[0].depth}`?n?Y(n,`h${i[0].depth}`)?(s.d(1),s=At(i),n=`h${i[0].depth}`,s.c(),s.m(e.parentNode,e)):s.p(i,o):(s=At(i),n=`h${i[0].depth}`,s.c(),s.m(e.parentNode,e)):n&&(s.d(1),s=null,n=`h${i[0].depth}`)},i(i){t||(p(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function Qo(r,e,t){let{$$slots:n={},$$scope:s}=e,{token:i}=e,{options:o}=e,l;return r.$$set=a=>{"token"in a&&t(0,i=a.token),"options"in a&&t(2,o=a.options),"$$scope"in a&&t(4,s=a.$$scope)},r.$$.update=()=>{var a,u;5&r.$$.dirty&&t(1,(a=i.text,u=o.slugger,l=u.slug(a).replace(/--+/g,"-")))},[i,l,o,void 0,s,n]}class Go extends K{constructor(e){super(),ee(this,e,Qo,Wo,Y,{token:0,options:2,renderers:3})}get renderers(){return this.$$.ctx[3]}}function Jo(r){let e,t;const n=r[4].default,s=be(n,r,r[3],null);return{c(){e=b("blockquote"),s&&s.c()},m(i,o){g(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Ee(s,n,i,i[3],t?Be(n,i[3],o,null):_e(i[3]),null)},i(i){t||(p(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function Yo(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Xo extends K{constructor(e){super(),ee(this,e,Yo,Jo,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Nn(r,e,t){const n=r.slice();return n[3]=e[t],n}function Sn(r){let e,t,n=pe(r[0]),s=[];for(let o=0;o<n.length;o+=1)s[o]=Pn(Nn(r,n,o));const i=o=>$(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();e=ke()},m(o,l){for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(o,l);g(o,e,l),t=!0},p(o,l){if(7&l){let a;for(n=pe(o[0]),a=0;a<n.length;a+=1){const u=Nn(o,n,a);s[a]?(s[a].p(u,l),p(s[a],1)):(s[a]=Pn(u),s[a].c(),p(s[a],1),s[a].m(e.parentNode,e))}for(H(),a=n.length;a<s.length;a+=1)i(a);W()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)p(s[l]);t=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)$(s[l]);t=!1},d(o){o&&h(e),Le(s,o)}}}function Pn(r){let e,t;return e=new Xs({props:{token:r[3],renderers:r[1],options:r[2]}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.token=n[3]),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Ko(r){let e,t,n=r[0]&&Sn(r);return{c(){n&&n.c(),e=ke()},m(s,i){n&&n.m(s,i),g(s,e,i),t=!0},p(s,[i]){s[0]?n?(n.p(s,i),1&i&&p(n,1)):(n=Sn(s),n.c(),p(n,1),n.m(e.parentNode,e)):n&&(H(),$(n,1,1,()=>{n=null}),W())},i(s){t||(p(n),t=!0)},o(s){$(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function el(r,e,t){let{tokens:n}=e,{renderers:s}=e,{options:i}=e;return r.$$set=o=>{"tokens"in o&&t(0,n=o.tokens),"renderers"in o&&t(1,s=o.renderers),"options"in o&&t(2,i=o.options)},[n,s,i]}class vt extends K{constructor(e){super(),ee(this,e,el,Ko,Y,{tokens:0,renderers:1,options:2})}}function In(r){let e,t,n;var s=r[1][r[0].type];function i(o,l){return{props:{token:o[0],options:o[2],renderers:o[1],$$slots:{default:[sl]},$$scope:{ctx:o}}}}return s&&(e=Gt(s,i(r))),{c(){e&&L(e.$$.fragment),t=ke()},m(o,l){e&&M(e,o,l),g(o,t,l),n=!0},p(o,l){if(3&l&&s!==(s=o[1][o[0].type])){if(e){H();const a=e;$(a.$$.fragment,1,0,()=>{q(a,1)}),W()}s?(e=Gt(s,i(o)),L(e.$$.fragment),p(e.$$.fragment,1),M(e,t.parentNode,t)):e=null}else if(s){const a={};1&l&&(a.token=o[0]),4&l&&(a.options=o[2]),2&l&&(a.renderers=o[1]),15&l&&(a.$$scope={dirty:l,ctx:o}),e.$set(a)}},i(o){n||(e&&p(e.$$.fragment,o),n=!0)},o(o){e&&$(e.$$.fragment,o),n=!1},d(o){o&&h(t),e&&q(e,o)}}}function tl(r){let e,t=r[0].raw+"";return{c(){e=R(t)},m(n,s){g(n,e,s)},p(n,s){1&s&&t!==(t=n[0].raw+"")&&le(e,t)},i:J,o:J,d(n){n&&h(e)}}}function nl(r){let e,t;return e=new vt({props:{tokens:r[0].tokens,renderers:r[1],options:r[2]}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.tokens=n[0].tokens),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function sl(r){let e,t,n,s;const i=[nl,tl],o=[];function l(a,u){return"tokens"in a[0]&&a[0].tokens?0:1}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=ke()},m(a,u){o[e].m(a,u),g(a,n,u),s=!0},p(a,u){let c=e;e=l(a),e===c?o[e].p(a,u):(H(),$(o[c],1,1,()=>{o[c]=null}),W(),t=o[e],t?t.p(a,u):(t=o[e]=i[e](a),t.c()),p(t,1),t.m(n.parentNode,n))},i(a){s||(p(t),s=!0)},o(a){$(t),s=!1},d(a){a&&h(n),o[e].d(a)}}}function il(r){let e,t,n=r[1][r[0].type]&&In(r);return{c(){n&&n.c(),e=ke()},m(s,i){n&&n.m(s,i),g(s,e,i),t=!0},p(s,[i]){s[1][s[0].type]?n?(n.p(s,i),3&i&&p(n,1)):(n=In(s),n.c(),p(n,1),n.m(e.parentNode,e)):n&&(H(),$(n,1,1,()=>{n=null}),W())},i(s){t||(p(n),t=!0)},o(s){$(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function rl(r,e,t){let{token:n}=e,{renderers:s}=e,{options:i}=e;return r.$$set=o=>{"token"in o&&t(0,n=o.token),"renderers"in o&&t(1,s=o.renderers),"options"in o&&t(2,i=o.options)},[n,s,i]}class Xs extends K{constructor(e){super(),ee(this,e,rl,il,Y,{token:0,renderers:1,options:2})}}function On(r,e,t){const n=r.slice();return n[4]=e[t],n}function jn(r){let e,t;return e=new Xs({props:{token:{...r[4]},options:r[1],renderers:r[2]}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.token={...n[4]}),2&s&&(i.options=n[1]),4&s&&(i.renderers=n[2]),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function bt(r){let e,t,n,s=pe(r[0].items),i=[];for(let u=0;u<s.length;u+=1)i[u]=jn(On(r,s,u));const o=u=>$(i[u],1,1,()=>{i[u]=null});let l=[{start:t=r[0].start||1}],a={};for(let u=0;u<l.length;u+=1)a=Ls(a,l[u]);return{c(){e=b(r[3]);for(let u=0;u<i.length;u+=1)i[u].c();$t(r[3])(e,a)},m(u,c){g(u,e,c);for(let d=0;d<i.length;d+=1)i[d]&&i[d].m(e,null);n=!0},p(u,c){if(7&c){let d;for(s=pe(u[0].items),d=0;d<s.length;d+=1){const f=On(u,s,d);i[d]?(i[d].p(f,c),p(i[d],1)):(i[d]=jn(f),i[d].c(),p(i[d],1),i[d].m(e,null))}for(H(),d=s.length;d<i.length;d+=1)o(d);W()}$t(u[3])(e,a=Ms(l,[(!n||1&c&&t!==(t=u[0].start||1))&&{start:t}]))},i(u){if(!n){for(let c=0;c<s.length;c+=1)p(i[c]);n=!0}},o(u){i=i.filter(Boolean);for(let c=0;c<i.length;c+=1)$(i[c]);n=!1},d(u){u&&h(e),Le(i,u)}}}function ol(r){let e,t=r[3],n=r[3]&&bt(r);return{c(){n&&n.c(),e=ke()},m(s,i){n&&n.m(s,i),g(s,e,i)},p(s,[i]){s[3]?t?Y(t,s[3])?(n.d(1),n=bt(s),t=s[3],n.c(),n.m(e.parentNode,e)):n.p(s,i):(n=bt(s),t=s[3],n.c(),n.m(e.parentNode,e)):t&&(n.d(1),n=null,t=s[3])},i:J,o(s){$(n,s)},d(s){s&&h(e),n&&n.d(s)}}}function ll(r,e,t){let n,{token:s}=e,{options:i}=e,{renderers:o}=e;return r.$$set=l=>{"token"in l&&t(0,s=l.token),"options"in l&&t(1,i=l.options),"renderers"in l&&t(2,o=l.renderers)},r.$$.update=()=>{1&r.$$.dirty&&t(3,n=s.ordered?"ol":"ul")},[s,i,o,n]}class al extends K{constructor(e){super(),ee(this,e,ll,ol,Y,{token:0,options:1,renderers:2})}}function ul(r){let e,t;const n=r[4].default,s=be(n,r,r[3],null);return{c(){e=b("li"),s&&s.c()},m(i,o){g(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Ee(s,n,i,i[3],t?Be(n,i[3],o,null):_e(i[3]),null)},i(i){t||(p(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function cl(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class dl extends K{constructor(e){super(),ee(this,e,cl,ul,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function pl(r){let e;return{c(){e=b("br")},m(t,n){g(t,e,n)},p:J,i:J,o:J,d(t){t&&h(e)}}}function fl(r,e,t){return[void 0,void 0,void 0]}class gl extends K{constructor(e){super(),ee(this,e,fl,pl,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function hl(r){let e,t,n,s,i=r[0].text+"";return{c(){e=b("pre"),t=b("code"),n=R(i),D(t,"class",s=`lang-${r[0].lang}`)},m(o,l){g(o,e,l),T(e,t),T(t,n)},p(o,[l]){1&l&&i!==(i=o[0].text+"")&&le(n,i),1&l&&s!==(s=`lang-${o[0].lang}`)&&D(t,"class",s)},i:J,o:J,d(o){o&&h(e)}}}function $l(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n,void 0,void 0]}class ml extends K{constructor(e){super(),ee(this,e,$l,hl,Y,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Dl(r){let e,t,n=r[0].raw.slice(1,r[0].raw.length-1)+"";return{c(){e=b("code"),t=R(n)},m(s,i){g(s,e,i),T(e,t)},p(s,[i]){1&i&&n!==(n=s[0].raw.slice(1,s[0].raw.length-1)+"")&&le(t,n)},i:J,o:J,d(s){s&&h(e)}}}function Fl(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n,void 0,void 0]}class xl extends K{constructor(e){super(),ee(this,e,Fl,Dl,Y,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Vn(r,e,t){const n=r.slice();return n[3]=e[t],n}function Zn(r,e,t){const n=r.slice();return n[6]=e[t],n}function Un(r,e,t){const n=r.slice();return n[9]=e[t],n}function Hn(r){let e,t,n,s;return t=new vt({props:{tokens:r[9].tokens,options:r[1],renderers:r[2]}}),{c(){e=b("th"),L(t.$$.fragment),n=Z(),D(e,"scope","col")},m(i,o){g(i,e,o),M(t,e,null),T(e,n),s=!0},p(i,o){const l={};1&o&&(l.tokens=i[9].tokens),2&o&&(l.options=i[1]),4&o&&(l.renderers=i[2]),t.$set(l)},i(i){s||(p(t.$$.fragment,i),s=!0)},o(i){$(t.$$.fragment,i),s=!1},d(i){i&&h(e),q(t)}}}function Wn(r){let e,t,n;return t=new vt({props:{tokens:r[6].tokens,options:r[1],renderers:r[2]}}),{c(){e=b("td"),L(t.$$.fragment)},m(s,i){g(s,e,i),M(t,e,null),n=!0},p(s,i){const o={};1&i&&(o.tokens=s[6].tokens),2&i&&(o.options=s[1]),4&i&&(o.renderers=s[2]),t.$set(o)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function Qn(r){let e,t,n,s=pe(r[3]),i=[];for(let l=0;l<s.length;l+=1)i[l]=Wn(Zn(r,s,l));const o=l=>$(i[l],1,1,()=>{i[l]=null});return{c(){e=b("tr");for(let l=0;l<i.length;l+=1)i[l].c();t=Z()},m(l,a){g(l,e,a);for(let u=0;u<i.length;u+=1)i[u]&&i[u].m(e,null);T(e,t),n=!0},p(l,a){if(7&a){let u;for(s=pe(l[3]),u=0;u<s.length;u+=1){const c=Zn(l,s,u);i[u]?(i[u].p(c,a),p(i[u],1)):(i[u]=Wn(c),i[u].c(),p(i[u],1),i[u].m(e,t))}for(H(),u=s.length;u<i.length;u+=1)o(u);W()}},i(l){if(!n){for(let a=0;a<s.length;a+=1)p(i[a]);n=!0}},o(l){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)$(i[a]);n=!1},d(l){l&&h(e),Le(i,l)}}}function Cl(r){let e,t,n,s,i,o,l=pe(r[0].header),a=[];for(let m=0;m<l.length;m+=1)a[m]=Hn(Un(r,l,m));const u=m=>$(a[m],1,1,()=>{a[m]=null});let c=pe(r[0].rows),d=[];for(let m=0;m<c.length;m+=1)d[m]=Qn(Vn(r,c,m));const f=m=>$(d[m],1,1,()=>{d[m]=null});return{c(){e=b("table"),t=b("thead"),n=b("tr");for(let m=0;m<a.length;m+=1)a[m].c();s=Z(),i=b("tbody");for(let m=0;m<d.length;m+=1)d[m].c()},m(m,A){g(m,e,A),T(e,t),T(t,n);for(let v=0;v<a.length;v+=1)a[v]&&a[v].m(n,null);T(e,s),T(e,i);for(let v=0;v<d.length;v+=1)d[v]&&d[v].m(i,null);o=!0},p(m,[A]){if(7&A){let v;for(l=pe(m[0].header),v=0;v<l.length;v+=1){const _=Un(m,l,v);a[v]?(a[v].p(_,A),p(a[v],1)):(a[v]=Hn(_),a[v].c(),p(a[v],1),a[v].m(n,null))}for(H(),v=l.length;v<a.length;v+=1)u(v);W()}if(7&A){let v;for(c=pe(m[0].rows),v=0;v<c.length;v+=1){const _=Vn(m,c,v);d[v]?(d[v].p(_,A),p(d[v],1)):(d[v]=Qn(_),d[v].c(),p(d[v],1),d[v].m(i,null))}for(H(),v=c.length;v<d.length;v+=1)f(v);W()}},i(m){if(!o){for(let A=0;A<l.length;A+=1)p(a[A]);for(let A=0;A<c.length;A+=1)p(d[A]);o=!0}},o(m){a=a.filter(Boolean);for(let A=0;A<a.length;A+=1)$(a[A]);d=d.filter(Boolean);for(let A=0;A<d.length;A+=1)$(d[A]);o=!1},d(m){m&&h(e),Le(a,m),Le(d,m)}}}function kl(r,e,t){let{token:n}=e,{options:s}=e,{renderers:i}=e;return r.$$set=o=>{"token"in o&&t(0,n=o.token),"options"in o&&t(1,s=o.options),"renderers"in o&&t(2,i=o.renderers)},[n,s,i]}class wl extends K{constructor(e){super(),ee(this,e,kl,Cl,Y,{token:0,options:1,renderers:2})}}function vl(r){let e,t,n=r[0].text+"";return{c(){e=new di(!1),t=ke(),e.a=t},m(s,i){e.m(n,s,i),g(s,t,i)},p(s,[i]){1&i&&n!==(n=s[0].text+"")&&e.p(n)},i:J,o:J,d(s){s&&(h(t),e.d())}}}function yl(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n,void 0,void 0]}class Al extends K{constructor(e){super(),ee(this,e,yl,vl,Y,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function bl(r){let e,t;const n=r[4].default,s=be(n,r,r[3],null);return{c(){e=b("p"),s&&s.c()},m(i,o){g(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Ee(s,n,i,i[3],t?Be(n,i[3],o,null):_e(i[3]),null)},i(i){t||(p(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function El(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}let _l=class extends K{constructor(r){super(),ee(this,r,El,bl,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}};function Bl(r){let e,t,n,s;const i=r[4].default,o=be(i,r,r[3],null);return{c(){e=b("a"),o&&o.c(),D(e,"href",t=Tn(r[0].href)?Rn(r[1].baseUrl,r[0].href):r[0].href),D(e,"title",n=r[0].title)},m(l,a){g(l,e,a),o&&o.m(e,null),s=!0},p(l,[a]){o&&o.p&&(!s||8&a)&&Ee(o,i,l,l[3],s?Be(i,l[3],a,null):_e(l[3]),null),(!s||3&a&&t!==(t=Tn(l[0].href)?Rn(l[1].baseUrl,l[0].href):l[0].href))&&D(e,"href",t),(!s||1&a&&n!==(n=l[0].title))&&D(e,"title",n)},i(l){s||(p(o,l),s=!0)},o(l){$(o,l),s=!1},d(l){l&&h(e),o&&o.d(l)}}}function zl(r,e,t){let{$$slots:n={},$$scope:s}=e,{token:i}=e,{options:o}=e;return r.$$set=l=>{"token"in l&&t(0,i=l.token),"options"in l&&t(1,o=l.options),"$$scope"in l&&t(3,s=l.$$scope)},[i,o,void 0,s,n]}class Ll extends K{constructor(e){super(),ee(this,e,zl,Bl,Y,{token:0,options:1,renderers:2})}get renderers(){return this.$$.ctx[2]}}function Ml(r){let e;const t=r[4].default,n=be(t,r,r[3],null);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),e=!0},p(s,[i]){n&&n.p&&(!e||8&i)&&Ee(n,t,s,s[3],e?Be(t,s[3],i,null):_e(s[3]),null)},i(s){e||(p(n,s),e=!0)},o(s){$(n,s),e=!1},d(s){n&&n.d(s)}}}function ql(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Rl extends K{constructor(e){super(),ee(this,e,ql,Ml,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Tl(r){let e,t;const n=r[4].default,s=be(n,r,r[3],null);return{c(){e=b("dfn"),s&&s.c()},m(i,o){g(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Ee(s,n,i,i[3],t?Be(n,i[3],o,null):_e(i[3]),null)},i(i){t||(p(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function Nl(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Sl extends K{constructor(e){super(),ee(this,e,Nl,Tl,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Pl(r){let e,t;const n=r[4].default,s=be(n,r,r[3],null);return{c(){e=b("del"),s&&s.c()},m(i,o){g(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Ee(s,n,i,i[3],t?Be(n,i[3],o,null):_e(i[3]),null)},i(i){t||(p(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function Il(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Ol extends K{constructor(e){super(),ee(this,e,Il,Pl,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function jl(r){let e,t;const n=r[4].default,s=be(n,r,r[3],null);return{c(){e=b("em"),s&&s.c()},m(i,o){g(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Ee(s,n,i,i[3],t?Be(n,i[3],o,null):_e(i[3]),null)},i(i){t||(p(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function Vl(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Zl extends K{constructor(e){super(),ee(this,e,Vl,jl,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Ul(r){let e;return{c(){e=b("hr")},m(t,n){g(t,e,n)},p:J,i:J,o:J,d(t){t&&h(e)}}}function Hl(r,e,t){return[void 0,void 0,void 0]}class Wl extends K{constructor(e){super(),ee(this,e,Hl,Ul,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Ql(r){let e,t;const n=r[4].default,s=be(n,r,r[3],null);return{c(){e=b("strong"),s&&s.c()},m(i,o){g(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Ee(s,n,i,i[3],t?Be(n,i[3],o,null):_e(i[3]),null)},i(i){t||(p(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function Gl(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Jl extends K{constructor(e){super(),ee(this,e,Gl,Ql,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Yl(r){let e,t,n,s;return{c(){e=b("img"),Re(e.src,t=r[0].href)||D(e,"src",t),D(e,"title",n=r[0].title),D(e,"alt",s=r[0].text),D(e,"class","markdown-image svelte-z38cge")},m(i,o){g(i,e,o)},p(i,[o]){1&o&&!Re(e.src,t=i[0].href)&&D(e,"src",t),1&o&&n!==(n=i[0].title)&&D(e,"title",n),1&o&&s!==(s=i[0].text)&&D(e,"alt",s)},i:J,o:J,d(i){i&&h(e)}}}function Xl(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n,void 0,void 0]}class Kl extends K{constructor(e){super(),ee(this,e,Xl,Yl,Y,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ea(r){let e;const t=r[4].default,n=be(t,r,r[3],null);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),e=!0},p(s,[i]){n&&n.p&&(!e||8&i)&&Ee(n,t,s,s[3],e?Be(t,s[3],i,null):_e(s[3]),null)},i(s){e||(p(n,s),e=!0)},o(s){$(n,s),e=!1},d(s){n&&n.d(s)}}}function ta(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Gn extends K{constructor(e){super(),ee(this,e,ta,ea,Y,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function na(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let Je={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function Jn(r){Je=r}const Ks=/[&<>"']/,sa=new RegExp(Ks.source,"g"),ei=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,ia=new RegExp(ei.source,"g"),ra={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Yn=r=>ra[r];function Te(r,e){if(e){if(Ks.test(r))return r.replace(sa,Yn)}else if(ei.test(r))return r.replace(ia,Yn);return r}const oa=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function la(r){return r.replace(oa,(e,t)=>(t=t.toLowerCase())==="colon"?":":t.charAt(0)==="#"?t.charAt(1)==="x"?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):"")}const aa=/(^|[^\[])\^/g;function Fe(r,e){let t=typeof r=="string"?r:r.source;e=e||"";const n={replace:(s,i)=>{let o=typeof i=="string"?i:i.source;return o=o.replace(aa,"$1"),t=t.replace(s,o),n},getRegex:()=>new RegExp(t,e)};return n}function Xn(r){try{r=encodeURI(r).replace(/%25/g,"%")}catch{return null}return r}const et={exec:()=>null};function Kn(r,e){const t=r.replace(/\|/g,(s,i,o)=>{let l=!1,a=i;for(;--a>=0&&o[a]==="\\";)l=!l;return l?"|":" |"}).split(/ \|/);let n=0;if(t[0].trim()||t.shift(),t.length>0&&!t[t.length-1].trim()&&t.pop(),e)if(t.length>e)t.splice(e);else for(;t.length<e;)t.push("");for(;n<t.length;n++)t[n]=t[n].trim().replace(/\\\|/g,"|");return t}function pt(r,e,t){const n=r.length;if(n===0)return"";let s=0;for(;s<n;){const i=r.charAt(n-s-1);if(i!==e||t){if(i===e||!t)break;s++}else s++}return r.slice(0,n-s)}function es(r,e,t,n){const s=e.href,i=e.title?Te(e.title):null,o=r[1].replace(/\\([\[\]])/g,"$1");if(r[0].charAt(0)!=="!"){n.state.inLink=!0;const l={type:"link",raw:t,href:s,title:i,text:o,tokens:n.inlineTokens(o)};return n.state.inLink=!1,l}return{type:"image",raw:t,href:s,title:i,text:Te(o)}}class xt{constructor(e){ge(this,"options");ge(this,"rules");ge(this,"lexer");this.options=e||Je}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const n=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:pt(n,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const n=t[0],s=function(i,o){const l=i.match(/^(\s+)(?:```)/);if(l===null)return o;const a=l[1];return o.split(`
`).map(u=>{const c=u.match(/^\s+/);if(c===null)return u;const[d]=c;return d.length>=a.length?u.slice(a.length):u}).join(`
`)}(n,t[3]||"");return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:s}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(/#$/.test(n)){const s=pt(n,"#");this.options.pedantic?n=s.trim():s&&!/ $/.test(s)||(n=s.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const n=pt(t[0].replace(/^ *>[ \t]?/gm,""),`
`),s=this.lexer.state.top;this.lexer.state.top=!0;const i=this.lexer.blockTokens(n);return this.lexer.state.top=s,{type:"blockquote",raw:t[0],tokens:i,text:n}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const s=n.length>1,i={type:"list",raw:"",ordered:s,start:s?+n.slice(0,-1):"",loose:!1,items:[]};n=s?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=s?n:"[*+-]");const o=new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`);let l="",a="",u=!1;for(;e;){let c=!1;if(!(t=o.exec(e))||this.rules.block.hr.test(e))break;l=t[0],e=e.substring(l.length);let d=t[2].split(`
`,1)[0].replace(/^\t+/,C=>" ".repeat(3*C.length)),f=e.split(`
`,1)[0],m=0;this.options.pedantic?(m=2,a=d.trimStart()):(m=t[2].search(/[^ ]/),m=m>4?1:m,a=d.slice(m),m+=t[1].length);let A=!1;if(!d&&/^ *$/.test(f)&&(l+=f+`
`,e=e.substring(f.length+1),c=!0),!c){const C=new RegExp(`^ {0,${Math.min(3,m-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),N=new RegExp(`^ {0,${Math.min(3,m-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),F=new RegExp(`^ {0,${Math.min(3,m-1)}}(?:\`\`\`|~~~)`),x=new RegExp(`^ {0,${Math.min(3,m-1)}}#`);for(;e;){const y=e.split(`
`,1)[0];if(f=y,this.options.pedantic&&(f=f.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),F.test(f)||x.test(f)||C.test(f)||N.test(e))break;if(f.search(/[^ ]/)>=m||!f.trim())a+=`
`+f.slice(m);else{if(A||d.search(/[^ ]/)>=4||F.test(d)||x.test(d)||N.test(d))break;a+=`
`+f}A||f.trim()||(A=!0),l+=y+`
`,e=e.substring(y.length+1),d=f.slice(m)}}i.loose||(u?i.loose=!0:/\n *\n *$/.test(l)&&(u=!0));let v,_=null;this.options.gfm&&(_=/^\[[ xX]\] /.exec(a),_&&(v=_[0]!=="[ ] ",a=a.replace(/^\[[ xX]\] +/,""))),i.items.push({type:"list_item",raw:l,task:!!_,checked:v,loose:!1,text:a,tokens:[]}),i.raw+=l}i.items[i.items.length-1].raw=l.trimEnd(),i.items[i.items.length-1].text=a.trimEnd(),i.raw=i.raw.trimEnd();for(let c=0;c<i.items.length;c++)if(this.lexer.state.top=!1,i.items[c].tokens=this.lexer.blockTokens(i.items[c].text,[]),!i.loose){const d=i.items[c].tokens.filter(m=>m.type==="space"),f=d.length>0&&d.some(m=>/\n.*\n/.test(m.raw));i.loose=f}if(i.loose)for(let c=0;c<i.items.length;c++)i.items[c].loose=!0;return i}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const n=t[1].toLowerCase().replace(/\s+/g," "),s=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",i=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:s,title:i}}}table(e){const t=this.rules.block.table.exec(e);if(!t||!/[:|]/.test(t[2]))return;const n=Kn(t[1]),s=t[2].replace(/^\||\| *$/g,"").split("|"),i=t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===s.length){for(const l of s)/^ *-+: *$/.test(l)?o.align.push("right"):/^ *:-+: *$/.test(l)?o.align.push("center"):/^ *:-+ *$/.test(l)?o.align.push("left"):o.align.push(null);for(const l of n)o.header.push({text:l,tokens:this.lexer.inline(l)});for(const l of i)o.rows.push(Kn(l,o.header.length).map(a=>({text:a,tokens:this.lexer.inline(a)})));return o}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:Te(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const n=t[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;const o=pt(n.slice(0,-1),"\\");if((n.length-o.length)%2==0)return}else{const o=function(l,a){if(l.indexOf(a[1])===-1)return-1;let u=0;for(let c=0;c<l.length;c++)if(l[c]==="\\")c++;else if(l[c]===a[0])u++;else if(l[c]===a[1]&&(u--,u<0))return c;return-1}(t[2],"()");if(o>-1){const l=(t[0].indexOf("!")===0?5:4)+t[1].length+o;t[2]=t[2].substring(0,o),t[0]=t[0].substring(0,l).trim(),t[3]=""}}let s=t[2],i="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(s);o&&(s=o[1],i=o[3])}else i=t[3]?t[3].slice(1,-1):"";return s=s.trim(),/^</.test(s)&&(s=this.options.pedantic&&!/>$/.test(n)?s.slice(1):s.slice(1,-1)),es(t,{href:s&&s.replace(this.rules.inline.anyPunctuation,"$1"),title:i&&i.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const s=t[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!s){const i=n[0].charAt(0);return{type:"text",raw:i,text:i}}return es(n,s,n[0],this.lexer)}}emStrong(e,t,n=""){let s=this.rules.inline.emStrongLDelim.exec(e);if(s&&!(s[3]&&n.match(/[\p{L}\p{N}]/u))&&(!(s[1]||s[2])||!n||this.rules.inline.punctuation.exec(n))){const i=[...s[0]].length-1;let o,l,a=i,u=0;const c=s[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(c.lastIndex=0,t=t.slice(-1*e.length+i);(s=c.exec(t))!=null;){if(o=s[1]||s[2]||s[3]||s[4]||s[5]||s[6],!o)continue;if(l=[...o].length,s[3]||s[4]){a+=l;continue}if((s[5]||s[6])&&i%3&&!((i+l)%3)){u+=l;continue}if(a-=l,a>0)continue;l=Math.min(l,l+a+u);const d=[...s[0]][0].length,f=e.slice(0,i+s.index+d+l);if(Math.min(i,l)%2){const A=f.slice(1,-1);return{type:"em",raw:f,text:A,tokens:this.lexer.inlineTokens(A)}}const m=f.slice(2,-2);return{type:"strong",raw:f,text:m,tokens:this.lexer.inlineTokens(m)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(/\n/g," ");const s=/[^ ]/.test(n),i=/^ /.test(n)&&/ $/.test(n);return s&&i&&(n=n.substring(1,n.length-1)),n=Te(n,!0),{type:"codespan",raw:t[0],text:n}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let n,s;return t[2]==="@"?(n=Te(t[1]),s="mailto:"+n):(n=Te(t[1]),s=n),{type:"link",raw:t[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}url(e){var n;let t;if(t=this.rules.inline.url.exec(e)){let s,i;if(t[2]==="@")s=Te(t[0]),i="mailto:"+s;else{let o;do o=t[0],t[0]=((n=this.rules.inline._backpedal.exec(t[0]))==null?void 0:n[0])??"";while(o!==t[0]);s=Te(t[0]),i=t[1]==="www."?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:s,href:i,tokens:[{type:"text",raw:s,text:s}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){let n;return n=this.lexer.state.inRawBlock?t[0]:Te(t[0]),{type:"text",raw:t[0],text:n}}}}const at=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,ti=/(?:[*+-]|\d{1,9}[.)])/,ni=Fe(/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,ti).getRegex(),It=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Ot=/(?!\s*\])(?:\\.|[^\[\]\\])+/,ua=Fe(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",Ot).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),ca=Fe(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,ti).getRegex(),yt="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",jt=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,da=Fe("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",jt).replace("tag",yt).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),ts=Fe(It).replace("hr",at).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",yt).getRegex(),Vt={blockquote:Fe(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",ts).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:ua,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:at,html:da,lheading:ni,list:ca,newline:/^(?: *(?:\n|$))+/,paragraph:ts,table:et,text:/^[^\n]+/},ns=Fe("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",at).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",yt).getRegex(),pa={...Vt,table:ns,paragraph:Fe(It).replace("hr",at).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",ns).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",yt).getRegex()},fa={...Vt,html:Fe(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",jt).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:et,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:Fe(It).replace("hr",at).replace("heading",` *#{1,6} *[^
]`).replace("lheading",ni).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},si=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,ii=/^( {2,}|\\)\n(?!\s*$)/,ut="\\p{P}$+<=>`^|~",ga=Fe(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,ut).getRegex(),ha=Fe(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,ut).getRegex(),$a=Fe("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,ut).getRegex(),ma=Fe("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,ut).getRegex(),Da=Fe(/\\([punct])/,"gu").replace(/punct/g,ut).getRegex(),Fa=Fe(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),xa=Fe(jt).replace("(?:-->|$)","-->").getRegex(),Ca=Fe("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",xa).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Ct=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,ka=Fe(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",Ct).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),ss=Fe(/^!?\[(label)\]\[(ref)\]/).replace("label",Ct).replace("ref",Ot).getRegex(),is=Fe(/^!?\[(ref)\](?:\[\])?/).replace("ref",Ot).getRegex(),Zt={_backpedal:et,anyPunctuation:Da,autolink:Fa,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:ii,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:et,emStrongLDelim:ha,emStrongRDelimAst:$a,emStrongRDelimUnd:ma,escape:si,link:ka,nolink:is,punctuation:ga,reflink:ss,reflinkSearch:Fe("reflink|nolink(?!\\()","g").replace("reflink",ss).replace("nolink",is).getRegex(),tag:Ca,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:et},wa={...Zt,link:Fe(/^!?\[(label)\]\((.*?)\)/).replace("label",Ct).getRegex(),reflink:Fe(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Ct).getRegex()},Lt={...Zt,escape:Fe(si).replace("])","~|])").getRegex(),url:Fe(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},va={...Lt,br:Fe(ii).replace("{2,}","*").getRegex(),text:Fe(Lt.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},ft={normal:Vt,gfm:pa,pedantic:fa},Ke={normal:Zt,gfm:Lt,breaks:va,pedantic:wa};class Ne{constructor(e){ge(this,"tokens");ge(this,"options");ge(this,"state");ge(this,"tokenizer");ge(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||Je,this.options.tokenizer=this.options.tokenizer||new xt,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:ft.normal,inline:Ke.normal};this.options.pedantic?(t.block=ft.pedantic,t.inline=Ke.pedantic):this.options.gfm&&(t.block=ft.gfm,this.options.breaks?t.inline=Ke.breaks:t.inline=Ke.gfm),this.tokenizer.rules=t}static get rules(){return{block:ft,inline:Ke}}static lex(e,t){return new Ne(t).lex(e)}static lexInline(e,t){return new Ne(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[]){let n,s,i,o;for(e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,(l,a,u)=>a+"    ".repeat(u.length));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(l=>!!(n=l.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.space(e))e=e.substring(n.raw.length),n.raw.length===1&&t.length>0?t[t.length-1].raw+=`
`:t.push(n);else if(n=this.tokenizer.code(e))e=e.substring(n.raw.length),s=t[t.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?t.push(n):(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(n=this.tokenizer.fences(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.heading(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.hr(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.blockquote(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.list(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.html(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.def(e))e=e.substring(n.raw.length),s=t[t.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(s.raw+=`
`+n.raw,s.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(n=this.tokenizer.table(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.lheading(e))e=e.substring(n.raw.length),t.push(n);else{if(i=e,this.options.extensions&&this.options.extensions.startBlock){let l=1/0;const a=e.slice(1);let u;this.options.extensions.startBlock.forEach(c=>{u=c.call({lexer:this},a),typeof u=="number"&&u>=0&&(l=Math.min(l,u))}),l<1/0&&l>=0&&(i=e.substring(0,l+1))}if(this.state.top&&(n=this.tokenizer.paragraph(i)))s=t[t.length-1],o&&s.type==="paragraph"?(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):t.push(n),o=i.length!==e.length,e=e.substring(n.raw.length);else if(n=this.tokenizer.text(e))e=e.substring(n.raw.length),s=t[t.length-1],s&&s.type==="text"?(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):t.push(n);else if(e){const l="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(l);break}throw new Error(l)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n,s,i,o,l,a,u=e;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(u))!=null;)c.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(u=u.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+u.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(u))!=null;)u=u.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+u.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(u))!=null;)u=u.slice(0,o.index)+"++"+u.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(l||(a=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>!!(n=c.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.escape(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(e))e=e.substring(n.raw.length),s=t[t.length-1],s&&n.type==="text"&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):t.push(n);else if(n=this.tokenizer.link(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(n.raw.length),s=t[t.length-1],s&&n.type==="text"&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(e,u,a))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(e))e=e.substring(n.raw.length),t.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(e))){if(i=e,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const d=e.slice(1);let f;this.options.extensions.startInline.forEach(m=>{f=m.call({lexer:this},d),typeof f=="number"&&f>=0&&(c=Math.min(c,f))}),c<1/0&&c>=0&&(i=e.substring(0,c+1))}if(n=this.tokenizer.inlineText(i))e=e.substring(n.raw.length),n.raw.slice(-1)!=="_"&&(a=n.raw.slice(-1)),l=!0,s=t[t.length-1],s&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):t.push(n);else if(e){const c="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(c);break}throw new Error(c)}}else e=e.substring(n.raw.length),t.push(n);return t}}class kt{constructor(e){ge(this,"options");this.options=e||Je}code(e,t,n){var i;const s=(i=(t||"").match(/^\S*/))==null?void 0:i[0];return e=e.replace(/\n$/,"")+`
`,s?'<pre><code class="language-'+Te(s)+'">'+(n?e:Te(e,!0))+`</code></pre>
`:"<pre><code>"+(n?e:Te(e,!0))+`</code></pre>
`}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e,t){return e}heading(e,t,n){return`<h${t}>${e}</h${t}>
`}hr(){return`<hr>
`}list(e,t,n){const s=t?"ol":"ul";return"<"+s+(t&&n!==1?' start="'+n+'"':"")+`>
`+e+"</"+s+`>
`}listitem(e,t,n){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(e){return`<p>${e}</p>
`}table(e,t){return t&&(t=`<tbody>${t}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+t+`</table>
`}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,t){const n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return"<br>"}del(e){return`<del>${e}</del>`}link(e,t,n){const s=Xn(e);if(s===null)return n;let i='<a href="'+(e=s)+'"';return t&&(i+=' title="'+t+'"'),i+=">"+n+"</a>",i}image(e,t,n){const s=Xn(e);if(s===null)return n;let i=`<img src="${e=s}" alt="${n}"`;return t&&(i+=` title="${t}"`),i+=">",i}text(e){return e}}class Ut{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class Ie{constructor(e){ge(this,"options");ge(this,"renderer");ge(this,"textRenderer");this.options=e||Je,this.options.renderer=this.options.renderer||new kt,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Ut}static parse(e,t){return new Ie(t).parse(e)}static parseInline(e,t){return new Ie(t).parseInline(e)}parse(e,t=!0){let n="";for(let s=0;s<e.length;s++){const i=e[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=i,l=this.options.extensions.renderers[o.type].call({parser:this},o);if(l!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){n+=l||"";continue}}switch(i.type){case"space":continue;case"hr":n+=this.renderer.hr();continue;case"heading":{const o=i;n+=this.renderer.heading(this.parseInline(o.tokens),o.depth,la(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=i;n+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=i;let l="",a="";for(let c=0;c<o.header.length;c++)a+=this.renderer.tablecell(this.parseInline(o.header[c].tokens),{header:!0,align:o.align[c]});l+=this.renderer.tablerow(a);let u="";for(let c=0;c<o.rows.length;c++){const d=o.rows[c];a="";for(let f=0;f<d.length;f++)a+=this.renderer.tablecell(this.parseInline(d[f].tokens),{header:!1,align:o.align[f]});u+=this.renderer.tablerow(a)}n+=this.renderer.table(l,u);continue}case"blockquote":{const o=i,l=this.parse(o.tokens);n+=this.renderer.blockquote(l);continue}case"list":{const o=i,l=o.ordered,a=o.start,u=o.loose;let c="";for(let d=0;d<o.items.length;d++){const f=o.items[d],m=f.checked,A=f.task;let v="";if(f.task){const _=this.renderer.checkbox(!!m);u?f.tokens.length>0&&f.tokens[0].type==="paragraph"?(f.tokens[0].text=_+" "+f.tokens[0].text,f.tokens[0].tokens&&f.tokens[0].tokens.length>0&&f.tokens[0].tokens[0].type==="text"&&(f.tokens[0].tokens[0].text=_+" "+f.tokens[0].tokens[0].text)):f.tokens.unshift({type:"text",text:_+" "}):v+=_+" "}v+=this.parse(f.tokens,u),c+=this.renderer.listitem(v,A,!!m)}n+=this.renderer.list(c,l,a);continue}case"html":{const o=i;n+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=i;n+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=i,l=o.tokens?this.parseInline(o.tokens):o.text;for(;s+1<e.length&&e[s+1].type==="text";)o=e[++s],l+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);n+=t?this.renderer.paragraph(l):l;continue}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}parseInline(e,t){t=t||this.renderer;let n="";for(let s=0;s<e.length;s++){const i=e[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=this.options.extensions.renderers[i.type].call({parser:this},i);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){n+=o||"";continue}}switch(i.type){case"escape":{const o=i;n+=t.text(o.text);break}case"html":{const o=i;n+=t.html(o.text);break}case"link":{const o=i;n+=t.link(o.href,o.title,this.parseInline(o.tokens,t));break}case"image":{const o=i;n+=t.image(o.href,o.title,o.text);break}case"strong":{const o=i;n+=t.strong(this.parseInline(o.tokens,t));break}case"em":{const o=i;n+=t.em(this.parseInline(o.tokens,t));break}case"codespan":{const o=i;n+=t.codespan(o.text);break}case"br":n+=t.br();break;case"del":{const o=i;n+=t.del(this.parseInline(o.tokens,t));break}case"text":{const o=i;n+=t.text(o.text);break}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}}class tt{constructor(e){ge(this,"options");this.options=e||Je}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}}ge(tt,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var Qe,Mt,ri,As;const Ue=new(As=class{constructor(...r){Wt(this,Qe);ge(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});ge(this,"options",this.setOptions);ge(this,"parse",ct(this,Qe,Mt).call(this,Ne.lex,Ie.parse));ge(this,"parseInline",ct(this,Qe,Mt).call(this,Ne.lexInline,Ie.parseInline));ge(this,"Parser",Ie);ge(this,"Renderer",kt);ge(this,"TextRenderer",Ut);ge(this,"Lexer",Ne);ge(this,"Tokenizer",xt);ge(this,"Hooks",tt);this.use(...r)}walkTokens(r,e){var n,s;let t=[];for(const i of r)switch(t=t.concat(e.call(this,i)),i.type){case"table":{const o=i;for(const l of o.header)t=t.concat(this.walkTokens(l.tokens,e));for(const l of o.rows)for(const a of l)t=t.concat(this.walkTokens(a.tokens,e));break}case"list":{const o=i;t=t.concat(this.walkTokens(o.items,e));break}default:{const o=i;(s=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&s[o.type]?this.defaults.extensions.childTokens[o.type].forEach(l=>{const a=o[l].flat(1/0);t=t.concat(this.walkTokens(a,e))}):o.tokens&&(t=t.concat(this.walkTokens(o.tokens,e)))}}return t}use(...r){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return r.forEach(t=>{const n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const i=e.renderers[s.name];e.renderers[s.name]=i?function(...o){let l=s.renderer.apply(this,o);return l===!1&&(l=i.apply(this,o)),l}:s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const i=e[s.level];i?i.unshift(s.tokenizer):e[s.level]=[s.tokenizer],s.start&&(s.level==="block"?e.startBlock?e.startBlock.push(s.start):e.startBlock=[s.start]:s.level==="inline"&&(e.startInline?e.startInline.push(s.start):e.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(e.childTokens[s.name]=s.childTokens)}),n.extensions=e),t.renderer){const s=this.defaults.renderer||new kt(this.defaults);for(const i in t.renderer){if(!(i in s))throw new Error(`renderer '${i}' does not exist`);if(i==="options")continue;const o=i,l=t.renderer[o],a=s[o];s[o]=(...u)=>{let c=l.apply(s,u);return c===!1&&(c=a.apply(s,u)),c||""}}n.renderer=s}if(t.tokenizer){const s=this.defaults.tokenizer||new xt(this.defaults);for(const i in t.tokenizer){if(!(i in s))throw new Error(`tokenizer '${i}' does not exist`);if(["options","rules","lexer"].includes(i))continue;const o=i,l=t.tokenizer[o],a=s[o];s[o]=(...u)=>{let c=l.apply(s,u);return c===!1&&(c=a.apply(s,u)),c}}n.tokenizer=s}if(t.hooks){const s=this.defaults.hooks||new tt;for(const i in t.hooks){if(!(i in s))throw new Error(`hook '${i}' does not exist`);if(i==="options")continue;const o=i,l=t.hooks[o],a=s[o];tt.passThroughHooks.has(i)?s[o]=u=>{if(this.defaults.async)return Promise.resolve(l.call(s,u)).then(d=>a.call(s,d));const c=l.call(s,u);return a.call(s,c)}:s[o]=(...u)=>{let c=l.apply(s,u);return c===!1&&(c=a.apply(s,u)),c}}n.hooks=s}if(t.walkTokens){const s=this.defaults.walkTokens,i=t.walkTokens;n.walkTokens=function(o){let l=[];return l.push(i.call(this,o)),s&&(l=l.concat(s.call(this,o))),l}}this.defaults={...this.defaults,...n}}),this}setOptions(r){return this.defaults={...this.defaults,...r},this}lexer(r,e){return Ne.lex(r,e??this.defaults)}parser(r,e){return Ie.parse(r,e??this.defaults)}},Qe=new WeakSet,Mt=function(r,e){return(t,n)=>{const s={...n},i={...this.defaults,...s};this.defaults.async===!0&&s.async===!1&&(i.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),i.async=!0);const o=ct(this,Qe,ri).call(this,!!i.silent,!!i.async);if(t==null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));if(i.hooks&&(i.hooks.options=i),i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(t):t).then(l=>r(l,i)).then(l=>i.hooks?i.hooks.processAllTokens(l):l).then(l=>i.walkTokens?Promise.all(this.walkTokens(l,i.walkTokens)).then(()=>l):l).then(l=>e(l,i)).then(l=>i.hooks?i.hooks.postprocess(l):l).catch(o);try{i.hooks&&(t=i.hooks.preprocess(t));let l=r(t,i);i.hooks&&(l=i.hooks.processAllTokens(l)),i.walkTokens&&this.walkTokens(l,i.walkTokens);let a=e(l,i);return i.hooks&&(a=i.hooks.postprocess(a)),a}catch(l){return o(l)}}},ri=function(r,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,r){const n="<p>An error occurred:</p><pre>"+Te(t.message+"",!0)+"</pre>";return e?Promise.resolve(n):n}if(e)return Promise.reject(t);throw t}},As);function De(r,e){return Ue.parse(r,e)}De.options=De.setOptions=function(r){return Ue.setOptions(r),De.defaults=Ue.defaults,Jn(De.defaults),De},De.getDefaults=na,De.defaults=Je,De.use=function(...r){return Ue.use(...r),De.defaults=Ue.defaults,Jn(De.defaults),De},De.walkTokens=function(r,e){return Ue.walkTokens(r,e)},De.parseInline=Ue.parseInline,De.Parser=Ie,De.parser=Ie.parse,De.Renderer=kt,De.TextRenderer=Ut,De.Lexer=Ne,De.lexer=Ne.lex,De.Tokenizer=xt,De.Hooks=tt,De.parse=De,De.options,De.setOptions,De.use,De.walkTokens,De.parseInline,Ie.parse,Ne.lex;const ya=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,Aa=Object.hasOwnProperty;class ba{constructor(){this.occurrences,this.reset()}slug(e,t){const n=this;let s=function(o,l){return typeof o!="string"?"":(l||(o=o.toLowerCase()),o.replace(ya,"").replace(/ /g,"-"))}(e,t===!0);const i=s;for(;Aa.call(n.occurrences,s);)n.occurrences[i]++,s=i+"-"+n.occurrences[i];return n.occurrences[s]=0,s}reset(){this.occurrences=Object.create(null)}}function Ea(r){let e,t;return e=new vt({props:{tokens:r[0],renderers:r[1],options:r[2]}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.tokens=n[0]),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function _a(r,e,t){(function(){const u=console.warn;console.warn=c=>{c.includes("unknown prop")||c.includes("unexpected slot")||u(c)},rt(()=>{console.warn=u})})();let n,s,i,{source:o}=e,{options:l={}}=e,{renderers:a={}}=e;return r.$$set=u=>{"source"in u&&t(3,o=u.source),"options"in u&&t(4,l=u.options),"renderers"in u&&t(5,a=u.renderers)},r.$$.update=()=>{var u;56&r.$$.dirty&&(t(0,(u=o,n=new Ne().lex(u))),t(1,s={heading:Go,blockquote:Xo,list:al,list_item:dl,br:gl,code:ml,codespan:xl,table:wl,html:Al,paragraph:_l,link:Ll,text:Rl,def:Sl,del:Ol,em:Zl,hr:Wl,strong:Jl,image:Kl,space:Gn,escape:Gn,...a}),t(2,i={baseUrl:"/",slugger:new ba,...l}))},[n,s,i,o,l,a]}class Ba extends K{constructor(e){super(),ee(this,e,_a,Ea,Y,{source:3,options:4,renderers:5})}}const za=r=>({}),rs=r=>({}),La=r=>({}),os=r=>({}),Ma=r=>({}),ls=r=>({});function qa(r){let e,t,n,s,i,o,l,a,u,c,d,f;const m=r[13].topBarLeft,A=be(m,r,r[12],ls),v=r[13].topBarRight,_=be(v,r,r[12],os);function C(y){r[16](y)}let N={options:{lineNumbers:"off",wrappingIndent:"same",padding:r[5],wordWrap:r[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"},text:r[3].text,lang:r[4]||r[3].lang,height:r[6]};r[0]!==void 0&&(N.editorInstance=r[0]),o=new wi({props:N}),qe.push(()=>He(o,"editorInstance",C));const F=r[13].actionsBar,x=be(F,r,r[12],rs);return{c(){e=b("div"),t=b("div"),n=b("div"),A&&A.c(),s=Z(),_&&_.c(),i=Z(),L(o.$$.fragment),a=Z(),u=b("div"),x&&x.c(),D(n,"class","c-codeblock__top-bar-left svelte-1jljgam"),D(t,"class","c-codeblock__top-bar-anchor monaco-component svelte-1jljgam"),D(u,"class","c-codeblock__actions-bar-anchor svelte-1jljgam"),D(e,"class","c-codeblock svelte-1jljgam"),D(e,"role","button"),D(e,"tabindex","0")},m(y,k){g(y,e,k),T(e,t),T(t,n),A&&A.m(n,null),T(t,s),_&&_.m(t,null),T(e,i),M(o,e,null),T(e,a),T(e,u),x&&x.m(u,null),r[17](e),c=!0,d||(f=[nt(window,"focus",r[15]),nt(e,"mouseenter",r[14])],d=!0)},p(y,[k]){A&&A.p&&(!c||4096&k)&&Ee(A,m,y,y[12],c?Be(m,y[12],k,Ma):_e(y[12]),ls),_&&_.p&&(!c||4096&k)&&Ee(_,v,y,y[12],c?Be(v,y[12],k,La):_e(y[12]),os);const E={};36&k&&(E.options={lineNumbers:"off",wrappingIndent:"same",padding:y[5],wordWrap:y[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"}),8&k&&(E.text=y[3].text),24&k&&(E.lang=y[4]||y[3].lang),64&k&&(E.height=y[6]),!l&&1&k&&(l=!0,E.editorInstance=y[0],We(()=>l=!1)),o.$set(E),x&&x.p&&(!c||4096&k)&&Ee(x,F,y,y[12],c?Be(F,y[12],k,za):_e(y[12]),rs)},i(y){c||(p(A,y),p(_,y),p(o.$$.fragment,y),p(x,y),c=!0)},o(y){$(A,y),$(_,y),$(o.$$.fragment,y),$(x,y),c=!1},d(y){y&&h(e),A&&A.d(y),_&&_.d(y),q(o),x&&x.d(y),r[17](null),d=!1,Bs(f)}}}function Ra(r,e,t){let n,{$$slots:s={},$$scope:i}=e,{scroll:o=!1}=e,{token:l}=e,{language:a}=e,{padding:u={top:0,bottom:0}}=e,{editorInstance:c}=e,{element:d}=e,{height:f}=e;const m=Rt.getContext().monaco;ze(r,m,C=>t(18,n=C));const A=Tt(),v=()=>{if(!c)return;const C=c.getSelections();if(!(C!=null&&C.length))return;const N=c.getModel();if(C.map(F=>(N==null?void 0:N.getValueLengthInRange(F))||0).reduce((F,x)=>F+x,0)!==0)return C.sort(n==null?void 0:n.Range.compareRangesUsingStarts).map(F=>(N==null?void 0:N.getValueInRange(F))||"").join(`
`)},_=()=>{if(c)return c.getValue()||""};return r.$$set=C=>{"scroll"in C&&t(2,o=C.scroll),"token"in C&&t(3,l=C.token),"language"in C&&t(4,a=C.language),"padding"in C&&t(5,u=C.padding),"editorInstance"in C&&t(0,c=C.editorInstance),"element"in C&&t(1,d=C.element),"height"in C&&t(6,f=C.height),"$$scope"in C&&t(12,i=C.$$scope)},r.$$.update=()=>{var C;32&r.$$.dirty&&(C=u,c==null||c.updateOptions({padding:C})),65&r.$$.dirty&&(c==null||c.updateOptions({scrollbar:{vertical:f!==void 0?"auto":"hidden"}}))},[c,d,o,l,a,u,f,m,A,()=>c&&(v()||_())||"",v,_,i,s,function(C){pi.call(this,r,C)},()=>A.requestLayout(),function(C){c=C,t(0,c)},function(C){qe[C?"unshift":"push"](()=>{d=C,t(1,d)})}]}class as extends K{constructor(e){super(),ee(this,e,Ra,qa,Y,{scroll:2,token:3,language:4,padding:5,editorInstance:0,element:1,height:6,getSelectionOrContents:9,getSelections:10,getContents:11})}get getSelectionOrContents(){return this.$$.ctx[9]}get getSelections(){return this.$$.ctx[10]}get getContents(){return this.$$.ctx[11]}}const Ta=r=>({codespanContents:2&r}),us=r=>({codespanContents:r[1]});function Na(r){let e,t,n;const s=r[4].default,i=be(s,r,r[3],us),o=i||function(l){let a;return{c(){a=R(l[1])},m(u,c){g(u,a,c)},p(u,c){2&c&&le(a,u[1])},d(u){u&&h(a)}}}(r);return{c(){e=b("span"),t=b("code"),o&&o.c(),D(t,"class","markdown-codespan svelte-1ufogiu")},m(l,a){g(l,e,a),T(e,t),o&&o.m(t,null),r[5](e),n=!0},p(l,[a]){i?i.p&&(!n||10&a)&&Ee(i,s,l,l[3],n?Be(s,l[3],a,Ta):_e(l[3]),us):o&&o.p&&(!n||2&a)&&o.p(l,n?a:-1)},i(l){n||(p(o,l),n=!0)},o(l){$(o,l),n=!1},d(l){l&&h(e),o&&o.d(l),r[5](null)}}}function Sa(r,e,t){let n,{$$slots:s={},$$scope:i}=e,{token:o}=e,{element:l}=e;return r.$$set=a=>{"token"in a&&t(2,o=a.token),"element"in a&&t(0,l=a.element),"$$scope"in a&&t(3,i=a.$$scope)},r.$$.update=()=>{4&r.$$.dirty&&t(1,n=o.raw.slice(1,o.raw.length-1))},[l,n,o,i,s,function(a){qe[a?"unshift":"push"](()=>{l=a,t(0,l)})}]}class cs extends K{constructor(e){super(),ee(this,e,Sa,Na,Y,{token:2,element:0})}}function Pa(r){let e,t,n,s,i=r[0].text+"";return{c(){e=b("span"),t=R("~"),n=R(i),s=R("~")},m(o,l){g(o,e,l),T(e,t),T(e,n),T(e,s)},p(o,[l]){1&l&&i!==(i=o[0].text+"")&&le(n,i)},i:J,o:J,d(o){o&&h(e)}}}function Ia(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n]}class ds extends K{constructor(e){super(),ee(this,e,Ia,Pa,Y,{token:0})}}function Oa(r){let e,t;const n=r[1].default,s=be(n,r,r[0],null);return{c(){e=b("p"),s&&s.c(),D(e,"class","augment-markdown-paragraph svelte-1edcdk9")},m(i,o){g(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||1&o)&&Ee(s,n,i,i[0],t?Be(n,i[0],o,null):_e(i[0]),null)},i(i){t||(p(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function ja(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(0,s=i.$$scope)},[s,n]}class ps extends K{constructor(e){super(),ee(this,e,ja,Oa,Y,{})}}function Va(r){let e,t,n;return t=new Ba({props:{source:r[0],renderers:{codespan:cs,code:as,paragraph:ps,del:ds,...r[1]}}}),{c(){e=b("div"),L(t.$$.fragment),D(e,"class","c-markdown svelte-n6ddeo")},m(s,i){g(s,e,i),M(t,e,null),n=!0},p(s,[i]){const o={};1&i&&(o.source=s[0]),2&i&&(o.renderers={codespan:cs,code:as,paragraph:ps,del:ds,...s[1]}),t.$set(o)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function Za(r,e,t){let{markdown:n}=e,{renderers:s={}}=e;return r.$$set=i=>{"markdown"in i&&t(0,n=i.markdown),"renderers"in i&&t(1,s=i.renderers)},[n,s]}class Ua extends K{constructor(e){super(),ee(this,e,Za,Va,Y,{markdown:0,renderers:1})}}function Ha(r){let e;return{c(){e=R(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&le(e,t[1])},d(t){t&&h(e)}}}function Wa(r){let e;return{c(){e=R(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&le(e,t[1])},d(t){t&&h(e)}}}function Qa(r){let e,t,n;function s(l,a){return l[2]?Wa:Ha}let i=s(r),o=i(r);return{c(){e=b("span"),t=b("code"),o.c(),D(t,"class","markdown-codespan svelte-164mxpf"),D(t,"style",n=r[2]?`background-color: ${r[1]}; color: ${r[3]?"white":"black"}`:""),ve(t,"markdown-string",r[4])},m(l,a){g(l,e,a),T(e,t),o.m(t,null),r[6](e)},p(l,[a]){i===(i=s(l))&&o?o.p(l,a):(o.d(1),o=i(l),o&&(o.c(),o.m(t,null))),14&a&&n!==(n=l[2]?`background-color: ${l[1]}; color: ${l[3]?"white":"black"}`:"")&&D(t,"style",n),16&a&&ve(t,"markdown-string",l[4])},i:J,o:J,d(l){l&&h(e),o.d(),r[6](null)}}}function Ga(r,e,t){let n,s,i,o,{token:l}=e,{element:a}=e;return r.$$set=u=>{"token"in u&&t(5,l=u.token),"element"in u&&t(0,a=u.element)},r.$$.update=()=>{32&r.$$.dirty&&t(1,n=l.raw.slice(1,l.raw.length-1)),2&r.$$.dirty&&t(4,s=n.startsWith('"')),2&r.$$.dirty&&t(2,i=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&r.$$.dirty&&t(3,o=i&&function(u){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(u))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let c,d,f;return u.length===4?(c=parseInt(u.charAt(1),16),d=parseInt(u.charAt(2),16),f=parseInt(u.charAt(3),16),c*=17,d*=17,f*=17):(c=parseInt(u.slice(1,3),16),d=parseInt(u.slice(3,5),16),f=parseInt(u.slice(5,7),16)),.299*c+.587*d+.114*f<130}(n))},[a,n,i,o,s,l,function(u){qe[u?"unshift":"push"](()=>{a=u,t(0,a)})}]}class Ja extends K{constructor(e){super(),ee(this,e,Ga,Qa,Y,{token:5,element:0})}}function Ya(r){let e,t;return e=new Ua({props:{markdown:r[1](r[0]),renderers:r[2]}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.markdown=n[1](n[0])),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Xa(r,e,t){let{markdown:n}=e;const s={codespan:Ja};return r.$$set=i=>{"markdown"in i&&t(0,n=i.markdown)},[n,i=>i.replace(/`?#[0-9a-fA-F]{3,6}`?/g,o=>o.startsWith("`")?o:`\`${o}\``),s]}class Ka extends K{constructor(e){super(),ee(this,e,Xa,Ya,Y,{markdown:0})}}const{Boolean:oi,Map:eu}=Ei;function fs(r,e,t){const n=r.slice();return n[45]=e[t],n[46]=e,n[47]=t,n}function gs(r,e,t){const n=r.slice();return n[48]=e[t],n[49]=e,n[50]=t,n}function hs(r,e,t){const n=r.slice();return n[51]=e[t],n[52]=e,n[53]=t,n}function $s(r){let e,t,n,s,i,o,l,a;t=new js({}),o=new Oe({props:{variant:"ghost",size:1,$$slots:{default:[tu]},$$scope:{ctx:r}}}),o.$on("click",r[28]);let u=r[3]&&ms(r);return{c(){e=b("div"),L(t.$$.fragment),n=Z(),s=R(r[17]),i=Z(),L(o.$$.fragment),l=Z(),u&&u.c(),D(e,"class","c-diff-view__error svelte-ibi4q5")},m(c,d){g(c,e,d),M(t,e,null),T(e,n),T(e,s),T(e,i),M(o,e,null),T(e,l),u&&u.m(e,null),a=!0},p(c,d){(!a||131072&d[0])&&le(s,c[17]);const f={};8388608&d[1]&&(f.$$scope={dirty:d,ctx:c}),o.$set(f),c[3]?u?(u.p(c,d),8&d[0]&&p(u,1)):(u=ms(c),u.c(),p(u,1),u.m(e,null)):u&&(H(),$(u,1,1,()=>{u=null}),W())},i(c){a||(p(t.$$.fragment,c),p(o.$$.fragment,c),p(u),a=!0)},o(c){$(t.$$.fragment,c),$(o.$$.fragment,c),$(u),a=!1},d(c){c&&h(e),q(t),q(o),u&&u.d()}}}function tu(r){let e;return{c(){e=R("Retry")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function ms(r){let e,t;return e=new Oe({props:{variant:"ghost",size:1,$$slots:{default:[nu]},$$scope:{ctx:r}}}),e.$on("click",function(){fi(r[3])&&r[3].apply(this,arguments)}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){r=n;const i={};8388608&s[1]&&(i.$$scope={dirty:s,ctx:r}),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function nu(r){let e;return{c(){e=R("Render as list")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function su(r){let e,t,n,s,i,o,l,a,u,c,d,f,m,A=r[1]&&r[2]!==r[1]&&Ds(r),v=r[2]&&Fs(r);o=new fe({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[uu]},$$scope:{ctx:r}}}),a=new Js({props:{changedFiles:r[0]}});const _=[du,cu],C=[];function N(F,x){return F[15]&&F[14].length===0?0:F[5]&&F[5].length>0?1:-1}return~(d=N(r))&&(f=C[d]=_[d](r)),{c(){e=b("div"),t=b("div"),n=b("div"),A&&A.c(),s=Z(),v&&v.c(),i=Z(),L(o.$$.fragment),l=Z(),L(a.$$.fragment),u=Z(),c=b("div"),f&&f.c(),D(n,"class","c-diff-view__tree__header svelte-ibi4q5"),D(t,"class","c-diff-view__tree svelte-ibi4q5"),D(c,"class","c-diff-view__explanation svelte-ibi4q5"),D(e,"class","c-diff-view__layout svelte-ibi4q5")},m(F,x){g(F,e,x),T(e,t),T(t,n),A&&A.m(n,null),T(n,s),v&&v.m(n,null),T(n,i),M(o,n,null),T(n,l),M(a,n,null),T(e,u),T(e,c),~d&&C[d].m(c,null),m=!0},p(F,x){F[1]&&F[2]!==F[1]?A?(A.p(F,x),6&x[0]&&p(A,1)):(A=Ds(F),A.c(),p(A,1),A.m(n,s)):A&&(H(),$(A,1,1,()=>{A=null}),W()),F[2]?v?(v.p(F,x),4&x[0]&&p(v,1)):(v=Fs(F),v.c(),p(v,1),v.m(n,i)):v&&(H(),$(v,1,1,()=>{v=null}),W());const y={};8388608&x[1]&&(y.$$scope={dirty:x,ctx:F}),o.$set(y);const k={};1&x[0]&&(k.changedFiles=F[0]),a.$set(k);let E=d;d=N(F),d===E?~d&&C[d].p(F,x):(f&&(H(),$(C[E],1,1,()=>{C[E]=null}),W()),~d?(f=C[d],f?f.p(F,x):(f=C[d]=_[d](F),f.c()),p(f,1),f.m(c,null)):f=null)},i(F){m||(p(A),p(v),p(o.$$.fragment,F),p(a.$$.fragment,F),p(f),m=!0)},o(F){$(A),$(v),$(o.$$.fragment,F),$(a.$$.fragment,F),$(f),m=!1},d(F){F&&h(e),A&&A.d(),v&&v.d(),q(o),q(a),~d&&C[d].d()}}}function iu(r){let e,t,n;return t=new fe({props:{size:2,color:"secondary",$$slots:{default:[Ru]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),D(e,"class","c-diff-view__empty svelte-ibi4q5")},m(s,i){g(s,e,i),M(t,e,null),n=!0},p(s,i){const o={};8388608&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function Ds(r){let e,t,n,s;return e=new fe({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[ru]},$$scope:{ctx:r}}}),n=new fe({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[ou]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=Z(),L(n.$$.fragment)},m(i,o){M(e,i,o),g(i,t,o),M(n,i,o),s=!0},p(i,o){const l={};8388608&o[1]&&(l.$$scope={dirty:o,ctx:i}),e.$set(l);const a={};2&o[0]|8388608&o[1]&&(a.$$scope={dirty:o,ctx:i}),n.$set(a)},i(i){s||(p(e.$$.fragment,i),p(n.$$.fragment,i),s=!0)},o(i){$(e.$$.fragment,i),$(n.$$.fragment,i),s=!1},d(i){i&&h(t),q(e,i),q(n,i)}}}function ru(r){let e;return{c(){e=R("Changes from agent")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function ou(r){let e;return{c(){e=R(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n[0]&&le(e,t[1])},d(t){t&&h(e)}}}function Fs(r){let e,t,n,s;return e=new fe({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[lu]},$$scope:{ctx:r}}}),n=new fe({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[au]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=Z(),L(n.$$.fragment)},m(i,o){M(e,i,o),g(i,t,o),M(n,i,o),s=!0},p(i,o){const l={};8388608&o[1]&&(l.$$scope={dirty:o,ctx:i}),e.$set(l);const a={};4&o[0]|8388608&o[1]&&(a.$$scope={dirty:o,ctx:i}),n.$set(a)},i(i){s||(p(e.$$.fragment,i),p(n.$$.fragment,i),s=!0)},o(i){$(e.$$.fragment,i),$(n.$$.fragment,i),s=!1},d(i){i&&h(t),q(e,i),q(n,i)}}}function lu(r){let e;return{c(){e=R("Last user prompt")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function au(r){let e;return{c(){e=R(r[2])},m(t,n){g(t,e,n)},p(t,n){4&n[0]&&le(e,t[2])},d(t){t&&h(e)}}}function uu(r){let e;return{c(){e=R("Changed files")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function cu(r){let e,t,n=pe(r[5]),s=[];for(let o=0;o<n.length;o+=1)s[o]=ws(fs(r,n,o));const i=o=>$(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();e=ke()},m(o,l){for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(o,l);g(o,e,l),t=!0},p(o,l){if(257766384&l[0]){let a;for(n=pe(o[5]),a=0;a<n.length;a+=1){const u=fs(o,n,a);s[a]?(s[a].p(u,l),p(s[a],1)):(s[a]=ws(u),s[a].c(),p(s[a],1),s[a].m(e.parentNode,e))}for(H(),a=n.length;a<s.length;a+=1)i(a);W()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)p(s[l]);t=!0}},o(o){s=s.filter(oi);for(let l=0;l<s.length;l+=1)$(s[l]);t=!1},d(o){o&&h(e),Le(s,o)}}}function du(r){let e,t,n,s,i;return t=new Ge({props:{content:r[8]?"Applying changes...":r[9]?"All changes applied":r[10]?"Apply all changes":"No changes to apply",$$slots:{default:[qu]},$$scope:{ctx:r}}}),s=new Ho({props:{count:2}}),{c(){e=b("div"),L(t.$$.fragment),n=Z(),L(s.$$.fragment),D(e,"class","c-diff-view__controls svelte-ibi4q5")},m(o,l){g(o,e,l),M(t,e,null),g(o,n,l),M(s,o,l),i=!0},p(o,l){const a={};1792&l[0]&&(a.content=o[8]?"Applying changes...":o[9]?"All changes applied":o[10]?"Apply all changes":"No changes to apply"),3840&l[0]|8388608&l[1]&&(a.$$scope={dirty:l,ctx:o}),t.$set(a)},i(o){i||(p(t.$$.fragment,o),p(s.$$.fragment,o),i=!0)},o(o){$(t.$$.fragment,o),$(s.$$.fragment,o),i=!1},d(o){o&&(h(e),h(n)),q(t),q(s,o)}}}function pu(r){let e,t=r[45].title+"";return{c(){e=R(t)},m(n,s){g(n,e,s)},p(n,s){32&s[0]&&t!==(t=n[45].title+"")&&le(e,t)},d(n){n&&h(e)}}}function fu(r){let e;return{c(){e=b("div"),D(e,"class","c-diff-view__skeleton-title svelte-ibi4q5")},m(t,n){g(t,e,n)},p:J,d(t){t&&h(e)}}}function gu(r){let e,t;return e=new Ka({props:{markdown:r[45].description}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};32&s[0]&&(i.markdown=n[45].description),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function hu(r){let e,t,n;return{c(){e=b("div"),t=Z(),n=b("div"),D(e,"class","c-diff-view__skeleton-text svelte-ibi4q5"),D(n,"class","c-diff-view__skeleton-text svelte-ibi4q5")},m(s,i){g(s,e,i),g(s,t,i),g(s,n,i)},p:J,i:J,o:J,d(s){s&&(h(e),h(t),h(n))}}}function $u(r){let e,t,n;return e=new _i({}),{c(){L(e.$$.fragment),t=R(`
                        Expand All`)},m(s,i){M(e,s,i),g(s,t,i),n=!0},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){$(e.$$.fragment,s),n=!1},d(s){s&&h(t),q(e,s)}}}function mu(r){let e,t,n;return e=new ki({}),{c(){L(e.$$.fragment),t=R(`
                        Collapse All`)},m(s,i){M(e,s,i),g(s,t,i),n=!0},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){$(e.$$.fragment,s),n=!1},d(s){s&&h(t),q(e,s)}}}function Du(r){let e,t,n,s;const i=[mu,$u],o=[];function l(a,u){return a[22]?1:0}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=ke()},m(a,u){o[e].m(a,u),g(a,n,u),s=!0},p(a,u){let c=e;e=l(a),e!==c&&(H(),$(o[c],1,1,()=>{o[c]=null}),W(),t=o[e],t||(t=o[e]=i[e](a),t.c()),p(t,1),t.m(n.parentNode,n))},i(a){s||(p(t),s=!0)},o(a){$(t),s=!1},d(a){a&&h(n),o[e].d(a)}}}function Fu(r){let e,t,n,s;return n=new ot({}),{c(){e=R(`Apply all
                          `),t=b("div"),L(n.$$.fragment),D(t,"class","c-diff-view__controls__icon svelte-ibi4q5")},m(i,o){g(i,e,o),g(i,t,o),M(n,t,null),s=!0},i(i){s||(p(n.$$.fragment,i),s=!0)},o(i){$(n.$$.fragment,i),s=!1},d(i){i&&(h(e),h(t)),q(n)}}}function xu(r){let e,t,n;return t=new fe({props:{size:2,$$slots:{default:[ku]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),D(e,"class","c-diff-view__applied svelte-ibi4q5")},m(s,i){g(s,e,i),M(t,e,null),n=!0},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function Cu(r){let e,t,n,s,i;return t=new qt({props:{size:1,useCurrentColor:!0}}),s=new fe({props:{size:2,$$slots:{default:[wu]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),n=Z(),L(s.$$.fragment),D(e,"class","c-diff-view__applying svelte-ibi4q5")},m(o,l){g(o,e,l),M(t,e,null),T(e,n),M(s,e,null),i=!0},i(o){i||(p(t.$$.fragment,o),p(s.$$.fragment,o),i=!0)},o(o){$(t.$$.fragment,o),$(s.$$.fragment,o),i=!1},d(o){o&&h(e),q(t),q(s)}}}function ku(r){let e,t,n;return t=new wt({props:{iconName:"check"}}),{c(){e=R(`Applied
                              `),L(t.$$.fragment)},m(s,i){g(s,e,i),M(t,s,i),n=!0},p:J,i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t,s)}}}function wu(r){let e;return{c(){e=R("Applying...")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function vu(r){let e,t,n,s;const i=[Cu,xu,Fu],o=[];function l(a,u){return a[8]?0:a[9]?1:2}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=ke()},m(a,u){o[e].m(a,u),g(a,n,u),s=!0},p(a,u){let c=e;e=l(a),e!==c&&(H(),$(o[c],1,1,()=>{o[c]=null}),W(),t=o[e],t||(t=o[e]=i[e](a),t.c()),p(t,1),t.m(n.parentNode,n))},i(a){s||(p(t),s=!0)},o(a){$(t),s=!1},d(a){a&&h(n),o[e].d(a)}}}function yu(r){let e,t;return e=new Oe({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[12],$$slots:{default:[vu]},$$scope:{ctx:r}}}),e.$on("click",r[27]),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};4096&s[0]&&(i.disabled=n[12]),768&s[0]|8388608&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Au(r){let e,t=r[48].title+"";return{c(){e=R(t)},m(n,s){g(n,e,s)},p(n,s){32&s[0]&&t!==(t=n[48].title+"")&&le(e,t)},d(n){n&&h(e)}}}function bu(r){let e;return{c(){e=b("div"),D(e,"class","c-diff-view__skeleton-text svelte-ibi4q5")},m(t,n){g(t,e,n)},p:J,d(t){t&&h(e)}}}function xs(r){let e,t,n,s,i,o=r[48].warning+"";return t=new js({}),{c(){e=b("div"),L(t.$$.fragment),n=Z(),s=R(o),D(e,"class","c-diff-view__warning svelte-ibi4q5")},m(l,a){g(l,e,a),M(t,e,null),T(e,n),T(e,s),i=!0},p(l,a){(!i||32&a[0])&&o!==(o=l[48].warning+"")&&le(s,o)},i(l){i||(p(t.$$.fragment,l),i=!0)},o(l){$(t.$$.fragment,l),i=!1},d(l){l&&h(e),q(t)}}}function Cs(r,e){let t,n,s,i,o,l=e[47],a=e[50],u=e[51];function c(...C){return e[36](e[51],...C)}function d(){return e[37](e[51])}function f(C){e[38](C,e[51])}const m=()=>e[39](n,l,a,u),A=()=>e[39](null,l,a,u);function v(C){e[40](C)}let _={path:e[51].path,change:e[51],descriptions:e[48].descriptions,isExpandedDefault:e[7][e[51].path]!==void 0?!e[7][e[51].path]:e[6],isApplying:e[13][e[51].path]==="pending",hasApplied:e[13][e[51].path]==="applied",onCodeChange:c,onApplyChanges:d,isAgentFromDifferentRepo:e[4]};return e[7][e[51].path]!==void 0&&(_.isCollapsed=e[7][e[51].path]),e[19]!==void 0&&(_.areDescriptionsVisible=e[19]),n=new Po({props:_}),qe.push(()=>He(n,"isCollapsed",f)),m(),qe.push(()=>He(n,"areDescriptionsVisible",v)),{key:r,first:null,c(){t=b("div"),L(n.$$.fragment),D(t,"class","c-diff-view__changes-item svelte-ibi4q5"),this.first=t},m(C,N){g(C,t,N),M(n,t,null),o=!0},p(C,N){l===(e=C)[47]&&a===e[50]&&u===e[51]||(A(),l=e[47],a=e[50],u=e[51],m());const F={};32&N[0]&&(F.path=e[51].path),32&N[0]&&(F.change=e[51]),32&N[0]&&(F.descriptions=e[48].descriptions),224&N[0]&&(F.isExpandedDefault=e[7][e[51].path]!==void 0?!e[7][e[51].path]:e[6]),8224&N[0]&&(F.isApplying=e[13][e[51].path]==="pending"),8224&N[0]&&(F.hasApplied=e[13][e[51].path]==="applied"),32&N[0]&&(F.onCodeChange=c),32&N[0]&&(F.onApplyChanges=d),16&N[0]&&(F.isAgentFromDifferentRepo=e[4]),!s&&160&N[0]&&(s=!0,F.isCollapsed=e[7][e[51].path],We(()=>s=!1)),!i&&524288&N[0]&&(i=!0,F.areDescriptionsVisible=e[19],We(()=>i=!1)),n.$set(F)},i(C){o||(p(n.$$.fragment,C),o=!0)},o(C){$(n.$$.fragment,C),o=!1},d(C){C&&h(t),A(),q(n)}}}function ks(r){let e,t,n,s,i,o,l,a,u,c,d,f=[],m=new eu;function A(x,y){return x[16]&&x[48].descriptions.length===0?bu:Au}i=new Jr({props:{type:r[48].type}});let v=A(r),_=v(r),C=!r[16]&&r[48].warning&&xs(r),N=pe(r[48].changes);const F=x=>x[51].id;for(let x=0;x<N.length;x+=1){let y=hs(r,N,x),k=F(y);m.set(k,f[x]=Cs(k,y))}return{c(){e=b("div"),t=b("div"),n=b("div"),s=b("div"),L(i.$$.fragment),o=Z(),l=b("h5"),_.c(),a=Z(),C&&C.c(),u=Z(),c=b("div");for(let x=0;x<f.length;x+=1)f[x].c();D(s,"class","c-diff-view__icon svelte-ibi4q5"),D(l,"class","c-diff-view__title svelte-ibi4q5"),D(n,"class","c-diff-view__content svelte-ibi4q5"),D(t,"class","c-diff-view__header svelte-ibi4q5"),D(c,"class","c-diff-view__changes svelte-ibi4q5"),D(e,"class","c-diff-view__subsection svelte-ibi4q5"),D(e,"id",`subsection-${r[47]}-${r[50]}`)},m(x,y){g(x,e,y),T(e,t),T(t,n),T(n,s),M(i,s,null),T(n,o),T(n,l),_.m(l,null),T(n,a),C&&C.m(n,null),T(e,u),T(e,c);for(let k=0;k<f.length;k+=1)f[k]&&f[k].m(c,null);d=!0},p(x,y){const k={};32&y[0]&&(k.type=x[48].type),i.$set(k),v===(v=A(x))&&_?_.p(x,y):(_.d(1),_=v(x),_&&(_.c(),_.m(l,null))),!x[16]&&x[48].warning?C?(C.p(x,y),65568&y[0]&&p(C,1)):(C=xs(x),C.c(),p(C,1),C.m(n,null)):C&&(H(),$(C,1,1,()=>{C=null}),W()),101458160&y[0]&&(N=pe(x[48].changes),H(),f=qs(f,y,F,1,x,N,m,c,Rs,Cs,null,hs),W())},i(x){if(!d){p(i.$$.fragment,x),p(C);for(let y=0;y<N.length;y+=1)p(f[y]);d=!0}},o(x){$(i.$$.fragment,x),$(C);for(let y=0;y<f.length;y+=1)$(f[y]);d=!1},d(x){x&&h(e),q(i),_.d(),C&&C.d();for(let y=0;y<f.length;y+=1)f[y].d()}}}function ws(r){let e,t,n,s,i,o,l,a,u,c,d,f;function m(E,S){return E[16]&&E[45].title==="Loading..."?fu:pu}let A=m(r),v=A(r);const _=[hu,gu],C=[];function N(E,S){return E[16]&&E[45].description===""?0:1}l=N(r),a=C[l]=_[l](r);let F=r[47]===0&&function(E){let S,j,O,G,ce;return j=new Oe({props:{variant:"ghost-block",color:"neutral",size:2,$$slots:{default:[Du]},$$scope:{ctx:E}}}),j.$on("click",E[24]),G=new Ge({props:{content:E[20],$$slots:{default:[yu]},$$scope:{ctx:E}}}),{c(){S=b("div"),L(j.$$.fragment),O=Z(),L(G.$$.fragment),D(S,"class","c-diff-view__controls svelte-ibi4q5")},m(ne,he){g(ne,S,he),M(j,S,null),T(S,O),M(G,S,null),ce=!0},p(ne,he){const $e={};4194304&he[0]|8388608&he[1]&&($e.$$scope={dirty:he,ctx:ne}),j.$set($e);const Q={};1048576&he[0]&&(Q.content=ne[20]),4864&he[0]|8388608&he[1]&&(Q.$$scope={dirty:he,ctx:ne}),G.$set(Q)},i(ne){ce||(p(j.$$.fragment,ne),p(G.$$.fragment,ne),ce=!0)},o(ne){$(j.$$.fragment,ne),$(G.$$.fragment,ne),ce=!1},d(ne){ne&&h(S),q(j),q(G)}}}(r),x=pe(r[45].sections||[]),y=[];for(let E=0;E<x.length;E+=1)y[E]=ks(gs(r,x,E));const k=E=>$(y[E],1,1,()=>{y[E]=null});return{c(){e=b("div"),t=b("div"),n=b("div"),s=b("h5"),v.c(),i=Z(),o=b("div"),a.c(),u=Z(),F&&F.c(),c=Z();for(let E=0;E<y.length;E+=1)y[E].c();d=Z(),D(s,"class","c-diff-view__title svelte-ibi4q5"),D(o,"class","c-diff-view__description svelte-ibi4q5"),D(n,"class","c-diff-view__content svelte-ibi4q5"),D(t,"class","c-diff-view__header svelte-ibi4q5"),D(e,"class","c-diff-view__section svelte-ibi4q5"),D(e,"id",`section-${r[47]}`)},m(E,S){g(E,e,S),T(e,t),T(t,n),T(n,s),v.m(s,null),T(n,i),T(n,o),C[l].m(o,null),T(t,u),F&&F.m(t,null),T(e,c);for(let j=0;j<y.length;j+=1)y[j]&&y[j].m(e,null);T(e,d),f=!0},p(E,S){A===(A=m(E))&&v?v.p(E,S):(v.d(1),v=A(E),v&&(v.c(),v.m(s,null)));let j=l;if(l=N(E),l===j?C[l].p(E,S):(H(),$(C[j],1,1,()=>{C[j]=null}),W(),a=C[l],a?a.p(E,S):(a=C[l]=_[l](E),a.c()),p(a,1),a.m(o,null)),E[47]===0&&F.p(E,S),101523696&S[0]){let O;for(x=pe(E[45].sections||[]),O=0;O<x.length;O+=1){const G=gs(E,x,O);y[O]?(y[O].p(G,S),p(y[O],1)):(y[O]=ks(G),y[O].c(),p(y[O],1),y[O].m(e,d))}for(H(),O=x.length;O<y.length;O+=1)k(O);W()}},i(E){if(!f){p(a),p(F);for(let S=0;S<x.length;S+=1)p(y[S]);f=!0}},o(E){$(a),$(F),y=y.filter(oi);for(let S=0;S<y.length;S+=1)$(y[S]);f=!1},d(E){E&&h(e),v.d(),C[l].d(),F&&F.d(),Le(y,E)}}}function Eu(r){let e,t,n,s;return n=new ot({}),{c(){e=R(`Apply all
                  `),t=b("div"),L(n.$$.fragment),D(t,"class","c-diff-view__controls__icon svelte-ibi4q5")},m(i,o){g(i,e,o),g(i,t,o),M(n,t,null),s=!0},i(i){s||(p(n.$$.fragment,i),s=!0)},o(i){$(n.$$.fragment,i),s=!1},d(i){i&&(h(e),h(t)),q(n)}}}function _u(r){let e,t,n;return t=new fe({props:{size:2,$$slots:{default:[zu]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),D(e,"class","c-diff-view__applied svelte-ibi4q5")},m(s,i){g(s,e,i),M(t,e,null),n=!0},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t)}}}function Bu(r){let e,t,n,s,i;return t=new qt({props:{size:1,useCurrentColor:!0}}),s=new fe({props:{size:2,$$slots:{default:[Lu]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),n=Z(),L(s.$$.fragment),D(e,"class","c-diff-view__applying svelte-ibi4q5")},m(o,l){g(o,e,l),M(t,e,null),T(e,n),M(s,e,null),i=!0},i(o){i||(p(t.$$.fragment,o),p(s.$$.fragment,o),i=!0)},o(o){$(t.$$.fragment,o),$(s.$$.fragment,o),i=!1},d(o){o&&h(e),q(t),q(s)}}}function zu(r){let e,t,n;return t=new wt({props:{iconName:"check"}}),{c(){e=R(`Applied
                      `),L(t.$$.fragment)},m(s,i){g(s,e,i),M(t,s,i),n=!0},p:J,i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),q(t,s)}}}function Lu(r){let e;return{c(){e=R("Applying...")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Mu(r){let e,t,n,s;const i=[Bu,_u,Eu],o=[];function l(a,u){return a[8]?0:a[9]?1:2}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=ke()},m(a,u){o[e].m(a,u),g(a,n,u),s=!0},p(a,u){let c=e;e=l(a),e!==c&&(H(),$(o[c],1,1,()=>{o[c]=null}),W(),t=o[e],t||(t=o[e]=i[e](a),t.c()),p(t,1),t.m(n.parentNode,n))},i(a){s||(p(t),s=!0)},o(a){$(t),s=!1},d(a){a&&h(n),o[e].d(a)}}}function qu(r){let e,t;return e=new Oe({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[8]||r[9]||r[11].length>0||!r[10],$$slots:{default:[Mu]},$$scope:{ctx:r}}}),e.$on("click",r[27]),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};3840&s[0]&&(i.disabled=n[8]||n[9]||n[11].length>0||!n[10]),768&s[0]|8388608&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Ru(r){let e;return{c(){e=R("No files changed")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Tu(r){let e,t,n,s,i,o=r[17]&&$s(r);const l=[iu,su],a=[];function u(c,d){return c[21]?0:1}return n=u(r),s=a[n]=l[n](r),{c(){e=b("div"),o&&o.c(),t=Z(),s.c(),D(e,"class","c-diff-view svelte-ibi4q5")},m(c,d){g(c,e,d),o&&o.m(e,null),T(e,t),a[n].m(e,null),i=!0},p(c,d){c[17]?o?(o.p(c,d),131072&d[0]&&p(o,1)):(o=$s(c),o.c(),p(o,1),o.m(e,t)):o&&(H(),$(o,1,1,()=>{o=null}),W());let f=n;n=u(c),n===f?a[n].p(c,d):(H(),$(a[f],1,1,()=>{a[f]=null}),W(),s=a[n],s?s.p(c,d):(s=a[n]=l[n](c),s.c()),p(s,1),s.m(e,null))},i(c){i||(p(o),p(s),i=!0)},o(c){$(o),$(s),i=!1},d(c){c&&h(e),o&&o.d(),a[n].d()}}}function Nu(r,e,t){let n,s,i,o,l,a,u,c,{changedFiles:d}=e,{agentLabel:f}=e,{latestUserPrompt:m}=e,{onApplyChanges:A}=e,{onRenderBackup:v}=e,{preloadedExplanation:_}=e,{isAgentFromDifferentRepo:C=!1}=e,N="",F=!1,x=[],y=[],k=!1,E=!1,S=null,j=!0,O={},G=[],ce=!1,ne=!1,he=!0;const $e=Ve({});ze(r,$e,P=>t(13,c=P));let Q={};function ye(P,te){t(32,Q[P]=te,Q)}async function xe(P,te,re){if(A)return $e.update(ue=>(ue[P]="pending",ue)),new Promise(ue=>{A==null||A(P,te,re).then(()=>{$e.update(me=>(me[P]="applied",me)),ue()})})}function oe(P){const te={title:"Changed Files",description:`${P.length} files were changed`,sections:[]},re=[],ue=[],me=[];return P.forEach(w=>{w.old_path?w.new_path?ue.push(w):me.push(w):re.push(w)}),re.length>0&&te.sections.push(we("Added files","feature",re)),ue.length>0&&te.sections.push(we("Modified files","fix",ue)),me.length>0&&te.sections.push(we("Deleted files","chore",me)),[te]}function we(P,te,re){const ue=[];return re.forEach(me=>{const w=me.new_path||me.old_path,z=me.old_contents||"",B=me.new_contents||"",U=me.old_path?me.old_path:"",V=mt(U,me.new_path||"/dev/null",z,B,"","",{context:3}),X=`${Pe(w)}-${Pe(z+B)}`;ue.push({id:X,path:w,diff:V,originalCode:z,modifiedCode:B})}),{title:P,descriptions:[],type:te,changes:ue}}async function Me(){if(!F)return;if(t(15,k=!0),t(16,E=!1),t(17,S=null),t(14,y=[]),t(5,x=[]),l)return void t(15,k=!1);const P=102400;let te=0;if(d.forEach(re=>{var ue,me;te+=(((ue=re.old_contents)==null?void 0:ue.length)||0)+(((me=re.new_contents)==null?void 0:me.length)||0)}),d.length>12||te>512e3){try{t(5,x=oe(d))}catch(re){console.error("Failed to create simple explanation:",re),t(17,S="Failed to create explanation for large changes.")}t(15,k=!1)}else try{const re=new yi(w=>Ts.postMessage(w)),ue=new Map,me=d.map(w=>{const z=w.new_path||w.old_path,B=w.old_contents||"",U=w.new_contents||"",V=`${Pe(z)}-${Pe(B+U)}`;return ue.set(V,{old_path:w.old_path,new_path:w.new_path,old_contents:B,new_contents:U,change_type:w.change_type}),{id:V,old_path:w.old_path,new_path:w.new_path,change_type:w.change_type}});try{const w=me.length===1;let z=[];w?z=me.map(B=>({path:B.new_path||B.old_path,changes:[{id:B.id,path:B.new_path||B.old_path,diff:`File: ${B.new_path||B.old_path}
Change type: ${B.change_type||"modified"}`,originalCode:"",modifiedCode:""}]})):z=(await re.send({type:"get-diff-group-changes-request",data:{changedFiles:me,changesById:!0,apikey:N}},3e4)).data.groupedChanges,t(14,y=z.map(B=>({path:B.path,changes:B.changes.map(U=>{if(U.id&&ue.has(U.id)){const V=ue.get(U.id);let X=U.diff;return X&&!X.startsWith("File:")||(X=mt(V.old_path||"",V.new_path||"",V.old_contents||"",V.new_contents||"")),{...U,diff:X,old_path:V.old_path,new_path:V.new_path,old_contents:V.old_contents,new_contents:V.new_contents,change_type:V.change_type,originalCode:V.old_contents||"",modifiedCode:V.new_contents||""}}return U})})))}catch(w){console.error("Failed to group changes with LLM, falling back to simple grouping:",w);try{const z=me.map(B=>{if(B.id&&ue.has(B.id)){const U=ue.get(B.id);return{...B,old_path:U.old_path,new_path:U.new_path,old_contents:U.old_contents||"",new_contents:U.new_contents||"",change_type:U.change_type}}return B});t(5,x=oe(z)),t(14,y=x[0].sections.map(B=>({path:B.title,changes:B.changes}))),t(16,E=!1)}catch(z){console.error("Failed to create simple explanation:",z),t(17,S="Failed to group changes. Please try again.")}}if(t(15,k=!1),!y||y.length===0)throw new Error("Failed to group changes");if(!x||x.length===0){t(5,x=function(z){const B={title:"Loading...",description:"",sections:[]};return z.forEach(U=>{const V=U.changes.map(de=>{if(de.id)return de;const ae=Pe(de.path),Ce=Pe(de.originalCode+de.modifiedCode);return{...de,id:`${ae}-${Ce}`}}),X={title:U.path,descriptions:[],type:"other",changes:V};B.sections.push(X)}),[B]}(y));const w=x[0].sections.map(z=>({path:z.title,changes:z.changes.map(B=>{var de,ae,Ce;const U=((de=B.originalCode)==null?void 0:de.length)||0,V=((ae=B.modifiedCode)==null?void 0:ae.length)||0,X=((Ce=B.diff)==null?void 0:Ce.length)||0;return U>P||V>P||X>P?{id:B.id,path:B.path,diff:`File: ${B.path}
Content too large to include in explanation request (${Math.max(U,V,X)} bytes)`,originalCode:U>P?`[File content too large: ${U} bytes]`:B.originalCode,modifiedCode:V>P?`[File content too large: ${V} bytes]`:B.modifiedCode}:{id:B.id,path:B.path,diff:B.diff,originalCode:B.originalCode,modifiedCode:B.modifiedCode}})}));t(16,E=!0);try{const z=(await re.send({type:"get-diff-descriptions-request",data:{groupedChanges:w,apikey:N}},1e5)).data.explanation;z&&z.length>0&&z.forEach((B,U)=>{B.sections&&B.sections.forEach((V,X)=>{V.changes&&V.changes.forEach(de=>{const ae=x[U];if(ae&&ae.sections){const Ce=ae.sections[X];if(Ce&&Ce.changes){const Se=Ce.changes.find(Xe=>Xe.id===de.id);Se&&(de.originalCode=Se.originalCode,de.modifiedCode=Se.modifiedCode,de.diff=Se.diff)}}})})}),t(5,x=z)}catch(z){console.error("Failed to get descriptions, using skeleton explanation:",z)}}x.length===0&&t(17,S="Failed to generate explanation.")}catch(re){console.error("Failed to get explanation:",re),t(17,S=re instanceof Error?re.message:"An error occurred while generating the explanation.")}finally{t(15,k=!1),t(16,E=!1)}}rt(()=>{const P=localStorage.getItem("anthropic_apikey");P&&(N=P),t(31,F=!0)});let se="",I="Apply all changes locally";return r.$$set=P=>{"changedFiles"in P&&t(0,d=P.changedFiles),"agentLabel"in P&&t(1,f=P.agentLabel),"latestUserPrompt"in P&&t(2,m=P.latestUserPrompt),"onApplyChanges"in P&&t(29,A=P.onApplyChanges),"onRenderBackup"in P&&t(3,v=P.onRenderBackup),"preloadedExplanation"in P&&t(30,_=P.preloadedExplanation),"isAgentFromDifferentRepo"in P&&t(4,C=P.isAgentFromDifferentRepo)},r.$$.update=()=>{if(8193&r.$$.dirty[0]&&d&&$e.set(d.reduce((P,te)=>{const re=te.new_path||te.old_path;return P[re]=c[re]??"none",P},{})),1&r.$$.dirty[0]&&t(35,a=JSON.stringify(d)),1073741824&r.$$.dirty[0]|21&r.$$.dirty[1]&&F&&a&&a!==se&&(t(33,se=a),_&&_.length>0?(t(5,x=_),t(15,k=!1),t(16,E=!1)):Me(),t(8,ce=!1),t(9,ne=!1),t(32,Q={})),224&r.$$.dirty[0]&&x&&x.length>0){const P=dt(x);Array.from(P).forEach(ue=>{O[ue]===void 0&&t(7,O[ue]=!j,O)});const te=Object.keys(O).filter(ue=>O[ue]),re=Array.from(P);re.length>0&&t(6,j=!re.some(ue=>te.includes(ue)))}if(128&r.$$.dirty[0]&&t(22,n=Object.values(O).some(Boolean)),32&r.$$.dirty[0]|2&r.$$.dirty[1]&&x&&x.length>0&&x.flatMap(P=>P.sections||[]).flatMap(P=>P.changes).forEach(P=>{Q[P.path]||t(32,Q[P.path]=P.modifiedCode,Q)}),32&r.$$.dirty[0]&&t(34,s=JSON.stringify(x)),8224&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(10,i=(()=>{if(s&&c){const P=dt(x);return P.size!==0&&Array.from(P).some(te=>c[te]!=="applied")}return!1})()),8192&r.$$.dirty[0]&&t(9,ne=Object.keys(c).every(P=>c[P]==="applied")),8192&r.$$.dirty[0]&&t(11,o=Object.keys(c).filter(P=>c[P]==="pending")),1&r.$$.dirty[0]&&t(21,l=d.length===0),8736&r.$$.dirty[0]|8&r.$$.dirty[1]&&s&&ne){const P=dt(x);Array.from(P).every(te=>c[te]==="applied")||t(9,ne=!1)}3856&r.$$.dirty[0]&&t(12,u=C||ce||ne||o.length>0||!i),7952&r.$$.dirty[0]&&(u?C?t(20,I="Cannot apply changes from a different repository locally"):ce?t(20,I="Applying changes..."):ne?t(20,I="All changes applied"):o.length>0?t(20,I="Waiting for changes to apply"):i||t(20,I="No changes to apply"):t(20,I="Apply all changes locally"))},[d,f,m,v,C,x,j,O,ce,ne,i,o,u,c,y,k,E,S,G,he,I,l,n,$e,function(){const P=dt(x),te=Object.values(O).some(Boolean);t(6,j=te),Array.from(P).forEach(re=>{t(7,O[re]=!j,O)})},ye,xe,function(){if(!A)return;t(8,ce=!0),t(9,ne=!1);const{filesToApply:P,areAllPathsApplied:te}=gi(x,d,Q);te||P.length===0?t(9,ne=te):hi(P,xe).then(()=>{t(8,ce=!1),t(9,ne=!0)})},Me,A,_,F,Q,se,s,a,(P,te)=>{ye(P.path,te)},P=>{xe(P.path,P.originalCode,P.modifiedCode)},function(P,te){r.$$.not_equal(O[te.path],P)&&(O[te.path]=P,t(7,O),t(5,x),t(6,j),t(31,F),t(35,a),t(33,se),t(30,_),t(0,d))},function(P,te,re,ue){qe[P?"unshift":"push"](()=>{G[100*te+10*re+ue.path.length%10]=P,t(18,G)})},function(P){he=P,t(19,he)}]}class Su extends K{constructor(e){super(),ee(this,e,Nu,Tu,Y,{changedFiles:0,agentLabel:1,latestUserPrompt:2,onApplyChanges:29,onRenderBackup:3,preloadedExplanation:30,isAgentFromDifferentRepo:4},null,[-1,-1])}}function vs(r){let e,t,n=r[7].opts,s=ys(r);return{c(){e=b("div"),s.c(),D(e,"class","file-explorer-contents svelte-5tfpo4")},m(i,o){g(i,e,o),s.m(e,null),t=!0},p(i,o){128&o&&Y(n,n=i[7].opts)?(H(),$(s,1,1,J),W(),s=ys(i),s.c(),p(s,1),s.m(e,null)):s.p(i,o)},i(i){t||(p(s),t=!0)},o(i){$(s),t=!1},d(i){i&&h(e),s.d(i)}}}function Pu(r){var n,s;let e,t;return e=new Su({props:{changedFiles:r[0],onApplyChanges:r[9],agentLabel:r[3],latestUserPrompt:r[4],onRenderBackup:r[10],preloadedExplanation:(s=(n=r[7])==null?void 0:n.opts)==null?void 0:s.preloadedExplanation,isAgentFromDifferentRepo:r[5]}}),{c(){L(e.$$.fragment)},m(i,o){M(e,i,o),t=!0},p(i,o){var a,u;const l={};1&o&&(l.changedFiles=i[0]),8&o&&(l.agentLabel=i[3]),16&o&&(l.latestUserPrompt=i[4]),64&o&&(l.onRenderBackup=i[10]),128&o&&(l.preloadedExplanation=(u=(a=i[7])==null?void 0:a.opts)==null?void 0:u.preloadedExplanation),32&o&&(l.isAgentFromDifferentRepo=i[5]),e.$set(l)},i(i){t||(p(e.$$.fragment,i),t=!0)},o(i){$(e.$$.fragment,i),t=!1},d(i){q(e,i)}}}function Iu(r){let e,t;return e=new Hr({props:{changedFiles:r[0],onApplyChanges:r[9],pendingFiles:r[1],appliedFiles:r[2]}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.changedFiles=n[0]),2&s&&(i.pendingFiles=n[1]),4&s&&(i.appliedFiles=n[2]),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function ys(r){let e,t,n,s;const i=[Iu,Pu],o=[];function l(a,u){return a[6]==="changedFiles"?0:1}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=ke()},m(a,u){o[e].m(a,u),g(a,n,u),s=!0},p(a,u){let c=e;e=l(a),e===c?o[e].p(a,u):(H(),$(o[c],1,1,()=>{o[c]=null}),W(),t=o[e],t?t.p(a,u):(t=o[e]=i[e](a),t.c()),p(t,1),t.m(n.parentNode,n))},i(a){s||(p(t),s=!0)},o(a){$(t),s=!1},d(a){a&&h(n),o[e].d(a)}}}function Ou(r){let e,t,n,s=r[0]&&vs(r);return{c(){e=b("div"),t=b("div"),s&&s.c(),D(t,"class","file-explorer-main svelte-5tfpo4"),D(e,"class","diff-page svelte-5tfpo4")},m(i,o){g(i,e,o),T(e,t),s&&s.m(t,null),n=!0},p(i,[o]){i[0]?s?(s.p(i,o),1&o&&p(s,1)):(s=vs(i),s.c(),p(s,1),s.m(t,null)):s&&(H(),$(s,1,1,()=>{s=null}),W())},i(i){n||(p(s),n=!0)},o(i){$(s),n=!1},d(i){i&&h(e),s&&s.d()}}}function ju(r,e,t){let n,{changedFiles:s=[]}=e,{pendingFiles:i=[]}=e,{appliedFiles:o=[]}=e,{agentLabel:l}=e,{latestUserPrompt:a}=e,{isAgentFromDifferentRepo:u=!1}=e;const c=ht(Ft.key),d=ht(Dt.key);ze(r,d,m=>t(7,n=m));let f="summary";return function(m){m.subscribe(A=>{if(A){const v=document.getElementById(Bt(A));v&&v.scrollIntoView({behavior:"smooth",block:"center"})}})}(function(m=null){const A=Ve(m);return Et(Ws,A),A}(null)),r.$$set=m=>{"changedFiles"in m&&t(0,s=m.changedFiles),"pendingFiles"in m&&t(1,i=m.pendingFiles),"appliedFiles"in m&&t(2,o=m.appliedFiles),"agentLabel"in m&&t(3,l=m.agentLabel),"latestUserPrompt"in m&&t(4,a=m.latestUserPrompt),"isAgentFromDifferentRepo"in m&&t(5,u=m.isAgentFromDifferentRepo)},[s,i,o,l,a,u,f,n,d,async(m,A,v)=>{await c.applyChanges(m,A,v)},()=>{t(6,f="changedFiles")}]}class Vu extends K{constructor(e){super(),ee(this,e,ju,Ou,Y,{changedFiles:0,pendingFiles:1,appliedFiles:2,agentLabel:3,latestUserPrompt:4,isAgentFromDifferentRepo:5})}}function Zu(r){let e,t,n,s,i;return t=new qt({props:{size:1}}),{c(){e=b("div"),L(t.$$.fragment),n=Z(),s=b("p"),s.textContent="Loading diff view...",D(e,"class","l-center svelte-ccste2")},m(o,l){g(o,e,l),M(t,e,null),T(e,n),T(e,s),i=!0},p:J,i(o){i||(p(t.$$.fragment,o),i=!0)},o(o){$(t.$$.fragment,o),i=!1},d(o){o&&h(e),q(t)}}}function Uu(r){let e,t;return e=new Vu({props:{changedFiles:r[0].changedFiles,agentLabel:r[0].sessionSummary,latestUserPrompt:r[0].userPrompt,pendingFiles:r[3].applyingFilePaths||[],appliedFiles:r[3].appliedFilePaths||[],isAgentFromDifferentRepo:r[0].isAgentFromDifferentRepo||!1}}),{c(){L(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.changedFiles=n[0].changedFiles),1&s&&(i.agentLabel=n[0].sessionSummary),1&s&&(i.latestUserPrompt=n[0].userPrompt),1&s&&(i.isAgentFromDifferentRepo=n[0].isAgentFromDifferentRepo||!1),e.$set(i)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){q(e,n)}}}function Hu(r){let e,t,n,s;const i=[Uu,Zu],o=[];function l(a,u){return a[0]?0:1}return t=l(r),n=o[t]=i[t](r),{c(){e=b("div"),n.c(),D(e,"class","l-main svelte-ccste2")},m(a,u){g(a,e,u),o[t].m(e,null),s=!0},p(a,u){let c=t;t=l(a),t===c?o[t].p(a,u):(H(),$(o[c],1,1,()=>{o[c]=null}),W(),n=o[t],n?n.p(a,u):(n=o[t]=i[t](a),n.c()),p(n,1),n.m(e,null))},i(a){s||(p(n),s=!0)},o(a){$(n),s=!1},d(a){a&&h(e),o[t].d()}}}function Wu(r){let e,t,n,s;return e=new vi.Root({props:{$$slots:{default:[Hu]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(i,o){M(e,i,o),t=!0,n||(s=nt(window,"message",r[1].onMessageFromExtension),n=!0)},p(i,[o]){const l={};33&o&&(l.$$scope={dirty:o,ctx:i}),e.$set(l)},i(i){t||(p(e.$$.fragment,i),t=!0)},o(i){$(e.$$.fragment,i),t=!1},d(i){q(e,i),n=!1,s()}}}function Qu(r,e,t){let n,s,i=new $i(Ts),o=new Dt(i);ze(r,o,a=>t(4,s=a)),i.registerConsumer(o);let l=new Ft(i);return Et(Ft.key,l),Et(Dt.key,o),rt(()=>(o.onPanelLoaded(),()=>{i.dispose()})),r.$$.update=()=>{16&r.$$.dirty&&t(0,n=s.opts)},[n,i,o,l,s]}new class extends K{constructor(r){super(),ee(this,r,Qu,Wu,Y,{})}}({target:document.getElementById("app")});
