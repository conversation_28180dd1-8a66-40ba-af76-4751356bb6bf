import{S as _,i as j,s as H,a as W,b as $t,K as ut,L as mt,M as dt,N as pt,h as b,d as J,O as ft,g as gt,n as M,j as Q,E as ot,e as R,u as d,q as G,t as f,r as A,ag as z,a1 as at,w as C,x as E,y as x,z as w,A as I,B as y,C as ht,ai as B,D as O,F as vt,G as q,H as rt,T as it,V as S,c as L,f as N,a5 as xt,a0 as wt}from"./SpinnerAugment-JC8TPhVf.js";import"./design-system-init-Bf1-mlh4.js";import{h as T,W as k,e as X}from"./BaseButton-D8yhCvaJ.js";import{S as yt,O as bt}from"./OpenFileButton-BcPjyYjB.js";import{C as Rt,E as Lt}from"./chat-flags-model-u-ZFJEJp.js";import{M as ct}from"./TextTooltipAugment-BlDY2tAQ.js";import{M as Mt}from"./MarkdownEditor-DS0MQUBe.js";import{A as U,M as V}from"./types-BSMhNRWH.js";import{D}from"./index-BFtESN_v.js";import{B as St}from"./ButtonAugment-CRFFE3_i.js";import{C as Ft}from"./chevron-down-B-gSyyd4.js";import{F as Ct}from"./Filespan-DeFTcAEj.js";import"./open-in-new-window-BX_nUqUb.js";import"./types-Cgd-nZOV.js";import"./chat-types-NgqNgjwU.js";import"./index-DiI90jLk.js";import"./lodash-C-61Uc4F.js";import"./test_service_pb-DM0n7l7E.js";import"./file-paths-BcSg4gks.js";import"./types-B5Ac2hek.js";import"./Content-xvE836E_.js";import"./globals-D0QH3NT1.js";import"./IconButtonAugment-BQL_8yIN.js";import"./TextAreaAugment-DxOmi7vy.js";import"./CardAugment-BAO5rOsN.js";function Et(a){let t,s,e=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},a[0]],o={};for(let n=0;n<e.length;n+=1)o=W(o,e[n]);return{c(){t=$t("svg"),s=new ut(!0),this.h()},l(n){t=mt(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=dt(t);s=pt(c,!0),c.forEach(b),this.h()},h(){s.a=null,J(t,o)},m(n,c){ft(n,t,c),s.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M440.6 273.4c4.7-4.5 7.4-10.8 7.4-17.4s-2.7-12.8-7.4-17.4l-176-168c-9.6-9.2-24.8-8.8-33.9.8s-8.8 24.8.8 33.9L364.1 232H24c-13.3 0-24 10.7-24 24s10.7 24 24 24h340.1L231.4 406.6c-9.6 9.2-9.9 24.3-.8 33.9s24.3 9.9 33.9.8l176-168z"/>',t)},p(n,[c]){J(t,o=gt(e,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&c&&n[0]]))},i:M,o:M,d(n){n&&b(t)}}}function It(a,t,s){return a.$$set=e=>{s(0,t=W(W({},t),Q(e)))},[t=Q(t)]}class lt extends _{constructor(t){super(),j(this,t,It,Et,H,{})}}function Y(a,t,s){const e=a.slice();return e[16]=t[s],e[18]=s,e}function Z(a){let t,s,e,o;function n(i){a[13](i)}function c(i){a[14](i)}let $={$$slots:{default:[qt]},$$scope:{ctx:a}};return a[2]!==void 0&&($.requestClose=a[2]),a[1]!==void 0&&($.focusedIndex=a[1]),t=new D.Root({props:$}),C.push(()=>E(t,"requestClose",n)),C.push(()=>E(t,"focusedIndex",c)),{c(){x(t.$$.fragment)},m(i,l){w(t,i,l),o=!0},p(i,l){const r={};524401&l&&(r.$$scope={dirty:l,ctx:i}),!s&&4&l&&(s=!0,r.requestClose=i[2],I(()=>s=!1)),!e&&2&l&&(e=!0,r.focusedIndex=i[1],I(()=>e=!1)),t.$set(r)},i(i){o||(d(t.$$.fragment,i),o=!0)},o(i){f(t.$$.fragment,i),o=!1},d(i){y(t,i)}}}function zt(a){let t,s=(a[5]?a[5].path:"Rules")+"";return{c(){t=q(s)},m(e,o){R(e,t,o)},p(e,o){32&o&&s!==(s=(e[5]?e[5].path:"Rules")+"")&&rt(t,s)},d(e){e&&b(t)}}}function Dt(a){let t,s;return t=new lt({props:{slot:"iconLeft"}}),{c(){x(t.$$.fragment)},m(e,o){w(t,e,o),s=!0},p:M,i(e){s||(d(t.$$.fragment,e),s=!0)},o(e){f(t.$$.fragment,e),s=!1},d(e){y(t,e)}}}function Nt(a){let t,s;return t=new Ft({props:{slot:"iconRight"}}),{c(){x(t.$$.fragment)},m(e,o){w(t,e,o),s=!0},p:M,i(e){s||(d(t.$$.fragment,e),s=!0)},o(e){f(t.$$.fragment,e),s=!1},d(e){y(t,e)}}}function Bt(a){let t,s;return t=new St({props:{color:"neutral",variant:"soft",size:1,disabled:a[0],$$slots:{iconRight:[Nt],iconLeft:[Dt],default:[zt]},$$scope:{ctx:a}}}),{c(){x(t.$$.fragment)},m(e,o){w(t,e,o),s=!0},p(e,o){const n={};1&o&&(n.disabled=e[0]),524320&o&&(n.$$scope={dirty:o,ctx:e}),t.$set(n)},i(e){s||(d(t.$$.fragment,e),s=!0)},o(e){f(t.$$.fragment,e),s=!1},d(e){y(t,e)}}}function Gt(a){let t,s;return t=new Ct({props:{filepath:a[16].path}}),{c(){x(t.$$.fragment)},m(e,o){w(t,e,o),s=!0},p(e,o){const n={};16&o&&(n.filepath=e[16].path),t.$set(n)},i(e){s||(d(t.$$.fragment,e),s=!0)},o(e){f(t.$$.fragment,e),s=!1},d(e){y(t,e)}}}function tt(a){let t,s;function e(){return a[12](a[16])}return t=new D.Item({props:{onSelect:e,highlight:a[6]===a[18],$$slots:{default:[Gt]},$$scope:{ctx:a}}}),{c(){x(t.$$.fragment)},m(o,n){w(t,o,n),s=!0},p(o,n){a=o;const c={};16&n&&(c.onSelect=e),64&n&&(c.highlight=a[6]===a[18]),524304&n&&(c.$$scope={dirty:n,ctx:a}),t.$set(c)},i(o){s||(d(t.$$.fragment,o),s=!0)},o(o){f(t.$$.fragment,o),s=!1},d(o){y(t,o)}}}function et(a){let t,s,e,o;return t=new D.Separator({}),e=new D.Label({props:{$$slots:{default:[Ot]},$$scope:{ctx:a}}}),{c(){x(t.$$.fragment),s=O(),x(e.$$.fragment)},m(n,c){w(t,n,c),R(n,s,c),w(e,n,c),o=!0},p(n,c){const $={};524368&c&&($.$$scope={dirty:c,ctx:n}),e.$set($)},i(n){o||(d(t.$$.fragment,n),d(e.$$.fragment,n),o=!0)},o(n){f(t.$$.fragment,n),f(e.$$.fragment,n),o=!1},d(n){n&&b(s),y(t,n),y(e,n)}}}function At(a){let t,s=st(a[4][a[6]])+"";return{c(){t=q(s)},m(e,o){R(e,t,o)},p(e,o){80&o&&s!==(s=st(e[4][e[6]])+"")&&rt(t,s)},d(e){e&&b(t)}}}function Ot(a){let t,s;return t=new it({props:{size:1,color:"neutral",$$slots:{default:[At]},$$scope:{ctx:a}}}),{c(){x(t.$$.fragment)},m(e,o){w(t,e,o),s=!0},p(e,o){const n={};524368&o&&(n.$$scope={dirty:o,ctx:e}),t.$set(n)},i(e){s||(d(t.$$.fragment,e),s=!0)},o(e){f(t.$$.fragment,e),s=!1},d(e){y(t,e)}}}function Tt(a){let t,s,e,o=X(a[4]),n=[];for(let i=0;i<o.length;i+=1)n[i]=tt(Y(a,o,i));const c=i=>f(n[i],1,1,()=>{n[i]=null});let $=a[6]!==void 0&&a[4][a[6]]&&et(a);return{c(){for(let i=0;i<n.length;i+=1)n[i].c();t=O(),$&&$.c(),s=ot()},m(i,l){for(let r=0;r<n.length;r+=1)n[r]&&n[r].m(i,l);R(i,t,l),$&&$.m(i,l),R(i,s,l),e=!0},p(i,l){if(1104&l){let r;for(o=X(i[4]),r=0;r<o.length;r+=1){const m=Y(i,o,r);n[r]?(n[r].p(m,l),d(n[r],1)):(n[r]=tt(m),n[r].c(),d(n[r],1),n[r].m(t.parentNode,t))}for(G(),r=o.length;r<n.length;r+=1)c(r);A()}i[6]!==void 0&&i[4][i[6]]?$?($.p(i,l),80&l&&d($,1)):($=et(i),$.c(),d($,1),$.m(s.parentNode,s)):$&&(G(),f($,1,1,()=>{$=null}),A())},i(i){if(!e){for(let l=0;l<o.length;l+=1)d(n[l]);d($),e=!0}},o(i){n=n.filter(Boolean);for(let l=0;l<n.length;l+=1)f(n[l]);f($),e=!1},d(i){i&&(b(t),b(s)),vt(n,i),$&&$.d(i)}}}function qt(a){let t,s,e,o;return t=new D.Trigger({props:{$$slots:{default:[Bt]},$$scope:{ctx:a}}}),e=new D.Content({props:{side:"bottom",align:"start",$$slots:{default:[Tt]},$$scope:{ctx:a}}}),{c(){x(t.$$.fragment),s=O(),x(e.$$.fragment)},m(n,c){w(t,n,c),R(n,s,c),w(e,n,c),o=!0},p(n,c){const $={};524321&c&&($.$$scope={dirty:c,ctx:n}),t.$set($);const i={};524368&c&&(i.$$scope={dirty:c,ctx:n}),e.$set(i)},i(n){o||(d(t.$$.fragment,n),d(e.$$.fragment,n),o=!0)},o(n){f(t.$$.fragment,n),f(e.$$.fragment,n),o=!1},d(n){n&&b(s),y(t,n),y(e,n)}}}function kt(a){let t,s,e=!a[3]&&a[4].length>0&&Z(a);return{c(){e&&e.c(),t=ot()},m(o,n){e&&e.m(o,n),R(o,t,n),s=!0},p(o,[n]){!o[3]&&o[4].length>0?e?(e.p(o,n),24&n&&d(e,1)):(e=Z(o),e.c(),d(e,1),e.m(t.parentNode,t)):e&&(G(),f(e,1,1,()=>{e=null}),A())},i(o){s||(d(e),s=!0)},o(o){f(e),s=!1},d(o){o&&b(t),e&&e.d(o)}}}function st(a){return`Move to ${a.path}`}function _t(a,t,s){let e,o,n,c,$=M,i=()=>($(),$=ht(v,g=>s(6,c=g)),v);a.$$.on_destroy.push(()=>$());let{onRuleSelected:l}=t,{disabled:r=!1}=t;const m=B([]);z(a,m,g=>s(4,o=g));const u=B(!0);z(a,u,g=>s(3,e=g));const h=B(void 0);let v;z(a,h,g=>s(5,n=g)),i();let p=()=>{};function F(g){h.set(g),l(g),p()}return at(()=>{u.set(!0),T.postMessage({type:k.getRulesListRequest,data:{query:"",maxResults:100}});const g=K=>{var P;((P=K.data)==null?void 0:P.type)===k.getRulesListResponse&&(m.set(K.data.data||[]),u.set(!1))};return window.addEventListener("message",g),()=>{window.removeEventListener("message",g)}}),a.$$set=g=>{"onRuleSelected"in g&&s(11,l=g.onRuleSelected),"disabled"in g&&s(0,r=g.disabled)},[r,v,p,e,o,n,c,m,u,h,F,l,g=>F(g),function(g){p=g,s(2,p)},function(g){v=g,i(s(1,v))}]}class jt extends _{constructor(t){super(),j(this,t,_t,kt,H,{onRuleSelected:11,disabled:0})}}function Ht(a){let t;return{c(){t=q("User Guidelines")},m(s,e){R(s,t,e)},d(s){s&&b(t)}}}function Ut(a){let t,s,e;return s=new lt({}),{c(){t=S("div"),x(s.$$.fragment),L(t,"slot","iconLeft"),L(t,"class","c-move-text-btn__left_icon svelte-1yddhs6")},m(o,n){R(o,t,n),w(s,t,null),e=!0},p:M,i(o){e||(d(s.$$.fragment,o),e=!0)},o(o){f(s.$$.fragment,o),e=!1},d(o){o&&b(t),y(s)}}}function nt(a){let t,s,e;return s=new jt({props:{onRuleSelected:a[10],disabled:!a[2]}}),{c(){t=S("div"),x(s.$$.fragment),L(t,"class","c-move-text-btn svelte-1yddhs6")},m(o,n){R(o,t,n),w(s,t,null),e=!0},p(o,n){const c={};4&n&&(c.disabled=!o[2]),s.$set(c)},i(o){e||(d(s.$$.fragment,o),e=!0)},o(o){f(s.$$.fragment,o),e=!1},d(o){o&&b(t),y(s)}}}function Vt(a){let t;return{c(){t=q("Open Memories")},m(s,e){R(s,t,e)},d(s){s&&b(t)}}}function Wt(a){let t,s;return t=new it({props:{slot:"text",size:1,$$slots:{default:[Vt]},$$scope:{ctx:a}}}),{c(){x(t.$$.fragment)},m(e,o){w(t,e,o),s=!0},p(e,o){const n={};1048576&o&&(n.$$scope={dirty:o,ctx:e}),t.$set(n)},i(e){s||(d(t.$$.fragment,e),s=!0)},o(e){f(t.$$.fragment,e),s=!1},d(e){y(t,e)}}}function Kt(a){let t,s,e,o,n,c,$,i,l;o=new yt({props:{tooltip:{neutral:"Move highlighted text to user guidelines",success:"Text moved to user guidelines"},stateVariant:{success:"solid",neutral:"soft"},defaultColor:"neutral",onClick:a[12],disabled:!a[2],stickyColor:!1,persistOnTooltipClose:!0,replaceIconOnSuccess:!0,size:1,$$slots:{iconLeft:[Ut],default:[Ht]},$$scope:{ctx:a}}});let r=a[5]&&nt(a);return i=new bt({props:{size:1,path:a[1],variant:"soft",onOpenLocalFile:a[13],$$slots:{text:[Wt]},$$scope:{ctx:a}}}),{c(){t=S("div"),s=S("div"),e=S("div"),x(o.$$.fragment),n=O(),r&&r.c(),c=O(),$=S("div"),x(i.$$.fragment),L(e,"class","c-move-text-btn svelte-1yddhs6"),L(s,"class","l-file-controls-left svelte-1yddhs6"),L($,"class","l-file-controls-right svelte-1yddhs6"),L(t,"class","l-file-controls svelte-1yddhs6"),L(t,"slot","header")},m(m,u){R(m,t,u),N(t,s),N(s,e),w(o,e,null),N(s,n),r&&r.m(s,null),N(t,c),N(t,$),w(i,$,null),l=!0},p(m,u){const h={};4&u&&(h.disabled=!m[2]),1048576&u&&(h.$$scope={dirty:u,ctx:m}),o.$set(h),m[5]?r?(r.p(m,u),32&u&&d(r,1)):(r=nt(m),r.c(),d(r,1),r.m(s,null)):r&&(G(),f(r,1,1,()=>{r=null}),A());const v={};2&u&&(v.path=m[1]),2&u&&(v.onOpenLocalFile=m[13]),1048576&u&&(v.$$scope={dirty:u,ctx:m}),i.$set(v)},i(m){l||(d(o.$$.fragment,m),d(r),d(i.$$.fragment,m),l=!0)},o(m){f(o.$$.fragment,m),f(r),f(i.$$.fragment,m),l=!1},d(m){m&&b(t),y(o),r&&r.d(),y(i)}}}function Pt(a){let t,s,e,o,n,c;function $(u){a[14](u)}function i(u){a[15](u)}function l(u){a[16](u)}function r(u){a[17](u)}let m={saveFunction:a[8],variant:"surface",size:2,resize:"vertical",class:"markdown-editor",$$slots:{header:[Kt]},$$scope:{ctx:a}};return a[2]!==void 0&&(m.selectedText=a[2]),a[3]!==void 0&&(m.selectionStart=a[3]),a[4]!==void 0&&(m.selectionEnd=a[4]),a[0]!==void 0&&(m.value=a[0]),t=new Mt({props:m}),C.push(()=>E(t,"selectedText",$)),C.push(()=>E(t,"selectionStart",i)),C.push(()=>E(t,"selectionEnd",l)),C.push(()=>E(t,"value",r)),{c(){x(t.$$.fragment)},m(u,h){w(t,u,h),c=!0},p(u,[h]){const v={};1048614&h&&(v.$$scope={dirty:h,ctx:u}),!s&&4&h&&(s=!0,v.selectedText=u[2],I(()=>s=!1)),!e&&8&h&&(e=!0,v.selectionStart=u[3],I(()=>e=!1)),!o&&16&h&&(o=!0,v.selectionEnd=u[4],I(()=>o=!1)),!n&&1&h&&(n=!0,v.value=u[0],I(()=>n=!1)),t.$set(v)},i(u){c||(d(t.$$.fragment,u),c=!0)},o(u){f(t.$$.fragment,u),c=!1},d(u){y(t,u)}}}function Jt(a,t,s){let e,o,{text:n}=t,{path:c}=t;const $=new ct(T),i=new Rt;z(a,i,p=>s(11,o=p));const l=new Lt(T,$,i);(async function(){try{const p=await l.getChatInitData();i.update({enableRules:p.enableRules??!1,enableDebugFeatures:p.enableDebugFeatures??!1})}catch(p){console.error("Failed to initialize flags:",p)}})();let r="",m=0,u=0;const h=async()=>{c&&l.saveFile({repoRoot:"",pathName:c,content:n})};async function v(p){if(r){const F=n.substring(0,m)+n.substring(u);return s(0,n=F),p==="userGuidelines"?(l.updateUserGuidelines(r),l.reportAgentSessionEvent({eventName:U.memoriesMove,conversationId:"",eventData:{memoriesMoveData:{target:V.userGuidelines}}})):(l.updateWorkspaceGuidelines(r),l.reportAgentSessionEvent({eventName:U.memoriesMove,conversationId:"",eventData:{memoriesMoveData:{target:V.augmentGuidelines}}})),await h(),"success"}}return a.$$set=p=>{"text"in p&&s(0,n=p.text),"path"in p&&s(1,c=p.path)},a.$$.update=()=>{2048&a.$$.dirty&&s(5,e=o.enableRules)},[n,c,r,m,u,e,i,l,h,v,async function(p){if(r){l.updateRuleFile(p.path,r);const F=n.substring(0,m)+n.substring(u);s(0,n=F),await h(),l.reportAgentSessionEvent({eventName:U.memoriesMove,conversationId:"",eventData:{memoriesMoveData:{target:V.rules}}})}},o,()=>v("userGuidelines"),async()=>(l.openFile({repoRoot:"",pathName:c}),"success"),function(p){r=p,s(2,r)},function(p){m=p,s(3,m)},function(p){u=p,s(4,u)},function(p){n=p,s(0,n)}]}class Qt extends _{constructor(t){super(),j(this,t,Jt,Pt,H,{text:0,path:1})}}function Xt(a){let t;return{c(){t=q("Loading memories...")},m(s,e){R(s,t,e)},p:M,i:M,o:M,d(s){s&&b(t)}}}function Yt(a){let t,s;return t=new Qt({props:{text:a[0],path:a[1]}}),{c(){x(t.$$.fragment)},m(e,o){w(t,e,o),s=!0},p(e,o){const n={};1&o&&(n.text=e[0]),2&o&&(n.path=e[1]),t.$set(n)},i(e){s||(d(t.$$.fragment,e),s=!0)},o(e){f(t.$$.fragment,e),s=!1},d(e){y(t,e)}}}function Zt(a){let t,s,e,o,n,c;const $=[Yt,Xt],i=[];function l(r,m){return r[0]!==null&&r[1]!==null?0:1}return s=l(a),e=i[s]=$[s](a),{c(){t=S("div"),e.c(),L(t,"class","c-memories-container svelte-1vchs21")},m(r,m){R(r,t,m),i[s].m(t,null),o=!0,n||(c=xt(window,"message",a[2].onMessageFromExtension),n=!0)},p(r,[m]){let u=s;s=l(r),s===u?i[s].p(r,m):(G(),f(i[u],1,1,()=>{i[u]=null}),A(),e=i[s],e?e.p(r,m):(e=i[s]=$[s](r),e.c()),d(e,1),e.m(t,null))},i(r){o||(d(e),o=!0)},o(r){f(e),o=!1},d(r){r&&b(t),i[s].d(),n=!1,c()}}}function te(a,t,s){let e,o;const n=new ct(T),c=B(null);z(a,c,l=>s(0,e=l));const $=B(null);z(a,$,l=>s(1,o=l));const i={handleMessageFromExtension(l){const r=l.data;if(r&&r.type===k.loadFile){if(r.data.content!==void 0){const m=r.data.content.replace(/^\n+/,"");c.set(m)}r.data.pathName&&$.set(r.data.pathName)}return!0}};return at(()=>{n.registerConsumer(i),T.postMessage({type:k.memoriesLoaded})}),wt(()=>{n.dispose()}),[e,o,n,c,$]}new class extends _{constructor(a){super(),j(this,a,te,Zt,H,{})}}({target:document.getElementById("app")});
