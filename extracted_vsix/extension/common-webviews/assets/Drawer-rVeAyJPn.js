import{S as O,i as P,s as Q,R as E,V as M,D as X,c as z,ab as T,a3 as g,e as F,f as W,a5 as D,a6 as ee,X as S,Y,Z as j,u as x,q as te,t as B,r as ae,ac as de,h as G,a7 as se,a1 as oe,y as H,z as J,ad as ne,ae as I,B as K,w as U}from"./SpinnerAugment-JC8TPhVf.js";import{I as le}from"./IconButtonAugment-BQL_8yIN.js";import{f as V}from"./index-CW7fyhvB.js";import{r as re}from"./resize-observer-DdAtcrRr.js";import{E as ie}from"./ellipsis-ce3_p-Q7.js";const ce=a=>({}),Z=a=>({}),ue=a=>({}),A=a=>({});function C(a){let t,e,s,u;return e=new le({props:{variant:"solid",color:"accent",size:2,radius:"full",title:"Show panel",$$slots:{default:[me]},$$scope:{ctx:a}}}),e.$on("click",a[12]),{c(){t=M("div"),H(e.$$.fragment),z(t,"class","c-drawer__hidden-indicator svelte-18f0m3o")},m(l,$){F(l,t,$),J(e,t,null),u=!0},p(l,$){const p={};16777216&$&&(p.$$scope={dirty:$,ctx:l}),e.$set(p)},i(l){u||(x(e.$$.fragment,l),l&&ne(()=>{u&&(s||(s=I(t,V,{y:0,x:0,duration:200},!0)),s.run(1))}),u=!0)},o(l){B(e.$$.fragment,l),l&&(s||(s=I(t,V,{y:0,x:0,duration:200},!1)),s.run(0)),u=!1},d(l){l&&G(t),K(e),l&&s&&s.end()}}}function me(a){let t,e;return t=new ie({}),{c(){H(t.$$.fragment)},m(s,u){J(t,s,u),e=!0},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){B(t.$$.fragment,s),e=!1},d(s){K(t,s)}}}function he(a){let t,e,s,u,l,$,p,k,v,f,i,m,L;const _=a[20].left,h=E(_,a,a[24],A),y=a[20].right,r=E(y,a,a[24],Z);let n=a[0]&&a[3]&&C(a);return{c(){t=M("div"),e=M("div"),s=M("div"),h&&h.c(),u=X(),l=M("div"),$=X(),p=M("div"),r&&r.c(),k=X(),n&&n.c(),z(s,"class","c-drawer__left-content svelte-18f0m3o"),s.inert=a[7],T(s,"width","var(--augment-drawer-width)"),T(s,"min-width","var(--augment-drawer-width)"),T(s,"max-width","var(--augment-drawer-width)"),z(e,"class","c-drawer__left svelte-18f0m3o"),T(e,"--augment-drawer-width",a[8]+"px"),z(l,"aria-hidden","true"),z(l,"class","c-drawer__handle svelte-18f0m3o"),g(l,"is-locked",a[4]),z(p,"class","c-drawer__right svelte-18f0m3o"),z(t,"class",v="c-drawer "+a[2]+" svelte-18f0m3o"),g(t,"is-dragging",a[7]),g(t,"is-hidden",!a[8]),g(t,"is-column",a[4])},m(d,c){F(d,t,c),W(t,e),W(e,s),h&&h.m(s,null),a[21](e),W(t,u),W(t,l),W(t,$),W(t,p),r&&r.m(p,null),W(t,k),n&&n.m(t,null),a[22](t),i=!0,m||(L=[D(window,"mousemove",a[10]),D(window,"mouseup",a[11]),D(l,"mousedown",a[9]),D(l,"dblclick",a[12]),ee(f=re.call(null,t,{onResize:a[23]}))],m=!0)},p(d,[c]){h&&h.p&&(!i||16777216&c)&&S(h,_,d,d[24],i?j(_,d[24],c,ue):Y(d[24]),A),(!i||128&c)&&(s.inert=d[7]),(!i||256&c)&&T(e,"--augment-drawer-width",d[8]+"px"),(!i||16&c)&&g(l,"is-locked",d[4]),r&&r.p&&(!i||16777216&c)&&S(r,y,d,d[24],i?j(y,d[24],c,ce):Y(d[24]),Z),d[0]&&d[3]?n?(n.p(d,c),9&c&&x(n,1)):(n=C(d),n.c(),x(n,1),n.m(t,null)):n&&(te(),B(n,1,1,()=>{n=null}),ae()),(!i||4&c&&v!==(v="c-drawer "+d[2]+" svelte-18f0m3o"))&&z(t,"class",v),f&&de(f.update)&&2&c&&f.update.call(null,{onResize:d[23]}),(!i||132&c)&&g(t,"is-dragging",d[7]),(!i||260&c)&&g(t,"is-hidden",!d[8]),(!i||20&c)&&g(t,"is-column",d[4])},i(d){i||(x(h,d),x(r,d),x(n),i=!0)},o(d){B(h,d),B(r,d),B(n),i=!1},d(d){d&&G(t),h&&h.d(d),a[21](null),r&&r.d(d),n&&n.d(),a[22](null),m=!1,se(L)}}}function fe(a,t,e){let s,u,l,$,{$$slots:p={},$$scope:k}=t,{initialWidth:v=300}=t,{expandedMinWidth:f=50}=t,{minimizedWidth:i=0}=t,{minimized:m=!1}=t,{class:L=""}=t,{showButton:_=!0}=t,{deadzone:h=0}=t,{columnLayoutThreshold:y=600}=t,{layoutMode:r}=t,n=!1,d=v,c=v,w=!1;function R(){if(u){if(r!==void 0)return e(4,w=r==="column"),void(w&&e(7,n=!1));e(4,w=u.clientWidth<y),w&&e(7,n=!1)}}return oe(R),a.$$set=o=>{"initialWidth"in o&&e(14,v=o.initialWidth),"expandedMinWidth"in o&&e(15,f=o.expandedMinWidth),"minimizedWidth"in o&&e(16,i=o.minimizedWidth),"minimized"in o&&e(0,m=o.minimized),"class"in o&&e(2,L=o.class),"showButton"in o&&e(3,_=o.showButton),"deadzone"in o&&e(17,h=o.deadzone),"columnLayoutThreshold"in o&&e(18,y=o.columnLayoutThreshold),"layoutMode"in o&&e(1,r=o.layoutMode),"$$scope"in o&&e(24,k=o.$$scope)},a.$$.update=()=>{3&a.$$.dirty&&(m?(e(1,r="row"),e(4,w=!1)):r!=="row"||m||(e(1,r=void 0),R())),18&a.$$.dirty&&r!==void 0&&(e(4,w=r==="column"),w&&e(7,n=!1)),589825&a.$$.dirty&&e(8,c=m?i:d)},[m,r,L,_,w,s,u,n,c,function(o){w||(e(7,n=!0),l=o.clientX,$=s.offsetWidth,o.preventDefault())},function(o){if(!n||!s||w)return;const N=o.clientX-l,q=u.clientWidth-200,b=$+N;b<f?b<f-h?e(0,m=!0):(e(19,d=f),e(0,m=!1)):b>q?(e(19,d=q),e(0,m=!1)):(e(19,d=b),e(0,m=!1))},function(){e(7,n=!1),e(19,d=Math.max(d,f))},function(){e(0,m=!m)},R,v,f,i,h,y,d,p,function(o){U[o?"unshift":"push"](()=>{s=o,e(5,s)})},function(o){U[o?"unshift":"push"](()=>{u=o,e(6,u)})},()=>r===void 0&&R(),k]}class ze extends O{constructor(t){super(),P(this,t,fe,he,Q,{initialWidth:14,expandedMinWidth:15,minimizedWidth:16,minimized:0,class:2,showButton:3,deadzone:17,columnLayoutThreshold:18,layoutMode:1})}}export{ze as D};
