<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment Smart Diff View</title>
    <script nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <script type="module" crossorigin src="./assets/diff-view-BK5fXVx_.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-JC8TPhVf.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-Bf1-mlh4.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-D8yhCvaJ.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-BQL_8yIN.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/chat-types-NgqNgjwU.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/test_service_pb-DM0n7l7E.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-BcSg4gks.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/chat-flags-model-u-ZFJEJp.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-bSDyFrZo.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-BSMhNRWH.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/Content-xvE836E_.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/TextTooltipAugment-BlDY2tAQ.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-3TLxExQu.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-DfKf7sb_.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-BAO5rOsN.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-BFtESN_v.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-E1jEbeRV.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/Keybindings-lGeuBRaW.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-CRFFE3_i.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-DeFTcAEj.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-D8Nb6HkU.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-DiI90jLk.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-CK0xjdO4.js" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-CRmW_T8r.css" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-DvMdfQ3F.css" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-BTu-iglL.css" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="stylesheet" crossorigin href="./assets/Content-D0WttAzY.css" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="stylesheet" crossorigin href="./assets/TextTooltipAugment-BIMZ5dVo.css" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="stylesheet" crossorigin href="./assets/pen-to-square-Dvw-pMXw.css" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BAo8Ti0V.css" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-eY12-hdZ.css" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="stylesheet" crossorigin href="./assets/Keybindings-BFFBoxX3.css" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-CNK8zC8i.css" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-tclW2Ian.css" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
    <link rel="stylesheet" crossorigin href="./assets/diff-view-DwmCBV60.css" nonce="nonce-p0JYEZb5E4Wl892dUXl6GQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
