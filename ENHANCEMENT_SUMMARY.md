# VS Code Runner Enhancement: Application Name Randomization

## Overview

The `simple-vscode-runner.sh` script has been enhanced with application name randomization functionality. This feature allows users to launch VS Code instances with randomized application names, providing additional privacy and making each instance easily identifiable.

## New Features

### 🎲 Application Name Randomization

- **New Flag**: `--randomize-name` or `-r`
- **Functionality**: Adds a random 4-digit suffix to the VS Code application name
- **Example**: "Visual Studio Code 7432" instead of "Visual Studio Code"

### 🔧 Technical Implementation

1. **Random Suffix Generation**: Generates a 4-digit number (1000-9999)
2. **App Bundle Duplication**: Creates a temporary copy of the VS Code app bundle
3. **Info.plist Modification**: Updates the application's display name
4. **Automatic Cleanup**: Removes temporary app bundles on exit and during cleanup

### 📝 Enhanced Logging

- Shows randomization status (ENABLED/DISABLED)
- Displays the randomized application name when active
- Provides clear feedback about the random suffix being used

## Usage Examples

### Basic Usage
```bash
# Generate new fingerprint with randomized app name
./scripts/simple-vscode-runner.sh --new-fingerprint --randomize-name

# Use existing fingerprint with randomized app name
./scripts/simple-vscode-runner.sh --fingerprint-id fp_20241201_123456 --randomize-name
```

### Advanced Usage
```bash
# Combine with privacy level and workspace
./scripts/simple-vscode-runner.sh --new-fingerprint --randomize-name --privacy-level maximum /path/to/project

# Short form flags
./scripts/simple-vscode-runner.sh -n -r
```

## Benefits

1. **Enhanced Privacy**: Each VS Code instance appears as a different application
2. **Easy Identification**: Random suffixes make it easy to distinguish between multiple instances
3. **System Integration**: Works seamlessly with macOS application management
4. **Automatic Cleanup**: No manual intervention required for temporary files

## Technical Details

### Files Modified
- `scripts/simple-vscode-runner.sh` - Main script with randomization functionality

### Files Added
- `scripts/test-randomized-name.sh` - Test script demonstrating the new features
- `ENHANCEMENT_SUMMARY.md` - This documentation file

### Key Functions Added
- `generate_random_suffix()` - Generates 4-digit random numbers
- `create_randomized_app()` - Creates temporary app bundle with modified name
- `cleanup_randomized_app()` - Cleans up temporary app bundles

### Compatibility
- **Platform**: macOS (requires `/Applications/Visual Studio Code.app/`)
- **Dependencies**: Standard Unix tools (cp, sed, rm)
- **Backward Compatibility**: All existing functionality preserved

## Testing

Run the test script to verify functionality:
```bash
./scripts/test-randomized-name.sh
```

The test script demonstrates:
- Help documentation updates
- Random suffix generation
- Example usage commands
- Feature capabilities

## Security Considerations

- Temporary app bundles are created in `/tmp/` directory
- Automatic cleanup prevents accumulation of temporary files
- Original VS Code installation remains unchanged
- No persistent modifications to the system

## Future Enhancements

Potential improvements could include:
- Custom suffix patterns (letters + numbers)
- Persistent randomized app names
- Integration with other privacy tools
- Cross-platform support (Windows, Linux)

---

**Note**: This enhancement maintains full backward compatibility. Existing scripts and workflows will continue to work without modification.
