/* Import fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
@layer base {
  * {
    @apply border-monokai-bg-tertiary;
  }
  
  html {
    @apply scroll-smooth;
  }
  
  body {
    @apply bg-monokai-bg-primary text-monokai-text-primary font-sans antialiased;
    @apply scrollbar-monokai;
  }
  
  /* Headings */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold text-monokai-text-primary;
  }
  
  h1 {
    @apply text-3xl;
  }
  
  h2 {
    @apply text-2xl;
  }
  
  h3 {
    @apply text-xl;
  }
  
  h4 {
    @apply text-lg;
  }
  
  /* Links */
  a {
    @apply text-monokai-blue hover:text-monokai-green transition-colors duration-200;
  }
  
  /* Code blocks */
  code {
    @apply font-mono text-sm bg-monokai-bg-secondary px-1 py-0.5 rounded;
  }
  
  pre {
    @apply font-mono text-sm bg-monokai-bg-secondary p-4 rounded-lg overflow-x-auto;
    @apply scrollbar-monokai;
  }
  
  pre code {
    @apply bg-transparent p-0;
  }
  
  /* Form elements */
  input, textarea, select {
    @apply bg-monokai-bg-secondary border border-monokai-bg-tertiary rounded-lg px-3 py-2;
    @apply text-monokai-text-primary placeholder-monokai-text-muted;
    @apply focus:outline-none focus:ring-2 focus:ring-monokai-green focus:border-transparent;
    @apply transition-all duration-200;
  }
  
  input:disabled, textarea:disabled, select:disabled {
    @apply opacity-50 cursor-not-allowed;
  }
  
  /* Buttons */
  button {
    @apply transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-monokai-bg-primary;
  }
  
  /* Selection */
  ::selection {
    @apply bg-monokai-purple bg-opacity-30;
  }
}

/* Component styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium;
    @apply transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-monokai-bg-primary;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply bg-monokai-green text-monokai-bg-primary hover:bg-opacity-90 focus:ring-monokai-green;
  }
  
  .btn-secondary {
    @apply bg-monokai-bg-secondary text-monokai-text-primary border border-monokai-bg-tertiary;
    @apply hover:bg-monokai-bg-tertiary focus:ring-monokai-bg-tertiary;
  }
  
  .btn-danger {
    @apply bg-monokai-red text-white hover:bg-opacity-90 focus:ring-monokai-red;
  }
  
  .btn-ghost {
    @apply text-monokai-text-primary hover:bg-monokai-bg-secondary focus:ring-monokai-bg-tertiary;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-lg;
  }
  
  /* Card styles */
  .card {
    @apply bg-monokai-bg-secondary rounded-xl border border-monokai-bg-tertiary;
    @apply shadow-monokai backdrop-blur-sm;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-monokai-bg-tertiary;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-monokai-bg-tertiary;
  }
  
  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply bg-monokai-green bg-opacity-20 text-monokai-green;
  }
  
  .badge-warning {
    @apply bg-monokai-yellow bg-opacity-20 text-monokai-yellow;
  }
  
  .badge-danger {
    @apply bg-monokai-red bg-opacity-20 text-monokai-red;
  }
  
  .badge-info {
    @apply bg-monokai-blue bg-opacity-20 text-monokai-blue;
  }
  
  .badge-purple {
    @apply bg-monokai-purple bg-opacity-20 text-monokai-purple;
  }
  
  /* Progress bar */
  .progress {
    @apply w-full bg-monokai-bg-tertiary rounded-full h-2;
  }
  
  .progress-bar {
    @apply h-full rounded-full transition-all duration-300;
  }
  
  .progress-bar-success {
    @apply bg-monokai-green;
  }
  
  .progress-bar-warning {
    @apply bg-monokai-yellow;
  }
  
  .progress-bar-danger {
    @apply bg-monokai-red;
  }
  
  /* Tooltip */
  .tooltip {
    @apply absolute z-50 px-2 py-1 text-xs bg-monokai-bg-primary text-monokai-text-primary;
    @apply border border-monokai-bg-tertiary rounded shadow-lg;
    @apply opacity-0 pointer-events-none transition-opacity duration-200;
  }
  
  .tooltip.show {
    @apply opacity-100 pointer-events-auto;
  }
  
  /* Syntax highlighting for JSON */
  .json-key {
    @apply text-monokai-blue;
  }
  
  .json-string {
    @apply text-monokai-yellow;
  }
  
  .json-number {
    @apply text-monokai-purple;
  }
  
  .json-boolean {
    @apply text-monokai-red;
  }
  
  .json-null {
    @apply text-monokai-comment;
  }
  
  /* Loading states */
  .loading {
    @apply animate-pulse;
  }
  
  .spinner {
    @apply animate-spin rounded-full border-2 border-monokai-bg-tertiary border-t-monokai-green;
  }
  
  /* Sidebar */
  .sidebar {
    @apply bg-monokai-bg-secondary border-r border-monokai-bg-tertiary;
    @apply transition-transform duration-300 ease-in-out;
  }
  
  .sidebar-collapsed {
    @apply -translate-x-full;
  }
  
  /* Navigation */
  .nav-item {
    @apply flex items-center px-4 py-2 text-monokai-text-secondary;
    @apply hover:text-monokai-text-primary hover:bg-monokai-bg-tertiary;
    @apply transition-all duration-200 rounded-lg;
  }
  
  .nav-item.active {
    @apply text-monokai-green bg-monokai-green bg-opacity-10;
  }
  
  /* Tabs */
  .tab {
    @apply px-4 py-2 text-monokai-text-secondary border-b-2 border-transparent;
    @apply hover:text-monokai-text-primary transition-all duration-200;
  }
  
  .tab.active {
    @apply text-monokai-green border-monokai-green;
  }
}

/* Utility styles */
@layer utilities {
  /* Text gradients */
  .text-gradient-success {
    @apply bg-gradient-to-r from-monokai-green to-monokai-blue bg-clip-text text-transparent;
  }
  
  .text-gradient-warning {
    @apply bg-gradient-to-r from-monokai-yellow to-monokai-orange bg-clip-text text-transparent;
  }
  
  .text-gradient-danger {
    @apply bg-gradient-to-r from-monokai-red to-monokai-pink bg-clip-text text-transparent;
  }
  
  /* Background gradients */
  .bg-gradient-primary {
    @apply bg-gradient-to-br from-monokai-bg-primary to-monokai-bg-secondary;
  }
  
  .bg-gradient-success {
    @apply bg-gradient-to-r from-monokai-green to-monokai-blue;
  }
  
  .bg-gradient-warning {
    @apply bg-gradient-to-r from-monokai-yellow to-monokai-orange;
  }
  
  .bg-gradient-danger {
    @apply bg-gradient-to-r from-monokai-red to-monokai-pink;
  }
  
  /* Animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }
  
  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slideIn {
    from { transform: translateY(-10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }
  
  @keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
  }
  
  /* Glass effect */
  .glass {
    @apply bg-monokai-bg-secondary bg-opacity-80 backdrop-blur-sm;
    @apply border border-monokai-bg-tertiary border-opacity-50;
  }
  
  /* Glow effects */
  .glow-green {
    box-shadow: 0 0 20px rgba(169, 220, 118, 0.3);
  }
  
  .glow-red {
    box-shadow: 0 0 20px rgba(255, 97, 136, 0.3);
  }
  
  .glow-blue {
    box-shadow: 0 0 20px rgba(120, 220, 232, 0.3);
  }
  
  .glow-purple {
    box-shadow: 0 0 20px rgba(171, 157, 242, 0.3);
  }
}
