import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, Copy, Eye, EyeOff } from 'lucide-react';

interface JsonViewerProps {
  data: any;
  title?: string;
  collapsible?: boolean;
  maxHeight?: string;
}

interface JsonNodeProps {
  data: any;
  keyName?: string;
  level?: number;
  isLast?: boolean;
  parentCollapsed?: boolean;
}

const JsonViewer: React.FC<JsonViewerProps> = ({ 
  data, 
  title = "JSON Data",
  collapsible = true,
  maxHeight = "400px"
}) => {
  const [collapsed, setCollapsed] = useState(false);
  const [showRaw, setShowRaw] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(data, null, 2));
      // Could add a toast notification here
    } catch (error) {
      console.error('Failed to copy JSON:', error);
    }
  };

  return (
    <div className="bg-monokai-bg-primary rounded-lg border border-monokai-bg-tertiary">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-monokai-bg-tertiary">
        <div className="flex items-center space-x-2">
          {collapsible && (
            <button
              onClick={() => setCollapsed(!collapsed)}
              className="p-1 hover:bg-monokai-bg-tertiary rounded transition-colors"
            >
              {collapsed ? (
                <ChevronRight className="w-4 h-4 text-monokai-text-muted" />
              ) : (
                <ChevronDown className="w-4 h-4 text-monokai-text-muted" />
              )}
            </button>
          )}
          <h4 className="font-medium text-monokai-text-primary">{title}</h4>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowRaw(!showRaw)}
            className="p-1 hover:bg-monokai-bg-tertiary rounded transition-colors"
            title={showRaw ? "Show formatted view" : "Show raw JSON"}
          >
            {showRaw ? (
              <EyeOff className="w-4 h-4 text-monokai-text-muted" />
            ) : (
              <Eye className="w-4 h-4 text-monokai-text-muted" />
            )}
          </button>
          <button
            onClick={handleCopy}
            className="p-1 hover:bg-monokai-bg-tertiary rounded transition-colors"
            title="Copy JSON to clipboard"
          >
            <Copy className="w-4 h-4 text-monokai-text-muted" />
          </button>
        </div>
      </div>

      {/* Content */}
      {!collapsed && (
        <div 
          className="p-3 overflow-auto scrollbar-monokai font-mono text-sm"
          style={{ maxHeight }}
        >
          {showRaw ? (
            <pre className="text-monokai-text-primary whitespace-pre-wrap">
              {JSON.stringify(data, null, 2)}
            </pre>
          ) : (
            <JsonNode data={data} level={0} />
          )}
        </div>
      )}
    </div>
  );
};

const JsonNode: React.FC<JsonNodeProps> = ({ 
  data, 
  keyName, 
  level = 0, 
  isLast = true,
  parentCollapsed = false 
}) => {
  const [collapsed, setCollapsed] = useState(level > 2); // Auto-collapse deep levels
  const indent = level * 20;

  const renderValue = (value: any, key?: string): JSX.Element => {
    if (value === null) {
      return <span className="text-monokai-comment">null</span>;
    }

    if (typeof value === 'boolean') {
      return <span className="text-monokai-red">{value.toString()}</span>;
    }

    if (typeof value === 'number') {
      return <span className="text-monokai-purple">{value}</span>;
    }

    if (typeof value === 'string') {
      // Special handling for certain field types
      if (key && (key.includes('hash') || key.includes('signature') || key === 'id')) {
        return (
          <span className="text-monokai-yellow">
            "{value.length > 20 ? `${value.substring(0, 20)}...` : value}"
          </span>
        );
      }
      
      if (key && (key.includes('timestamp') || key.includes('_at'))) {
        return <span className="text-monokai-blue">"{value}"</span>;
      }
      
      return <span className="text-monokai-yellow">"{value}"</span>;
    }

    if (Array.isArray(value)) {
      if (value.length === 0) {
        return <span className="text-monokai-text-primary">[]</span>;
      }

      return (
        <div>
          <button
            onClick={() => setCollapsed(!collapsed)}
            className="inline-flex items-center hover:bg-monokai-bg-tertiary rounded px-1 transition-colors"
          >
            {collapsed ? (
              <ChevronRight className="w-3 h-3 text-monokai-text-muted mr-1" />
            ) : (
              <ChevronDown className="w-3 h-3 text-monokai-text-muted mr-1" />
            )}
            <span className="text-monokai-text-primary">
              [{collapsed ? `${value.length} items` : ''}
            </span>
          </button>
          
          {!collapsed && (
            <div className="ml-4">
              {value.map((item, index) => (
                <div key={index} style={{ marginLeft: `${indent + 20}px` }}>
                  <div className="flex">
                    <span className="text-monokai-text-muted mr-2">{index}:</span>
                    {renderValue(item)}
                    {index < value.length - 1 && <span className="text-monokai-text-muted">,</span>}
                  </div>
                </div>
              ))}
            </div>
          )}
          
          <span className="text-monokai-text-primary">]</span>
        </div>
      );
    }

    if (typeof value === 'object') {
      const keys = Object.keys(value);
      if (keys.length === 0) {
        return <span className="text-monokai-text-primary">{'{}'}</span>;
      }

      return (
        <div>
          <button
            onClick={() => setCollapsed(!collapsed)}
            className="inline-flex items-center hover:bg-monokai-bg-tertiary rounded px-1 transition-colors"
          >
            {collapsed ? (
              <ChevronRight className="w-3 h-3 text-monokai-text-muted mr-1" />
            ) : (
              <ChevronDown className="w-3 h-3 text-monokai-text-muted mr-1" />
            )}
            <span className="text-monokai-text-primary">
              {'{'}
              {collapsed && <span className="text-monokai-text-muted ml-1">{keys.length} keys</span>}
            </span>
          </button>
          
          {!collapsed && (
            <div className="ml-4">
              {keys.map((objKey, index) => (
                <JsonNode
                  key={objKey}
                  data={value[objKey]}
                  keyName={objKey}
                  level={level + 1}
                  isLast={index === keys.length - 1}
                  parentCollapsed={collapsed}
                />
              ))}
            </div>
          )}
          
          <span className="text-monokai-text-primary">{'}'}</span>
        </div>
      );
    }

    return <span className="text-monokai-text-primary">{String(value)}</span>;
  };

  if (parentCollapsed) {
    return null;
  }

  return (
    <div style={{ marginLeft: `${indent}px` }} className="leading-relaxed">
      <div className="flex items-start">
        {keyName && (
          <>
            <span className="text-monokai-blue mr-2">"{keyName}":</span>
          </>
        )}
        <div className="flex-1">
          {renderValue(data, keyName)}
          {!isLast && <span className="text-monokai-text-muted">,</span>}
        </div>
      </div>
    </div>
  );
};

export default JsonViewer;
