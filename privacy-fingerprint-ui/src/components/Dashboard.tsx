import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Shield, 
  Fingerprint, 
  BarChart3, 
  RotateCcw, 
  Code, 
  BookOpen,
  Plus,
  TrendingUp,
  Clock,
  AlertTriangle,
  CheckCircle,
  Activity
} from 'lucide-react';
import { invoke } from '@tauri-apps/api/tauri';
import { useAppStore, useAppSelectors } from '@stores/appStore';
import { useNotificationStore } from '@stores/notificationStore';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { 
    currentFingerprint, 
    fingerprintHistory, 
    currentAnalysis,
    systemInfo,
    rustToolAvailable,
    setFingerprintHistory 
  } = useAppStore();
  
  const { 
    needsRotation, 
    getAveragePrivacyScore, 
    getLatestHistoryEntry 
  } = useAppSelectors();
  
  const { addNotification } = useNotificationStore();

  // Load fingerprint history on component mount
  useEffect(() => {
    const loadHistory = async () => {
      try {
        const history = await invoke('get_fingerprint_history');
        setFingerprintHistory(history);
      } catch (error) {
        console.error('Failed to load fingerprint history:', error);
      }
    };

    loadHistory();
  }, [setFingerprintHistory]);

  const rotationNeeded = needsRotation();
  const averagePrivacyScore = getAveragePrivacyScore();
  const latestEntry = getLatestHistoryEntry();

  // Quick action cards
  const quickActions = [
    {
      title: 'Generate Fingerprint',
      description: 'Create a new privacy-protected fingerprint',
      icon: Fingerprint,
      color: 'green',
      action: () => navigate('/generate'),
      disabled: false,
    },
    {
      title: 'Analyze Privacy',
      description: 'Analyze current fingerprint privacy protection',
      icon: BarChart3,
      color: 'blue',
      action: () => navigate('/analysis'),
      disabled: !currentFingerprint,
    },
    {
      title: 'Rotate Fingerprint',
      description: 'Rotate fingerprint for enhanced privacy',
      icon: RotateCcw,
      color: 'purple',
      action: () => navigate('/rotation'),
      disabled: !currentFingerprint,
      urgent: rotationNeeded,
    },
    {
      title: 'VS Code Integration',
      description: 'Integrate with VS Code extensions',
      icon: Code,
      color: 'yellow',
      action: () => navigate('/integration'),
      disabled: false,
    },
  ];

  // Status cards
  const statusCards = [
    {
      title: 'Current Fingerprint',
      value: currentFingerprint ? 'Active' : 'None',
      subtitle: currentFingerprint 
        ? `Created ${new Date(currentFingerprint.created_at).toLocaleDateString()}`
        : 'Generate your first fingerprint',
      icon: Shield,
      color: currentFingerprint ? 'green' : 'gray',
      status: currentFingerprint ? 'success' : 'warning',
    },
    {
      title: 'Privacy Score',
      value: currentFingerprint 
        ? `${Math.round(currentFingerprint.privacy_metadata.privacy_score * 100)}%`
        : '--',
      subtitle: currentFingerprint 
        ? `${currentFingerprint.privacy_level} privacy level`
        : 'No fingerprint available',
      icon: TrendingUp,
      color: currentFingerprint 
        ? (currentFingerprint.privacy_metadata.privacy_score >= 0.8 ? 'green' : 
           currentFingerprint.privacy_metadata.privacy_score >= 0.6 ? 'yellow' : 'red')
        : 'gray',
      status: currentFingerprint 
        ? (currentFingerprint.privacy_metadata.privacy_score >= 0.8 ? 'success' : 'warning')
        : 'neutral',
    },
    {
      title: 'Rotation Status',
      value: rotationNeeded ? 'Due' : 'Current',
      subtitle: currentFingerprint?.next_rotation 
        ? `Next: ${new Date(currentFingerprint.next_rotation).toLocaleDateString()}`
        : 'No rotation scheduled',
      icon: Clock,
      color: rotationNeeded ? 'yellow' : 'green',
      status: rotationNeeded ? 'warning' : 'success',
    },
    {
      title: 'System Status',
      value: rustToolAvailable ? 'Ready' : 'Limited',
      subtitle: rustToolAvailable 
        ? 'All features available'
        : 'Rust tool not available',
      icon: Activity,
      color: rustToolAvailable ? 'green' : 'red',
      status: rustToolAvailable ? 'success' : 'error',
    },
  ];

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'green': return 'text-monokai-green bg-monokai-green';
      case 'blue': return 'text-monokai-blue bg-monokai-blue';
      case 'purple': return 'text-monokai-purple bg-monokai-purple';
      case 'yellow': return 'text-monokai-yellow bg-monokai-yellow';
      case 'red': return 'text-monokai-red bg-monokai-red';
      default: return 'text-monokai-text-muted bg-monokai-text-muted';
    }
  };

  return (
    <div className="space-y-6">
      {/* Welcome section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-monokai-text-primary mb-2">
            Welcome to Privacy Fingerprint Generator
          </h1>
          <p className="text-monokai-text-secondary">
            Generate privacy-protected device fingerprints for security research and education.
          </p>
        </div>
        
        {!currentFingerprint && (
          <button
            onClick={() => navigate('/generate')}
            className="btn btn-primary flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Generate First Fingerprint</span>
          </button>
        )}
      </div>

      {/* Status overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statusCards.map((card) => {
          const Icon = card.icon;
          const colorClasses = getColorClasses(card.color);
          
          return (
            <div key={card.title} className="card">
              <div className="card-body">
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-2 rounded-lg ${colorClasses.split(' ')[1]} bg-opacity-20`}>
                    <Icon className={`w-5 h-5 ${colorClasses.split(' ')[0]}`} />
                  </div>
                  
                  {card.status === 'success' && (
                    <CheckCircle className="w-4 h-4 text-monokai-green" />
                  )}
                  {card.status === 'warning' && (
                    <AlertTriangle className="w-4 h-4 text-monokai-yellow" />
                  )}
                  {card.status === 'error' && (
                    <AlertTriangle className="w-4 h-4 text-monokai-red" />
                  )}
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-monokai-text-muted mb-1">
                    {card.title}
                  </h3>
                  <div className="text-2xl font-bold text-monokai-text-primary mb-1">
                    {card.value}
                  </div>
                  <p className="text-sm text-monokai-text-muted">
                    {card.subtitle}
                  </p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Quick actions */}
      <div>
        <h2 className="text-xl font-semibold text-monokai-text-primary mb-4">
          Quick Actions
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action) => {
            const Icon = action.icon;
            const colorClasses = getColorClasses(action.color);
            
            return (
              <button
                key={action.title}
                onClick={action.action}
                disabled={action.disabled}
                className={`
                  card hover:shadow-lg transition-all duration-200 text-left
                  ${action.disabled 
                    ? 'opacity-50 cursor-not-allowed' 
                    : 'hover:scale-105 cursor-pointer'
                  }
                  ${action.urgent ? 'ring-2 ring-monokai-yellow ring-opacity-50' : ''}
                `}
              >
                <div className="card-body">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className={`p-2 rounded-lg ${colorClasses.split(' ')[1]} bg-opacity-20`}>
                      <Icon className={`w-5 h-5 ${colorClasses.split(' ')[0]}`} />
                    </div>
                    {action.urgent && (
                      <div className="w-2 h-2 bg-monokai-yellow rounded-full animate-pulse"></div>
                    )}
                  </div>
                  
                  <h3 className="font-medium text-monokai-text-primary mb-1">
                    {action.title}
                  </h3>
                  <p className="text-sm text-monokai-text-muted">
                    {action.description}
                  </p>
                  
                  {action.urgent && (
                    <div className="mt-2 text-xs text-monokai-yellow">
                      Action required
                    </div>
                  )}
                </div>
              </button>
            );
          })}
        </div>
      </div>

      {/* Recent activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent fingerprints */}
        <div className="card">
          <div className="card-header">
            <h3 className="font-semibold text-monokai-text-primary">
              Recent Activity
            </h3>
          </div>
          <div className="card-body">
            {fingerprintHistory.length === 0 ? (
              <div className="text-center py-8">
                <Fingerprint className="w-12 h-12 text-monokai-text-muted mx-auto mb-4" />
                <p className="text-monokai-text-muted">
                  No fingerprints generated yet
                </p>
                <button
                  onClick={() => navigate('/generate')}
                  className="btn btn-primary mt-4"
                >
                  Generate Your First Fingerprint
                </button>
              </div>
            ) : (
              <div className="space-y-3">
                {fingerprintHistory.slice(0, 5).map((entry) => (
                  <div
                    key={entry.id}
                    className="flex items-center justify-between p-3 bg-monokai-bg-primary rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`w-2 h-2 rounded-full ${
                        entry.action === 'generated' ? 'bg-monokai-green' :
                        entry.action === 'rotated' ? 'bg-monokai-purple' :
                        'bg-monokai-blue'
                      }`}></div>
                      <div>
                        <div className="text-sm font-medium text-monokai-text-primary">
                          Fingerprint {entry.action}
                        </div>
                        <div className="text-xs text-monokai-text-muted">
                          {new Date(entry.timestamp).toLocaleString()}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-monokai-text-primary">
                        {Math.round(entry.privacy_score * 100)}%
                      </div>
                      <div className="text-xs text-monokai-text-muted">
                        {entry.privacy_level}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* System information */}
        <div className="card">
          <div className="card-header">
            <h3 className="font-semibold text-monokai-text-primary">
              System Information
            </h3>
          </div>
          <div className="card-body">
            {systemInfo ? (
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-monokai-text-muted">Operating System</span>
                  <span className="text-monokai-text-primary font-medium">
                    {systemInfo.os} {systemInfo.arch}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-monokai-text-muted">Platform</span>
                  <span className="text-monokai-text-primary font-medium">
                    {systemInfo.platform}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-monokai-text-muted">CPU Cores</span>
                  <span className="text-monokai-text-primary font-medium">
                    {systemInfo.cpu_count}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-monokai-text-muted">Rust Tool</span>
                  <span className={`font-medium ${
                    systemInfo.rust_tool_available ? 'text-monokai-green' : 'text-monokai-red'
                  }`}>
                    {systemInfo.rust_tool_available ? 'Available' : 'Not Available'}
                  </span>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-monokai-text-muted">Loading system information...</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Educational section */}
      <div className="card">
        <div className="card-body">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-monokai-text-primary mb-2">
                Learn About Privacy Fingerprinting
              </h3>
              <p className="text-monokai-text-secondary">
                Understand privacy concepts, best practices, and ethical guidelines for fingerprinting.
              </p>
            </div>
            <button
              onClick={() => navigate('/education')}
              className="btn btn-secondary flex items-center space-x-2"
            >
              <BookOpen className="w-4 h-4" />
              <span>Explore</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
