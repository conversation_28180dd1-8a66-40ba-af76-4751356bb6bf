import React, { useState } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { 
  Settings as SettingsIcon, 
  Shield, 
  Palette, 
  Database, 
  RotateCcw,
  Save,
  RefreshCw,
  Info
} from 'lucide-react';
import { useAppStore } from '@stores/appStore';
import { notificationHelpers } from '@stores/notificationStore';
import type { AppConfig, PrivacyLevel } from '@types/index';

const Settings: React.FC = () => {
  const { config, updateConfig, reset } = useAppStore();
  const [localConfig, setLocalConfig] = useState<AppConfig>(config);
  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await invoke('update_config', { newConfig: localConfig });
      updateConfig(localConfig);
      notificationHelpers.success('Settings Saved', 'Configuration updated successfully');
    } catch (error) {
      notificationHelpers.error('Save Failed', `Failed to save settings: ${error}`);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = async () => {
    try {
      const defaultConfig = await invoke<AppConfig>('reset_config');
      setLocalConfig(defaultConfig);
      updateConfig(defaultConfig);
      notificationHelpers.success('Settings Reset', 'Configuration reset to defaults');
    } catch (error) {
      notificationHelpers.error('Reset Failed', `Failed to reset settings: ${error}`);
    }
  };

  const updateLocalConfig = (updates: Partial<AppConfig>) => {
    setLocalConfig(prev => ({ ...prev, ...updates }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-monokai-text-primary mb-2">Settings</h1>
        <p className="text-monokai-text-secondary">
          Configure privacy fingerprint generator preferences and behavior.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Privacy Settings */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center space-x-2">
              <Shield className="w-5 h-5 text-monokai-green" />
              <h3 className="font-semibold text-monokai-text-primary">Privacy Settings</h3>
            </div>
          </div>
          
          <div className="card-body space-y-4">
            <div>
              <label className="block text-sm font-medium text-monokai-text-primary mb-2">
                Default Privacy Level
              </label>
              <select
                value={localConfig.privacy_level}
                onChange={(e) => updateLocalConfig({ privacy_level: e.target.value })}
                className="w-full"
              >
                <option value="Low">Low - Basic protection</option>
                <option value="Medium">Medium - Moderate protection</option>
                <option value="High">High - Strong protection</option>
                <option value="Maximum">Maximum - Strongest protection</option>
              </select>
              <p className="text-xs text-monokai-text-muted mt-1">
                Default privacy level for new fingerprints
              </p>
            </div>

            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={localConfig.auto_rotation}
                  onChange={(e) => updateLocalConfig({ auto_rotation: e.target.checked })}
                  className="rounded border-monokai-bg-tertiary"
                />
                <span className="text-sm text-monokai-text-primary">Enable automatic rotation</span>
              </label>
              <p className="text-xs text-monokai-text-muted mt-1 ml-6">
                Automatically rotate fingerprints for enhanced privacy
              </p>
            </div>

            {localConfig.auto_rotation && (
              <div>
                <label className="block text-sm font-medium text-monokai-text-primary mb-2">
                  Rotation Interval (hours)
                </label>
                <input
                  type="number"
                  min="1"
                  max="168"
                  value={localConfig.rotation_interval}
                  onChange={(e) => updateLocalConfig({ rotation_interval: parseInt(e.target.value) || 24 })}
                  className="w-full"
                />
                <p className="text-xs text-monokai-text-muted mt-1">
                  How often to rotate fingerprints (1-168 hours)
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Integration Settings */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center space-x-2">
              <Database className="w-5 h-5 text-monokai-blue" />
              <h3 className="font-semibold text-monokai-text-primary">Integration Settings</h3>
            </div>
          </div>
          
          <div className="card-body space-y-4">
            <div>
              <label className="block text-sm font-medium text-monokai-text-primary mb-2">
                VS Code Extension Path
              </label>
              <input
                type="text"
                value={localConfig.extension_path || ''}
                onChange={(e) => updateLocalConfig({ extension_path: e.target.value || undefined })}
                placeholder="/path/to/vscode/extension"
                className="w-full"
              />
              <p className="text-xs text-monokai-text-muted mt-1">
                Path to VS Code extension for integration
              </p>
            </div>

            <div className="p-3 bg-monokai-bg-primary rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Info className="w-4 h-4 text-monokai-blue" />
                <span className="text-sm font-medium text-monokai-text-primary">Integration Status</span>
              </div>
              <div className="text-xs text-monokai-text-muted space-y-1">
                <div>• Extension path: {localConfig.extension_path || 'Not configured'}</div>
                <div>• Integration: {localConfig.extension_path ? 'Configured' : 'Not configured'}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Appearance Settings */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center space-x-2">
              <Palette className="w-5 h-5 text-monokai-purple" />
              <h3 className="font-semibold text-monokai-text-primary">Appearance</h3>
            </div>
          </div>
          
          <div className="card-body space-y-4">
            <div>
              <label className="block text-sm font-medium text-monokai-text-primary mb-2">
                Theme
              </label>
              <select
                value={localConfig.theme}
                onChange={(e) => updateLocalConfig({ theme: e.target.value })}
                className="w-full"
              >
                <option value="monokai-pro">Monokai Pro (Dark)</option>
                <option value="light">Light Theme</option>
                <option value="dark">Dark Theme</option>
              </select>
              <p className="text-xs text-monokai-text-muted mt-1">
                Application color theme
              </p>
            </div>

            <div className="p-3 bg-monokai-bg-primary rounded-lg">
              <div className="text-sm font-medium text-monokai-text-primary mb-2">Theme Preview</div>
              <div className="flex space-x-2">
                <div className="w-4 h-4 bg-monokai-green rounded"></div>
                <div className="w-4 h-4 bg-monokai-blue rounded"></div>
                <div className="w-4 h-4 bg-monokai-purple rounded"></div>
                <div className="w-4 h-4 bg-monokai-yellow rounded"></div>
                <div className="w-4 h-4 bg-monokai-red rounded"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Advanced Settings */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center space-x-2">
              <SettingsIcon className="w-5 h-5 text-monokai-yellow" />
              <h3 className="font-semibold text-monokai-text-primary">Advanced</h3>
            </div>
          </div>
          
          <div className="card-body space-y-4">
            <div className="p-3 bg-monokai-bg-primary rounded-lg">
              <div className="text-sm font-medium text-monokai-text-primary mb-2">Data Management</div>
              <div className="space-y-2">
                <button
                  onClick={() => {
                    // Clear fingerprint history
                    notificationHelpers.info('Feature Coming Soon', 'Data clearing functionality will be available in a future update');
                  }}
                  className="btn btn-secondary btn-sm w-full"
                >
                  Clear Fingerprint History
                </button>
                <button
                  onClick={() => {
                    // Export settings
                    notificationHelpers.info('Feature Coming Soon', 'Settings export functionality will be available in a future update');
                  }}
                  className="btn btn-secondary btn-sm w-full"
                >
                  Export Settings
                </button>
              </div>
            </div>

            <div className="p-3 bg-monokai-bg-primary rounded-lg">
              <div className="text-sm font-medium text-monokai-text-primary mb-2">Debug Information</div>
              <div className="text-xs text-monokai-text-muted space-y-1">
                <div>• Version: 1.0.0</div>
                <div>• Platform: {navigator.platform}</div>
                <div>• User Agent: {navigator.userAgent.substring(0, 50)}...</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between p-4 bg-monokai-bg-secondary rounded-lg">
        <div className="text-sm text-monokai-text-muted">
          Changes are saved automatically when you click Save.
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={handleReset}
            className="btn btn-secondary flex items-center space-x-2"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Reset to Defaults</span>
          </button>
          
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="btn btn-primary flex items-center space-x-2"
          >
            {isSaving ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Saving...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>Save Settings</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Settings;
