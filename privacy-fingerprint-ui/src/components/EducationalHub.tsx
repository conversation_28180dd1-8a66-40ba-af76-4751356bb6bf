import React from 'react';
import { BookOpen, Shield, Users, FileText } from 'lucide-react';

const EducationalHub: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-monokai-text-primary mb-2">Educational Hub</h1>
        <p className="text-monokai-text-secondary">
          Learn about privacy concepts, fingerprinting techniques, and ethical guidelines.
        </p>
      </div>

      <div className="card">
        <div className="card-body text-center py-12">
          <BookOpen className="w-16 h-16 text-monokai-text-muted mx-auto mb-4" />
          <h3 className="text-lg font-medium text-monokai-text-primary mb-2">
            Educational Content Coming Soon
          </h3>
          <p className="text-monokai-text-muted mb-6">
            This section will provide comprehensive educational resources about privacy fingerprinting, best practices, and ethical considerations.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-md mx-auto">
            <div className="p-3 bg-monokai-bg-secondary rounded-lg">
              <Shield className="w-6 h-6 text-monokai-green mx-auto mb-2" />
              <div className="text-xs text-monokai-text-muted">Privacy Concepts</div>
            </div>
            <div className="p-3 bg-monokai-bg-secondary rounded-lg">
              <Users className="w-6 h-6 text-monokai-blue mx-auto mb-2" />
              <div className="text-xs text-monokai-text-muted">Ethical Guidelines</div>
            </div>
            <div className="p-3 bg-monokai-bg-secondary rounded-lg">
              <FileText className="w-6 h-6 text-monokai-purple mx-auto mb-2" />
              <div className="text-xs text-monokai-text-muted">Best Practices</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EducationalHub;
