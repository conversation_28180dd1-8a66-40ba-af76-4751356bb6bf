import React from 'react';
import { useLocation } from 'react-router-dom';
import Sidebar from './Sidebar';
import Header from './Header';
import NotificationCenter from './NotificationCenter';
import ProgressBar from './ProgressBar';
import { useAppStore } from '@stores/appStore';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const { uiState, progressState } = useAppStore();

  return (
    <div className="flex h-screen bg-monokai-bg-primary overflow-hidden">
      {/* Sidebar */}
      <div className={`
        transition-all duration-300 ease-in-out
        ${uiState.sidebarCollapsed ? 'w-16' : 'w-64'}
      `}>
        <Sidebar />
      </div>

      {/* Main content area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header />

        {/* Progress bar */}
        {progressState.isActive && (
          <ProgressBar 
            progress={progressState.progress}
            message={progressState.message}
            stage={progressState.stage}
          />
        )}

        {/* Main content */}
        <main className="flex-1 overflow-auto bg-monokai-bg-primary">
          <div className="container mx-auto px-6 py-6 max-w-7xl">
            {children}
          </div>
        </main>
      </div>

      {/* Notification center */}
      <NotificationCenter />
    </div>
  );
};

export default Layout;
