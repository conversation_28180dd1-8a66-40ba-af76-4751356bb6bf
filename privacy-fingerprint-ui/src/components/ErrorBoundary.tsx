import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/dashboard';
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-monokai-bg-primary flex items-center justify-center p-6">
          <div className="max-w-md w-full text-center">
            {/* Error icon */}
            <div className="mb-6">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-monokai-red bg-opacity-20 rounded-full mb-4">
                <AlertTriangle className="w-8 h-8 text-monokai-red" />
              </div>
              <h1 className="text-2xl font-bold text-monokai-text-primary mb-2">
                Something went wrong
              </h1>
              <p className="text-monokai-text-secondary">
                The application encountered an unexpected error.
              </p>
            </div>

            {/* Error details */}
            {this.state.error && (
              <div className="mb-6 p-4 bg-monokai-bg-secondary rounded-lg text-left">
                <h3 className="text-sm font-medium text-monokai-text-primary mb-2">
                  Error Details:
                </h3>
                <code className="text-xs text-monokai-red font-mono break-all">
                  {this.state.error.message}
                </code>
                {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
                  <details className="mt-2">
                    <summary className="text-xs text-monokai-text-muted cursor-pointer">
                      Stack Trace
                    </summary>
                    <pre className="text-xs text-monokai-text-muted mt-2 overflow-auto max-h-32">
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </details>
                )}
              </div>
            )}

            {/* Actions */}
            <div className="space-y-3">
              <button
                onClick={this.handleReload}
                className="w-full btn btn-primary flex items-center justify-center space-x-2"
              >
                <RefreshCw className="w-4 h-4" />
                <span>Reload Application</span>
              </button>
              
              <button
                onClick={this.handleGoHome}
                className="w-full btn btn-secondary flex items-center justify-center space-x-2"
              >
                <Home className="w-4 h-4" />
                <span>Go to Dashboard</span>
              </button>
            </div>

            {/* Help text */}
            <div className="mt-6 text-xs text-monokai-text-muted">
              <p>If this problem persists, please check the console for more details.</p>
              <p className="mt-1">
                This is a development build. Error details are shown for debugging.
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
