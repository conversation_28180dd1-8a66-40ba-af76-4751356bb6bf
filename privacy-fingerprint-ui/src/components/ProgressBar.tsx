import React from 'react';
import { Loader2 } from 'lucide-react';

interface ProgressBarProps {
  progress: number;
  message?: string;
  stage?: string;
  showPercentage?: boolean;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  message,
  stage,
  showPercentage = true,
}) => {
  const clampedProgress = Math.max(0, Math.min(100, progress));

  return (
    <div className="bg-monokai-bg-secondary border-b border-monokai-bg-tertiary px-6 py-3">
      <div className="flex items-center space-x-4">
        {/* Loading spinner */}
        <Loader2 className="w-4 h-4 text-monokai-green animate-spin flex-shrink-0" />

        {/* Progress content */}
        <div className="flex-1 min-w-0">
          {/* Message and stage */}
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2 min-w-0">
              {message && (
                <span className="text-sm text-monokai-text-primary truncate">
                  {message}
                </span>
              )}
              {stage && (
                <>
                  <span className="text-monokai-text-muted">•</span>
                  <span className="text-sm text-monokai-text-muted truncate">
                    {stage}
                  </span>
                </>
              )}
            </div>
            
            {showPercentage && (
              <span className="text-sm text-monokai-text-muted font-mono flex-shrink-0">
                {Math.round(clampedProgress)}%
              </span>
            )}
          </div>

          {/* Progress bar */}
          <div className="w-full bg-monokai-bg-tertiary rounded-full h-2">
            <div
              className="bg-monokai-green h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${clampedProgress}%` }}
            >
              {/* Animated shine effect */}
              <div className="h-full bg-gradient-to-r from-transparent via-white via-transparent to-transparent opacity-20 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressBar;
