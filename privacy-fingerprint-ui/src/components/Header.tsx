import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { 
  Bell, 
  Search, 
  HelpCircle, 
  Monitor, 
  Wifi, 
  WifiOff,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { useAppStore, useAppSelectors } from '@stores/appStore';
import { useNotificationStore } from '@stores/notificationStore';

const Header: React.FC = () => {
  const location = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [showNotifications, setShowNotifications] = useState(false);
  
  const { 
    systemInfo, 
    rustToolAvailable, 
    currentFingerprint,
    isAnyOperationInProgress 
  } = useAppStore();
  
  const { needsRotation } = useAppSelectors();
  const { notifications, getUnreadCount } = useNotificationStore();

  // Get page title based on current route
  const getPageTitle = () => {
    switch (location.pathname) {
      case '/dashboard': return 'Dashboard';
      case '/generate': return 'Generate Fingerprint';
      case '/analysis': return 'Privacy Analysis';
      case '/rotation': return 'Rotation Manager';
      case '/integration': return 'VS Code Integration';
      case '/education': return 'Educational Hub';
      case '/settings': return 'Settings';
      default: return 'Privacy Fingerprint Generator';
    }
  };

  const unreadCount = getUnreadCount();
  const rotationNeeded = needsRotation();

  return (
    <header className="bg-monokai-bg-secondary border-b border-monokai-bg-tertiary px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left section - Page title and breadcrumb */}
        <div className="flex items-center space-x-4">
          <div>
            <h1 className="text-xl font-semibold text-monokai-text-primary">
              {getPageTitle()}
            </h1>
            <div className="flex items-center space-x-2 text-sm text-monokai-text-muted">
              <span>Privacy Fingerprint Generator</span>
              <span>•</span>
              <span>{getPageTitle()}</span>
            </div>
          </div>
        </div>

        {/* Center section - Search */}
        <div className="flex-1 max-w-md mx-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-monokai-text-muted" />
            <input
              type="text"
              placeholder="Search fingerprints, analysis, or help..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-monokai-bg-primary border border-monokai-bg-tertiary rounded-lg
                       text-monokai-text-primary placeholder-monokai-text-muted
                       focus:outline-none focus:ring-2 focus:ring-monokai-green focus:border-transparent
                       transition-all duration-200"
            />
          </div>
        </div>

        {/* Right section - Status indicators and actions */}
        <div className="flex items-center space-x-4">
          {/* System status indicators */}
          <div className="flex items-center space-x-2">
            {/* Rust tool status */}
            <div className="flex items-center space-x-1">
              {rustToolAvailable ? (
                <CheckCircle className="w-4 h-4 text-monokai-green" />
              ) : (
                <AlertTriangle className="w-4 h-4 text-monokai-red" />
              )}
              <span className="text-xs text-monokai-text-muted">
                {rustToolAvailable ? 'Tool Ready' : 'Tool Missing'}
              </span>
            </div>

            {/* Network status */}
            <div className="flex items-center space-x-1">
              <Wifi className="w-4 h-4 text-monokai-green" />
              <span className="text-xs text-monokai-text-muted">Online</span>
            </div>

            {/* System info */}
            {systemInfo && (
              <div className="flex items-center space-x-1">
                <Monitor className="w-4 h-4 text-monokai-blue" />
                <span className="text-xs text-monokai-text-muted">
                  {systemInfo.os} {systemInfo.arch}
                </span>
              </div>
            )}
          </div>

          {/* Divider */}
          <div className="w-px h-6 bg-monokai-bg-tertiary"></div>

          {/* Fingerprint status */}
          {currentFingerprint && (
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                rotationNeeded ? 'bg-monokai-yellow animate-pulse' : 'bg-monokai-green'
              }`}></div>
              <span className="text-xs text-monokai-text-muted">
                {rotationNeeded ? 'Rotation Due' : 'Fingerprint Active'}
              </span>
              {rotationNeeded && (
                <Clock className="w-4 h-4 text-monokai-yellow" />
              )}
            </div>
          )}

          {/* Operation status */}
          {isAnyOperationInProgress && (
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-monokai-blue rounded-full animate-pulse"></div>
              <span className="text-xs text-monokai-text-muted">Processing...</span>
            </div>
          )}

          {/* Notifications */}
          <div className="relative">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative p-2 rounded-lg hover:bg-monokai-bg-tertiary transition-colors"
              title="Notifications"
            >
              <Bell className="w-5 h-5 text-monokai-text-secondary" />
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-monokai-red text-white text-xs
                               rounded-full flex items-center justify-center font-medium">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </span>
              )}
            </button>

            {/* Notifications dropdown */}
            {showNotifications && (
              <div className="absolute right-0 mt-2 w-80 bg-monokai-bg-primary border border-monokai-bg-tertiary
                            rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto scrollbar-monokai">
                <div className="p-4 border-b border-monokai-bg-tertiary">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-monokai-text-primary">Notifications</h3>
                    <span className="text-xs text-monokai-text-muted">
                      {notifications.length} total
                    </span>
                  </div>
                </div>
                
                <div className="max-h-64 overflow-y-auto">
                  {notifications.length === 0 ? (
                    <div className="p-4 text-center text-monokai-text-muted">
                      No notifications
                    </div>
                  ) : (
                    notifications.slice(0, 10).map((notification) => (
                      <div
                        key={notification.id}
                        className="p-3 border-b border-monokai-bg-tertiary last:border-b-0
                                 hover:bg-monokai-bg-secondary transition-colors"
                      >
                        <div className="flex items-start space-x-3">
                          <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                            notification.type === 'success' ? 'bg-monokai-green' :
                            notification.type === 'error' ? 'bg-monokai-red' :
                            notification.type === 'warning' ? 'bg-monokai-yellow' :
                            'bg-monokai-blue'
                          }`}></div>
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-monokai-text-primary text-sm">
                              {notification.title}
                            </div>
                            <div className="text-xs text-monokai-text-muted mt-1">
                              {notification.message}
                            </div>
                            <div className="text-xs text-monokai-text-muted mt-1">
                              {new Date(notification.timestamp).toLocaleTimeString()}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Help */}
          <button
            className="p-2 rounded-lg hover:bg-monokai-bg-tertiary transition-colors"
            title="Help & Documentation"
          >
            <HelpCircle className="w-5 h-5 text-monokai-text-secondary" />
          </button>
        </div>
      </div>

      {/* Click outside to close notifications */}
      {showNotifications && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowNotifications(false)}
        ></div>
      )}
    </header>
  );
};

export default Header;
