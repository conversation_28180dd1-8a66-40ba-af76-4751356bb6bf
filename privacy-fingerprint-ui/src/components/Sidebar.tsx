import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { 
  Shield, 
  Fingerprint, 
  BarChart3, 
  RotateCcw, 
  Code, 
  BookOpen, 
  Settings, 
  Home,
  ChevronLeft,
  ChevronRight,
  Lock
} from 'lucide-react';
import { useAppStore } from '@stores/appStore';

const Sidebar: React.FC = () => {
  const location = useLocation();
  const { uiState, toggleSidebar, currentFingerprint } = useAppStore();
  const { sidebarCollapsed } = uiState;

  const navigationItems = [
    {
      path: '/dashboard',
      icon: Home,
      label: 'Dashboard',
      description: 'Overview and quick actions',
    },
    {
      path: '/generate',
      icon: Fingerprint,
      label: 'Generate',
      description: 'Create privacy fingerprints',
    },
    {
      path: '/analysis',
      icon: BarChart3,
      label: 'Analysis',
      description: 'Privacy protection analysis',
      disabled: !currentFingerprint,
    },
    {
      path: '/rotation',
      icon: RotateCcw,
      label: 'Rotation',
      description: 'Fingerprint rotation management',
      disabled: !currentFingerprint,
    },
    {
      path: '/integration',
      icon: Code,
      label: 'VS Code',
      description: 'Extension integration',
    },
    {
      path: '/education',
      icon: BookOpen,
      label: 'Education',
      description: 'Privacy concepts and guides',
    },
    {
      path: '/settings',
      icon: Settings,
      label: 'Settings',
      description: 'Application configuration',
    },
  ];

  return (
    <div className={`
      h-full bg-monokai-bg-secondary border-r border-monokai-bg-tertiary
      flex flex-col transition-all duration-300 ease-in-out
      ${sidebarCollapsed ? 'w-16' : 'w-64'}
    `}>
      {/* Header */}
      <div className="p-4 border-b border-monokai-bg-tertiary">
        <div className="flex items-center justify-between">
          {!sidebarCollapsed && (
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-monokai-green bg-opacity-20 rounded-lg">
                <Shield className="w-6 h-6 text-monokai-green" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-monokai-text-primary">
                  Privacy FP
                </h1>
                <p className="text-xs text-monokai-text-muted">
                  Fingerprint Generator
                </p>
              </div>
            </div>
          )}
          
          <button
            onClick={toggleSidebar}
            className="p-1.5 rounded-lg hover:bg-monokai-bg-tertiary transition-colors"
            title={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {sidebarCollapsed ? (
              <ChevronRight className="w-4 h-4 text-monokai-text-secondary" />
            ) : (
              <ChevronLeft className="w-4 h-4 text-monokai-text-secondary" />
            )}
          </button>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigationItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path;
          const isDisabled = item.disabled;

          return (
            <div key={item.path} className="relative group">
              <NavLink
                to={item.path}
                className={`
                  flex items-center space-x-3 px-3 py-2.5 rounded-lg
                  transition-all duration-200 relative
                  ${isActive 
                    ? 'bg-monokai-green bg-opacity-10 text-monokai-green border-l-2 border-monokai-green' 
                    : isDisabled
                    ? 'text-monokai-text-muted cursor-not-allowed opacity-50'
                    : 'text-monokai-text-secondary hover:text-monokai-text-primary hover:bg-monokai-bg-tertiary'
                  }
                  ${sidebarCollapsed ? 'justify-center' : ''}
                `}
                onClick={(e) => {
                  if (isDisabled) {
                    e.preventDefault();
                  }
                }}
              >
                <Icon className={`w-5 h-5 ${sidebarCollapsed ? '' : 'flex-shrink-0'}`} />
                
                {!sidebarCollapsed && (
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">
                      {item.label}
                    </div>
                    <div className="text-xs text-monokai-text-muted truncate">
                      {item.description}
                    </div>
                  </div>
                )}

                {isDisabled && !sidebarCollapsed && (
                  <Lock className="w-4 h-4 text-monokai-text-muted" />
                )}
              </NavLink>

              {/* Tooltip for collapsed sidebar */}
              {sidebarCollapsed && (
                <div className="
                  absolute left-full ml-2 px-2 py-1 bg-monokai-bg-primary
                  text-monokai-text-primary text-sm rounded shadow-lg
                  opacity-0 group-hover:opacity-100 transition-opacity
                  pointer-events-none z-50 whitespace-nowrap
                  border border-monokai-bg-tertiary
                ">
                  <div className="font-medium">{item.label}</div>
                  <div className="text-xs text-monokai-text-muted">
                    {item.description}
                  </div>
                  {isDisabled && (
                    <div className="text-xs text-monokai-red mt-1">
                      Requires fingerprint
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-monokai-bg-tertiary">
        {!sidebarCollapsed ? (
          <div className="text-center">
            <div className="text-xs text-monokai-text-muted mb-2">
              Privacy-First Design
            </div>
            <div className="flex items-center justify-center space-x-1">
              <div className="w-2 h-2 bg-monokai-green rounded-full animate-pulse"></div>
              <span className="text-xs text-monokai-green">
                Secure & Anonymous
              </span>
            </div>
          </div>
        ) : (
          <div className="flex justify-center">
            <div className="w-3 h-3 bg-monokai-green rounded-full animate-pulse"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
