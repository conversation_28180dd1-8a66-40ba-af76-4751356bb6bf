import React from 'react';
import { Shield, Loader2 } from 'lucide-react';

interface LoadingScreenProps {
  message?: string;
  progress?: number;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({ 
  message = 'Initializing Privacy Fingerprint Generator...', 
  progress 
}) => {
  return (
    <div className="min-h-screen bg-gradient-primary flex items-center justify-center">
      <div className="text-center max-w-md mx-auto px-6">
        {/* Logo */}
        <div className="mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-monokai-green bg-opacity-20 rounded-2xl mb-4">
            <Shield className="w-10 h-10 text-monokai-green" />
          </div>
          <h1 className="text-2xl font-bold text-monokai-text-primary mb-2">
            Privacy Fingerprint Generator
          </h1>
          <p className="text-monokai-text-secondary">
            Secure • Anonymous • Educational
          </p>
        </div>

        {/* Loading indicator */}
        <div className="mb-6">
          <div className="flex items-center justify-center mb-4">
            <Loader2 className="w-8 h-8 text-monokai-green animate-spin" />
          </div>
          
          {/* Progress bar */}
          {progress !== undefined && (
            <div className="w-full bg-monokai-bg-tertiary rounded-full h-2 mb-4">
              <div 
                className="bg-monokai-green h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.max(0, Math.min(100, progress))}%` }}
              ></div>
            </div>
          )}
          
          <p className="text-monokai-text-muted text-sm animate-pulse">
            {message}
          </p>
        </div>

        {/* Features */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="p-3 bg-monokai-bg-secondary bg-opacity-50 rounded-lg">
            <div className="w-2 h-2 bg-monokai-green rounded-full mx-auto mb-2"></div>
            <p className="text-xs text-monokai-text-muted">Privacy First</p>
          </div>
          <div className="p-3 bg-monokai-bg-secondary bg-opacity-50 rounded-lg">
            <div className="w-2 h-2 bg-monokai-blue rounded-full mx-auto mb-2"></div>
            <p className="text-xs text-monokai-text-muted">Real System</p>
          </div>
          <div className="p-3 bg-monokai-bg-secondary bg-opacity-50 rounded-lg">
            <div className="w-2 h-2 bg-monokai-purple rounded-full mx-auto mb-2"></div>
            <p className="text-xs text-monokai-text-muted">Educational</p>
          </div>
        </div>

        {/* Version info */}
        <div className="mt-8 text-xs text-monokai-text-muted">
          Version 1.0.0 • Built with Tauri & React
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
