import React from 'react';
import { X, CheckCircle, AlertTriangle, AlertCircle, Info } from 'lucide-react';
import { useNotificationStore } from '@stores/notificationStore';
import type { Notification } from '@types/index';

const NotificationCenter: React.FC = () => {
  const { notifications, removeNotification } = useNotificationStore();

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-monokai-green" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-monokai-red" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-monokai-yellow" />;
      case 'info':
        return <Info className="w-5 h-5 text-monokai-blue" />;
      default:
        return <Info className="w-5 h-5 text-monokai-blue" />;
    }
  };

  const getNotificationStyles = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return 'border-monokai-green bg-monokai-green bg-opacity-10';
      case 'error':
        return 'border-monokai-red bg-monokai-red bg-opacity-10';
      case 'warning':
        return 'border-monokai-yellow bg-monokai-yellow bg-opacity-10';
      case 'info':
        return 'border-monokai-blue bg-monokai-blue bg-opacity-10';
      default:
        return 'border-monokai-bg-tertiary bg-monokai-bg-secondary';
    }
  };

  // Only show the most recent 5 notifications
  const visibleNotifications = notifications.slice(0, 5);

  if (visibleNotifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
      {visibleNotifications.map((notification) => (
        <div
          key={notification.id}
          className={`
            p-4 rounded-lg border backdrop-blur-sm shadow-lg
            animate-slide-in transition-all duration-300
            ${getNotificationStyles(notification.type)}
          `}
        >
          <div className="flex items-start space-x-3">
            {/* Icon */}
            <div className="flex-shrink-0 mt-0.5">
              {getNotificationIcon(notification.type)}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="font-medium text-monokai-text-primary text-sm">
                {notification.title}
              </div>
              <div className="text-monokai-text-secondary text-sm mt-1">
                {notification.message}
              </div>
              <div className="text-monokai-text-muted text-xs mt-2">
                {new Date(notification.timestamp).toLocaleTimeString()}
              </div>
            </div>

            {/* Close button */}
            <button
              onClick={() => removeNotification(notification.id)}
              className="flex-shrink-0 p-1 rounded hover:bg-monokai-bg-tertiary transition-colors"
              title="Dismiss notification"
            >
              <X className="w-4 h-4 text-monokai-text-muted" />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default NotificationCenter;
