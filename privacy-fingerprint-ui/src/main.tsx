import React from "react";
import ReactDOM from "react-dom/client";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import App from "./App";
import "./styles/globals.css";

// Hide loading fallback
const loadingFallback = document.getElementById('loading-fallback');
if (loadingFallback) {
  loadingFallback.style.display = 'none';
}

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  <React.StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </React.StrictMode>,
);
