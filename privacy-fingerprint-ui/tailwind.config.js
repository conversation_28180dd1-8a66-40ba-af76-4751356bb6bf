/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Monokai Pro color palette
        monokai: {
          // Background colors
          'bg-primary': '#2D2A2E',
          'bg-secondary': '#403E41',
          'bg-tertiary': '#5B595C',
          'bg-hover': '#727072',
          
          // Text colors
          'text-primary': '#FCFCFA',
          'text-secondary': '#C1C0C0',
          'text-muted': '#939293',
          
          // Accent colors
          'green': '#A9DC76',
          'yellow': '#FFD866',
          'orange': '#FC9867',
          'red': '#FF6188',
          'purple': '#AB9DF2',
          'blue': '#78DCE8',
          'pink': '#FF6188',
          
          // Syntax highlighting colors
          'comment': '#727072',
          'string': '#FFD866',
          'number': '#AB9DF2',
          'keyword': '#FF6188',
          'function': '#A9DC76',
          'variable': '#FCFCFA',
        }
      },
      fontFamily: {
        'mono': ['JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', 'monospace'],
        'sans': ['Inter', 'system-ui', 'sans-serif'],
      },
      fontSize: {
        'xs': '0.75rem',
        'sm': '0.875rem',
        'base': '1rem',
        'lg': '1.125rem',
        'xl': '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem',
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
      },
      boxShadow: {
        'monokai': '0 4px 6px -1px rgba(45, 42, 46, 0.1), 0 2px 4px -1px rgba(45, 42, 46, 0.06)',
        'monokai-lg': '0 10px 15px -3px rgba(45, 42, 46, 0.1), 0 4px 6px -2px rgba(45, 42, 46, 0.05)',
        'monokai-xl': '0 20px 25px -5px rgba(45, 42, 46, 0.1), 0 10px 10px -5px rgba(45, 42, 46, 0.04)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'spin-slow': 'spin 3s linear infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideIn: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
      backdropBlur: {
        'xs': '2px',
      },
    },
  },
  plugins: [
    // Custom plugin for Monokai Pro utilities
    function({ addUtilities, theme }) {
      const newUtilities = {
        '.text-gradient-monokai': {
          background: `linear-gradient(135deg, ${theme('colors.monokai.green')}, ${theme('colors.monokai.blue')})`,
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
          'background-clip': 'text',
        },
        '.bg-gradient-monokai': {
          background: `linear-gradient(135deg, ${theme('colors.monokai.bg-primary')}, ${theme('colors.monokai.bg-secondary')})`,
        },
        '.border-gradient-monokai': {
          border: '1px solid transparent',
          background: `linear-gradient(${theme('colors.monokai.bg-primary')}, ${theme('colors.monokai.bg-primary')}) padding-box, linear-gradient(135deg, ${theme('colors.monokai.green')}, ${theme('colors.monokai.purple')}) border-box`,
        },
        '.scrollbar-monokai': {
          '&::-webkit-scrollbar': {
            width: '8px',
          },
          '&::-webkit-scrollbar-track': {
            background: theme('colors.monokai.bg-secondary'),
          },
          '&::-webkit-scrollbar-thumb': {
            background: theme('colors.monokai.bg-tertiary'),
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: theme('colors.monokai.bg-hover'),
          },
        },
      };
      addUtilities(newUtilities);
    },
  ],
}
