use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tauri::{State, Window};
use crate::{AppState, execute_privacy_tool, parse_tool_output};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PrivacyAnalysis {
    pub overall_privacy_score: f64,
    pub anonymization_effectiveness: f64,
    pub tracking_resistance: f64,
    pub data_minimization_score: f64,
    pub privacy_risks: Vec<PrivacyRisk>,
    pub recommendations: Vec<String>,
    pub compliance_status: ComplianceStatus,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PrivacyRisk {
    pub risk_type: String,
    pub severity: RiskSeverity,
    pub description: String,
    pub mitigation: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskSeverity {
    Low,
    Medium,
    High,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceStatus {
    pub gdpr_compliant: bool,
    pub ccpa_compliant: bool,
    pub data_retention_compliant: bool,
    pub anonymization_compliant: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct VSCodeIntegrationResult {
    pub success: bool,
    pub extension_path: String,
    pub fingerprint_location: String,
    pub api_bindings_created: Vec<String>,
    pub trial_prevention_enabled: bool,
    pub privacy_features_enabled: Vec<String>,
    pub integration_metadata: IntegrationMetadata,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegrationMetadata {
    pub integration_timestamp: String,
    pub vscode_version_detected: Option<String>,
    pub extension_manifest_found: bool,
    pub storage_permissions: Vec<String>,
    pub privacy_compliance_level: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegrationStatus {
    pub is_integrated: bool,
    pub extension_path: Option<String>,
    pub last_integration: Option<String>,
    pub bindings_status: HashMap<String, bool>,
    pub trial_prevention_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EducationalContent {
    pub topic: String,
    pub title: String,
    pub description: String,
    pub examples: Vec<String>,
    pub best_practices: Vec<String>,
    pub related_topics: Vec<String>,
}

#[tauri::command]
pub async fn analyze_privacy(
    state: State<'_, AppState>,
    window: Window,
) -> Result<PrivacyAnalysis, String> {
    log::info!("Analyzing privacy protection");
    
    let current_fp = {
        let fp_guard = state.current_fingerprint.lock().await;
        fp_guard.clone()
    };
    
    if let Some(fingerprint) = current_fp {
        window.emit("privacy_analysis_started", ()).map_err(|e| e.to_string())?;
        
        // Create temporary file for analysis
        let temp_path = std::env::temp_dir().join("analyze_fingerprint.json");
        let fp_json = serde_json::to_string_pretty(&fingerprint).map_err(|e| e.to_string())?;
        std::fs::write(&temp_path, fp_json).map_err(|e| e.to_string())?;
        
        let args = vec![
            "--format", "json",
            "analyze-privacy",
            "--fingerprint-path", temp_path.to_str().unwrap(),
            "--generate-report",
        ];
        
        match execute_privacy_tool(args).await {
            Ok(output) => {
                let _ = std::fs::remove_file(&temp_path);
                
                match parse_tool_output::<PrivacyAnalysis>(&output) {
                    Ok(analysis) => {
                        window.emit("privacy_analysis_completed", &analysis).map_err(|e| e.to_string())?;
                        Ok(analysis)
                    }
                    Err(e) => {
                        let error_msg = format!("Failed to parse privacy analysis: {}", e);
                        window.emit("privacy_analysis_failed", &error_msg).map_err(|e| e.to_string())?;
                        Err(error_msg)
                    }
                }
            }
            Err(e) => {
                let _ = std::fs::remove_file(&temp_path);
                let error_msg = format!("Failed to analyze privacy: {}", e);
                window.emit("privacy_analysis_failed", &error_msg).map_err(|e| e.to_string())?;
                Err(error_msg)
            }
        }
    } else {
        Err("No fingerprint to analyze".to_string())
    }
}

#[tauri::command]
pub async fn get_privacy_recommendations(
    state: State<'_, AppState>,
) -> Result<Vec<String>, String> {
    let current_fp = {
        let fp_guard = state.current_fingerprint.lock().await;
        fp_guard.clone()
    };
    
    if let Some(fingerprint) = current_fp {
        let mut recommendations = Vec::new();
        
        // Analyze privacy score and provide recommendations
        let privacy_score = fingerprint.privacy_metadata.privacy_score;
        
        if privacy_score < 0.7 {
            recommendations.push("Consider using a higher privacy level for better protection".to_string());
        }
        
        if !fingerprint.rotation_enabled {
            recommendations.push("Enable automatic fingerprint rotation to prevent long-term tracking".to_string());
        }
        
        if fingerprint.privacy_metadata.anonymization_applied.len() < 3 {
            recommendations.push("Apply additional anonymization techniques for enhanced privacy".to_string());
        }
        
        if fingerprint.privacy_metadata.tracking_resistance < 0.8 {
            recommendations.push("Increase tracking resistance by using more obfuscation methods".to_string());
        }
        
        // Add general recommendations
        recommendations.push("Regularly review and update privacy settings".to_string());
        recommendations.push("Monitor privacy analysis reports for potential improvements".to_string());
        
        Ok(recommendations)
    } else {
        Ok(vec!["Generate a fingerprint first to get personalized recommendations".to_string()])
    }
}

#[tauri::command]
pub async fn integrate_vscode(
    extension_path: String,
    enable_trial_prevention: bool,
    state: State<'_, AppState>,
    window: Window,
) -> Result<VSCodeIntegrationResult, String> {
    log::info!("Integrating with VS Code extension at: {}", extension_path);
    
    window.emit("vscode_integration_started", ()).map_err(|e| e.to_string())?;
    
    let mut args = vec![
        "--format", "json",
        "integrate",
        "--extension-path", &extension_path,
    ];
    
    if enable_trial_prevention {
        args.push("--enable-trial-prevention");
    }
    
    match execute_privacy_tool(args).await {
        Ok(output) => {
            match parse_tool_output::<VSCodeIntegrationResult>(&output) {
                Ok(result) => {
                    // Update app config with extension path
                    {
                        let mut config = state.config.lock().await;
                        config.extension_path = Some(extension_path);
                    }
                    
                    window.emit("vscode_integration_completed", &result).map_err(|e| e.to_string())?;
                    Ok(result)
                }
                Err(e) => {
                    let error_msg = format!("Failed to parse integration result: {}", e);
                    window.emit("vscode_integration_failed", &error_msg).map_err(|e| e.to_string())?;
                    Err(error_msg)
                }
            }
        }
        Err(e) => {
            let error_msg = format!("Failed to integrate with VS Code: {}", e);
            window.emit("vscode_integration_failed", &error_msg).map_err(|e| e.to_string())?;
            Err(error_msg)
        }
    }
}

#[tauri::command]
pub async fn get_integration_status(
    state: State<'_, AppState>,
) -> Result<IntegrationStatus, String> {
    let config = state.config.lock().await;
    
    let is_integrated = config.extension_path.is_some();
    let extension_path = config.extension_path.clone();
    
    // Check if bindings exist
    let mut bindings_status = HashMap::new();
    if let Some(ref path) = extension_path {
        let bindings = vec![
            "fingerprint_storage.ts",
            "trial_prevention.ts",
            "lifecycle_events.ts",
            "system_info.ts",
            "data_encryption.ts",
        ];
        
        for binding in bindings {
            let binding_path = std::path::Path::new(path).join("src").join(binding);
            bindings_status.insert(binding.to_string(), binding_path.exists());
        }
    }
    
    Ok(IntegrationStatus {
        is_integrated,
        extension_path,
        last_integration: None, // Would need to track this
        bindings_status,
        trial_prevention_active: false, // Would need to check this
    })
}

#[tauri::command]
pub async fn export_privacy_report(
    file_path: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let current_fp = {
        let fp_guard = state.current_fingerprint.lock().await;
        fp_guard.clone()
    };
    
    if let Some(fingerprint) = current_fp {
        // Generate privacy analysis first
        let temp_path = std::env::temp_dir().join("report_fingerprint.json");
        let fp_json = serde_json::to_string_pretty(&fingerprint).map_err(|e| e.to_string())?;
        std::fs::write(&temp_path, fp_json).map_err(|e| e.to_string())?;
        
        let args = vec![
            "analyze-privacy",
            "--fingerprint-path", temp_path.to_str().unwrap(),
            "--generate-report",
        ];
        
        match execute_privacy_tool(args).await {
            Ok(_) => {
                let _ = std::fs::remove_file(&temp_path);
                
                // Check if privacy_report.md was generated and copy it
                let report_path = std::path::Path::new("privacy_report.md");
                if report_path.exists() {
                    std::fs::copy(report_path, &file_path).map_err(|e| e.to_string())?;
                    Ok(true)
                } else {
                    Err("Privacy report was not generated".to_string())
                }
            }
            Err(e) => {
                let _ = std::fs::remove_file(&temp_path);
                Err(format!("Failed to generate privacy report: {}", e))
            }
        }
    } else {
        Err("No fingerprint to generate report for".to_string())
    }
}

#[tauri::command]
pub async fn get_educational_content(topic: String) -> Result<EducationalContent, String> {
    match topic.as_str() {
        "privacy" => Ok(EducationalContent {
            topic: "privacy".to_string(),
            title: "Privacy-Focused Fingerprinting".to_string(),
            description: "Learn about privacy-first fingerprinting that balances uniqueness with user anonymity.".to_string(),
            examples: vec![
                "Generates authentic system fingerprints without compromising privacy".to_string(),
                "Implements data anonymization and obfuscation techniques".to_string(),
                "Supports configurable privacy levels (low to maximum)".to_string(),
                "Enables fingerprint rotation to prevent long-term tracking".to_string(),
                "Uses differential privacy techniques where applicable".to_string(),
            ],
            best_practices: vec![
                "Always use the highest privacy level appropriate for your use case".to_string(),
                "Enable automatic fingerprint rotation".to_string(),
                "Regularly review privacy analysis reports".to_string(),
                "Minimize data collection to only what's necessary".to_string(),
                "Be transparent about data collection with users".to_string(),
            ],
            related_topics: vec!["rotation".to_string(), "compliance".to_string(), "anonymization".to_string()],
        }),
        "rotation" => Ok(EducationalContent {
            topic: "rotation".to_string(),
            title: "Fingerprint Rotation".to_string(),
            description: "Automatic fingerprint rotation prevents long-term tracking while maintaining functionality.".to_string(),
            examples: vec![
                "Periodically generates new fingerprints with similar characteristics".to_string(),
                "Maintains enough similarity for legitimate functionality".to_string(),
                "Prevents cross-session tracking and profiling".to_string(),
                "Configurable rotation intervals and triggers".to_string(),
                "Preserves essential system characteristics".to_string(),
            ],
            best_practices: vec![
                "Set appropriate rotation intervals based on privacy needs".to_string(),
                "Monitor rotation effectiveness through similarity scores".to_string(),
                "Use gradual rotation for better functionality preservation".to_string(),
                "Enable threat-based rotation triggers".to_string(),
                "Keep rotation history for analysis".to_string(),
            ],
            related_topics: vec!["privacy".to_string(), "tracking".to_string(), "security".to_string()],
        }),
        "compliance" => Ok(EducationalContent {
            topic: "compliance".to_string(),
            title: "Privacy Compliance".to_string(),
            description: "Understanding and achieving compliance with privacy regulations like GDPR and CCPA.".to_string(),
            examples: vec![
                "GDPR compliance through data minimization".to_string(),
                "CCPA compliance with user control mechanisms".to_string(),
                "Automatic data retention policy enforcement".to_string(),
                "Anonymization compliance verification".to_string(),
                "Transparent data collection practices".to_string(),
            ],
            best_practices: vec![
                "Implement data minimization principles".to_string(),
                "Provide clear user consent mechanisms".to_string(),
                "Enable user data deletion capabilities".to_string(),
                "Maintain audit trails for compliance verification".to_string(),
                "Regular compliance assessments and updates".to_string(),
            ],
            related_topics: vec!["privacy".to_string(), "gdpr".to_string(), "ccpa".to_string()],
        }),
        _ => Err(format!("Unknown educational topic: {}", topic)),
    }
}

#[tauri::command]
pub async fn get_privacy_explanation(concept: String) -> Result<String, String> {
    match concept.as_str() {
        "anonymization" => Ok("Data anonymization is the process of removing or modifying personally identifiable information from data sets. In fingerprinting, this includes hashing usernames, hostnames, and other personal identifiers to protect user privacy while maintaining uniqueness.".to_string()),
        "obfuscation" => Ok("Data obfuscation involves deliberately making data unclear or confusing to protect sensitive information. For example, instead of storing exact CPU models, we store obfuscated signatures that preserve general characteristics while hiding specific details.".to_string()),
        "differential_privacy" => Ok("Differential privacy is a mathematical framework that provides strong privacy guarantees by adding carefully calibrated noise to data. This ensures that individual data points cannot be identified while preserving overall statistical properties.".to_string()),
        "tracking_resistance" => Ok("Tracking resistance measures how well a fingerprint prevents cross-session tracking and profiling. Higher tracking resistance means it's more difficult for third parties to correlate fingerprints across different sessions or time periods.".to_string()),
        "data_minimization" => Ok("Data minimization is the principle of collecting and processing only the minimum amount of personal data necessary for a specific purpose. In fingerprinting, this means using ranges instead of exact values and avoiding unnecessary data collection.".to_string()),
        _ => Err(format!("Unknown privacy concept: {}", concept)),
    }
}
