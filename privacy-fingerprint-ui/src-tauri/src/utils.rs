use std::path::PathBuf;
use anyhow::Result;

/// Get the application data directory
pub fn get_app_data_dir() -> Result<PathBuf> {
    let app_dir = dirs::data_dir()
        .ok_or_else(|| anyhow::anyhow!("Failed to get data directory"))?
        .join("privacy-fingerprint-ui");
    
    if !app_dir.exists() {
        std::fs::create_dir_all(&app_dir)?;
    }
    
    Ok(app_dir)
}

/// Get the fingerprints storage directory
pub fn get_fingerprints_dir() -> Result<PathBuf> {
    let fingerprints_dir = get_app_data_dir()?.join("fingerprints");
    
    if !fingerprints_dir.exists() {
        std::fs::create_dir_all(&fingerprints_dir)?;
    }
    
    Ok(fingerprints_dir)
}

/// Get the reports storage directory
pub fn get_reports_dir() -> Result<PathBuf> {
    let reports_dir = get_app_data_dir()?.join("reports");
    
    if !reports_dir.exists() {
        std::fs::create_dir_all(&reports_dir)?;
    }
    
    Ok(reports_dir)
}

/// Format file size in human-readable format
pub fn format_file_size(size: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = size as f64;
    let mut unit_index = 0;
    
    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }
    
    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

/// Validate privacy level string
pub fn validate_privacy_level(level: &str) -> bool {
    matches!(level, "Low" | "Medium" | "High" | "Maximum")
}

/// Get privacy level color for UI
pub fn get_privacy_level_color(level: &str) -> &'static str {
    match level {
        "Low" => "#FF6188",      // Red
        "Medium" => "#FFD866",   // Yellow
        "High" => "#A9DC76",     // Green
        "Maximum" => "#AB9DF2",  // Purple
        _ => "#FCFCFA",          // Default light gray
    }
}

/// Calculate privacy score color
pub fn get_privacy_score_color(score: f64) -> &'static str {
    if score >= 0.8 {
        "#A9DC76"  // Green
    } else if score >= 0.6 {
        "#FFD866"  // Yellow
    } else if score >= 0.4 {
        "#FC9867"  // Orange
    } else {
        "#FF6188"  // Red
    }
}

/// Format timestamp for display
pub fn format_timestamp(timestamp: &str) -> String {
    // Parse ISO timestamp and format for display
    if let Ok(dt) = chrono::DateTime::parse_from_rfc3339(timestamp) {
        dt.format("%Y-%m-%d %H:%M:%S").to_string()
    } else {
        timestamp.to_string()
    }
}

/// Generate unique filename with timestamp
pub fn generate_filename(prefix: &str, extension: &str) -> String {
    let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
    format!("{}_{}.{}", prefix, timestamp, extension)
}

/// Sanitize filename for cross-platform compatibility
pub fn sanitize_filename(filename: &str) -> String {
    filename
        .chars()
        .map(|c| match c {
            '<' | '>' | ':' | '"' | '/' | '\\' | '|' | '?' | '*' => '_',
            c => c,
        })
        .collect()
}

/// Check if a path is a valid VS Code extension directory
pub fn is_valid_vscode_extension_dir(path: &str) -> bool {
    let path = PathBuf::from(path);
    
    // Check if package.json exists
    let package_json = path.join("package.json");
    if !package_json.exists() {
        return false;
    }
    
    // Try to read and parse package.json
    if let Ok(content) = std::fs::read_to_string(&package_json) {
        if let Ok(json) = serde_json::from_str::<serde_json::Value>(&content) {
            // Check for VS Code engine requirement
            return json
                .get("engines")
                .and_then(|e| e.get("vscode"))
                .is_some();
        }
    }
    
    false
}

/// Get VS Code extensions directory for the current platform
pub fn get_vscode_extensions_dir() -> Option<PathBuf> {
    let home_dir = dirs::home_dir()?;
    
    #[cfg(target_os = "windows")]
    {
        Some(home_dir.join(".vscode").join("extensions"))
    }
    
    #[cfg(target_os = "macos")]
    {
        Some(home_dir.join(".vscode").join("extensions"))
    }
    
    #[cfg(target_os = "linux")]
    {
        Some(home_dir.join(".vscode").join("extensions"))
    }
    
    #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
    {
        None
    }
}

/// Find VS Code extensions matching a pattern
pub fn find_vscode_extensions(pattern: &str) -> Vec<PathBuf> {
    let mut extensions = Vec::new();
    
    if let Some(extensions_dir) = get_vscode_extensions_dir() {
        if let Ok(entries) = std::fs::read_dir(&extensions_dir) {
            for entry in entries.flatten() {
                if let Ok(file_type) = entry.file_type() {
                    if file_type.is_dir() {
                        let dir_name = entry.file_name().to_string_lossy().to_lowercase();
                        if dir_name.contains(&pattern.to_lowercase()) {
                            let path = entry.path();
                            if is_valid_vscode_extension_dir(&path.to_string_lossy()) {
                                extensions.push(path);
                            }
                        }
                    }
                }
            }
        }
    }
    
    extensions.sort();
    extensions
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_format_file_size() {
        assert_eq!(format_file_size(0), "0 B");
        assert_eq!(format_file_size(1023), "1023 B");
        assert_eq!(format_file_size(1024), "1.0 KB");
        assert_eq!(format_file_size(1536), "1.5 KB");
        assert_eq!(format_file_size(1048576), "1.0 MB");
    }
    
    #[test]
    fn test_validate_privacy_level() {
        assert!(validate_privacy_level("Low"));
        assert!(validate_privacy_level("Medium"));
        assert!(validate_privacy_level("High"));
        assert!(validate_privacy_level("Maximum"));
        assert!(!validate_privacy_level("Invalid"));
        assert!(!validate_privacy_level(""));
    }
    
    #[test]
    fn test_sanitize_filename() {
        assert_eq!(sanitize_filename("normal_file.txt"), "normal_file.txt");
        assert_eq!(sanitize_filename("file<with>bad:chars"), "file_with_bad_chars");
        assert_eq!(sanitize_filename("file/with\\path|chars"), "file_with_path_chars");
    }
}
