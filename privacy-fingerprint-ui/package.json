{"name": "privacy-fingerprint-ui", "private": true, "version": "1.0.0", "type": "module", "description": "Privacy-focused fingerprint generator desktop application", "author": "Security Research Team", "license": "MIT", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "tauri:bundle": "tauri build --bundles all"}, "dependencies": {"@tauri-apps/api": "^1.5.3", "chart.js": "^4.4.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "prismjs": "^1.29.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "zustand": "^4.4.7"}, "devDependencies": {"@tauri-apps/cli": "^1.5.8", "@types/node": "^20.10.0", "@types/prismjs": "^1.26.3", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.2", "vite": "^5.0.5"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.12.0"}