<!doctype html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Privacy Fingerprint Generator</title>
    
    <!-- Preload fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Meta tags for security and performance -->
    <meta name="description" content="Privacy-focused fingerprint generator for security research and educational purposes">
    <meta name="keywords" content="privacy, fingerprinting, security, research, education">
    <meta name="author" content="Security Research Team">
    
    <!-- Security headers -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; script-src 'self' 'unsafe-inline';">
    
    <style>
      /* Prevent flash of unstyled content */
      body {
        background-color: #2D2A2E;
        color: #FCFCFA;
        font-family: 'Inter', system-ui, sans-serif;
      }
      
      /* Loading spinner */
      .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(169, 220, 118, 0.3);
        border-radius: 50%;
        border-top-color: #A9DC76;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      /* Custom scrollbar for webkit browsers */
      ::-webkit-scrollbar {
        width: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: #403E41;
      }
      
      ::-webkit-scrollbar-thumb {
        background: #5B595C;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #727072;
      }
    </style>
  </head>
  <body class="bg-monokai-bg-primary text-monokai-text-primary antialiased">
    <div id="root" class="min-h-screen">
      <!-- Loading fallback -->
      <div id="loading-fallback" class="flex items-center justify-center min-h-screen">
        <div class="text-center">
          <div class="loading-spinner mx-auto mb-4"></div>
          <p class="text-monokai-text-secondary">Loading Privacy Fingerprint Generator...</p>
        </div>
      </div>
    </div>
    
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Remove loading fallback once React app loads -->
    <script>
      window.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
          const fallback = document.getElementById('loading-fallback');
          if (fallback && document.querySelector('[data-reactroot]')) {
            fallback.style.display = 'none';
          }
        }, 1000);
      });
    </script>
  </body>
</html>
