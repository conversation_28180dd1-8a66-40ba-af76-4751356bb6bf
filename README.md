# VS Code Extension Security Research Tools

A comprehensive suite of Rust-based educational tools for analyzing and understanding VS Code extension trial/subscription bypass techniques and security mechanisms.

## ⚠️ IMPORTANT DISCLAIMER

**These tools are designed for EDUCATIONAL and SECURITY RESEARCH purposes only.**

- **DO NOT** use these tools to bypass commercial software licenses
- **DO NOT** use these tools to circumvent trial restrictions
- **DO NOT** use these tools for any illegal or unethical purposes
- These tools work with mock/dummy data and do not actually bypass real extensions
- The goal is to understand and improve extension security mechanisms

## 🎯 Purpose

This project provides legitimate security research tools that help:

- **Security Researchers**: Understand extension protection mechanisms
- **Extension Developers**: Improve trial/subscription security
- **Educators**: Teach about software security and protection techniques
- **Students**: Learn about system fingerprinting and authentication flows

## 🛠️ Tools Included

### 1. Trial Reset Simulator
Simulates common trial bypass attempts to demonstrate why they fail with modern extensions.

```bash
cargo run --bin trial-reset-simulator -- --help
```

**Features:**
- Simulates storage clearing attempts
- Demonstrates system identifier modification
- Shows why modern extensions resist these techniques
- Educational analysis of prevention mechanisms

### 2. Fingerprint Analyzer
Analyzes system fingerprinting techniques used by VS Code extensions.

```bash
cargo run --bin fingerprint-analyzer -- --help
```

**Features:**
- Collects system information (machine ID, hardware specs)
- Demonstrates fingerprint creation algorithms
- Shows what data points extensions typically gather
- Analyzes fingerprint uniqueness and persistence

### 3. Storage Inspector
Safely examines VS Code extension storage patterns without modification.

```bash
cargo run --bin storage-inspector -- --help
```

**Features:**
- Read-only analysis of VS Code storage mechanisms
- Examines globalState, secrets, and workspace storage
- Identifies trial-related data persistence patterns
- Safe inspection without modifying actual data

### 4. Network Traffic Simulator
Simulates subscription validation network requests for educational purposes.

```bash
cargo run --bin network-traffic-simulator -- --help
```

**Features:**
- Simulates authentication flows and API endpoints
- Demonstrates subscription validation mechanisms
- Shows network-based trial tracking
- Educational tool for understanding API security

### 5. Profile Validator (NEW)
Validates VS Code profile configurations and analyzes their impact on trial tracking.

```bash
cargo run --bin profile-validator -- --help
```

**Features:**
- VS Code profile detection and analysis
- Trial data persistence validation across profiles
- Cross-profile and server-side persistence testing
- Security assessment and bypass resistance scoring
- Profile recommendations for different testing scenarios
- Educational explanations of profile concepts

## 🚀 Quick Start

### Prerequisites
- Rust 1.70+ installed
- VS Code (for realistic testing environment)

### Installation

```bash
# Clone the repository
git clone https://github.com/example/vscode-extension-security-tools
cd vscode-extension-security-tools

# Build all tools
cargo build --release

# Run a specific tool
cargo run --bin fingerprint-analyzer
```

### Basic Usage

```bash
# Analyze system fingerprinting
cargo run --bin fingerprint-analyzer -- --output fingerprint-report.json

# Simulate trial reset attempts (educational only)
cargo run --bin trial-reset-simulator -- --simulate-all

# Inspect storage patterns
cargo run --bin storage-inspector -- --vscode-path ~/.vscode

# Simulate network validation
cargo run --bin network-traffic-simulator -- --mock-server

# Validate VS Code profile configuration
cargo run --bin profile-validator -- detect-profile --detailed
```

## 📚 Educational Value

Each tool includes comprehensive documentation explaining:

- **How the technique works**: Technical implementation details
- **Why it typically fails**: Modern protection mechanisms
- **Security implications**: Impact on software protection
- **Countermeasures**: How developers can improve security
- **Ethical considerations**: Responsible disclosure and usage

## 🔒 Security and Ethics

### Ethical Guidelines

1. **Research Only**: Use only for legitimate security research
2. **No Real Bypassing**: Tools work with mock data, not real extensions
3. **Responsible Disclosure**: Report vulnerabilities through proper channels
4. **Educational Focus**: Share knowledge to improve security, not exploit it
5. **Legal Compliance**: Ensure usage complies with local laws and ToS

### Built-in Safeguards

- **Read-only operations**: No modification of actual extension data
- **Mock data usage**: Simulations use dummy data, not real extensions
- **Ethical warnings**: Clear disclaimers and usage guidelines
- **Educational focus**: Comprehensive explanations of security concepts

## 🧪 Testing

```bash
# Run all tests
cargo test

# Run tests for a specific tool
cargo test --bin fingerprint-analyzer

# Run with verbose output
cargo test -- --nocapture
```

## 📖 Documentation

- [Documentation Hub](docs/README.md) - Complete documentation index
- [Trial Reset Simulator Guide](tools/rust/trial-reset-simulator/README.md)
- [Fingerprint Analyzer Guide](tools/rust/fingerprint-analyzer/README.md)
- [Storage Inspector Guide](tools/rust/storage-inspector/README.md)
- [Network Traffic Simulator Guide](tools/rust/network-traffic-simulator/README.md)
- [Profile Validator Guide](tools/rust/profile-validator/README.md)
- [VSIX Analyzer Guide](tools/python/vsix-analyzer/README.md)
- [Ethical Usage Guidelines](docs/guides/ETHICS.md)

## 🤝 Contributing

We welcome contributions that enhance the educational value of these tools:

1. Fork the repository
2. Create a feature branch
3. Add comprehensive tests and documentation
4. Ensure ethical compliance
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚖️ Legal Notice

This software is provided for educational and research purposes only. Users are responsible for ensuring their usage complies with applicable laws, terms of service, and ethical guidelines. The authors disclaim any responsibility for misuse of these tools.

## 🔗 Related Resources

- [VS Code Extension API Documentation](https://code.visualstudio.com/api)
- [Software Security Best Practices](https://owasp.org/)
- [Responsible Disclosure Guidelines](https://www.bugcrowd.com/resource/what-is-responsible-disclosure/)
